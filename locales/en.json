{
    "validations": {
        "required": "{property} field is required",
        "minLength": "The {property} field must be at least {min} characters",
        "email": "Value is not a valid email address",
        "minValue": "The {property} field must be greater than {min}",
        "sameAs": "The {property} field must be the same as {sameAs}",
        "maxValue": "The {property} field must be less or equal {max}"
    },
    "fields": {
        "ar": "Field By Arabic",
        "en": "Field By English",
        "receipt": "Receipt",
        "beneficiary_name": "Beneficiary Name",
        "email": "Email Address",
        "new_email": "New Email Address",
        "password": "Password",
        "name": "Name",
        "type": "Type",
        "category_id": "Category",
        "price": "Price",
        "slug": "Slug",
        "team_id": "Branch",
        "description": "Description",
        "minutes": "Minutes",
        "hours": "Hours",
        "minute": "Min",
        "hour": "Hr",
        "title": "Title",
        "content": "Content",
        "clients_in_same_time": "Number of clients at the same time",
        "end_at": "End At",
        "start_at": "Start At",
        "end_time": "End Time",
        "start_time": "Start Time",
        "day": "Day",
        "address": "Address",
        "htmlTag": "Field Type",
        "label": "Label",
        "values": "Options",
        "first_name": "First Name",
        "last_name": "Last Name",
        "code": "Code",
        "discount_amount": "Discount ",
        "usage_limit": "Usage Limit",
        "start_date": "Start Date",
        "end_date": "End Date",
        "company_name": "Company Name",
        "industry": "Industry",
        "expiry_date": "Expiry Date",
        "subdomain": "Subdomain",
        "background": "Background color",
        "primary": "Primary color",
        "secondary": "Secondary color",
        "maintenance_title": "Maintenance Title",
        "maintenance_body": "Maintenance Body",
        "number_of_employees": "Number of employees",
        "password_confirmation": "Password Confirmation",
        "current_password": "Current Password",
        "your_current_plan": "Your Current Plan",
        "your_next_billing_date": "Your Next Billing Date",
        "current_plan_features": "Current Plan Features",
        "phone": "Phone",
        "start_of_week": "Start of Week",
        "industry_id": "Industry",
        "lang": "Language",
        "currency": "Currency",
        "country": "Country",
        "time_format": "Time Format",
        "status": "Status",
        "color": "Color",
        "customer_id": "Customer",
        "staff_id": "Staff",
        "startDate": "Start Date",
        "id": "service",
        "staff_users": "Staff",
        "date": "Date",
        "slot": "Slot",
        "staffId": "Service provider",
        "serviceId": "Service",
        "confirmPassword": "Confirm Password",
        "app_id": "App",
        "scheduled_at": "Campaign Schedule at",
        "quantity": "Quantity",
        "duration": "Duration",
        "service_duration": "Service Duration",
        "value": "Value",
        "location": "branch type",
        "start": "Start Date",
        "vat_id": "VAT ID ",
        "billing_address": "Address",
        "billing_name": "Tax Registration Number",
        "amount": "Amount",
        "reference_no": "Reference No",
        "taxable": "Taxable",
        "payment_method": "Payment Method",
        "storeUrl": "Store URL",
        "select_branch": "Select Branch",
        "total_amount": "Total Amount",
        "add_service": "Add Service",
        "add_product": "Add Product",
        "systemType": "System Type",
        "service_id": "Service",
        "gift_card": "Gift Card",
        "select_items_for_discount": "Select items for discount",
        "branches": "Branches",
        "services": "Services",
        "staff": "Staff",
        "customers": "Customers",
        "products": "Products",
        "categories": "Categories",
        "coupons": "Coupons"

    },
    "appTitle": "Mahjoz booking application",
    "home": "Home",
    "cash": "Cash",
    "color": "Color",
    "syncedInvoices": "Synced Invoices",
    "activity": "Activity logs",
    "status": "Status",
    "booking_number": "Appointment Number",
    "the_location": "Location",
    "duration": "Duration",
    "alert": "Alert",
    "note": "Note",
    "confirmRefund": "A return invoice will be made for the entire invoice, and the amount will be returned to the customer for the full amount paid only.",
    "no_refundable_items": "There are no refundable items",
    "cancel": "Cancel",
    "invoices": "Invoices",
    "invoice": {
        "heading": "Invoices",
        "number": "Invoice Number",
        "number_template": "Invoice #{id}",
        "created_at": "Created At",
        "download": "Download",
        "previous": "Previous",
        "next": "Next",
        "amount": "Amount",
        "type": "Type",
        "created_by": "Created By",
        "invoice": "Invoice",
        "refund": "Refund",
        "no_invoices": "No invoices yet",
        "billing": {
            "title": "Billing Information",
            "billing_name": "Company Name",
            "billing_country": "Country",
            "billing_address": "Address Line",
            "vat_id": "VAT ID",
            "vat_id_format": "VAT ID must be 15 digits starting and ending with 3",
            "save": "Save Changes",
            "saved": "Billing information saved successfully"
        }
    },
    "duration_hours": "Duration (Hours)",
    "duration_minutes": "Duration (Minutes)",
    "Booking rejected successfully": "Booking status changed to Rejected",
    "Booking confirmed successfully": "Booking status changed to Confirmed",
    "Booking Change Status": "Booking Status",
    "reasonsForCancellation": "Reason for cancellation",
    "description": "Description",
    "price": "Price",
    "deposit": "Deposit",
    "viewReport": "View Report",
    "deposit_help": "The amount that must be paid as a deposit when booking online.",
    "coupon_code_help": "Letters and numbers only, without spaces",
    "viewBooking": "View appointment",
    "enable_deposit": "Enable deposit",
    "followingServicesWillBeCanceled": "The following services will be cancelled:",
    "bookingCancelledSuccessfully": "Booking cancelled successfully",
    "bookingCancelledError": "There was an error when while canceling the appointment",
    "New": "New",
    "close": "Close",
    "details": "Details",
    "history": "History",
    "additionalInfo": "Additional Info",
    "fail": "Request failed",
    "Loading": "Loading",
    "today": "Today",
    "defaults roles cannot be edited or deleted.": "The default roles cannot be edited or deleted",
    "Error": "Error",
    "Success": "Success",
    "invalid": "Invalid",
    "InvoiceTo": "Invoice To",
    "InvoiceDate": "Invoice Date",
    "checkout": "Complete Payment",
    "subtotal": "Subtotal",
    "vat": "VAT",
    "refund_amount": "Refund Amount",
    "remaining": "Remaining",
    "number": "Num",
    "total": "Total",
    "paid": "Paid",
    "active": "Active",
    "confirm": "Confirm",
    "btn_confirm": "Confirm",
    "reject": "Reject",
    "noActive": "No Active",
    "start_of_week": "Start of Week",
    "checkDomain": "This field does not allow the Arabic language, numbers or symbols",
    "Confirm re-schedule booking's time to": "Confirm re-schedule appointment's time to",
    "Bookings": "Bookings",
    "subscriptionExpired": "Your subscription has expired",
    "showPackages": "Plans & Pricing",
    "Customer": "Customer",
    "Date": "Date",
    "trail": "{user} , Your free trial expires after {day} days",
    "subscribe_banner": "Subscribe",
    "extend_trial": "Sorry , Your free trail expired ! ,to extend your trail",
    "Click_here": "Click Here",
    "Traillday": "day",
    "Trailldays": "days",
    "twodays": "Two day",
    "oneday": "One day",
    "current": "current",
    "save": "Save",
    "yes": "Yes",
    "no": "No",
    "path": "Path",
    "enter": "Enter",
    "view": "View",
    "download": "Download",
    "country": "Country",
    "quantity": "Quantity",
    "dueAmount": "Due amount",
    "tax": "TAX INVOICE",
    "bufferTime": "buffer time",
    "bufferTimePlaceholder": "5 or 10 mins",
    "displayOnBookingPage": "Display on store",
    "social_media": "Social Media Accounts",
    "management": "Maintenance Mode",
    "welcomeHeadErr": "Please handle the following errors",
    "bookingDetails": "Booking Details",
    "from": "From",
    "to": "To",
    "with": "with",
    "report_campaign": "Campaign Report",
    "warn_campaign_content_var": "You should add varaible name in campaign content for sending whatsapp message to avoid blocking whatsapp account",
    "noNotes": "No notes available",
    "backToPages": "Back To Booking Page Settings",
    "qrCode": "Download QrCode",
    "coiped": "Copied",
    "soon": "Soon",
    "writeDesc": "Write Description",
    "pageDesc": "Description",
    "pageTitle": "Title",
    "title": "Title",
    "pageNotAvalible": "No Data Avaliable Please Create One page at least",
    "unauthorized": "You do not have permission to view this webpage | 401",
    "errRequired": "Value is required",
    "activate": "Activate",
    "deactivate": "Deactivate User",
    "pos": {
        "send_invoice": "Send Invoice",
        "status": {
            "paid": "Paid",
            "unpaid": "Unpaid",
            "partial": "Partial",
            "partially-paid": "Partial Paid",
            "partial-paid": "Partial Paid",
            "draft": "Draft",
            "approved": "Un Paid",
            "confirmed": "Confirmed",
            "active": "Active",
            "waiting-for-payment": "Waiting For Payment",
            "completed": "Completed"
        },
        "print": "Print",
        "invoice": "Invoice",
        "show_invoice": "Show Invoice",
        "refund": "Refund",
        "services": "Services",
        "products": "Products",
        "packages": "Packages",
        "amount": "Price",
        "guest": "Guest",
        "pay": "Pay now",
        "select_client": "Select Client",
        "click_to_edit_the_amount": "Modify amount",
        "payment_method": "Payment Method",
        "checkout": "Complete payment",
        "add_payment": "Payment registration",
        "subtotal": "Subtotal",
        "discount": "Discount",
        "totalTaxable": "Total Taxable",
        "total": "Total",
        "tax": "Tax"
    },
    "operations": {
        "created": "Item created successfully",
        "updated": "Item updated successfully",
        "deleted": "Item deleted successfully",
        "switched": "Switched to the branch successfully",
        "activated": "Activated successfully",
        "deactivated": "Deactivated successfully",
        "toggled": "Toggled successfully",
        "installed": "App installed successfully",
        "uninstalled": "App uninstalled successfully",
        "addCustomize": "customization completed successfully",
        "deleteCustomize": "customization deleted successfully",
        "isSwitched": "Switched Done",
        "booked": "Payment registration",
        "notFound": "Page is not found",
        "located": "The Branch has been successfully located",
        "updated_notifications_settings": "Notifications settings updated successfully",
        "error": "Sorry, something went wrong. Please try again later or contact our support team.",
        "invitation": {
            "sent": "Invitation sent successfully",
            "accepted": "Invitation accepted successfully",
            "deleted": "Invitation deleted successfully"
        },
        "subscriptionEnded": "Your subscription has ended",
        "restored": "Data restored successfully",
        "installRedirect": "You are being redirected to authorization page...",
        "emailSent": "Email was successfully sent",
        "whatsappSent": "WhatsApp was successfully sent",
        "retried": "Notification was retried",
        "providerAdded": "Service provider User added successfully"
    },
    "unverified_email": {
        "alert": "Your Account needs email activation! Please activate your email by clicking on the sent activation link. It is necessary to log in to your account before clicking on the link if the activation is from a different device",
        "resend": "Resend verification link",
        "resend_code": "Resend code",
        "already_have_account": "Already have an account?",
        "verify_otp": "Verify OTP",
        "verify_otp_desc1": "We've sent a 4-digit verification code to your mobile number ",
        "verify_otp_desc2": "Please enter the code to verify your number."
    },
    "password": {
        "title": "Forgot Password",
        "subtitle": "Don't worry! It happens. Please enter the email address associated with your account.",
        "yourPassword": "Forgot your password?",
        "enterPassword": "Password",
        "confirm": "Confirm Password",
        "reset": " Reset Password"
    },
    "bookingItems": {
        "print": "Print Booking",
        "viewInvoice": "View Invoice",
        "remove": "Remove Booking",
        "bookPay": "Payment registration",
        "cancel": "Cancel Booking",
        "cancelConfirmation": "Cancel Appointment Confirmation",
        "cancelDescription": "The service {service} with {staff} will be cancelled.",
        "cancelReason": "Reason for cancellation",
        "otherReason": "Do you have another reason? Share it with us",
        "otherReasonPlaceholder": "Write here...",
        "cancelAppointment": "Cancel Appointment"
    },
    "htmlTags": {
        "text": "Input text",
        "select": "Options",
        "textarea": "Large Input text",
        "number": "Number",
        "date": "Date",
        "enter_options": "Enter options",
        "location": "Location in Map"
    },
    "form": {
        "content": "content",
        "name": "Name",
        "firstName": "First Name",
        "fillForm": "Please fill out this field.",
        "notification_content": "Content",
        "userId": "User",
        "lastName": " Last Name",
        "jane": "Jane",
        "file": "File",
        "publish_page": "Publish",
        "Booking Number": "Appointment Number",
        "date_rage": "Date Range",
        "new_invoice": "New Invoice",
        "paymentMethodId": "Payment Method",
        "doe": "Doe",
        "counts_of_services": "Count of Services",
        "rate": "Tax rate (%)",
        "customer_search": "Customer Search",
        "new note": "New Note",
        "password": "Password",
        "currentpassword": "Current Password",
        "newpassword": "New Password",
        "newpasswordconfirmation": "New Password Confirmation",
        "new_email": "New Email",
        "address": "Enter Address",
        "addressHere": "Enter Address here",
        "upload": "Upload",
        "note": "Note",
        "message": "Your message...",
        "save": "Save",
        "create": "Create",
        "with": "with",
        "reasonsForCancelation": "Reasons for cancelation",
        "update": "Update",
        "delete": "Delete",
        "deleteOrder": "Delete Order",
        "phone": "Phone",
        "phone2": "Phone 2",
        "callCenter": "Customer Service Contact Number",
        "timeOffs": "Create New TimeOffs",
        "register": "Sign up",
        "signout": "Sign out",
        "signIn": "Sign in",
        "title": "Sign in to your account",
        "signUp": "Sign up here",
        "signinHere": "Sign in here",
        "signupFor": "Sign up now for free account",
        "signinWith": "Sign in with",
        "email": "Email address",
        "remember": "Remember me",
        "enabled": "Enabled",
        "select": "Choose",
        "cancel": "Cancel",
        "category": "Category",
        "image": "Image",
        "showBooking": "Show Booking",
        "edit": "Edit",
        "clientSaameTime": "How many clients can be served at the same time",
        "commission": "Commission Percentage",
        "startWith": "Start From",
        "endWith": "End",
        "TimeOffslabel": "timeOffs Description",
        "day": "Day Break",
        "selectday": "Chooise Day",
        "breaks": "Create New Breaks",
        "location": "Branch",
        "online": "Online",
        "on-site": "On-site",
        "showtransaction": "Show Transaction",
        "deleteTransaction": "Delete Transaction",
        "customerName": "Customer Name",
        "staffName": "Service provider name",
        "serviceName": "Services Name",
        "searchStaff": "search for ...",
        "searchCustomer": "Please search for the customer",
        "approveTrans": "Approve Transaction",
        "rejectTrans": "Reject Transaction",
        "filterName": "Filter By Payment Method",
        "all": "All",
        "bookPay": "Payment registration",
        "invoice": "View the invoice",
        "print": "Print",
        "tags": "Tags",
        "noPhoneNum": "there's no phone number",
        "sort_order_package": "Package Order",
        "package_name": "Package Name",
        "sort_order": "Order at the store",
        "noEmail": "there's no email",
        "deleteCustomer": "Remove Customer",
        "block": "Block",
        "unblock": "Unblock",
        "blocked": "Blocked",
        "unblocked": "Unblocked",
        "deleteService": "Remove Service",
        "deletePackage": "Remove Package",
        "duplicate": "Duplicate",
        "duplicateService": "Duplicate Service",
        "showCustomer": "Show Customer",
        "showStaff": "Show Service provider",
        "deleteStaff": "Remove Service provider",
        "showService": "Show Service",
        "fieldLabel": "Label",
        "fieldName": "Name in English",
        "fieldValues": "Options",
        "fieldRequired": "Is required?",
        "label": "Label",
        "htmlTag": "Type",
        "is_required": "Is required?",
        "options": "Options",
        "fieldHtmlTaq": "Type",
        "fieldOptions": "Options",
        "downloadReceipt": "Download the receipt",
        "target": "Place",
        "product": "Product",
        "uploadFile": "Attached file address",
        "deleteProduct": "Delete Product",
        "notification_title": "Notification Title",
        "sms": "SMS",
        "whatsapp": "Whatsapp",
        "print_invoice": "Print Invoice",
        "metaData": {
            "both": "Both",
            "web_only": "Dashboard Only",
            "bookingpage_only": "Booking Page Only"
        },
        "addBooking": "New Booking",
        "pendingBookings": "Pending Bookings",
        "nopendingBookings": "No Pending Bookings",
        "htmlTags": {
            "text": "Short Text",
            "number": "Number",
            "date": "Date",
            "select": "Multi Choice",
            "textarea": "Long Text",
            "file": "Attachment",
            "location": "Location in Map"
        },
        "id": "ID",
        "branches": "Branches",
        "client": "Customer",
        "sale_date": "Sales Date",
        "no_of_items": "No. of items",
        "status": "Status",
        "price": "Price",
        "invoice#": "Invoice #",
        "staff": "Service provider",
        "discount": "Discount",
        "qty": "Qnt",
        "close": "Close",
        "action": "Action",
        "download": "Download",
        "staff_users": "Related Service provider",
        "search_phone_or_name": "Search By Name,Phone",
        "staffId": "Service provider",
        "branchId": "Branch",
        "isPublicService": "Public Service? (all staff can provide it)",
        "company_name": "Company / Institution ",
        "service": "Service",
        "paymentMethodType": "Payment Method Type",
        "sent": "Sent",
        "sent_at": "Sent At",
        "not_sent": "Not Sent Yet",
        "packageName": "Package Type",
        "order_note": "Order Note",
        "invoiced": "Invoiced",
        "not-invoiced": "Not Invoiced",
        "payment_status": "Payment Status",
        "support_provider": "Allow Service Provider to access the app Provider",
        "toggleInActive": "Disable App Account",
        "toggleActive": "Enable App Account",
        "boundary_fees": "Transportation Fees",
        "productName": "Product Name",
        "terms_conditions": "Terms and Conditions"
    },
    "formPlaceHolder": {
        "name": "Enter name",
        "firstName": "Enter first name",
        "lastName": "Enter last name",
        "email": "Enter email",
        "phoneNumber": "Enter Phone ",
        "address": "Enter address",
        "BookingNum": "Appointment Number",
        "body": "Enter body",
        "title": "Enter title",
        "search": "Search",
        "client": "Name",
        "pass": "Enter password",
        "currentPass": "Enter current password",
        "newPass": "Enter new password",
        "confirmPass": "Confirm new passowrd",
        "registerPass": "Enter password",
        "registerConfirmPass": "Confirm password",
        "clientSameTime": "Enter number clients can be served at the same time",
        "date": "Enter Date",
        "createdBy": "Created By",
        "note": "Enter Note",
        "attachTitle": "Enter Title",
        "company_name": "Enter Company Name",
        "commission": "Enter Commission"
    },
    "errorMsg": {
        "requiredInput": "This field is required !",
        "passwordMatch": "Password must be matches"
    },
    "notifications": {
        "title": "View notifications",
        "days": "Last 7 days",
        "all": " View all",
        "stats": "stats",
        "loading": "Loading...",
        "booking": "Appointments"
    },
    "coupons": {
        "createCoupon": "Create Coupon",
        "updateCoupon": "Update Coupon",
        "title": "Coupons",
        "name": "Name",
        "startAt": "Start",
        "endAt": "End",
        "code": "Code",
        "description": "Description",
        "discount_amount": "Discount Amount",
        "discount_type": "Discount Type",
        "start_date": "Start Date",
        "status": "Active",
        "end_date": "End Date",
        "usage_limit": "Usage Limit for all",
        "usage_limit_per_user": "Usage Limit for user",
        "fixed_discount": "Fixed Discount",
        "rate": "Percentage Discount",
        "inactive": "Inactive",
        "percentage_discount": "Percentage Discount",
        "applicable_items": "Applicable Items",
        "status_action": "Discount Coupon Status"
    },
    "listing": {
        "title": "Instructions",
        "l1": "Select dates and you will be prompted to create a new event",
        "l2": "Drag, drop, and resize events",
        "l3": "Click an event to delete it",
        "weekends": "toggle weekends",
        "all": "All Events",
        "sure": "Are you sure you want to delete the event",
        "service": "Services",
        "setting": "Settings"
    },
    "email": {
        "title": "Email",
        "enter": "Enter Email"
    },
    "pagination": {
        "title": "Previous",
        "previous": "Previous",
        "next": "Next",
        "show": "Showing",
        "result": "results",
        "empty": "No data found",
        "to": "to",
        "of": "of",
        "total": "Total"
    },
    "steps": {
        "done": "Done",
        "title": "Enter Name",
        "business": "Business Name",
        "provider_name": "Provider Name",
        "service_name": "Service Name",
        "industry": "Industry",
        "food": "Food",
        "grocery": "Grocery",
        "barber": "Barber",
        "petroleum": "Petrolium",
        "oman": "Oman",
        "time": "Time",
        "timeplaceholder": "Time in minutes",
        "staff": "Assign to Service provider",
        "add": "Add",
        "use": "Use setting",
        "closed": "Closed for business",
        "domain": "Company domain (part of URL, cannot be changed)",
        "domain_help": "Your company domain will become part of your store website URL. It cannot be changed!",
        "price": "Price",
        "ready": "Congrats, your account is ready to activate!",
        "review": "Review",
        "secondstepvalidation": "Atleast one day must be enabled",
        "back": "back",
        "timezone": "Timezone",
        "number_of_employees": "Number of employees",
        "staffplaceholder": "Select Service provider",
        "duration": "Duration",
        "durationplaceHolder": "Enter time in minutes"
    },
    "homepage": {
        "title": "Dashboard",
        "staff": "Service provider",
        "staffs": "Service providers",
        "customers": "Customers",
        "calendar": "Calendar",
        "profile": "My Profile",
        "reviews": "Reviews",
        "apps": "Apps",
        "services": "Services",
        "categories": "Categories",
        "or": "Or",
        "bookingpage": "Booking website",
        "booking": "Bookings",
        "logo": "Logo",
        "subscription": "Subscriptions",
        "transaction": "Transactions",
        "reports": "Reports",
        "sales": "Sales",
        "products": "Products",
        "orders": "Orders",
        "invoices": "Invoices",
        "packages": "Packages",
        "refunds": "Refunds",
        "purchased-packages": "Purchased Packages",
        "purchased-package-details": "Purchased Package Details",
        "package-contains": "Package contents",
        "time-table-package": "Package dates",
        "boundaries": "Boundaries",
        "expenses": "Expenses",
        "expense-types": "Expense Types",
        "pos_system": "Point of Sale (POS)",
        "provider_app": "Provider App"
    },
    "table": {
        "title": "A list of all the appointments in your account including their name, title, email and role.",
        "add": "Add appointment",
        "t": " Title",
        "status": "Status",
        "role": "Role",
        "edit": "Edit",
        "export_excel": "Export Data",
        "date": "Date",
        "createdBy": "Created By",
        "note": "Note"
    },
    "dashboard": {
        "stats": {
            "all_bookings": "All Bookings",
            "upcoming_bookings": "Upcoming Bookings",
            "all_customers": "Customers",
            "sum_of_bookings": "Sum Bookings"
        },
        "booking": {
            "start": "Start",
            "end": "End",
            "type": "Type",
            "source": "Source",
            "customer": "Customer",
            "staff": "Service providers",
            "day": "Day",
            "des": "Description",
            "date": "Date",
            "time": "Time",
            "amount": "Amount",
            "paymentMethod": "Payment Method",
            "service": "Service",
            "additional": "Additional information",
            "attachment": "Attachment",
            "meeting_link": "Meeting Link",
            "transportation_fee_amount": "transportation fees",
            "no_service": "No Service",
            "no_category": "No Category",
            "no_branch": "No Branch",
        }
    },
    "bookingPage": {
        "GeneralSetting": "General Setting",
        "Appearance": "Appearance",
        "Contact": "Contact",
        "websiteManagement": "Website Management",
        "Gallery": "Gallery",
        "website_seo": "SEO and Search Engine",
        "setting": "Settings",
        "page": "Pages",
        "procedures": "Actions",
        "confirmDelete": "You Are Sure To Delete This Page",
        "page_desc": "Add custom pages in store website ",
        "setting_desc": "Manage Booking Page Setting",
        "appearance": "Appearance",
        "branding": "Branding",
        "colors": "Colors",
        "save": "Save",
        "upload": "Upload",
        "recommended_size": "Recommended size",
        "favicon_desc": "Browser favicon",
        "banner_desc": "Store banner image",
        "preview": "Preview",
        "change": "Change",
        "clear": "Clear"
    },
    "staff": {
        "create": "Create Service provider",
        "update": "Update Service provider",
        "services": "Services",
        "bookings": "Appointments",
        "staffNamePlacholder": "Service provider name",
        "workingHours": "Working Hours",
        "TimeOff": " Time Offs",
        "break": "Break",
        "apps": "Sync",
        "zones": "Zones"
    },
    "altNav": {
        "visit_store": "Visit Store",
        "settings-panel": " General Settings ",
        "store-panel": " Store Settings ",
        "ServiceMetrics": "Assign Services & Providers ",
        "pricing": "Pricing and Plans",
        "calendar": "calendar",
        "plugins": "plugins",
        "state": "state",
        "dashboard": "Dashboard",
        "Sales": "Sales",
        "customers": "customers",
        "invoices": "Invoices",
        "marketing": "Marketing",
        "coupons": "coupons",
        "reviews": "reviews",
        "offers": "Promotional offers",
        "management": "manage",
        "packages": "Packages",
        "staffs": "Service providers",
        "services": "services",
        "categories": "Categories",
        "sales": "sales",
        "bookings": "appointment",
        "transactions": "transaction",
        "subscription": "subscription",
        "bookingPage": "Store",
        "design": "design",
        "pages": "pages",
        "template": "template",
        "branches": "branchs",
        "general": "general",
        "taxes": "Taxes",
        "roles": "Roles & Permissions",
        "notifications": "notifications",
        "payments": "payments",
        "status": "status ",
        "labels": "Booking Status",
        "tags": "tags",
        "customFields": "Custom Fields",
        "apps": "Apps & Integrations",
        "users": "users",
        "reasons": "Cancellation Reasons",
        "Categories": "Categories",
        "Invoices": "Invoices",
        "reports": "reports",
        "booking": "appointment",
        "transaction": "transaction",
        "bookingpage": "Store website",
        "settings": "settings",
        "markting": "Marketing",
        "branchs": "branches",
        "orders": "Orders",
        "customPages": "Custom Pages",
        "questionsAndComments": "Questions and Comments",
        "plans": "Plans & Pricing",
        "products": "Products",
        "point-of-sale": "Point of Sale (POS)",
        "campaigns": "Campaigns",
        "refunds": "Refunds",
        "purchased-packages": "Purchased Packages",
        "store": "Store",
        "boundaries": "Boundaries",
        "mersal-whatsapp": "WhatsApp",
        "storeAnalytics": "Store Analytics",
        "order_images": "Order Images",
        "expenses": "Expenses",
        "expense-types": "Expense Types",
        "manage_subscription": "Subscripe Now",
        "trial_ending_soon": "Track your free trial progress",
    },
    "modalPlacholder": {
        "staffName": "Service provider name",
        "serviceName": "Services Name",
        "branch": "Branch Name",
        "email": "Email",
        "message": "Messages",
        "staffDes": "Official holiday , Eid al-Adha",
        "address": "For example: Saudi Arabia, Riyadh, Al-Olaya District, King Fahd Street",
        "packageName": "Package Name",
        "package_valid_period": "Valid Period",
        "package_max_number_of_available": "Max Number of Available",
        "quantity": "Quantity"
    },
    "modalHeader": {
        "data_service": "Data Service",
        "edit_terminal": "Edit Terminal",
        "add_terminal": "Add Terminal",
        "createMetaData": "Add Meta Data",
        "editMetaData": "Edit Meta Data",
        "add_payment": "Add Payment Transaction",
        "impersonate": "Impersonate An Account",
        "zones_settings": "Zones Settings",
        "moving_confirmation": "Re-schedule confirmation",
        "createBooking": "Create Booking",
        "updateBooking": "Update Booking",
        "submit": "Submit",
        "roles": "Add Roles",
        "notes": "Create Notes",
        "attachment": "Create Attachment",
        "users": "Users",
        "product": "Create Product",
        "taxes": "Taxes",
        "service": "Add Service",
        "customer": "Add Customer",
        "employee": "Add service provider",
        "employee_subtitle" : "Add service provider Details",
        "editCustomer": "Edit Customer",
        "editEmployee": "Edit Employee",
        "editService": "Edit Service Information",
        "editBranch": "Edit Branch",
        "duplicateService": "Duplicate Service",
        "booking": "Add Appointment",
        "viewBooking": "Appointment ",
        "category": "Add Category",
        "editCategory": "Edit Category",
        "branch": "Add new branch",
        "createBranch": "Create Branch",
        "updateBranch": "Edit Branch",
        "user": "Send invitation",
        "updateInterval": "Update Interval",
        "timeoff": "Add TimeOffs",
        "break": "Add Break Time",
        "createCustomField": "Create Custom Field",
        "editCustomField": "Update Custom Field",
        "timeoffEdit": "Update TimeOffs",
        "breakEdit": "Update Breaks",
        "updateBankAccount": "Update Bank Account",
        "addBankAccount": "Add Bank Account",
        "CalendarModal": "Booking Information",
        "bookPay": "Payment registration",
        "invoice": "View the invoice",
        "tag": "Add Tags",
        "confirmation": "Confirm deletion",
        "teamWorkingHours": "Edit the branch's working hours",
        "coupon": "Add Coupons",
        "addProduct": "Add Product",
        "editProduct": "Edit Product",
        "duplicateProduct": "Duplicate Product",
        "invoice#": "Invoice #",
        "selectClient": "Select a customer",
        "create_canelation_reason": "Add Cancellation Reason",
        "edit_canelation_reason": "Edit Cancellation Reason",
        "editNotification": "Edit Notification",
        "addNotification": "Create Notification",
        "createCoupon": "Create Coupon",
        "updateCoupon": "Update Coupon",
        "createOffer": "Create Offer",
        "updateOffer": "Update Offer",
        "status_order_no": "Order Status #{param}",
        "transaction_no": "Transaction details #{param}",
        "new_appointment": "New Appointment",
        "set_appointment": "Set an appointment for {param}",
        "create_recurring_appointment": "Create Recurring Appointment",
        "assign_customer_to_order": "Assign Customer To Order",
        "package": "Create Package",
        "editPackage": "Edit Package",
        "edit_working_hour": "Edit Working Hours",
        "add_working_hour": "Create Working Hours",
        "createPaymentMethod": "Create Payment Method",
        "editPaymentMethod": "Edit Payment Method",
        "edit_campaign": "Edit Marketing Campaign",
        "create_campaign": "Create Marketing Campaign",
        "create_Refund": "Create Refund",
        "campaign": "Marketing Campaign Details",
        "subscribe_to_plan": "Subscibe To Plan { param }",
        "new_zone": "Add New Zone",
        "create_order": "Create Order / Appointment",
        "edit_order": "Edit Order Items",
        "update_location": "Update Location",
        "expense_type": "Add Expense Types",
        "expense": "Add Expense",
        "editExpense": "Edit Expense",
        "createExpense": "Add Expense",
        "cancelConfirmation": "Cancel Appointment Confirmation",
        "discount_modal": "Add Discount",
        "edit_description": "Edit Description",
        "edit_staff_service": "Edit Staff Service",
        "create": "Create",
        "discount_modal": "Add Discount",
        "branch_details": "Branch Details",
        "edit_coupon": "Edit Discount Code",
        "edit_coupon_scope": "Edit Coupon Scope"
    },
    "modalSubtitle": {
        "create_order": "Add order details and select a suitable date.",
        "customer": "Add your customer details",
        "service": "Add your service details",
        "createBranch": "Add your branch details and location information.",
        "updateBranch": "Update your branch details and location information.",
        "createCoupon": "Add your coupon details",
    },
    "confirmModal": {
        "msg": "Are sure of the deleting process",
        "deleteOder": "When you delete the order, any appointments associated with it will be deleted. Are you sure about the deletion process ?",
        "deleteStaff": "When you delete the service provider, any appointments associated with it will be deleted. Are you sure about the deletion process ?",
        "confirm_deactivate": "Are you sure you want to deactivate this user?"
    },
    "errorValidation": {
        "name": "Name field is required",
        "nameMinLen": "Name field must be more than 3 character",
        "lastName": "Last name field is required",
        "category": "Category field is required",
        "durationPriceMin": "This field must be more than 0",
        "duration": "Duration field is required",
        "price": "Price field is required",
        "color": "Color field is required"
    },
    "emptyState": {
        "customize_zone": "Add new zones ",
        "zones": "No zones available",
        "empployee": "Add New Service provider",
        "services": "Add New Service",
        "customers": "Add New Customer",
        "staffHourse": "No opening hours for customization Would you like to customize?",
        "addStaffHours": "Customize",
        "register": "Register",
        "canelCustomize": "Cancel Customize",
        "timeoff": "Add Time Offs For Service provider",
        "break": "Add Breaks For Service provider",
        "addnewpage": "Add New Page",
        "bookPay": "Payment registration",
        "note": "Add New Notes",
        "attachment": "Add New Attachment",
        "upload": "Upload"
    },
    "services": {
        "create": "Create Service",
        "update": "Update Service",
        "services": "Services",
        "bookings": "bookings",
        "staffs": "Service providers",
        "title": "Services",
        "add": "Add Service",
        "export": "Export",
        "assign_providers": "Assign Providers"
    },
    "customers": {
        "addCustomer": "Get started by add customer ",
        "Customer_status": "Customer Status"
    },
    "teams": {
        "title": "Branches",
        "desc": "A list of all branches",
        "create": "Create New Branch",
        "update": "Update Branch",
        "additional": "Additional Settings",
        "form": {
            "name": "Name",
            "personal": "Personal Team",
            "industry": "Industry"
        }
    },
    "booking": {
        "staff": "Service provider Member",
        "services": "Services",
        "dateAndTime": "Date and Time",
        "customFields": "Custom Fields",
        "service": "Service",
        "start": "Start",
        "duration": "Duration",
        "totalDuration": "Total Duration",
        "minutes": "Minutes",
        "end": "End",
        "price": "Price",
        "totalPrice": "Total Price",
        "sr": "SR",
        "created_at": "Created At",
        "source": "Source",
        "customer": "Customer",
        "label": "Label",
        "status": "Status",
        "notes": "Notes",
        "day": "Date",
        "time": "Time",
        "booking_number": "Number",
        "notFoundBooking": "No time slot for this day",
        "payment_status": "Payment Status",
        "paid": "paid",
        "unpaid": "unpaid",
        "bookingHeader": "Booking Details",
        "paymentStatus": "Payment Status",
        "startBooking": "Start of booking",
        "endBooking": "End of booking",
        "partially-paid": "partially-paid",
        "the_status": {
            "completed": "Completed",
            "canceled": "Canceled",
            "confirmed": "Confirmed",
            "no-show": "No Show",
            "pending": "Pending",
            "rejected": "Rejected",
            "unapproved": "Waiting for approval",
            "unpaid": "Unpaid"
        },
        "sources": {
            "calendar": "System",
            "booking-page": "Booking Page",
            "pos": "POS",
            "pos2": "POS",
            "bookingPage": "Booking Page"
        },
        "no_service": "There is no service available",
        "meeting_link": "Meeting Link"
    },
    "transaction": {
        "transaction_number": "Transaction Number",
        "order_number": "Order Number",
        "customer": "Customer",
        "date": "Date",
        "payment_method": "Payment Method",
        "amount": "Amount",
        "currency": "Currency",
        "status": "Status",
        "pending": "Pending",
        "confirmed": "Confirmed",
        "refused": "Refused",
        "attachments": "Attachments",
        "wait": "Under Receipt",
        "payment_gateway_id": "Payment Gateway ID",
        "payment_gateway_id_description": "Copy the reference number to search for it in the payment gateway"
    },
    "reviews": {
        "heading": "Reviews",
        "pending": "Pending",
        "approved": "Approved",
        "rejected": "Rejected",
        "on": "On",
        "approve": "Approve",
        "reject": "Reject",
        "hide": "Hide",
        "details": {
            "heading": "Review Details",
            "by": "By"
        }
    },
    "offers": {
        "newOffers": "Create Offers",
        "title": "Offers",
        "name": "name",
        "expiry_date": "Expiry Date",
        "status": "Status",
        "code": "Code",
        "booking_page ": "Booking Page",
        "webapp": "Web App",
        "services ": "Services",
        "createOffers": "Create Offers",
        "updateOffers": "Update Offers"
    },
    "comments": {
        "heading": "Comments",
        "un_published": "Un Published",
        "published": "Published",
        "on": "On",
        "approve": "Approve",
        "reject": "Reject",
        "reply": "Reply",
        "update": "Update",
        "replied": "Replied By",
        "details": {
            "heading": "Comment Details",
            "by": "By",
            "reply": "Reply to comment"
        }
    },
    "packages": {
        "heading": "Packages",
        "services": "Services",
        "item": "item",
        "title": "Package",
        "create": "Create",
        "update": "Update",
        "delete": "Delete"
    },
    "setting": "Settings",
    "settings": {
        "actions": "Actions",
        "editWorkingHour": "Edit Working Hours",
        "timeFormat": "Time Format",
        "logo": "Logo",
        "hour24": "24",
        "hour12": "AM/PM",
        "taxes": "Taxes",
        "labels": "Booking Status",
        "teamWorking": "Branch Working Hours",
        "syncedInvoices": {
            "title": "Synced Invoices",
            "prerequisite": "You must first install and configure wafeq App from the app store before using this feature.",
            "synced": "Synced Invoices",
            "unsynced": "Unsynced Invoices",
            "sent_to_wafeq": "Send To Wafeq",
            "sentInvoiceSuccess": "Invoice was successfully sent to Wafeq",
            "sentInvoiceError": "Something went wrong during sending invoice to Wafeq",
            "headers": {
                "invoice_number": "invoice_number",
                "amount": "amount",
                "currency": "currency",
                "created_at": "created_at",
                "created_by": "created_by",
                "wafeq_id": "Wafeq ID"
            }
        },
        "navigation": {
            "GeneralSetting_desc": "Modify the site name, description, and site domain",
            "bookingpage_settings": "Booking Page Settings",
            "bookingpage_settings_desc": "brief description",
            "general_settings": "General Settings",
            "general_settings_desc": "Amending the general settings such as the trade name and adding branches",
            "team_users_settings": "Team Users",
            "team_users_settings_desc": "To add users who have the authority to manage the branch",
            "category__desc": "Adding and modifying service categories",
            "working_hours_settings": "Working Hours",
            "working_hours_settings_desc": "Determine branch working hours",
            "notification_center": "Notification Center",
            "notification_center_desc": "To manage notifications and transmission channels",
            "tags_management": "Tags Management",
            "tags_management_desc": "Manage tags for customers, bookings ...etc",
            "custom_fields": "Custom Fields",
            "custom_fields_desc": "Manage custom fields for customers, bookings ...etc",
            "team_settings": "Branches",
            "payement_desc": "Adding Payement System",
            "payement": "Payement",
            "roles": "Roles",
            "taxes": "Taxes",
            "roles_desc": "Manage Roles",
            "taxes_desc": "Manage Taxes",
            "category_crud": "Category Management"
        },
        "settingCrumbs": {
            "WorkingHours": "Working Hours",
            "TeamUsers": "Team Users",
            "TagsManagement": "Tags Management",
            "Payement": "Payement",
            "NotificationCenter": "Notification Center",
            "GeneralSettings": "General Settings",
            "CustomFields": "Custom Fields",
            "Category": "Category Management",
            "Branches": "Branches",
            "roles": "Roles",
            "taxes": "Taxes",
            "roles_desc": "Manage Roles",
            "taxes_desc": "Manage Taxes"
        },
        "team_settings_desc": "brief description",
        "pay_on_arrive_description": "Allow to customer to pay when arrival.",
        "sms": "sms",
        "email": "email",
        "whatsapp": "Whatsapp",
        "apps_settings": "Apps Settings",
        "apps_settings_desc": "brief description",
        "category_crud": "Category Management",
        "role": "Roles & Permissions",
        "onOff": "Off / On",
        "addBank": "New Bank Account",
        "Appearance_desc": "Change colors and pictures",
        "Contact_desc": "Add and modify social media and customer service number",
        "websiteManagement_desc": "Manage the site or activate maintenance mode",
        "Gallery_desc": "Add photos to the Gallery",
        "bookingpage": {
            "heading": "Booking Page Settings",
            "basic": "Basic Information",
            "briefBaic": "This information will be displayed publicly so be careful what you share.",
            "title": "Site title",
            "subdomain": "Subdomain",
            "embded_script": "Embded Script",
            "description": "Site brief",
            "description2": "a brief description for your site",
            "images": "Site photos",
            "images2": "update your site logo, banner and favicon.",
            "logo": "Logo",
            "banner": "Banner image",
            "favicon": "Broswe icon",
            "socials": "Social media accounts",
            "socials2": "update your site social medai accounts",
            "facebook": "Facebook",
            "whatsapp": "Whatsapp Number",
            "tiktok": "tiktok",
            "youtube": "Youtube",
            "twitter": "Twitter",
            "x": "X",
            "instagram": "Instagram",
            "linkedin": "Linkedin",
            "snapchat": "Snapchat",
            "email": "Email",
            "styles": "Site look and feel",
            "styles2": "update your site primary color, secondary color and background color",
            "primary": "Primary color",
            "secondary": "Secondary color",
            "background": "Background color",
            "change": "Change",
            "add": "Add",
            "clear": "Clear",
            "delete": "Delete",
            "desc": "Enter description for your website",
            "create": "Save",
            "facebookLink": "Copy facebook link here",
            "tiktokLink": "Copy tiktok link here",
            "twitterLink": "Copy twitter link here",
            "instagramLink": "Copy instagram link here",
            "youtubeLink": "Copy youtube link here",
            "linkedinLink": "Copy linkedin link here",
            "snapchatLink": "Copy snapchat link here",
            "emailLink": "Copy email link here",
            "LogoErr": "Change logo first!",
            "BannerErr": "Change banner first!",
            "IconErr": "Change browser icon first!",
            "oneItem": "Chooise one image",
            "sitemap": "Sitemap URL",
            "appearance": "Appearance",
            "branding": "Branding",
            "colors": "Colors",
            "save": "Save",
            "upload": "Upload",
            "recommended_size": "Recommended size",
            "favicon_desc": "Browser favicon",
            "banner_desc": "Store banner image",
            "preview": "Preview",
            "suggestions": "Suggested colors"
        },
        "payementLabels": {
            "onlinePayment": "Online Payment",
            "paymentInstallments": "Payment Installments",
            "bankTransfers": "bank Transfers",
            "paymentaArrival": "Payment upon arrival",
            "addAccount": "Add Bank Account",
            "bankUser": "Beneficiary Name",
            "bankName": "Bank",
            "iban": "IBAN",
            "bankNo": "Account ID",
            "paymenetError": "Payment method requires activation of at least one application"
        },
        "general": {
            "heading": "General Settings",
            "basic": "Basic Information",
            "name": "Name",
            "sector": "Sector",
            "address": "Address"
        },
        "workinghours": {
            "heading": "Working hours Settings",
            "name": "name",
            "day_intervals": "Available working hours for this day.",
            "empty": "No working hours been set for this day."
        },
        "reports": {
            "heading": "Reports"
        },
        "category": {
            "title": "Categories",
            "desc": "Add services categories"
        },
        "teamusers": {
            "heading": "Branch Settings",
            "basic": "Basic Information",
            "name": "name",
            "users": "Management",
            "branch-users": "List of all branch users",
            "providers": "Provider Accounts",
            "change-to-provider": "Change to Provider",
            "change-to-user": "Change to User",
            "no-provider": "No staff selected",
            "provider": "Provider Accounts",
            "provider-users": "List of all provider users",
            "table": {
                "name": "Name",
                "email": "Email Address",
                "remove": "Remove",
                "role": "Role",
                "status": "Status",
                "provider": "Provider"
            },
            "invitation": {
                "title": "Invite User",
                "invite": "Send invitation",
                "heading": "Pending Invitations",
                "text": "List of all pending invitations"
            },
            "deactivate": "Deactivate User",
            "confirmDeactivate": "Are you sure you want to deactivate this user?"
        },
        "notifications": {
            "heading": "Notification Center",
            "staff": "Service provider",
            "customers": "Customers"
        },
        "apps": {
            "heading": "Apps Store",
            "desc": "You can install and integrate apps with one click",
            "payment_app": "Online Payment",
            "notifications_apps": "Notifications Apps",
            "install": {
                "installBtn": "Install",
                "help_link": "How to activite?",
                "updateBtn": "Update Info",
                "heading": "App Information",
                "paragraph": "Enter the following data to install this app",
                "email": "Email :  ",
                "phone_number": "Phone : ",
                "licence": "Licence : ",
                "uninstall": "Uninstall "
            },
            "save_config": "Save Configuration",
            "configure": "App Configuration",
            "configured": "Configured",
            "needs_config": "Need Configuration"
        },
        "logo_validations": {
            "size": "- Logo size should be less than 500 Kb",
            "dimensions": "- Logo dimensions should be less than 500 x 500 and ratio 1:1",
            "format": "- Logo should be in image jpeg,png,jpg formats"
        },
        "tags": {
            "staff": "Service provider Tags",
            "customers": "Customer Tags",
            "services": "Service Tags",
            "bookings": "Booking Tags",
            "staff_desc": "Manage service providers tags",
            "customers_desc": "Manage Customer Tags",
            "services_desc": "Manage Service Tags",
            "bookings_desc": "Manage Booking Tags"
        },
        "customFields": {
            "staff": "Service provider Custom Fields",
            "customers": "Customer Custom Fields",
            "services": "Service Custom Fields",
            "bookings": "Booking Custom Fields",
            "staff_desc": "Manage service provider Custom Fields",
            "customers_desc": "Manage Customer Custom Fields",
            "services_desc": "Manage Service Custom Fields",
            "bookings_desc": "Manage Booking Custom Fields"
        },
        "canelationReasons": {
            "reason": "The reason",
            "heading": "Cancellation Reasons",
            "reason_type": "Reason Type",
            "booking-cancel": "Appointment Cancellation Reasons",
            "invoice-refund": "Invoice Cancellation Reasons"
        }
    },
    "plugins": {
        "heading": "Plugins",
        "desc": "You can install Plugins with one click"
    },
    "days": {
        "sun": "Sunday",
        "mon": "Monday",
        "tue": "Tuesday",
        "wed": "Wednesday",
        "thu": "Thursday",
        "fri": "Friday",
        "sat": "Saturday "
    },
    "welcome": {
        "heading": "Account Settings",
        "subtitle": "fill the following form to finish account settings",
        "to": "To",
        "domain": {
            "range": "must have between 3 and 20 characters",
            "checked": "Domain checked..."
        },
        "form": {
            "company_name": "Company Name",
            "branch_name": "Branch Name",
            "profile": "Profile Image",
            "language": "Prefered Language",
            "industry": "Industry"
        },
        "headers": {
            "welcome": "Welcome",
            "hours": "Hours",
            "staff": "Service provider",
            "services": "Services",
            "done": "Done"
        },
        "placeholder": {
            "domain": "System Domain"
        },
        "days": {
            "Sunday": "Sunday",
            "Monday": "Monday",
            "Tuesday": "Tuesday",
            "Wednesday": "Wednesday",
            "Thursday": "Thursday",
            "Friday": "Friday",
            "Saturday": "Saturday"
        }
    },
    "calendar": {
        "day_short": {
            "Sun": "Sun",
            "Mon": "Mon",
            "Tue": "Tue",
            "Wed": "Wed",
            "Thu": "Thu",
            "Fri": "Fri",
            "Sat": "Sun"
        },
        "AM": "AM",
        "PM": "PM",
        "Today": "Today",
        "month_view": "Monthly",
        "week_view": "Weekly",
        "day_view": "Daily",
        "month_list": "Monthly list",
        "week_list": "Weekly list",
        "day_list": "Daily list",
        "all_staff": "All",
        "months": {
            "January": "January",
            "February": "February",
            "March": "March",
            "April": "April",
            "May": "May",
            "June": "June",
            "July": "July",
            "August": "August",
            "September": "September",
            "October": "October",
            "November": "November",
            "December": "December"
        },
        "notAvalible": "Not Available",
        "next": "Next",
        "prev": "Prev"
    },
    "maintenance": {
        "status": "status",
        "maintenance_title": "Message Title",
        "maintenance_body": "Message Body",
        "active": "Active",
        "maintenance": "Maintenance",
        "suspended": "Suspended"
    },
    "profile": {
        "personal": "Personal information",
        "passwords": "Change account password",
        "change_email": "Change Email",
        "personalInfo": "This information will be displayed publicly so be careful what you share.",
        "language": "Language",
        "Arabic": "Arabic",
        "English": "English"
    },
    "msg_accept_invitation": "click the link sent to your email to accept the invitation and start using the app",
    "permissions": {
        "marketing_campaigns": "Marketing Campaigns",
        "orders": "Orders",
        "services": "Services",
        "products": "Products",
        "customers": "Customers",
        "reports": "Reports",
        "bookings": "Bookings",
        "providers": "Providers",
        "marketing_reviews": "Reviews",
        "marketing_booking_page": "Booking Page",
        "marketing_coupons": "Coupons",
        "customers_notes_show_all": "Customer Notes All",
        "customer_notes_show_all": "Customer Notes All",
        "calendar_only_staff": "Calendar. only my calendar ",
        "calendar_all": "Calendar. all Service providers.",
        "settings_categories": "Categories",
        "settings_apps": "Apps & Integrations",
        "settings_custom_fields": "Custom Fields",
        "settings_tags": "Tags",
        "settings_labels": "Booking Status",
        "settings_payments": "Payments",
        "settings_roles": "Roles & Permissions",
        "settings_notifications": "Notifications ",
        "settings_users": "Users",
        "settings_payment_methods": "Payment Methods",
        "settings_plans": "Plans",
        "settings_branches": "Branches",
        "settings_general_setting": "General Setting",
        "settings_subscriptions": "Subscription",
        "settings_taxes": "Taxes",
        "settings_reasons": "Cancellation Reasons",
        "reports_appointments_booking_day_sheet": "Day Sheet Report",
        "reports_finance_services_most_requested": "Most Requested Services Report",
        "reports_business_customers_top_loyal": "Top Loyal Customers Report",
        "reports_business_customers_report": "General Customers Report",
        "reports_finance_sales_report": "General Sales Report",
        "reports_finance_sales_items": "Sales Items Report",
        "reports_finance_transaction_summary": "Transactions Summary Report",
        "summary": "Summary Report",
        "sales_invoices": "Invoices ",
        "sales_payments": "Payments",
        "marketing_offers": "Offers",
        "marketing_pages": "Marketing Pages",
        "settings_deleting_data": "Deleting Data",
        "comments": "Comments",
        "packages": "Packages",
        "plugins": "Plugins",
        "sales_packages": "Sales Packages",
        "sales_purchased_packages": "Purchased Packages",
        "boundaries": "Boundaries",
        "reports_finance_coupons_general": "Coupons Report",
        "reports_finance_packages_general": "Packages Report",
        "reports_finance_general_invoices": "Sales Invoices Report",
        "reports_finance_refund_invoices": "Refund Invoices Report",
        "reports_finance_expenses_general": "Expenses Report",
        "settings_type-expenses": "Expenses Types",
        "settings_expenses": "Settings Expenses",
        "type-expenses": "Expenses Types",
        "expenses": "Expenses",
        "reports_finance_transaction_refunds_summary": "Refunds Summary Report",
        "reports_finance_sales_return_invoices": "Sales Return Invoices Report"
    },
    "permissions_group": {
        "orders": "Orders",
        "customer": "Customer",
        "plugins": "Plugins",
        "settings": "Settings",
        "marketing": "Marketing",
        "sales": "Sales",
        "calendar": "Calendar",
        "services": "Services",
        "reports": "Reports",
        "appointments": "Appointments",
        "providers": "Providers",
        "customers": "Customers",
        "comments": "Comments",
        "packages": "Packages",
        "null": "Permissions group"
    },
    "currenices": {
        "SAR": "Saudi Riyal",
        "USD": "US Dollar",
        "BHD": "Bahraini Dinar",
        "AED": "Emirati Dirham",
        "OMR": "Omani Rial",
        "QAR": "Qatari Riyal",
        "EGP": "Egyptian Pound",
        "JOD": "Jordanian Dinar",
        "DZD": "Algerian Dinar",
        "IQD": "Iraqi Dinar",
        "KWD": "Kuwaiti Dinar",
        "TND": "Tunisian Dinar",
        "MAD": "Moroccan Dirham",
        "LBP": "Lebanese Pound",
        "SDG": "Sudanese Pound",
        "SYP": "Syrian Pound",
        "YER": "Yemeni Rial"
    },
    "paymentMethod": {
        "on_arrival": "On Arrival"
    },
    "tags": {
        "selectOption": "Select",
        "selectLabel": "Select",
        "selectedLabel": "Selected",
        "deselectLabel": "Clear",
        "clearAll": "Clear All",
        "noResults": "No results found"
    },
    "push_notifications": {
        "booking": {
            "created_title": "New Booking",
            "created_message": "New booking #{num} was created by {customer}"
        }
    },
    "countries": {
        "AD": "Andorra",
        "AE": "United Arab Emirates",
        "AF": "Afghanistan",
        "AG": "Antigua and Barbuda",
        "AI": "Anguilla",
        "AL": "Albania",
        "AM": "Armenia",
        "AO": "Angola",
        "AQ": "Antarctica",
        "AR": "Argentina",
        "AS": "American Samoa",
        "AT": "Austria",
        "AU": "Australia",
        "AW": "Aruba",
        "AX": "Åland Islands",
        "AZ": "Azerbaijan",
        "BA": "Bosnia and Herzegovina",
        "BB": "Barbados",
        "BD": "Bangladesh",
        "BE": "Belgium",
        "BF": "Burkina Faso",
        "BG": "Bulgaria",
        "BH": "Bahrain",
        "BI": "Burundi",
        "BJ": "Benin",
        "BL": "Saint Barthélemy",
        "BM": "Bermuda",
        "BN": "Brunei Darussalam",
        "BO": "Bolivia",
        "BQ": "Bonaire, Sint Eustatius and Saba",
        "BR": "Brazil",
        "BS": "Bahamas",
        "BT": "Bhutan",
        "BV": "Bouvet Island",
        "BW": "Botswana",
        "BY": "Belarus",
        "BZ": "Belize",
        "CA": "Canada",
        "CC": "Cocos (Keeling) Islands",
        "CD": "Congo, The Democratic Republic of the",
        "CF": "Central African Republic",
        "CG": "Congo",
        "CH": "Switzerland",
        "CI": "Côte d'Ivoire",
        "CK": "Cook Islands",
        "CL": "Chile",
        "CM": "Cameroon",
        "CN": "China",
        "CO": "Colombia",
        "CR": "Costa Rica",
        "CU": "Cuba",
        "CV": "Cabo Verde",
        "CW": "Curaçao",
        "CX": "Christmas Island",
        "CY": "Cyprus",
        "CZ": "Czechia",
        "DE": "Germany",
        "DJ": "Djibouti",
        "DK": "Denmark",
        "DM": "Dominica",
        "DO": "Dominican Republic",
        "DZ": "Algeria",
        "EC": "Ecuador",
        "EE": "Estonia",
        "EG": "Egypt",
        "EH": "Western Sahara",
        "ER": "Eritrea",
        "ES": "Spain",
        "ET": "Ethiopia",
        "FI": "Finland",
        "FJ": "Fiji",
        "FK": "Falkland Islands (Malvinas)",
        "FM": "Micronesia, Federated States of",
        "FO": "Faroe Islands",
        "FR": "France",
        "GA": "Gabon",
        "GB": "United Kingdom",
        "GD": "Grenada",
        "GE": "Georgia",
        "GF": "French Guiana",
        "GG": "Guernsey",
        "GH": "Ghana",
        "GI": "Gibraltar",
        "GL": "Greenland",
        "GM": "Gambia",
        "GN": "Guinea",
        "GP": "Guadeloupe",
        "GQ": "Equatorial Guinea",
        "GR": "Greece",
        "GS": "South Georgia and the South Sandwich Islands",
        "GT": "Guatemala",
        "GU": "Guam",
        "GW": "Guinea-Bissau",
        "GY": "Guyana",
        "HK": "Hong Kong",
        "HM": "Heard Island and McDonald Islands",
        "HN": "Honduras",
        "HR": "Croatia",
        "HT": "Haiti",
        "HU": "Hungary",
        "ID": "Indonesia",
        "IE": "Ireland",
        "IM": "Isle of Man",
        "IN": "India",
        "IO": "British Indian Ocean Territory",
        "IQ": "Iraq",
        "IR": "Iran",
        "IS": "Iceland",
        "IT": "Italy",
        "JE": "Jersey",
        "JM": "Jamaica",
        "JO": "Jordan",
        "JP": "Japan",
        "KE": "Kenya",
        "KG": "Kyrgyzstan",
        "KH": "Cambodia",
        "KI": "Kiribati",
        "KM": "Comoros",
        "KN": "Saint Kitts and Nevis",
        "KP": "North Korea",
        "KR": "South Korea",
        "KW": "Kuwait",
        "KY": "Cayman Islands",
        "KZ": "Kazakhstan",
        "LA": "Lao People's Democratic Republic",
        "LB": "Lebanon",
        "LC": "Saint Lucia",
        "LI": "Liechtenstein",
        "LK": "Sri Lanka",
        "LR": "Liberia",
        "LS": "Lesotho",
        "LT": "Lithuania",
        "LU": "Luxembourg",
        "LV": "Latvia",
        "LY": "Libya",
        "MA": "Morocco",
        "MC": "Monaco",
        "MD": "Moldova",
        "ME": "Montenegro",
        "MF": "Saint Martin (French part)",
        "MG": "Madagascar",
        "MH": "Marshall Islands",
        "MK": "North Macedonia",
        "ML": "Mali",
        "MM": "Myanmar",
        "MN": "Mongolia",
        "MO": "Macao",
        "MP": "Northern Mariana Islands",
        "MQ": "Martinique",
        "MR": "Mauritania",
        "MS": "Montserrat",
        "MT": "Malta",
        "MU": "Mauritius",
        "MV": "Maldives",
        "MW": "Malawi",
        "MX": "Mexico",
        "MY": "Malaysia",
        "MZ": "Mozambique",
        "NA": "Namibia",
        "NC": "New Caledonia",
        "NE": "Niger",
        "NF": "Norfolk Island",
        "NG": "Nigeria",
        "NI": "Nicaragua",
        "NL": "Netherlands",
        "NO": "Norway",
        "NP": "Nepal",
        "NR": "Nauru",
        "NU": "Niue",
        "NZ": "New Zealand",
        "OM": "Oman",
        "PA": "Panama",
        "PE": "Peru",
        "PF": "French Polynesia",
        "PG": "Papua New Guinea",
        "PH": "Philippines",
        "PK": "Pakistan",
        "PL": "Poland",
        "PM": "Saint Pierre and Miquelon",
        "PN": "Pitcairn",
        "PR": "Puerto Rico",
        "PS": "Palestine, State of",
        "PT": "Portugal",
        "PW": "Palau",
        "PY": "Paraguay",
        "QA": "Qatar",
        "RE": "Réunion",
        "RO": "Romania",
        "RS": "Serbia",
        "RU": "Russian Federation",
        "RW": "Rwanda",
        "SA": "Saudi Arabia",
        "SB": "Solomon Islands",
        "SC": "Seychelles",
        "SD": "Sudan",
        "SE": "Sweden",
        "SG": "Singapore",
        "SH": "Saint Helena, Ascension and Tristan da Cunha",
        "SI": "Slovenia",
        "SJ": "Svalbard and Jan Mayen",
        "SK": "Slovakia",
        "SL": "Sierra Leone",
        "SM": "San Marino",
        "SN": "Senegal",
        "SO": "Somalia",
        "SR": "Suriname",
        "SS": "South Sudan",
        "ST": "Sao Tome and Principe",
        "SV": "El Salvador",
        "SX": "Sint Maarten (Dutch part)",
        "SY": "Syrian Arab Republic",
        "SZ": "Eswatini",
        "TC": "Turks and Caicos Islands",
        "TD": "Chad",
        "TF": "French Southern Territories",
        "TG": "Togo",
        "TH": "Thailand",
        "TJ": "Tajikistan",
        "TK": "Tokelau",
        "TL": "Timor-Leste",
        "TM": "Turkmenistan",
        "TN": "Tunisia",
        "TO": "Tonga",
        "TR": "Türkiye",
        "TT": "Trinidad and Tobago",
        "TV": "Tuvalu",
        "TW": "Taiwan",
        "TZ": "Tanzania",
        "UA": "Ukraine",
        "UG": "Uganda",
        "UM": "United States Minor Outlying Islands",
        "US": "United States",
        "UY": "Uruguay",
        "UZ": "Uzbekistan",
        "VA": "Holy See (Vatican City State)",
        "VC": "Saint Vincent and the Grenadines",
        "VE": "Venezuela",
        "VG": "Virgin Islands, British",
        "VI": "Virgin Islands, U.S.",
        "VN": "Vietnam",
        "VU": "Vanuatu",
        "WF": "Wallis and Futuna",
        "WS": "Samoa",
        "YE": "Yemen",
        "YT": "Mayotte",
        "ZA": "South Africa",
        "ZM": "Zambia",
        "ZW": "Zimbabwe"
    },
    "reports": {
        "slug": {
            "orders_report": "Orders",
            "customers_report": "Customers",
            "services_report": "Services",
            "bookings_report": "Bookings",
            "transactions_report": "Transactions",
            "invoices_report": "Invoices",
            "purchased_packages_report": "Packages",
            "coupons_report": "Coupons",
            "pos_sessions_report": "Pos Sessions"
        },
        "group": {
            "business": "Business",
            "finance": "Finance",
            "appointments": "Appointments",
            "marketing": "Marketing",
            "pos": "Pos"
        }
    },
    "add_more_reasons_for_cancellation": "To add more reasons for cancellation",
    "products": {
        "title": "Products",
        "name": "product name"
    },
    "Minutes": "Minutes",
    "Hours": "Hours",
    "variables": "Variables",
    "create_notification_for": "Create Notification For",
    "notification_body": "Content",
    "notification_resend": "Resend ",
    "notification_fail_reason": "Failure Reason To Send",
    "channel": "Channel",
    "notification_create_at": "Time",
    "notification_sent_at": "Sent Time",
    "receiver_type": "Receiver Type",
    "notification_title": "Notification Title",
    "notification_channel": "Notification Channel",
    "app": "App",
    "receiver_name": "Receiver Name",
    "receiver_email": "Receiver Email",
    "receiver_phone": "Receiver Phone",
    "notification_details": "Notification Details",
    "1_4_employee": "1-4 Service provider",
    "5_9_employee": "5-9 Service provider",
    "10_49_employee": "10-49 Service provider",
    "50_more_employee": "50+ Service provider",
    "uncompleted_profile_alert": "Your profile is not completed yet",
    "home_page": "Home Page",
    "logout": "Logout",
    "go_back_dashboard": "Go Back To Dashboard",
    "if_you_need_to_this_page_please_contact_with_your_admin": "If you need to this page please contact with your admin",
    "sorry_we_couldnt_find_the_page_youre_looking_for": "Sorry, we couldn't find the page you're looking for.",
    "page_not_found": "Page Not Found",
    "subdomain_updated": "Subdomain updated successfully",
    "warning_visitors_will_not_be_able_to_access_your_website": "Warning! Visitors will not be able to access your website on the previous domain.",
    "edit": "Edit",
    "save_changes": "Save Changes",
    "saving": "Saving...",
    "booking_page": "Booking Page",
    "no_found_orders": "No Found Orders",
    "ago": "ago",
    "orders_completed": "Orders Completed",
    "payment": "Payment",
    "items": "Items",
    "total_order": "Total Order",
    "total_refunded_orders": "Total Refundedd Orders",
    "coupon_if_applied": "Coupon ",
    "discount_amount": "Discount ",
    "export_invoice": "Export Invoice",
    "order_summary": "Order Summary",
    "preview_invoice": "Preview Invoice",
    "preview_ticket": "Service Tickets",
    "send_invoice": "Send Invoice",
    "send_invoice_email": "Send Invoice To Email",
    "send_invoice_whatsapp": "Send Invoice To Whatsapp",
    "print_invoice": "Print Invoice",
    "copy": "Copy",
    "generate_payment_link": "Generate Payment Link",
    "generating_payment_link": "Generating Payment Link...",
    "order_log": "Order Log",
    "waiting_review": "Waiting for review",
    "copied_paymeny_url": "Payment link copied",
    "copied_email": "Email copied",
    "payment_link_has_been_sent_to_the_customer": "Payment link has been sent to the customer",
    "payment_link_can_not_be_sent": "To use payment link, order must have customer with phone number and email address",
    "notes": "Notes",
    "refunds_orders": "Refunds Orders",
    "main_order": "This order Refunded from Order num",
    "empty_refunds_orders": "No Refunds Orders",
    "empty_notes": "No notes",
    "payment_done": "Payment Done",
    "payment_not_done": "Payment Not Done Yet",
    "no_transactions": "No Transactions",
    "transactions_log": "Transactions Log",
    "order_status": "Order Status",
    "order_date": "Order Date",
    "order_number": "Order Number",
    "customer": "Customer",
    "choose_order_status": "Choose Order Status",
    "additionals_notes_for_customer": "Additional Notes For Customer",
    "send_notification_when_change_order_status_to_customer_sms": "Send notification when change order status to customer (SMS)",
    "enter_notes": "Enter Notes ... ",
    "change_status_order": "Change Status Order",
    "visit_store": "Visit Store",
    "confirmed": "Confirmed",
    "completed": "Completed",
    "add_new_notification": "Add New Notification",
    "item_details": "Item Details",
    "coupon_details": "Discount Code Details",
    "total_amount": "Total Amount",
    "itemName": "Item Name",
    "provider": "Provider",
    "parsedStart": "Start Date",
    "parsedEnd": "End Date",
    "source": "Source",
    "payment_status": "Payment Status",
    "invoice_status": "Invoice Status",
    "add_new_payment_transaction": "Add New Payment Transaction",
    "create_new_order": "Create New Order",
    "create_invoice": "Create Invoice",
    "no-show": "No Show",
    "options_invoice": "Invoice Options",
    "service_provider_notifications": "Service Provider Notifications",
    "tenant_gift_recievers": "Gift Order Notifications",
    "customer_notifications": "Customer Notifications",
    "SentNotifications": "Sent Notifications",
    "FailedNotifications": "Failed Notifications",
    "settings-general": "General Settings",
    "branches": "Branches",
    "users": "Users",
    "roles": "Roles & Permissions",
    "taxes": "Taxes",
    "subscription": "Subscription",
    "payments": "Payments",
    "categories": "Categories",
    "customfields": "Custom Fields",
    "tag": "Tags",
    "apps": "Apps",
    "reasons": "Cancellation Reasons",
    "plans": "Plans & Pricing",
    "paymentMethods": "Payment Methods",
    "notification": "Notifications",
    "settings-panel": "Settings ",
    "booked": "Booked",
    "customer_deleted": "Customer Deleted",
    "isHoliday": "Holiday",
    "additional_info": "Additional Information",
    "order_fees": "Order Fees",
    "fee_title": "Title",
    "fee_value": "Amount",
    "transaction_number": "no.",
    "POS": "System (POS)",
    "no_time_slot": "The Service Provider has no times available on this day",
    "Dear_Partner": "Dear Partner ,",
    "notification_note": "To enable notifications, it's required to activate notifications for each channel individually. For example, to enable WhatsApp messages, please go to Settings, then Apps, then Notifications. Choose one of the available apps dedicated to WhatsApp",
    "required_apps": " You can't add notification because there is no app for this type of notification, please go to settings then apps and activate one of the apps",
    "order_page": "Order Page",
    "tooManyRequests": "You have sent too many requests in a short time, please try again after 30 seconds",
    "when_change_order_status_to_canceled_will_change_all_appointments_status_to_canceled": "When you change the order status to canceled, then all appointments change to canceled",
    "when_change_order_status_to_refunded_system_will_create_credit_note_invoice": "When you change the order status to refunded, then the system will create a credit note invoice automated",
    "decliend": "Declined",
    "accepted": "Accepted",
    "recipt_file": "Recipt File",
    "appointments": "Appointments",
    "add_new_appointment": "Add New Appointment",
    "set_an_appointment": "Set an appointment",
    "you_cant_set_appointment_for_order": "You cannot assign an appointment to an order for a customer (guest), please edit the order to be able to do so.",
    "you_cant_set_appointment_for_order_limit_usage": "You consume 100 % of your this package ",
    "trash": "Trash",
    "delete_permanently": "Delete Permanently",
    "restore": "Restore",
    "slug_services": "The Service Link on the store webiste ",
    "slug_package": "The package Link on the store webiste ",
    "slug_rule": "must only contain English letters, numbers, underscores (_), and dashes (-).",
    "slug": "Slug",
    "orders_sources": "Orders Sources",
    "bookingPage_sales_count": " Store ",
    "pos_sales_count": " Dashboard ",
    "top_services_ordered": "Top Services Ordered",
    "sales": "Sales",
    "orders": "Orders",
    "customers_Count": "Customers",
    "next_bookings": "Next Bookings",
    "drag_service_image": " drop or click to upload service image",
    "drag_product_image": " drop or click to upload product image",
    "drop_service_image": "Drop service image",
    "drop_product_image": "Drop product image",
    "additionals_images": "Additional Images",
    "sholud_upload_main_image_before_upload_additional_images": "Please add a main image before adding additional images",
    "En": "English",
    "Ar": "Arabic",
    "daily": "Daily",
    "weekly": "Weekly",
    "monthly": "Monthly",
    "3months": "Every 3 Months",
    "yearly": "Yearly",
    "recurring_type": "Recurring Type",
    "recurring_end_date": "Recurring End Date",
    "purchased_date": "Purchased Date",
    "package_number": "Package Number",
    "create_recurring_appointment": "Create Recurring Appointment",
    "recurring_appointment": "Recurring Appointment",
    "lastest_orders": "Latest Orders",
    "today_appointments": "Today Appointments",
    "main_appointment_details": "Main Appointment Details",
    "main_appointment": "Main Appointment",
    "recurring_appointments": "Recurring Appointments",
    "assign_customer": "Assign Customer",
    "no_notifications": "You haven't received any notifications yet",
    "no_order_images": "No Order Images",
    "make_main_image": "Make the image the main image of the service",
    "images": "Images",
    "main_image": "Main Image",
    "can_share_service_link_by": "Can share service link by",
    "shareTo": {
        "facebook": "share on facebook",
        "x": "share on x",
        "whatsapp": "share on whatsapp",
        "linkedin": "share on linkedin"
    },
    "share": "Share by",
    "CopyLink": "Copy Link",
    "copied_link": "Link copied",
    "Reply": "Reply",
    "Change Status": "Change Status",
    "onbord": {
        "Membership_Registration": "Membership Registration",
        "staff": "Add Service Provider",
        "account_info": "Business Information",
        "create_account": "Create Account",
        "select_needs": "Select Your Needs",
        "store_details": "Store Details",
        "activity_details": "Activity Details",
        "service_setup": "Service Setup",
        "services": "Services",
        "which_type_of_service": "What type of services do you provide?",
        "choose_type_of_industry": "Industry",
        "Add_services": "Add Services",
        "Add_services_desc": "This will help us set up your store. Don't worry, you can edit and add more services later.",
        "app_title": "Download the app",
        "app_desc": "To manage your store easily and track your sales at any time",
        "launch_title": "Launch with us",
        "launch_desc": "Design your store from scratch and give your brand a unique digital identity",
        "onbord_title": "Welcome to Your Store",
        "onbord_desc": "Are you ready to launch your store on Mahjoz?",
        "customize_store_title": "Customize Your Store",
        "customize_store_desc": "Add your logo and social media links",
        "choose_design_title": "Choose Store Design",
        "choose_design_desc": "To provide a unique shopping experience that matches your brand",
        "add_first_product_title": "Add Your First Product",
        "add_first_product_desc": "Whether it's a product for sale or a booking service",
        "set_working_hours_title": "Set Working Hours",
        "set_working_hours_desc": "To facilitate order scheduling management",
        "setup_payment_title": "Enable Payment Options",
        "setup_payment_desc": "To receive payments easily and securely",
        "subscribe_plan_title": "Explore Subscription Plans",
        "subscribe_plan_desc": "Choose the package that suits your needs and launch your digital business",
        "add_staff_title": "Add Providers",
        "add_staff_desc": "To help you manage your store and provide better service to your customers",
        "skip": "Skip Onboarding",
        "hi": "Hi",
        "onboard_title": "Complete your onboarding"
    },
    "Mahjoz": "Mahjoz",
    "intro": "The newest service management and scheduling system, with Mahjoz, list your services, set schedule and working hours, launch your page and start receiving bookings and payments from customers. ",
    "trail_days": "Try Mahjoz for Free - 7 Days Trial",
    "unlimited_appointments": "Unlimited appointments",
    "premium_support": "Professional Support",
    "advanced_store": "Store Site",
    "mahjozFeatures": {
        "Comprehensive_Booking_Scheduling_Management_System": "Comprehensive Booking and Scheduling Management System System",
        "Online_payment_invoices": "Integrated Online Store",
        "integrationed_online_store": "Online payment and invoices"
    },
    "sign_up_description": "Mahjoz: Your Complete Booking and Scheduling Management System Elevate your business with Mahjoz, an all-in-one Booking and Scheduling management system. Empower your customers to book your services effortlessly through our integrated online store. Stay in full control, providing everything you need to boost sales and efficiently organize your appointments.",
    "our_clients": "Our Clients",
    "subscribe": "Subscribe",
    "plans_and_Pricing": "Plans & Pricing",
    "plans_and_Pricing_brief": "Plans compitable with all business sizes",
    "plans_and_Pricing_description": "Choose the plan that suits your business needs and start your free trial now",
    "featureGroups": {
        "general": "General",
        "payment": "Payment",
        "support": "Support",
        "integration": "Integration"
    },
    "block_msg": "This Customer is Blocked , can't make any order",
    "name_of_working_hour": "Name of working hour",
    "range_of_working_hour": "Range of working hour",
    "requset_fullscreen": "Do you want to enter full screen mode",
    "clear_search": "Clear Search",
    "search_by_product_name_or_service_name": "Search by product name or service name",
    "displayed_store": "Displayed at store",
    "not_displayed_store": "Not Displayed at store",
    "price_unit": "Price Unit",
    "save_as_draft": "Save as Draft",
    "draft_saved": "Draft saved",
    "preview": "Preview",
    "current-plan": "Current Plan",
    "next_billing_date": "Next billing date",
    "upgrade_plan": "Upgrade Plan",
    "renew_plan": "Renew Plan",
    "subscription_renewed": "Subscription Renewed",
    "no_active_plan": "No active plan",
    "subscriptions": "Subscriptions",
    "choose_the_right_plan_for_your_business": "Choose the right plan for your business",
    "choose_plan": "Choose Plan",
    "invoicing": "Invoicing",
    "tax-standard-invoice": "Standard Invoice",
    "simplified-tax-invoice": "Simplified Tax Invoice",
    "tax-standard-invoice-trial": "Standard Invoice (Trial)",
    "set_as_default": "Set as default",
    "invoice_templates": "Invoice Templates",
    "payment_method_description": "You can Customize the available payment methods for your POS device",
    "online_payment_description": "Activate the available payment applications in the store, so that your customers can pay online",
    "accounting_apps": "Accounting Apps",
    "startFreeTrial": "Start a 14-day free trial",
    "not_a_member": "Not a member ?",
    "exist_pos": "Exit POS",
    "scheduled_at": "Scheduled At",
    "app_id": "App",
    "campaign_content": "Campaign Content",
    "campaign_name": "Campaign Name",
    "marketing_campaigns": "Marketing Campaigns",
    "campaign_targeted_customers_count": "Targeted Customers Count",
    "create_new_marketing_campaign": "Create New Marketing Campaign",
    "will_send_notification_to_all_users": "This campaign will be sent to all customers",
    "scheduled_at_campaign": "Campaign Scheduled At",
    "recommended": "Recomended",
    "calendar_configuration": "Calendar Configuration",
    "yesterday": "Yesterday",
    "days_ago": "{days} days ago",
    "last_month": "Last Month",
    "this_month": "This Month",
    "create_appointment": "Create Appointment",
    "new_order": "New Order",
    "new_appointment": "New Appointment",
    "single_service": "Single Service",
    "multi_service": "Multi Services",
    "which_one_do_you_want_to_create": "Which one do you want to create?",
    "create_order": "New Order / Booking",
    "edit_order": "Edit Order / Booking",
    "refund_order": "Refund Order",
    "refund": "Refund",
    "refunded": "Refunded",
    "serviceProvider": {
        "tabs": {
            "services": {
                "toggle": "Toggle Service",
                "category": "Category",
                "name": "Name"
            },
            "zones": {
                "zone": "Zone",
                "toggle": "Toggle Service"
            }
        }
    },
    "next_order": "Next Order",
    "prev_order": "Previous Order",
    "staffs": "Service Providers",
    "customization_staff": "Customization Service Providers",
    "not_selected": "Not Selected",
    "pay_and_got_to_invoice": "Complete Payment and go to Invoice",
    "appearance": "Appearance",
    "gallery": "Gallery",
    "contacts": "Contacts",
    "website-management": "Website Management",
    "store-settings": "Store Settings",
    "store-general": "General Settings",
    "store-panel": "Store ",
    "store-settings-panel": "Store Settings",
    "store-settings-general": "General Settings",
    "not-found-plugin": "You don't have the necessary plugin for this page",
    "if_you_need_to_this_page_go_to_plugin_and_install_it": "If you need to this page please go to plugins and install it",
    "go_to_plugin": "Go to Plugins",
    "package_valid_period_example": "e.g : 30 Day",
    "package_max_number_of_available_example": "e.g : 100 clients",
    "customize-homepage": "Customize Homepage",
    "complete_payment": "Complete Payment",
    "online_payment": "Online Payment",
    "bank_transfer": "Bank Transfer",
    "beneficiary_name": "Beneficiary Name",
    "receipt_transfer": "Receipt Transfer",
    "pricePerUnit": "Price Per Unit",
    "totalPrice": "Total Price",
    "new_zone": "Add New Zone",
    "zones_settings": "Zones Settings",
    "address": "Address",
    "inactive": "Inactive",
    "selected_all": "Selected All",
    "on-site": "On Site",
    "online": "Online",
    "home-service": "Home Service",
    "branch_type": "Branch Type",
    "will_show_map_to_select_boundary": "The map will be displayed to select the location for the customer when booking",
    "end_of_day": "End of Day",
    "googleCalendar": {
        "title": "Sync With Google Calendar",
        "sync": "Sync",
        "unSync": "Cancel google calendar Sync",
        "unSynced": "google calendar sync cancellation was successful.",
        "synced": "google calendar sync was successful.",
        "syncedError": "google calendar sync was not successful."
    },
    "all": "All",
    "free_plan": "Free Plan",
    "payment_link": "Payment Link",
    "order_booking": "order booking details",
    "plan": "Plan",
    "set-appointment": "Set Appointment",
    "order": "Order",
    "customer_data": "Customer Data",
    "payment_transactions": "payment transaction",
    "show_boundaries_on_map": "Show boundaries on the map",
    "name_of_boundary": "Name of the area",
    "staffs_boundaries": "areas of service providers",
    "show_staff_boundaries_on_map": "Show service provider areas on the map",
    "cleat_map": "Clear areas from the map",
    "select_payment_methods": "Select Payment Method",
    "apply": "Apply",
    "coupon_not_found": "Coupon not found",
    "total_included_taxes": "Total (Included Taxes)",
    "impersonation": {
        "label": "Impersonate Account",
        "form_label": "Impersonate Account",
        "email_address": "Account Email Address",
        "end_impersonation": "End Impersonation",
        "success": "Impersonation was successful, You're being redirect...",
        "impersonate_mode": "You're In Impersonation Mode",
        "warning": "employee should be careful when entering customer accounts, as any login activity is recorded precisely. It is prohibited to log into customer accounts unless it is absolutely necessary, such as verifying a locked account, with the confirmation of the fact that no action is carried out within the account without formal authorization. Compliance with these instructions ensures the security of customer data and the protection of customer information."
    },
    "whatsapp": {
        "title": "WhatsApp Activation",
        "description": "It is a service that enables you to activate WhatsApp and use it to communicate with your customers easily.",
        "activate": "Activate",
        "deactivate": "Deactivate",
        "error_loading_qrcode": "Error Loading QR",
        "processing": "Processing...",
        "loading_qr": "Loading QR Code",
        "generating_qr_code": "Generating QR code, please wait...",
        "checking": "Checking Status",
        "working_status": {
            "success_title": "WhatsApp Activated Successfully",
            "connected_phone_label": "Connected Phone Number:",
            "status_connected": "Connected Now"
        },
        "installation_guide": {
            "title": "How to Link WhatsApp to Mahjoz",
            "method": "Method: Link via QR Code Scan",
            "step1": "Open WhatsApp on your phone.",
            "step2": "Go to \"Settings\" > \"Linked Devices\" > \"Link a Device\".",
            "step3": "Your camera will open to scan a QR code.",
            "step4": "Use your phone to scan the QR code shown on the platform.",
            "note": "Make sure your phone is connected to the internet and WhatsApp is active.",
            "visual_guide": "WhatsApp Mahjoz activation steps with visual guide",
            "scan_instruction": "Scan this QR code using WhatsApp"
        }
    },
    "warning_break_tab": "remove breaks and move it to working hour before : 1/11/2024",
    "add_discount": "Add Discount",
    "hide_discount": "Hide Discount",
    "global_discount": "Global Discount",
    "discount_type": "Discount Type",
    "fixed_amount": "Fixed Amount",
    "percentage": "Percentage",
    "apply_discount": "Apply Discount",
    "remove_discount": "Remove Discount",
    "apply_discount_to_all_items": "Apply Discount to All",
    "discount_applied_to_all": "Discount applied to {count} items",
    "enter_discount_amount": "Enter discount amount",
    "you_subscribed_to_plan": "You are on the {planName} plan",
    "renew_subscription": "Renew Subscription",
    "trial_ended": "Trial Ended",
    "subscription_ending_soon": "Subscription Ending Soon",
    "verify": "Verify",
    "verifying": "Verifying...",
    "Expense_Types": "Expense Types",
    "hints": {
        "discount": "You can enter the discount amount or percentage.",
        "general_discount_amount": "You can enter the discount as a fixed value or a percentage, and it can be applied to the entire order or distributed to specific elements like products, services, or packages.",
        "langinput": "You can enter the text in both Arabic and English."
    },
    "created_at": "Request Date",
    "delete_item": "Delete Item",
    "actions": "Actions",
    "save_and_add_new" : "Save and add new",
    "uploadArea": {
        "instruction": "Drag your files here to upload or",
        "link": "Select file",
        "sizeNote": "Image size must be less than {maxSize} KB",
        "formatNote": "Allowed formats: {formatNote} "
    },
    "advanced-provider-app": "Provider App Settings",
    "order_contains_items": "Order Contains Items",
    "price_after_discount": "Price After Discount",
    "branch":{
        "name": "Branch Name"
    }
}