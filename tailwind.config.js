/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme')

// Color definitions
const primary = {
  DEFAULT: '#558498',
  50: '#D9F0FF',
  100: '#bae6fd',
  200: '#8ED6FF',
  300: '#5EC6FF',
  400: '#2EB6FF',
  500: '#049cdb',
  600: '#1E9CCE',
  700: '#1A8AB4',
  800: '#16689A',
  900: '#14171C',
}

const secondary = {
  DEFAULT: '#082F49',
  dark: '#062436', // Darker shade for hover
}

const gray = {
  DEFAULT: '#C7C5D0',
}

// CSS variable color helper
const cssVarColor = colorName => ({ opacityVariable, opacityValue }) => {
  if (opacityValue !== undefined)
    return `rgba(var(--color-${colorName}), ${opacityValue})`

  if (opacityVariable !== undefined)
    return `rgba(var(--color-${colorName}), var(${opacityVariable}, 1))`

  return `rgb(var(--color-${colorName}))`
}

module.exports = {
  content: [
    './index.html',
    './src/**/*.{vue,js,ts,jsx,tsx}',
    './node_modules/vue-tailwind-datepicker/**/*.js',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Tajawal', 'sans', ...defaultTheme.fontFamily.sans],
      },
      fontSize: {
        hero: ['48px', { lineHeight: '64px' }],
        h1: ['32px', { lineHeight: '28px' }],
        h2: ['24px', { lineHeight: 'auto' }],
        h3: ['18px', { lineHeight: '32px' }],
        base: ['16px', { lineHeight: '24px' }],
        sm: ['14px', { lineHeight: '20px' }],
        xs: ['12px', { lineHeight: '16px' }],
      },
      width: {
        'container-mobile': '380px',
        'container-desktop': '1128px',
      },
      minWidth: {
        'container-mobile': '320px',
        'container-desktop': '1024px',
      },
      maxWidth: {
        'container-mobile': '1127px',
        'container-desktop': '1440px',
      },
      screens: {
        isSmallScreen: { max: '1127px' },
        isLargeScreen: { min: '1128px' },
      },
      colors: {
        primary,
        'vtd-primary': primary,
        'vtd-secondary': secondary,
        'secondary': cssVarColor('secondary'),
        'tertiary': cssVarColor('tertiary'),
        'secondary-dark': secondary.dark,
        'base-gray': gray.DEFAULT,
      },
      boxShadow: {
        '3xl': '0px 0px 5px 1px rgb(0 0 0 / 14%)',
      },
      gridColumnStart: {
        7: '7',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('tailwindcss-rtl'),
  ],
}
