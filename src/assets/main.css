:root {
  --color-primary: 38,64,91;
  --color-secondary: 8, 47, 73;
  --color-tertiary: 255,255,255;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

.error-input {
  @apply text-red-900 placeholder-red-300 border-red-300 focus:outline-none focus:ring-red-500 focus:border-red-500 !important;
}

.disable-input {
  @apply cursor-not-allowed disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 disabled:shadow-none !important;
}
.error-message {
  @apply mt-2 text-sm text-red-600;
}

[dir="rtl"] .arrow-icon {
  @apply  rotate-180
}

[dir="rtl"] .down-icon-right {
  @apply  rotate-90
}
input[type="color"] {
  @apply appearance-none
}

input[type="color"]::-webkit-color-swatch, input[type="color"]::-webkit-color-swatch {
  @apply rounded border-none
}
.global-text-color {
  @apply text-[#7A7A7A];
}

.multiselect__tags {
  @apply py-3 px-2 focus:border-primary-500 inline-block w-full border border-gray-300 rounded-md !important;
}

.multiselect__placeholder {
  @apply text-gray-400 mb-0 pt-0 text-sm leading-3 !important;
}

.multiselect__select {
  @apply h-full p-0 !important;
}
.multiselect__select::before{
  @apply absolute left-[10px] right-auto top-1/2 transform -translate-y-full
}

/* start of style for main slider */
input {
  line-height: 1 !important;
}
/* end of style for main slider */

/* Custom styles for the editor */
.ProseMirror {
  outline: none;
  min-height: 200px;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* MiniEditor specific styles */
.editor-area {
  /* Base paragraph styling */
}

.editor-area p {
  margin: 0.5em 0;
  line-height: 1.6;
}

.editor-area p:first-child {
  margin-top: 0;
}

.editor-area p:last-child {
  margin-bottom: 0;
}

/* Headings in editor */
.editor-area h1,
.editor-area h2,
.editor-area h3,
.editor-area h4,
.editor-area h5,
.editor-area h6 {
  font-weight: bold;
  margin: 1em 0 0.5em 0;
  line-height: 1.3;
}

.editor-area h1 { font-size: 2em; color: #111827; }
.editor-area h2 { font-size: 1.5em; color: #1f2937; }
.editor-area h3 { font-size: 1.25em; color: #374151; }
.editor-area h4 { font-size: 1.125em; color: #4b5563; }
.editor-area h5 { font-size: 1em; color: #6b7280; font-weight: 600; }
.editor-area h6 { font-size: 0.875em; color: #6b7280; font-weight: 600; }

/* Lists in editor */
.editor-area ul,
.editor-area ol {
  margin: 1em 0;
  padding-left: 1.5em;
}

.editor-area ul {
  list-style-type: disc;
}

.editor-area ol {
  list-style-type: decimal;
}

.editor-area li {
  margin: 0.25em 0;
  line-height: 1.6;
}

.editor-area li p {
  margin: 0;
}

/* Links in editor */
.editor-area a {
  color: #2563eb;
  text-decoration: underline;
  transition: color 0.2s;
}

.editor-area a:hover {
  color: #1d4ed8;
}

/* Text formatting */
.editor-area strong,
.editor-area b {
  font-weight: bold;
}

.editor-area em,
.editor-area i {
  font-style: italic;
}

.editor-area u {
  text-decoration: underline;
}

.editor-area s,
.editor-area strike,
.editor-area del {
  text-decoration: line-through;
}

/* Code styling */
.editor-area code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875em;
}

.editor-area pre {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1em 0;
}

.editor-area pre code {
  background: none;
  padding: 0;
}

/* Media elements */
.editor-area img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 0.5em 0;
  display: block;
}

.editor-area video {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 0.5em 0;
  display: block;
}

.editor-area audio {
  width: 100%;
  margin: 0.5em 0;
  display: block;
}

/* Media container with remove button */
.editor-area span[style*="position: relative"] {
  display: inline-block;
  margin: 0.5em 0;
}

/* Blockquotes */
.editor-area blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1em 0;
  font-style: italic;
  color: #6b7280;
}

/* Text alignment */
.editor-area [style*="text-align: left"] {
  text-align: left !important;
}

.editor-area [style*="text-align: center"] {
  text-align: center !important;
}

.editor-area [style*="text-align: right"] {
  text-align: right !important;
}

.editor-area [style*="text-align: justify"] {
  text-align: justify !important;
}

/* Font sizes */
.editor-area [style*="font-size"] {
  line-height: 1.4;
}

/* Highlight/Background colors */
.editor-area [style*="background-color"] {
  padding: 0.125rem 0.25rem;
  border-radius: 0.125rem;
}

/* RTL Support for editor */
.editor-area[dir="rtl"] {
  text-align: right;
}

.editor-area[dir="rtl"] ul,
.editor-area[dir="rtl"] ol {
  padding-left: 0;
  padding-right: 1.5em;
}

.editor-area[dir="rtl"] blockquote {
  border-left: none;
  border-right: 4px solid #e5e7eb;
  padding-left: 0;
  padding-right: 1rem;
}

/* Empty state styling */
.editor-area:empty:before {
  color: #9ca3af;
  font-style: italic;
}

/* Focus improvements */
.editor-area:focus {
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Selection styling */
.editor-area ::selection {
  background-color: rgba(99, 102, 241, 0.2);
}

/* Toolbar improvements */
.toolbar-btn[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: -2rem;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 50;
  pointer-events: none;
}

/* Color panel improvements */
.absolute.z-30 {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

/* Typography Styles */
.ProseMirror h1,
.prose h1 {
  font-size: 2em;
  font-weight: bold;
  margin: 1em 0;
  color: #111827;
}

.ProseMirror h2,
.prose h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 0.83em 0;
  color: #1f2937;
}

/* Lists */
.ProseMirror ul,
.prose ul {
  list-style-type: disc;
  padding-inline-start: 1.5em;
  margin: 1em 0;
}

.ProseMirror ol,
.prose ol {
  list-style-type: decimal;
  padding-inline-start: 1.5em;
  margin: 1em 0;
}

/* Links */
.ProseMirror a,
.prose a {
  color: #2563eb;
  text-decoration: underline;
  transition: color 0.2s;
}

.ProseMirror a:hover,
.prose a:hover {
  color: #1d4ed8;
}

/* Images */
.ProseMirror img,
.prose img {
  max-width: 100%;
  height: auto;
  margin: 1em 0;
  border-radius: 0.375rem;
}

/* Highlight */
.ProseMirror mark,
.prose mark {
  background-color: #fef08a;
  border-radius: 0.125rem;
  padding: 0.125rem 0;
}

/* RTL Support */
[dir="rtl"] .ProseMirror,
[dir="rtl"] .prose {
  text-align: right;
}

[dir="rtl"] .ProseMirror ul,
[dir="rtl"] .ProseMirror ol,
[dir="rtl"] .prose ul,
[dir="rtl"] .prose ol {
  padding-inline-start: 0;
  padding-inline-end: 1.5em;
}

/* Responsive Design */
@media (max-width: 640px) {
  .min-h-screen {
    padding: 1rem;
  }
  
  .flex-wrap .flex {
    flex-grow: 1;
  }
  
  .flex-wrap button {
    flex: 1;
  }
  
  /* Mobile editor improvements */
  .editor-container {
    max-width: 100%;
    margin: 0;
  }
  
  .flex-wrap {
    gap: 0.25rem;
  }
  
  .toolbar-btn {
    padding: 4px 6px;
  }
  
  .icon {
    width: 16px;
    height: 16px;
  }
  
  .editor-area {
    min-height: 100px;
    font-size: 14px;
  }
}
