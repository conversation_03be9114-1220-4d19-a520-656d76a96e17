.booking-calendar {
  font-family: Inter, Helvetica, sans-serif;
  font-weight: lighter;
}
.fc-theme-standard td,
.fc-theme-standard th {
  --fc-border-color: #eef0f2;
  border: 1px solid var(--fc-border-color);
}
.fc {
  .fc-popover {
    --fc-neutral-bg-color: transparent;
    padding: 8px 6px 16px 12px;
    border-radius: 6px;
    box-shadow: 0 28px 64px 0 rgba(22,45,61,.12);
    border: 0;
    background-color: #fff;
    width: 260px;
    transform: translateY(-40%);
    border: 1px solid #d6dce0;
    .fc-popover-title {
      font-size: 16px;
      font-weight: 500;
    }
  }
  .fc-button {
    font-family: Inter,Helvetica,sans-serif;
    cursor: pointer;
    height: 40px;
    border-radius: 50px !important;
    border: none;
    outline: none;
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    padding: 10px;
    position: relative;
    &.fc-button-primary {
      background-color: #fff;
      color: #586874;
    }
    &:hover {
      background-color: #f3f5f6;
    }
  }
  .fc-toolbar-chunk {
    @apply flex items-center;
  }
  .fc-toolbar-title {
    @apply text-2xl;
  }
  .fc-event {
    padding: 0 8px 0 4px;
    border-radius: 12px;
  }
  .fc-day-today {
    background-color: #fff !important;
  }
  .fc-dayGridMonth-view {
    .fc-daygrid-day-number {
      width: 20px;
      height: 20px;
      border-radius: 6px;
      letter-spacing: .1px;
      font-size: 12px;
      line-height: 20px;
      color: #586874;
      text-align: center;
      padding: 0;
      margin: 6px 6px 4px 6px;
    }
    .fc-day-today {
      .fc-daygrid-day-number {
        color: #fff;
        background-color: #ddd;
        font-weight: 600;
      }
    }
  }
  .fc-daygrid-event {
    margin-left: 0;
    margin-right: 0;
    @apply my-0.5;
  }

  .fc-daygrid-dot-event {
    &:hover {
      background-color: #f0f0f0;
    }
  }
  .fc-more-popover {
    z-index: 8;

    .fc-popover-body {
      padding: 0;
    }
    .fc-popover-header {
      padding: 0;
    }
  }
  .fc-daygrid-event-harness {

  }

  .fc-timegrid-slot-label,
  .fc-timegrid-slot-lane {
    border-top: 1px solid #eef0f2;
    z-index: 1;
  }

  .fc-timegrid-slot-lane.fc-timegrid-slot-minor,
  .fc-timegrid-slot-label.fc-timegrid-slot-minor {
    @apply border-0;
  }

  .fc-timeGridWeek-view .fc-timegrid-cols .fc-timegrid-col {
    border-right: 1px solid #eef0f2;
  }
}
