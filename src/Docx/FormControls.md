# Autocomplete and Selection Components

## Overview

This project uses several components for autocomplete and selection functionality. This document explains how to use them effectively in your forms.

## Multiselect Component

### Introduction

The project uses the `vue-multiselect` library for multi-selection functionality, which is especially useful for tag selection and other multiple-choice scenarios.

### Basic Usage

```vue
<script setup>
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.css";

// Options array with id and name properties
const tags = ref([
  { id: 1, name: "Tag 1" },
  { id: 2, name: "Tag 2" }
]);

// Selected items stored in form data
const formData = reactive({
  tags: []
});
</script>

<template>
  <Multiselect
    v-model="formData.tags"
    :multiple="true"
    track-by="id"
    label="name"
    :options="tags"
    :placeholder="$t('tags.selectOption')"
    :select-label="$t('tags.selectLabel')"
    :selected-label="$t('tags.selectedLabel')"
    :deselect-label="$t('tags.deselectLabel')"
    :close-on-select="false"
    :clear-on-select="false"
  />
</template>
```

### Key Properties

- **v-model**: Binds to your form data
- **multiple**: Enables multiple selection
- **track-by**: Property used to track selected items
- **label**: Property displayed in the UI
- **options**: Array of selectable options
- **close-on-select**: Whether to close dropdown on selection
- **clear-on-select**: Whether to clear selection after choosing

### Styling

The component follows the project's styling conventions. Import the CSS:

```js
import "vue-multiselect/dist/vue-multiselect.css";
```

## BaseComboBox Component

### Introduction

`BaseComboBox` is a custom component for autocomplete selection that's used in forms like the packages modal.

### Basic Usage

```vue
<BaseComboBox
  v-model="formData.serviceId"
  place-holder="serviceName"
  required
  arial-label="Search"
  :options="servicesOptions"
>
  {{ $t("booking.service") }}
</BaseComboBox>
```

### Key Properties

- **v-model**: Binds to your form data
- **place-holder**: Placeholder text
- **required**: Whether the field is required
- **options**: Array of options in format `{label: string, value: string}`
- **Default slot**: Label content

### Options Format

The options array should follow this structure:

```js
const options = [
  { label: "Option 1", value: "option1" },
  { label: "Option 2", value: "option2" }
];
```

## Phone Input Component

### Introduction

The `PhoneInput` component provides a standardized way to input phone numbers with country codes.

### Basic Usage

```vue
<PhoneInput 
  :model-value="formData.phone" 
  label="form.phone"
  @update:model-value="setPhoneNumber" 
/>
```

### Handling Phone Input

The component emits the phone number and country code separately. Create a handler:

```js
const setPhoneNumber = (
  phoneNumber: string,
  phoneObject: { countryCode: string; nationalNumber: string }
) => {
  formData.phone = phoneNumber
  formData.phone_country = phoneObject.countryCode
};
```

## LangInput Component

### Introduction

The `LangInput` component handles multilingual input fields, allowing users to enter content in different languages.

### Basic Usage

```vue
<LangInput
  v-model="formData.name_localized"
  :placeholder="$t('form.name')"
  :v$="v$"
/>
```

### Form Data Structure

For LangInput, your form data should have a structure like:

```js
const formData = reactive({
  name_localized: {
    ar: "",
    en: ""
  }
});
```

### Validation

To validate LangInput fields, use nested validation rules:

```js
const rules = {
  name_localized: {
    ar: {
      required: requiredIf(() => !formData.name_localized.en.trim())
    },
    en: {
      required: requiredIf(() => !formData.name_localized.ar.trim())
    }
  }
};
```

## SelectInput Component

### Introduction

`SelectInput` is a consistent dropdown selection component used throughout the application.

### Basic Usage

```vue
<form-group :validation="v$" name="team_id">
  <template #default="{ attrs }">
    <SelectInput
      v-bind="attrs"
      id="team_id"
      v-model="formData.team_id"
      :label="$t('modalPlacholder.branch')"
    >
      <option hidden selected value="">
        {{ $t("form.select") }}
      </option>
      <option
        v-for="team in teamsList"
        :key="team?.uuid"
        :value="team?.uuid"
      >
        {{ team.name }}
      </option>
    </SelectInput>
  </template>
</form-group>
```

### Integration with form-group

The SelectInput component integrates with the form-group validation system by binding the provided attrs.

## Best Practices

1. **Always provide labels and placeholders** - Improve usability with clear labels
2. **Handle empty states** - Provide feedback when no options are available
3. **Use consistent options format** - Follow the established patterns for each component
4. **Validate selected values** - Ensure selections meet form requirements
5. **Reset selection state** - Clear selections when resetting forms
6. **Handle async options** - Load options asynchronously when appropriate 