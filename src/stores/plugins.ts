import { defineStore } from 'pinia'
// import  from '../services/AppService'
import PluginService from '@/services/PluginService'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
import type { PluginType } from '@/types'
const { showNotification } = useNotifications()

export const usePluginsStore = defineStore({
  id: 'Plugins',
  state: () => ({
    plugins: [] as PluginType[],
    loader: false,
    pluginLoading: false,
    availablePlugins: [] as string[],

  }),
  getters: {
    getPlugins: state => state.plugins as PluginType[],
    hasPos: state => state.availablePlugins.includes('pos'),
    hasCommission: state => state.availablePlugins.includes('commission'),
    hasPackages: state => state.availablePlugins.includes('packages'),
    getAvailablePlugins: state => state.availablePlugins,
    hasBoundaries: state => state.availablePlugins.includes('boundaries'),
  },
  actions: {
    async fetchPlugins(): Promise<{ data: PluginType | Error }> {
      try {
        this.pluginLoading = true
        const { data } = await PluginService.fetchPlugins()
        this.availablePlugins = data.filter((plugin: PluginType) => plugin.installed).map((plugin: PluginType) => plugin.slug)
        this.plugins = data
        return data
      }
      finally {
        this.pluginLoading = false
      }
    },
    async installPlugin(pluginId: string): Promise<any> {
      await PluginService.installPlugin(pluginId)
      showNotification({
        title: i18n.global.t('Success'),
        type: 'success',
        message: i18n.global.t('operations.installed'),
      })
    },
    async uninstallPlugin(pluginId: string): Promise<any> {
      await PluginService.uninstallPlugin(pluginId)
      showNotification({
        title: i18n.global.t('Success'),
        type: 'success',
        message: i18n.global.t('operations.uninstalled'),
      })
    },
  },
})
