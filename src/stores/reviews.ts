import { defineStore } from 'pinia'
import ReviewsService from '@/services/ReviewsService'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'

const { showNotification } = useNotifications()

export const useReviewsStore = defineStore({
  id: 'Reviews',
  state: () => ({}),
  getters: {},
  actions: {
    async fetchReviews(page = 1, status = 'pending'): Promise<any> {
      return ReviewsService.fetchReviews(page, status).then(({ data }) => data)
    },

    async changeStatus(reviewId: string, status: string): Promise<any> {
      return ReviewsService.changeStatus(reviewId, status).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })

        return data
      })
    },
  },
})
