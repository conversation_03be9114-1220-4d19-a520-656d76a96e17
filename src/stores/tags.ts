import { defineStore } from 'pinia'
import TagsService from '../services/TagsService'
import i18n from '@/i18n'
import useNotifications from '@/composables/useNotifications'

const { showNotification } = useNotifications()

export const useTagStore = defineStore({
  id: 'Tags',

  state: () => ({
    tags: [],
    processing: true,
  }),

  getters: {
    getTags: state => state.tags,
  },

  actions: {
    async createTag(payload: { name: string; id: string; model: string; color: string }): Promise<void> {
      return TagsService.createTag(payload)
        .then(({ data }) => {
          const item = data.data
          if (payload.id) {
            showNotification({
              title: i18n.global.t('Success'),
              type: 'success',
              message: i18n.global.t('operations.updated'),
            })
            this.tags[item.model] = [...this.tags[item.model]].map((tag: { name: string; id: string }) => {
              if (tag.id === item.id)
                tag = item

              return tag
            })
          }
          else {
            showNotification({
              title: i18n.global.t('Success'),
              type: 'success',
              message: i18n.global.t('operations.created'),
            })
            this.tags[item.model] = [item, ...this.tags[item.model]]
          }
        })
    },
    async deleteTags(tagId: string): Promise<boolean> {
      return TagsService.deleteTag(tagId)
        .then((res) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })

          this.fetchTags()
          return true
        })
    },
    async fetchTags(model = ''): Promise<any> {
      return TagsService.fetchTags(model).then(({ data }) => {
        this.tags = data
        this.processing = false
        return data
      })
    },
  },
})
