import { defineStore } from 'pinia'
import couponsService from '../services/coupons'
import type { Coupons, CouponInfoPayload } from '../types/coupons'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

export const useCoupons = defineStore({
  id: 'coupons',
  state: () => ({
    coupons: [] as Coupons[],
    processing: false,
  }),
  getters: {
    getCoupons: state => state.coupons,
  },
  actions: {
    getCouponsList() {
      this.processing = true
      return couponsService.getListCoupons().then((res) => {
        this.coupons = res.data.data
        this.processing = false
        return res
      })
    },
    getCoupon(couponUuid: string) {
      return couponsService.getCoupon(couponUuid)
    },
    createCoupons(coupons: Coupons) {
      return couponsService.createCoupons(coupons).then((res) => {
        this.coupons = [...this.coupons, res.data.data]
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return res
      })
    },
    updateCouponInfo(coupons: CouponInfoPayload, couponUuid: string) {
      return couponsService.updateCouponInfo(coupons, couponUuid).then((res) => {
        this.coupons = this.coupons.map((item) => {
          if (item.uuid === couponUuid)
            return res.data.data

          return item
        })
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return res
      })
    },
    updateCouponScope(payload: { products?: string[]; categories?: string[]; branches?: string[] }, couponUuid: string) {
      return couponsService.updateCouponScope(payload, couponUuid).then((res) => {
        this.coupons = this.coupons.map((item) => {
          if (item.uuid === couponUuid) {
            return res.data.data
          }
          return item
        })
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return res
      }).catch((err) => {
        showNotification({
            title: i18n.global.t("Error"),
            type: "error",
            message: err.message || err.data.message,
        });
        return Promise.reject(err);
      })
    },
    updateCouponDescription(payload: { description: { en: string; ar: string } }, couponUuid: string) {
      return couponsService.updateCouponDescription(payload, couponUuid).then((res) => {
        this.coupons = this.coupons.map((item) => {
          if (item.uuid === couponUuid) {
            return res.data.data
          }
          return item
        })
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return res
      }).catch((err) => {
        showNotification({
            title: i18n.global.t("Error"),
            type: "error",
            message: err.message || err.data.message,
        });
        return Promise.reject(err);
      })
    },
    updateCouponStatus(payload: { status: number }, couponUuid: string) {
      return couponsService.updateCouponStatus(payload, couponUuid).then((res) => {
        this.coupons = this.coupons.map((item) => {
          if (item.uuid === couponUuid) {
            return res.data.data
          }
          return item
        })
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return res
      }).catch((err) => {
        showNotification({
            title: i18n.global.t("Error"),
            type: "error",
            message: err.message || err.data.message,
        });
        return Promise.reject(err);
      })
    },
    deleteCoupons(couponsUuid: string) {
      return couponsService.deleteCoupons(couponsUuid).then((res) => {
        this.getCouponsList()
        return res
      })
    },
  },
})
