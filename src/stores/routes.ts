import { defineStore, storeToRefs } from 'pinia'
import route from '@/router'
import asyncRoute from '@/router/asyncRoutes/index'
import { recursiveCheckingPermissionRoutes } from '@/utils/routePermissions'
import { useAuthStore } from '@/stores/auth'

export const useRoutesStore = defineStore({
  id: 'routes',
  state: () => ({
    // routes with same structure in route.ts , donot use route.getRoutes() because it will return all routes linearly
    routes: asyncRoute,
    accassableRoutes: [],
    permissionsRoutes: [],
  }),
  getters: {
    getRoutes: state => state.routes,
    getAccassableRoutes: state => state.accassableRoutes,
    getNavigationRoutes: (state) => {
      const { hasTenantActiveteWhatsapp } = storeToRefs(useAuthStore())
      return state.accassableRoutes.filter(route => route.meta?.showInSidebar || (route.meta?.featureFlag == 'mersal-whatsapp' && hasTenantActiveteWhatsapp.value)).sort((a, b) => a.meta.order - b.meta.order)
    },
  },
  actions: {
    setAccassableRoutes(routes: string[], permissionRoutes: string[]) {
      this.accassableRoutes = recursiveCheckingPermissionRoutes(routes, permissionRoutes)
    },
    updateRoutePermissions(permissionRoutes: string[]) {
      this.permissionsRoutes = permissionRoutes

      this.setAccassableRoutes(this.routes, this.permissionsRoutes)
    },
  },
})
