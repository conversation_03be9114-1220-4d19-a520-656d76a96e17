import { defineStore } from 'pinia'
import { ClipboardIcon, ClockIcon, CursorArrowRaysIcon, UsersIcon } from '@heroicons/vue/24/outline'
import DashboardService from '@/services/DashboardService'
import type { Booking, Order, Paginate } from '@/types'
const { tenant } = useAuthStore()
interface ISpurceStatistics {
  bookingPage_sales_count: number
  pos_sales_count: number
}
interface ISalesStatistics {
  customers: number
  next_appointment: number
  orders: number
  sales: number
}

export const useDashboardStore = defineStore({
  id: 'dashboard',

  state: () => ({
    latestOrders: {} as Paginate<Order>,
    salesStatistics: {} as ISalesStatistics,
    sourceStatistics: {} as ISpurceStatistics,
    topServicesStatistics: [],
    todayAppointments: {} as Paginate<Booking>,
  }),

  getters: {
    dashboardStats: ({ salesStatistics }) => ([
      { id: 1, name: 'sales', stat: `${salesStatistics.sales}`, icon: ClipboardIcon, change: '122', changeType: 'increase', route: 'orders' },
      { id: 2, name: 'orders', stat: salesStatistics.orders, icon: ClockIcon, change: '122', changeType: 'increase', route: 'orders' },
      { id: 3, name: 'customers_Count', stat: salesStatistics.customers, icon: UsersIcon, change: '5.4%', changeType: 'increase', route: '/management/customers' },
      { id: 4, name: 'next_bookings', stat: salesStatistics.next_appointment, icon: CursorArrowRaysIcon, change: '3.2%', changeType: 'decrease', route: 'bookings' },
    ]),

    getLatestOrders: state => state.latestOrders,
    getSourceStatistics: state => state.sourceStatistics,
    getTopServicesStatistics: state => state.topServicesStatistics,
    getTodayAppointments: state => state.todayAppointments,
  },

  actions: {
    fetchLatetsOrders(page: string | number = 1): Promise<void> {
      return DashboardService.fetchLatetsOrders<Paginate<Order>>(page).then(({ data }) => {
        this.latestOrders = data
      })
    },
    fetchSalesStatistics(from: string, to: string) {
      return DashboardService.fetchSalesStatistics(from, to).then(({ data }) => {
        this.salesStatistics = data
      })
    },
    fetchTopServices() {
      return DashboardService.fetchTopServices().then(({ data }) => {
        this.topServicesStatistics = data.top_services
      })
    },
    fetchSourceStatistics() {
      return DashboardService.fetchSourceStatistics().then(({ data }) => {
        this.sourceStatistics = data
      })
    },
    fetchTodayAppointments(page: string | number = 1): Promise<void> {
      return DashboardService.fetchTodayAppointments<Paginate<Booking>>(page).then(({ data }) => {
        this.todayAppointments = data
      })
    },
  },
})
