/* eslint-disable @typescript-eslint/no-unused-vars */
import { defineStore } from 'pinia'
import useNotifications from '@/composables/useNotifications'
import TrashService from '@/services/trashService'
import i18n from '@/i18n'
import type { Order, PaginationLinks, PaginationMeta } from '@/types'
const { showNotification } = useNotifications()
export const useTrash = defineStore({
  id: 'trash',
  state: () => ({
    trashedOrdersTable: {
      ordersList: [] as Order[],
      paginationMeta: {
        current_page: 1,
        from: 1,
        last_page: 1,
        links: [],
        path: '',
        per_page: 15,
        to: 15,
        total: 1,
      } as PaginationMeta,
      paginationLinks: {
        first: '',
        last: '',
        prev: null,
        next: null,
      } as PaginationLinks,
    },
  }),
  getters: {
    getTrashedOrders: state => state.trashedOrdersTable,
  },
  actions: {
    async fetchTrashedOrders(page = 1): avoid {
      const response = await TrashService.fetchTrashedOrders(page)
      this.trashedOrdersTable.ordersList = response.data.data
      this.trashedOrdersTable.paginationMeta = response.data.meta
      this.trashedOrdersTable.paginationLinks = response.data.links
    },
    async restoreOrder(orderId: string): avoid {
      const response = await TrashService.restoreOrder(orderId)
      this.trashedOrdersTable.ordersList = this.trashedOrdersTable.ordersList.filter(order => order.id !== orderId)
      showNotification({
        title: 'Success',
        type: 'success',
        message: i18n.global.t('operations.restored'),
      })
    },
    async forceDeleteOrder(orderId: string): avoid {
      const response = await TrashService.forceDeleteOrder(orderId)
      this.trashedOrdersTable.ordersList = this.trashedOrdersTable.ordersList.filter(order => order.id !== orderId)
      showNotification({
        title: 'Success',
        type: 'success',
        message: i18n.global.t('operations.deleted'),
      })
    },
  },
})
