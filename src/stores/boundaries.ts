import { defineStore } from 'pinia'
import useNotifications from '@/composables/useNotifications'
import BoundariesService from '@/services/boundariesServices'
import i18n from '@/i18n'
import StaffService from '@/services/StaffService'

type model = 'staff' | 'team'

const { showNotification } = useNotifications()
export const useBoundaries = defineStore({
  id: 'Boundaries',
  state: () => ({
    boundaries: [] as [],
    links: {},
    meta: {},
    proccessing: false,
  }),
  getters: {
    getBoundaries: state => state.boundaries,
    getBoundariesLinks: state => state.boundaries.links,
    getBoundariesMeta: state => state.boundaries.meta,
  },
  actions: {
    async fetchBoundaries(page = 1, filter?: string | null): Promise<any> {
      try {
        this.proccessing = true
        const boundariesResponse = await BoundariesService.fetchBoundaries(page, filter)
        this.boundaries = boundariesResponse.data.data
        this.boundaries.links = boundariesResponse.data.links
        this.boundaries.meta = boundariesResponse.data.meta
        return this.boundaries
      }
      catch (err) {
        return err
      }
      finally {
        this.proccessing = false
      }
    },

    async fetchSingleBoundary(uuid: string): Promise<any> {
      try {
        const response = await BoundariesService.fetchSingleBoundary(uuid)
        return response.data.data
      }
      catch (err) {
        return Promise.reject(err)
      }
    },
    async createBoundary(payload: object): Promise<any> {
      try {
        const response = await BoundariesService.createBoundary(payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return response.data.data
      }
      catch (err) {
        return Promise.reject(err)
      }
    },
    async updateBoundary(uuid: string, payload: object): Promise<any> {
      try {
        const response = await BoundariesService.updateBoundary(uuid, payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return response.data.data
      }
      catch (err) {
        return Promise.reject(err)
      }
    },
    async deleteBoundary(uuid: string): Promise<any> {
      try {
        const response = await BoundariesService.deleteBoundary(uuid)
        // this.boundaries = this.boundaries.filter((boundary: any) => boundary.id != boundaryId);
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        return response.data.data
      }
      catch (err) {
        return Promise.reject(err)
      }
    },
    async toggleStaffBoundaries(staffId: string, boundary_id: string): Promise<any> {
      try {
        const response = await StaffService.toggleStaffBoundaries(staffId, boundary_id)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.toggled'),
        })
        return response.data.data
      }
      catch (err) {
        return Promise.reject(err)
      }
    },
  },
})
