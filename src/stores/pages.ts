import { defineStore } from 'pinia'
import pageServices from '../services/pageServices'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

export const usePage = defineStore({
  id: 'page',
  state: () => ({

  }),
  getters: {

  },
  actions: {
    fetchPages() {
      return pageServices.getPages().then((res) => {
        return res
      }).catch((err) => {
        throw new Error(err)
      })
    },
    deletePage(pageUuid: string) {
      return pageServices.deletePage(pageUuid).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        return res
      }).catch((err) => {
        throw new Error(err)
      })
    },
    updatePage(formdata: any, pageUuid: string) {
      return pageServices.updatePage(formdata, pageUuid).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
      }).catch((err) => {
        throw new Error(err)
      })
    },
    addPage(payload: any) {
      return pageServices.addPage(payload).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
      }).catch((err) => {
        throw new Error(err)
      })
    },
    getPageById(pageuuid: string) {
      return pageServices.getPageById(pageuuid).then((res) => {
        return res
      }).catch((err) => {
        throw new Error(err)
      })
    },
  },

})
