import { defineStore } from 'pinia'
import whatsappService from '@/services/whatsappService'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

export type WHATSAPP_STATUS = 'WAITING' | 'SCAN_QR_CODE' | 'WORKING'

export const useWhatsapp = defineStore({
  id: 'whatsapp',
  state: () => ({
    isActivated: false,
    processing: false,
    status: 'WAITING',
    phone: '',
  }),
  getters: {
    getIsActivated: state => state.isActivated,
    getProcessing: state => state.processing,
    getStatus: state => state.status,
    getPhone: state => state.phone,
  },
  actions: {
    async checkStatus(): Promise<any> {
      this.processing = true

      try {
        const { data } = await whatsappService.checkStatus()
        this.status = data.status
        this.phone = data.phone || ''
        this.isActivated = data.connected
      }
      catch (error) {
        console.error(error)
      }
      finally {
        this.processing = false
      }
    },

    async activate(): Promise<any> {
      this.processing = true
      try {
        const { data } = await whatsappService.activate()
        this.isActivated = data.connected
        // check status after 3 seconds
        setTimeout(async () => {
          await this.checkStatus()
        }, 3000)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.installed'),
        })
      }
      catch (error) {
        console.error(error)
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: error.message || i18n.global.t('operations.error'),
        })
      }
      finally {
        this.processing = false
      }
    },

    async deactivate(): Promise<any> {
      this.processing = true
      try {
        const { data } = await whatsappService.deactivate()
        this.isActivated = !data.disconnected
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.uninstalled'),
        })
      }
      catch (error) {
        console.error(error)
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: i18n.global.t('operations.error'),
        })
      }
      finally {
        this.processing = false
      }
    },

    async qrCode(): Promise<any> {
      try {
        return await whatsappService.qrCode()
      }
      catch (error) {
        console.error(error)
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: i18n.global.t('error_loading_qrcode'),
        })

        return null
      }
    },
  },
})
