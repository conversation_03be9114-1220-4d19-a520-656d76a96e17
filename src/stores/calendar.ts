import { defineStore } from 'pinia'
import calenderServices from '@/services/calendarServices'

export const useCalendar = defineStore({
  id: 'calendar',
  state: () => ({

  }),
  actions: {
    async fetchCalenderStaffBooking(staffUuid: string) {
      return calenderServices.fetchCalenderStaffBooking(staffUuid).then((res) => {
        return res
      })
    },
    async fetchStaffWorkingHoursCalendar(staffUuid: string) {
      return calenderServices.fetchStaffWorkingHoursCalendar(staffUuid).then((res) => {
        return res
      })
    },
    async fetchStaffsWorkingHourCalender(staffUuids: string) {
      return calenderServices.fetchStaffsWorkingHourCalender(staffUuids).then((res) => {
        return res
      })
    },
  },
})
