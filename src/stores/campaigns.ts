import { defineStore } from 'pinia'
import CampaignServices from '@/services/CampaignService'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

export const useCampaigns = defineStore({
  id: 'Campaigns',
  state: () => ({
    compaignsList: [],
  }),
  getters: {
    getCampaignsList: state => state.compaignsList,
  },
  actions: {
    async fetchCampaignsList() {
      const { data } = await CampaignServices.fetchCampaignsList()
      this.compaignsList = data
      return data
    },
    async getCampaignApps() {
      const { data } = await CampaignServices.getCampaignApps()
      return data
    },
    async createCampaign(payload: any) {
      const res = await CampaignServices.createCampaign(payload)
      await this.fetchCampaignsList()
      showNotification({
        title: i18n.global.t('Success'),
        type: 'success',
        message: i18n.global.t('operations.created'),
      })
      return res
    },
    async updateCampaign(payload: any) {
      const res = await CampaignServices.updateCampaign(payload)
      await this.fetchCampaignsList()
      showNotification({
        title: i18n.global.t('Success'),
        type: 'success',
        message: i18n.global.t('operations.updated'),
      })
      return res
    },
    async deleteCampaign(campaignId: string) {
      try {
        await CampaignServices.deleteCampaign(campaignId)
        this.compaignsList = this.compaignsList.filter(
          campaign => campaign.uuid !== campaignId,
        )
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
      }
      catch (error) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: error.message,
        })
        return Promise.reject(error)
      }
    },
    async changeCampaignStatus(campaignId: string) {
      try {
        const { data } = await CampaignServices.changeCampaignStatus(campaignId)
        this.compaignsList = this.compaignsList.map((campaign) => {
          if (campaign.uuid === campaignId)
            return data

          return campaign
        })
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
      }
      catch (error) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: error.message,
        })
        return Promise.reject(error)
      }
    },

    async checkCampaignCustomers(campaignIds) {
      try {
        const { data } = await CampaignServices.checkCampaignCustomers(campaignIds)

        return data
      }
      catch (error) {
        console.error('Error fetching count:', error)
        return {}
      }
    },
  },
})
