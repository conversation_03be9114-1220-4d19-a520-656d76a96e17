/* eslint-disable @typescript-eslint/no-unused-vars */
import { defineStore } from 'pinia'
import StaffIntervalsService from '../services/StaffIntervalsService'
export const useStaffIntervalsStore = defineStore({
  id: 'StaffIntervals',
  state: () => ({
    StaffIntervals: {},
  }),
  getters: {
    getStaffIntervals: state => state.StaffIntervals,
  },
  actions: {
    async createStaffIntervals(payload: { StaffIntervalId: string
      body: {
        day: string
        from: string
        to: string
      }
    }): Promise<void> {
      return StaffIntervalsService.createStaffIntervals(payload)
        .then(({ data }) => {
          this.StaffIntervals = data.data
        })
    },
    async fetchStaffIntervalsById(StaffIntervalsId: string): Promise<boolean> {
      return StaffIntervalsService.fetchStaffIntervalsById(StaffIntervalsId)
        .then(({ data }) => {
          this.StaffIntervals = data.data
          return true
        })
    },
    async updateStaffIntervals(payload: { StaffIntervalId: string
      body: {
        day: string
        from: string
        to: string
      } }): Promise<boolean> {
      return StaffIntervalsService.updateStaffIntervals(payload)
        .then(({ data }) => {
          return true
        })
    },
    async removeStaffIntervals(payload: { StaffIntervalId: string
      body: {
        day: string
        from: string
        to: string
      } }): Promise<boolean> {
      return StaffIntervalsService.removeStaffIntervals(payload)
        .then(({ data }) => {
          return true
        })
    },
  },
})
