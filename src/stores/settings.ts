import { defineStore } from 'pinia'
import { get } from '@vueuse/core'
import SettingsService from '@/services/SettingsService'
import { useAuthStore } from '@/stores/auth'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

export const useAccountSettingStore = defineStore({
  id: 'settings',
  state: () => ({
    logoChange: false,
    bannerChange: false,
    browserIcon: false,
    logo: null as null | String,
    banner: null as null | String,
    browser: null as null | String,
    processing: false,
    logoSaveBtn: false,
    bannerSaveBtn: false,
    favSaveBtn: false,
    accountSetting: {
      processing: false,
      styles: {
        primary: '#3B71FE',
        secondary: '#6DC8F9',
        background: '#FFFFFF',
      },
      banner: '',
      logo: '',
      favicon: '',
      sitename: '',
      subdomain: '',
      custom_domain: 0,
      socials: {
        facebook: '',
        twitter: '',
        youtube: '',
        instagram: '',
        tiktok: '',
        linkedin: '',
        snapchat: '',
        email: '',
        whatsapp: '',
        whatsno: '',
      },
      phone_country: '',
      whatsapp: '',
      whatsno: '',
      contact: '',
      description: '',
      maintenance_title: '',
      maintenance_body: '',
      status: '',
      full_domain: '',
    },
    customeInputs: [],
  }),
  getters: {
    getAccountSettings: state => state.accountSetting,
    getSaveBtn: state => state.logoSaveBtn,
  },
  actions: {
    async fetchSettings(page: string): Promise<any> {
      this.processing = true
      return SettingsService.fetchSettings(page).then((res) => {
        this.accountSetting = { ...res.data.data }
        this.processing = false
        return res.data.data
      })
    },
    async updateAccountSetting(page: string, payload: any): Promise<any> {
      return SettingsService.updateSettings(page, payload).then((res) => {
        this.accountSetting = { ...res.data.data }
        showNotification({
          title: 'Success',
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        this.fetchSettings('booking-page-settings')
        useAuthStore().getProfile()
      })
    },
    async updateNotifications(payload: object): Promise<any> {
      return SettingsService.updateNotifications(payload).then(
        ({ data }) => data,
      )
    },
    async checkDomain(payload: object): Promise<any> {
      this.processing = true
      return SettingsService.checkDomain(payload).finally(() => {
        this.processing = false
      })
    },
    async updateDomain(payload: object): Promise<any> {
      return SettingsService.updateDomain(payload).then((res) => {
        showNotification({
          title: 'Success',
          type: 'success',
          message: i18n.global.t('subdomain_updated'),
        })
        return res
      })
    },
    async fetchAdvancedSettings(groups: string[]): Promise<any> {
      return SettingsService.getAdvancedSettings(groups).then((res) => {
        return res.data
      })
    },
    async generalCustomeSetting(settingPage: string): Promise<any> {
      return SettingsService.notifcationSetting().then((res) => {
        this.customeInputs = res.data.settings
        const obj = {
          booking_page: 'booking_page',
          invoices: 'invoices',
          notifications: 'notifications',
          calendar: 'calendar',
          order: 'order',
        }
        return res.data.settings[`${obj[settingPage]}`]
      })
    },
    async updateGeneralCustomeSettings(
      payload,
      groupName: string,
    ): Promise<any> {
      return SettingsService.updateGeneralCustomeSettings(
        payload,
        groupName,
      ).then(() => {
        showNotification({
          title: 'Success',
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
      })
    },
    async getImagesGallery(): Promise<any> {
      return SettingsService.getImageGallery().then(data => data)
    },
    async postImageGallery(image: FormData): Promise<any> {
      return SettingsService.addImageGallery(image)
        .then((res) => {
          showNotification({
            title: 'Success',
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return res.data
        })
        .catch((err) => {
          showNotification({
            title: 'Error',
            type: 'error',
            message: err.message,
          })
        })
    },
    async deleteImageGallery(imageId: string): Promise<any> {
      return SettingsService.deleteImageGallery(imageId)
        .then((res) => {
          showNotification({
            title: 'Success',
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })
          return res.data
        })
        .catch((err) => {
          showNotification({
            title: 'Error',
            type: 'error',
            message: err.message,
          })
        })
    },
    async getPlans(): Promise<any> {
      return SettingsService.getAllPlans().then(res => res.data)
    },
    async subscriptionPlan(plan_id: string) {
      try {
        const res = await SettingsService.subscriptionPlan(plan_id)
        return res.data
      }
      catch (err) {
        showNotification({
          title: 'Error',
          type: 'error',
          message: err.message,
        })
      }
    },
    checkoutSubscription(plan_id: string, priceId: string, payload?: {}) {
      return SettingsService.checkoutSubscription(plan_id, priceId, payload)
        .then((res) => {
          return res.data
        })
        .catch((err) => {
          showNotification({
            title: 'Error',
            type: 'error',
            message: err.message,
          })
        })
    },
    RenewSubscription(subscription: string, payload?: {}) {
      return SettingsService.RenewSubscription(subscription, payload)
        .then((res) => {
          return res.data
        })
        .catch((err) => {
          showNotification({
            title: 'Error',
            type: 'error',
            message: err.message,
          })
        })
    },
    getCheckout(plan_id: string, priceId: string, couponCode: string) {
      return SettingsService.getCheckout(plan_id, priceId, couponCode)
        .then((res) => {
          return res.data
        })
        .catch((err) => {
          showNotification({
            title: 'Error',
            type: 'error',
            message: err.message,
          })
        })
    },
    async getBankForSubscription() {
      const res = await SettingsService.getBankForSubscription()
      return res.data
    },
    async getCurrentSubscription() {
      const response = await SettingsService.getCurrentSubscription()
      return response.data
    },
    cancelSubscription() {
      const response = SettingsService.cancelSubscription()
      return response
    },
    resumeCanceledSubscription() {
      const response = SettingsService.resumeCanceledSubscription()
      return response
    },
    extendTrail() {
      return SettingsService.extendTrail()
        .then((res) => {
          showNotification({
            title: 'Success',
            type: 'success',
            message: res.data.message,
          })
          setTimeout(() => {
            location.reload()
          }, 3000)
        })
        .catch((err) => {
          showNotification({
            title: 'Error',
            type: 'error',
            message: err.message,
          })
        })
    },
    async getInvoiceTemplates() {
      const response = await SettingsService.getInvoiceTemplates()
      return response.data
    },
    async setDefaultInvoiceTemplate(templateId: string) {
      const response = await SettingsService.setDefaultInvoiceTemplate(
        templateId,
      )
      showNotification({
        title: 'Success',
        type: 'success',
        message: i18n.global.t('operations.updated'),
      })
      return response.data
    },
  },
})
