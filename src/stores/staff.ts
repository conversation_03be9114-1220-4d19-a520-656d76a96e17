import { defineStore } from 'pinia'
import StaffService from '../services/StaffService'
import staffTimeOffs from '../services/staffTimeOffs'
import useNotifications from '@/composables/useNotifications'
import type { Paginate, Service, Staff, WorkingHour } from '@/types'
import i18n from '@/i18n'
import type { StaffTimeOff } from '@/types/staffTimeOffs'
const { showNotification } = useNotifications()

export const useStaffStore = defineStore({
  id: 'staff',
  state: () => ({
    staffList: [],
    showForm: true,
    staffEnable: false,
    workingHour: {
      name: '',
      uuid: '',
    },
    intervals: [],
    staff: {},
    calendarSelectedStaffUuid: '',
    staffWorkingHours: [] as WorkingHour[],
  }),

  actions: {

    async createStaff(payload: Omit<Staff, 'uuid'>): Promise<Staff> {
      return StaffService.createStaff(payload).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return data
      })
    },
    async fetchStaff(page: string | number = 1, filters): Promise<Paginate<Staff>> {
      return StaffService.fetchStaff(page, filters).then(({ data }) => {
        return data
      })
    },
    async fetchAllStaff(): Promise<Paginate<Staff>> {
      return StaffService.fetchAllStaff().then(({ data }) => {
        return data
      })
    },
    async fetchStaffServices(staffUuid: string, page: any) {
      return StaffService.fetchStaffServices(staffUuid, page).then(
        ({ data }) => data,
      )
    },
    async toggleStaffService(staff: Staff, service: Service) {
      return StaffService.toggleStaffService(staff, service).then(
        ({ data }) => data,
      )
    },
    async fetchStaffById(staffUuid: string): Promise<Staff> {
      return StaffService.fetchStaffById(staffUuid).then(({ data }) => {
        this.staff = data
        return data
      })
    },
    // async enableWorkingHours(staffId: string): Promise<any> {
    //   return StaffService.enableHours(staffId).then(() => {
    //     showNotification({
    //       title: i18n.global.t("Success"),
    //       type: "success",
    //       message: i18n.global.t("operations.addCustomize"),
    //     });
    //     this.staffEnable = true;
    //     this.fetchInterval(staffId);
    //   });
    // },
    async disableWorkingHours(staffId: string): Promise<void> {
      // return StaffService.disbaleHours(staffId).then(() => {
      //   showNotification({
      //     title: i18n.global.t("Success"),
      //     type: "success",
      //     message: i18n.global.t("operations.deleteCustomize"),
      //   });
      //   this.staffEnable = false;
      // });
    },
    async fetchInterval(staffId: string): Promise<any> {
      return StaffService.fetchHours(staffId).then((data) => {
        this.workingHour = data.data.data.workingHour
        this.intervals = data.data.data.intervals
        return data.data.data
      })
    },

    // async updateInterval(payload: object, staffId: string) {
    //   return StaffService.updateInterval(payload).then((data) => {
    //     showNotification({
    //       title: i18n.global.t("Success"),
    //       type: "success",
    //       message: i18n.global.t("operations.updated"),
    //     });
    //     this.fetchInterval(staffId);
    //     return data;
    //   });
    // },
    async fetchStaffBoundaries(staffUuid: string, page: any) {
      return StaffService.fetchStaffBoundaries(staffUuid, page).then(({ data }) => {
        return data
      })
    },

    async toggleStaffBoundaries(staffId: string, boundary_id: string) {
      return StaffService.toggleStaffBoundaries(staffId, boundary_id).then(({ data }) => {
        return data
      })
    },

    async updateStaff(
      staffUuid: string,
      body: Omit<Staff, 'uuid'>,
    ): Promise<Staff> {
      return StaffService.updateStaff(staffUuid, { body }).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return data
      })
    },
    async removeStaff(staffUuid: string): Promise<boolean> {
      return StaffService.removeStaff(staffUuid).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        return true
      })
    },
    async addTimeOffs(
      staffUuid: string,
      payload: { name: string; start_at: string; end_at: string },
    ) {
      return staffTimeOffs.addTimeOffs(staffUuid, payload).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return true
      })
    },
    async getTimeOffs(staffUuid: string) {
      return staffTimeOffs.getTimeOffs(staffUuid).then(({ data }) => {
        return data
      })
    },
    async deleteTimeOffs(staffUuid: string, timeOffsUuid: string) {
      return staffTimeOffs.deleteTimeOffs(staffUuid, timeOffsUuid).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
      })
    },
    async updateTimeOffs(staffUuid: string, timeOffs: StaffTimeOff) {
      return staffTimeOffs.updateTimeOffs(staffUuid, timeOffs).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
      })
    },
    async addBreaks(
      staffUuid: string,
      payload: { name: string; start_at: string; end_at: string },
    ) {
      return staffTimeOffs.addBreaks(staffUuid, payload).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return true
      })
    },
    async getBreaks(staffUuid: string) {
      return staffTimeOffs.getBreaks(staffUuid).then(({ data }) => {
        return data
      })
    },
    async deleteBreaks(staffUuid: string, timeOffsUuid: string) {
      return staffTimeOffs.deleteBreaks(staffUuid, timeOffsUuid).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
      })
    },
    async updateBreaks(staffUuid: string, timeOffs: StaffTimeOff) {
      return staffTimeOffs.updateBreaks(staffUuid, timeOffs).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
      })
    },
    async createUserStaff(staffId: string, payload: Object) {
      return StaffService.createUserStaff(staffId, payload).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.providerAdded'),
        })
        return data
      })
    },

  },
})
