import { defineStore } from 'pinia'
import type { ExpenseType } from '@/types'
import ExpensesService from '@/services/ExpensesService'

export const useExpenseTypesStore = defineStore('expenseTypes', {
  state: () => ({
    processing: false,
  }),

  actions: {
    async fetchExpenseTypes() {
      return ExpensesService.fetchExpenseTypes()
    },

    async createExpenseType(data: { title: string }) {
      return ExpensesService.createExpenseType(data)
    },

    async updateExpenseType(uuid: string, data: { title: string }) {
      return ExpensesService.updateExpenseType(uuid, data)
    },

    async deleteExpenseType(uuid: string) {
      return ExpensesService.deleteExpenseType(uuid)
    },
  },
})
