import { defineStore } from 'pinia'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
import type { AppNotification, Order, OrderStatus } from '@/types'
import transactionServices from '@/services/transactionServices'
import OrderService from '@/services/OrderService'
import router from '@/router'
const { showNotification } = useNotifications()
export const useOrder = defineStore({
  id: 'orders',
  state: () => ({
    orderDetails: null as Order,
    orderStatuses: {} as Record<string, OrderStatus>,
    processingStatuses: false,
    confirmedOrdersCount: 0,
    appNotifications: [] as AppNotification[],
  }),
  getters: {
    getOrderDetails: state => state.orderDetails,
    getOrderStatuses: state => state.orderStatuses,
    getConfirmedOrdersCount: state => state.confirmedOrdersCount,
  },
  actions: {
    fetchStatus() {
      if (Object.keys(this.orderStatuses).length)
        return
      this.processingStatuses = true
      return OrderService.fetchOrdersStatus()
        .then((res) => {
          this.orderStatuses = res.data
        })
        .finally(() => {
          this.processingStatuses = false
        })
    },
    async fetchSingleOrder(orderId: string) {
      // clear order details
      try {
        this.orderDetails = {} as Order
        const { data } = await OrderService.fetchOrderById(orderId)
        this.orderDetails = data
        return data
      }
      catch (err) {
        return Promise.reject(err)
      }
    },
    updateOrderStatus(payload: Record<string, any>) {
      return OrderService.updateOrderStatus(payload)
        .then((res) => {
          const { fetchUnreadModulesCount } = useAuthStore()
          fetchUnreadModulesCount()
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: res.data.message ?? i18n.global.t('operations.updated'),
          })
        })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return Promise.reject(err)
        })
    },
    async updateOrderNote(orderId: string, payload: any) {
      return OrderService.updateOrderNote(orderId, payload)
        .then((res) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
        })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return false
          // throw new Error(err)
        })
    },
    async getOrderNotifications(orderId: string) {
      return OrderService.getOrderNotifications(orderId).then(({ data }) => {
        return data.notifications
      })
    },
    async deleteOrder(orderId: string) {
      return OrderService.deleteOrder(orderId)
        .then((res) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted', { entity: 'Order' }),
          })
        })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return false
          // throw new Error(err)
        })
    },
    async updateTransaction(
      transUuid: string,
      formData: { note: string; amount: number },
    ) {
      return transactionServices
        .updateTransaction(transUuid, formData)
        .then(({ data }) => {
          this.fetchSingleOrder(this.getOrderDetails.id)
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return Promise.reject(err)
        })
    },
    async updateTransactionStatus(transUuid: string, status: string) {
      return transactionServices
        .updateTransStatus(transUuid, status)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
    },
    deleteTransaction(transUuid: string) {
      return transactionServices
        .deleteTransaction(transUuid)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })
          return data
        })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return Promise.reject(err)
        })
    },
    createOrderInvoice(orderId: string) {
      return OrderService.createOrderInvoice(orderId)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.created'),
          })
          return data
        })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return Promise.reject(err)
        })
    },
    setAppointment(orderId: string, payload: Record<string, any>) {
      return OrderService.setAppointment(orderId, payload)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return Promise.reject(err)
        })
    },
    async assignCustomerToOrder(orderId: string, customerId: string) {
      try {
        const { data } = await OrderService.assignCustomerToOrder(
          orderId,
          customerId,
        )
        await this.fetchSingleOrder(orderId)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
      }
    },
    async updateOrder(orderId: string, payload: Record<string, any>) {
      try {
        const { data } = await OrderService.updateOrder(orderId, payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message || err.data.message,
        })
        return Promise.reject(err)
      }
    },
    async createOrder(payload: Record<string, any>) {
      try {
        const { data } = await OrderService.createOrder(payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message || err.data.message,
        })
        return Promise.reject(err)
      }
    },
    getRefundItems(orderId: string) {
      return OrderService.getRefundItems(orderId).then(({ data }) => {
        return data
      })
    },
    refundOrder(orderId: string, payload: Record<string, any>) {
      return OrderService.refundOrder(orderId, payload)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.created'),
          })
          // router.push({ path: `/orders/${data.order}` });
          return data.order
        })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return Promise.reject(err)
        })
    },
    async updateOrderMetaData(orderId: string, payload: Record<string, any>) {
      return OrderService.updateOrderMetaData(orderId, payload).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
      })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return false
        // throw new Error(err)
        })
    },
    async updateOrderLocation(orderId: string, payload: Record<string, any>) {
      return OrderService.updateOrderLocation(orderId, payload).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
      })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return false
        // throw new Error(err)
        })
    },
    async addOrderItem(orderId: string, payload: any) {
      return OrderService.addOrderItem(orderId, payload)
        .then((res) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
        })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return Promise.reject()
          // throw new Error(err)
        })
    },
    async deleteOrderItem(orderId: string, item: string) {
      return OrderService.deleteOrderItem(orderId, item)
        .then((res) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted', { entity: 'Item' }),
          })
        })
        .catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err.message || err.data.message,
          })
          return false
          // throw new Error(err)
        })
    },
    async updateOrderDiscount(orderId: string, payload: {
      discount_type: string
      discount_amount: number
      apply_all: boolean
      items: string[]
    }) {
      return await OrderService.updateOrderDiscount(orderId, payload)
    },
    async removeOrderDiscount(orderId: string) {
      return await OrderService.removeOrderDiscount(orderId)
    },
  },
})
