import { defineStore, storeToRefs } from 'pinia'
import WelcomeService from '../services/OnBoardingService'
import { useAuthStore } from './auth'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()
const { getTeams } = storeToRefs(useAuthStore())
export const useOnBoardingStore = defineStore({
  id: 'OnBoarding',
  state: () => ({
    processing: false,
    industries: [],
    currentStep: 0,
    employeesOptions: [
      '1_4',
      '5_9',
      '10_49',
      '50_more',
    ],
  }),
  getters: {
    getIndustries: (state) => {
      const otherIndustry = state.industries.find((industry: any) => industry.slug == 'others')
      const industries = state.industries.filter((industry: any) => industry.slug != 'others' && industry.slug)
      return [...industries, otherIndustry]
    },
    getStaff: state => getTeams.value?.length ? getTeams.value : state.onBoardingData.staffs,
    getActivating: state => state.activating,
    getCurrentStep: state => state.currentStep,

  },
  actions: {

    async updateLang(payload: object): Promise<void> {
      await WelcomeService.updateLang(payload)
    },
    async activateAccount(): Promise<void> {
      this.activating = true
      return WelcomeService.activateAccount(this.onBoardingData).then(() => {
        showNotification({
          type: i18n.global.t('Success'),
          title: 'Success',
          message: i18n.global.t('operations.activated'),
        })
        localStorage.setItem('userAuth', JSON.stringify(true))
      }).finally(() => {
        this.activating = false
      })
    },
    async getOnboarding(): Promise<void> {
      this.processing = true
      return WelcomeService.getOnBoarding().then(({ data }) => {
        this.currentStep = data.current_step
        this.industries = data.industries
        if (data.current_step == 3)
          this.changeStepManually(2)
      }).finally(() => {
        this.processing = false
      })
    },
    async fetchIndusties(): Promise<any> {
      WelcomeService.fetchIndustries().then(({ data }) => {
        this.industries = data.data
      })
    },
    async updateOnBoarding(payload: object): Promise<void> {
      this.processing = true
      return WelcomeService.updateOnboarding(payload).then(({ data }) => {
        this.currentStep = data.current_step
        if (data.current_step == 3)
          this.changeStepManually(2)

        return data
      }).finally(() => {
        this.processing = false
      })
    },
    changeStepManually(step: number): void {
      this.currentStep = step
    },
    createAccount(payload: Object): Promise<any> {
      return WelcomeService.createAccount(payload)
    },
  },
})
