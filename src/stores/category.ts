/* eslint-disable @typescript-eslint/no-unused-vars */
import { defineStore } from 'pinia'
import CategoryService from '../services/CategoryService'
import i18n from '@/i18n'
import useNotifications from '@/composables/useNotifications'

const { showNotification } = useNotifications()

export const useCategoryStore = defineStore({
  id: 'Category',
  state: () => ({
    singleCategory: {},
    categoriesList: [],
  }),
  getters: {
    getCategory: state => state.Category,
    getCategories: state => state.categoriesList,
  },
  actions: {
    async createCategory(payload: {
      name: string
      type: string
    }): Promise<any> {
      return CategoryService.createCategory(payload)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.created'),
          })
          this.Category = data
          this.fetchCategories()
          return data
        })
    },
    async fetchCategories(type = 'service', team = null): Promise<any> {
      return CategoryService.fetchCategories(type, team).then(({ data }) => {
        this.categoriesList = data.data
        return data
      })
    },
    async fetchCategoryById(categoryId: string): Promise<boolean> {
      return CategoryService.fetchCategoryById(categoryId)
        .then(({ data }) => {
          return data
        })
    },
    async updateCategory(categoryId: string, payload: { [key: string]: string }): Promise<boolean> {
      return CategoryService.updateCategory(categoryId, payload)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          this.fetchCategories()
          return true
        })
    },
    async removeCategory(categoryId: string): Promise<boolean> {
      return CategoryService.removeCategory(categoryId)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })
          this.fetchCategories()
          return true
        }).catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: err?.message,
          })
          return Promise.reject(err)
        })
    },
  },
})
