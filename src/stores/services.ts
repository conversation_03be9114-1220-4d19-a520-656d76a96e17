import { defineStore } from 'pinia'
import ServicesService from '../services/ServicesService'
import useNotifications from '@/composables/useNotifications'
import type { Paginate, Service, Staff } from '@/types'
import i18n from '@/i18n'

const { showNotification } = useNotifications()
export const useServicesStore = defineStore({
  id: 'services',
  state: () => ({
    showForm: true,
  }),
  getters: {},
  actions: {
    async createService(payload: Omit<Service, 'uuid'>): Promise<Service> {
      return ServicesService.createService(payload)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.created'),
          })
          return data
        })
    },
    async fetchServices(page = 1, params): Promise<Paginate<Service>> {
      return ServicesService.fetchService(page, params).then(({ data }) => data)
    },
    async fetchAllServices(): Promise<Service> {
      return ServicesService.fetchAllService().then(({ data }) => data)
    },
    async fetchServicesStaff(service: Service) {
      return ServicesService.fetchServicesStaff(service).then(({ data }) => data)
    },
    async toggleServicesStaff(service: Service, staff: Staff) {
      return ServicesService.toggleServicesStaff(service, staff).then(({ data }) => data)
    },
    async updateServicesStaff(service: Service, staffData: Record<string, any>): Promise<Service> {
      return ServicesService.updateServicesStaff(service, staffData).then(({ data }) => data)
    },
    async fetchServiceById(serviceId: string): Promise<Service> {
      return ServicesService.fetchServiceById(serviceId)
        .then(({ data }) => {
          return data
        })
    },
    async removeService(serviceUuid: string): Promise<boolean> {
      return ServicesService.removeService(serviceUuid)
        .then(() => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })
          return true
        })
    },
    async updateService(serviceUuid: string, body: Omit<Service, 'uuid'>): Promise<Service> {
      return ServicesService.updateService(serviceUuid, { body })
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        }).catch((error) => {
          throw error
        })
    },
    async fetchPackages(page = 1, params): Promise<Paginate<Service>> {
      return ServicesService.fetchPacakges(page, params).then(({ data }) => data)
    },
    async fetchAllPackages(): Promise<Service> {
      return ServicesService.fetchAllPackages().then(({ data }) => data)
    },
    async fetchSoldPackages(params): Promise<Paginate<any>> {
      return ServicesService.fetchPacakgesSold(params).then(({ data }) => data)
    },
    async fetchSoldPackage(packageId: string): Promise<any> {
      return ServicesService.fetchPacakgeSold(packageId).then(({ data }) => data)
    },
    async fetchPackageById(packageId: string): Promise<Paginate<Service>> {
      return ServicesService.fetchPackageById(packageId)
        .then(({ data }) => {
          return data
        })
    },
    async createPackage(payload: Omit<Service, 'uuid'>): Promise<Service> {
      return ServicesService.createPackage(payload)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.created'),
          })
          return data
        })
    },
    async updatePackage(serviceUuid: string, body: Omit<Service, 'uuid'>): Promise<Service> {
      return ServicesService.updatePackage(serviceUuid, { body })
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
    },
    async removePakcage(packageUuid: string): Promise<boolean> {
      return ServicesService.removePakcage(packageUuid)
        .then(() => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })
          return true
        })
    },
    addImageToService(serviceUuid: string, image: FormData): Promise<Service> {
      return ServicesService.addImageToService(serviceUuid, image)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
    },
    addImagesToService(serviceUuid: string, images: FormData): Promise<Service> {
      return ServicesService.addImagesToService(serviceUuid, images)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
    },
    removeImageFromService(serviceUuid: string, imageId: string): Promise<Service> {
      return ServicesService.removeImageFromService(serviceUuid, imageId)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
    },
    removeServiceStaff(serviceUuid: string, staffId: string): Promise<Service> {
      return ServicesService.removeServiceStaff(serviceUuid, staffId)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
    },
    makeMainImage(serviceUuid: string, imageUuid: string): Promise<Service> {
      return ServicesService.makeMainImage(serviceUuid, imageUuid)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data.data
        })
    },
    addImageToPackage(PackageUuid: string, image: FormData): Promise<Service> {
      return ServicesService.addImageToPackage(PackageUuid, image)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
    },
    removeImageFromPackage(PackageUuid: string, imageId: string): Promise<Service> {
      return ServicesService.removeImageFromPackage(PackageUuid, imageId)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
    },
    makeMainImagePackage(PackageUuid: string, imageUuid: string): Promise<Service> {
      return ServicesService.makeMainImagePackage(PackageUuid, imageUuid)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data.data
        })
    },
    deleteServiceOfPackage(packageUuid: string, serviceUuid: string): Promise<Service> {
      return ServicesService.deleteServiceOfPackage(packageUuid, serviceUuid)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })

          return data.data
        })
    },
  },

})
