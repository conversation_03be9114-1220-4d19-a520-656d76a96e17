import { defineStore } from 'pinia'
import productsService from '../services/products'
import useNotifications from '@/composables/useNotifications'
import type { Paginate, Service } from '@/types'
import type { Products } from '@/types/products'

import i18n from '@/i18n'

const { showNotification } = useNotifications()
export const useProductStore = defineStore({
  id: 'products',
  actions: {
    async fetchProducts(page = 1, params: any): Promise<Paginate<Products>> {
      return productsService.fetchProducts(page, params).then(({ data }) => data)
    },
    async fetchProductById(productUuid: string): Promise<Products> {
      return productsService.fetchProductById(productUuid).then(({ data }) => data)
    },
    async updateProduct(productUuid: string, body: Omit<Products, 'uuid'>): Promise<Products> {
      return productsService.updateProduct(productUuid, { body })
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        }).catch((error: any) => {
          throw error
        })
    },
    async createProduct(body: Omit<Products, 'uuid'>): Promise<Products> {
      return productsService.createProduct(body)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.created'),
          })
          return data
        }).catch((error: any) => {
          throw error
        })
    },
    async deleteProduct(productUuid: string): Promise<boolean> {
      return productsService.deleteProduct(productUuid)
        .then(() => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })
          return true
        })
    },
    async addImageToProduct(productUuid: string, image: FormData): Promise<boolean> {
      return productsService.addImageToProduct(productUuid, image)
        .then((data) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        }).catch(() => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: i18n.global.t('operations.error'),
          })
          return false
        })
    },
    async removeImageFromProduct(productUuid: string, imageId: string): Promise<boolean> {
      return productsService.removeImageFromProduct(productUuid, imageId)
        .then((data) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        }).catch(() => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: i18n.global.t('operations.error'),
          })
          return false
        })
    },
    makeMainImage(productUuid: string, imageId: string): Promise<Service> {
      return productsService.makeMainImage(productUuid, imageId)
        .then((data) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        }).catch(() => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: i18n.global.t('operations.error'),
          })
          return false
        })
    },
  },
})
