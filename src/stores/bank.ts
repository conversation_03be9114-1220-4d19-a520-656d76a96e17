import { defineStore } from 'pinia'
import bankServices from '@/services/bankServices'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
import type { newAccount } from '@/types/newAccounts'
const { showNotification } = useNotifications()

export const useBank = defineStore({
  id: 'bank',
  state: () => ({
    onlinePayment: [],
    installmentProgram: [],
    bankTransfer: [],
    onArrival: [],
    switches: {
      electronic_payement: {
        isSwitch: false,
        val: false,
        paymentId: '',
      },
      paymentInstallments: {
        isSwitch: false,
        val: false,
        paymentId: '',
      },
      bankTransfers: {
        isSwitch: false,
        val: false,
        paymentId: '',
      },
      paymentaArrival: {
        isSwitch: false,
        val: false,
        paymentId: '',
      },
      allAcounts: [],
    },
    banks: [],
    accounts: [],

  }),
  getters: {
    getOnlinePayment: state => state.onlinePayment,
    getInstallmentProgram: state => state.installmentProgram,
    getBankTransfer: state => state.bankTransfer,
    getOnArrival: state => state.onArrival,
    getSwitches: state => state.switches,
    getAllAccounts: state => state.accounts,

  },
  actions: {
    getBankList(): Promise<any> {
      return bankServices.bankList().then((res) => {
        this.banks = res.data.banks
        this.accounts = res.data.accounts
        return res
      })
    },
    addBankAccountFun(payload: newAccount): Promise<any> {
      return bankServices.addBankAccountFun(payload).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t(`${res.data.message}`),
        })
        this.getBankList()
        return res
      })
    },
    getAllPayment(): Promise<any> {
      return bankServices.fetchPayements().then(({ data }) => {
        // const data = res.data
        for (const prop of data.data) {
          if (prop.slug === 'on_arrival') {
            this.onArrival = prop
            this.switches.paymentaArrival.val = prop.installable
            this.switches.paymentaArrival.isSwitch = prop.installed
            this.switches.paymentaArrival.paymentId = prop.id
          }
        }
        return data
      }).catch((err) => {
        return err
      })
    },
    async togglePaymentMethodOnArrival() {
      return bankServices.togglePaymentMethod().then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.isSwitched'),
        })
        return res.data
      })
    },
    async getPayOnArrivalStatus() {
      return bankServices.getPayOnArrival().then((res) => {
        return res.data
      })
    },
    updateAccounts(account: newAccount) {
      return bankServices.updateAccount(account).then((_) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        this.getBankList()
      })
    },
    deleteAccount(accountId: string) {
      return bankServices.deleteAccount(accountId).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t(`${res.data.message}`),
        })
        this.getBankList()
      })
    },
  },
})
