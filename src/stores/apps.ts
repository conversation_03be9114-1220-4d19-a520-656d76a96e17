import { defineStore } from 'pinia'
import AppService from '../services/AppService'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
import type { AppType } from '@/types'
const { showNotification } = useNotifications()

export const useAppsStore = defineStore({
  id: 'Apps',
  state: () => ({
    apps: [] as AppType[],
    loader: false,
    categories: [],
    categoriesLoading: false,
    appsLoading: false,
  }),
  getters: {
    getApps: state => state.apps,
    getCategory: state => state.categories,
  },
  actions: {
    setTrueLoader() {
      this.loader = true
    },
    setFalseLoader() {
      this.loader = false
    },
    async fetchApps(): Promise<{ data: AppType[] }> {
      this.appsLoading = true
      return AppService.fetchApps().then(({ data }) => {
        this.apps = data
        this.appsLoading = false
        return data
      })
    },
    async fetchAppCategories(): Promise<any> {
      this.categoriesLoading = true
      return AppService.fetchAppCategories().then(({ data }) => {
        this.categories = data
        this.categoriesLoading = false
        return data
      })
    },
    async fetchAppsByCategory(categoryId: string): Promise<{ data: AppType }> {
      this.appsLoading = true
      return AppService.fetchAppsByCategory(categoryId).then(({ data }) => {
        this.apps = data
        this.appsLoading = false
        return data
      })
    },
    async installApp(appId: string, payload: object, requireOauth = false): Promise<any> {
      return AppService.installApp(appId, payload).then(({ data }) => {
        const message = !requireOauth ? 'operations.installed' : 'operations.installRedirect'
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t(message),
        })
        this.apps = data
        return data
      }).catch((error) => {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: error.message,
        })
        return Promise.reject(error)
      })
    },
    async uninstallApp(appId: string): Promise<any> {
      return AppService.uninstallApp(appId).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.uninstalled'),
        })
        this.apps = data
        return data
      }).catch((error) => {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: error.message,
        })
        return Promise.reject(error)
      },
      )
    },

    async fetchGoogleCalendarSyncLink(staffId: string): Promise<any> {
      return AppService.fetchGoogleCalendarSyncLink(staffId).then(({ data }) => {
        return data
      })
    },

    async unSyncGoogleCalendar(staffId: string): Promise<any> {
      return AppService.unSyncGoogleCalendar(staffId).then(({ data }) => {
        return data
      })
    },

    async getAppConfigurations(appId: string) {
      return AppService.getAppConfigurations(appId).then(({ data }) => {
        return data
      })
    },

    async submitConfig(appId: string, payload: object): Promise<any> {
      return AppService.submitConfig(appId, payload).then(({ data }) => data)
    },
  },
})
