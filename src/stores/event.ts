/* eslint-disable @typescript-eslint/no-unused-vars */
import { defineStore, storeToRefs } from 'pinia'
import EventService from '../services/EventService'
import type { Booking, BookingForm, Paginate } from '@/types'
import type { Lookups, RecurringAppointment } from '@/types/lookup'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'

const { showNotification } = useNotifications()
const { getUserInfo } = storeToRefs(useAuthStore())
export const useEventStore = defineStore({
  id: 'event',
  state: () => ({
    event: {},
    lookup: {
      customers: [],
      staff: [],
      status: [],
      workdays: [],
      services: [],
    } as Lookups,
    transactionList: [],
  }),
  getters: {
    getEvent: state => state.event,
    getLookups: state => state.lookup,
    getServices: state => state.lookup.services,
  },
  actions: {
    fetchTimeSlots(date: string, providerId: Array<string>, serviceId: Array<string>, totalDuration?: Array<number>, quantity?: Array<number | string>, visibleDays?: number) {
      const timezone = getUserInfo.value?.tenant?.timezone || 'Asia/Riyadh'
      return EventService.fetchTimeSlots({ date, providersIds: providerId, servicesIds: serviceId, timezone, totalsDuration: totalDuration, quantities: quantity, visibleDays })
    },

    async createEvent(payload: Omit<BookingForm, 'uuid'>): Promise<Booking> {
      return EventService.createEvent(payload)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.created'),
          })
          return data
        })
    },

    async fetchLookupData() {
      return EventService.fetchLookupData().then(({ data }) => {
        this.lookup = data
        return data
      })
    },
    async fetchEvents(calender = false, page = 1, filters = {}): Promise<Paginate<Booking>> {
      return EventService.fetchEvents(calender, page, filters).then(({ data }) => data)
    },

    async fetchEventById(eventId: string): Promise<Booking> {
      return EventService.fetchEventById(eventId)
        .then(({ data }) => {
          this.transactionList = data
          return data
        })
    },
    async updateEvent(payload: Booking): Promise<any> {
      return EventService.updateEvent(payload)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
    },
    async moveEvent(payload: Booking): Promise<boolean> {
      return EventService.moveEvent(payload)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return true
        })
    },
    async removeEvent(eventId: string): Promise<boolean> {
      return EventService.removeEvent(eventId)
        .then((_) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })
          return true
        }).catch((err) => {
          showNotification({
            title: i18n.global.t('Error'),
            type: 'error',
            message: (err.errors),
          })
          return false
        })
    },
    async payBookingAmount(uuid: string, data: any): Promise<Boolean> {
      return EventService.payBookingAmount(uuid, data).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('pos.amount_was_paid_successfully'),
        })
        return true
      })
    },
    async getInovices(url: string): Promise<string> {
      return EventService.getInovices(url).then(({ data }) => {
        return data
      })
    },
    async cancelBooking(uuid: string, reasonId: string): Promise<string> {
      return EventService.cancelBooking(uuid, reasonId).then(({ data }) => {
        return data
      })
    },
    async updateBookingStatus(bookingUuid: string, status: 'confirmed' | 'completed' | 'no-show'): Promise<string> {
      return EventService.updateBookingStatus(bookingUuid, status).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return data
      })
    },
    createRecurredAppointment(bookingUuid: string, payload: RecurringAppointment): Promise<string> {
      return EventService.createRecurredAppointment(bookingUuid, payload).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return data
      }).catch(({ message }) => {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: message || i18n.global.t('operations.error'),
        })
        return Promise.reject(message)
      })
    },
  },
})
