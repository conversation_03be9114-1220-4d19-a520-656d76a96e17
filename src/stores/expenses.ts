import { defineStore } from 'pinia'
import useNotifications from '@/composables/useNotifications'
import ExpensesService from '@/services/ExpensesService'
import i18n from '@/i18n'
// import { Expense, ExpenseType } from "@/types/expenses";
// import type { Expense, ExpenseType } from "@/types";
const { showNotification } = useNotifications()

export const useExpenses = defineStore({
  id: 'Expenses',
  state: () => ({
    expenses: [],
    expenseTypes: [],
    processing: false,
  }),
  getters: {
    getExpenses: state => state.expenses,
    getExpenseTypes: state => state.expenseTypes,

  },
  actions: {
    async fetchExpenses(page = 1): Promise<any> {
      try {
        const expensesResponse = await ExpensesService.fetchExpenses(page)
        return expensesResponse.data
      }
      catch (err) {
        return err
      }
    },
    async fetchExpenseTypes(): Promise<any> {
      try {
        const expenseTypesResponse = await ExpensesService.fetchExpenseTypes()
        return expenseTypesResponse.data
      }
      catch (err) {
        return err
      }
    },
    async createExpense(payload: object): Promise<any> {
      try {
        const response = await ExpensesService.createExpense(payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return response.data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
        return Promise.reject(err)
      }
    },
    async updateExpense(expenseId: string, payload: object): Promise<any> {
      try {
        const response = await ExpensesService.updateExpense(expenseId, payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return response.data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
        return Promise.reject(err)
      }
    },
    async deleteExpense(expenseId: string): Promise<any> {
      try {
        const response = await ExpensesService.deleteExpense(expenseId)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        return response.data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
        return Promise.reject(err)
      }
    },
    async createTypeExpenses(payload: object): Promise<any> {
      try {
        const response = await ExpensesService.createExpenseType(payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return response.data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
        return Promise.reject(err)
      }
    },
    async updateTypeExpenses(expenseTypeId: string, payload: object): Promise<any> {
      try {
        const response = await ExpensesService.updateExpenseType(expenseTypeId, payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return response.data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
        return Promise.reject(err)
      }
    },
    async deleteTypeExpenses(expenseTypeId: string): Promise<any> {
      try {
        const response = await ExpensesService.deleteExpenseType(expenseTypeId)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        return response.data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
        return Promise.reject(err)
      }
    },
  },
})
