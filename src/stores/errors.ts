import { defineStore } from 'pinia'

interface Error {
  [key: string]: string[]

}
export const useServerErrors = defineStore({
  id: 'errors',
  state: () => ({
    serverErrors: {},
    cacheErrors: {},
  }),
  getters: {
    getServerErrors: (state: any): Error => state.serverErrors,
    getCacheErrors: (state: any): Error => state.cacheErrors,
  },
  actions: {
    setServerErrors(payload: Error): void {
      this.serverErrors = payload
      this.cacheErrors = payload
    },
    clearServerErrors(): void {
      this.serverErrors = {}
    },
    clearCacheErrors(): void {
      this.serverErrors = {}
      this.cacheErrors = {}
    },
    getErrorByKey(key: string): string[] {
      return this.getCacheErrors[key] || []
    },
  },
})
