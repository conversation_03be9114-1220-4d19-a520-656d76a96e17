import { defineStore } from 'pinia'
import { find, flatten, uniqueId } from 'lodash'
import posServices from '@/services/posServices'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

// @ts-expect-error
// @ts-expect-error
export const usePosStore = defineStore({
  id: 'pos',
  state: () => ({
    list: [] as any[],
    paymentMethodList: [],
    productList: [],
    serviceList: {},
    packageList: [],
    taxList: [],
    checkoutItems: [] as any[],
    checkoutData: {
      customer: {},
      team_id: '',
      payment_method: '',
    },
    invoiceDetail: {} as any,
    processing: false,
    createdOrderDetails: {} as any,
  }),
  getters: {
    sales: state => state.list,
    paymentMethods: state => state.paymentMethodList,
    products: state => state.productList,
    packages: state => state.packageList,
    services: state => state.serviceList,
    taxes: state => state.taxList,
    items: state => state.checkoutItems.map((item) => {
      return {
        ...item,
        staff: find(flatten(Object.values(state.serviceList)), o => o.id === item.id)?.staff,
      }
    }),
    invoice: state => state.invoiceDetail,
    formData: state => ({
      team_id: state.checkoutData?.team_id,
      customer_id: state.checkoutData?.customer?.uuid,
      booking_id: state.checkoutData?.booking_id,
      services: state.checkoutItems.filter(c => c.type === 'services').map(o => ({
        item_id: o.id,
        price: o.price,
        quantity: o.qty,
        discount: o.discount,
        staff_id: o.selectedStaff,
      })),
      products: state.checkoutItems.filter(c => c.type === 'products').map(o => ({
        item_id: o.id,
        price: o.price,
        quantity: o.qty,
        discount: o.discount,
        staff_id: o.selectedStaff,
      })),
      packages: state.checkoutItems.filter(c => c.type === 'packages').map(o => ({
        item_id: o.id,
        price: o.price,
        quantity: o.qty,
        discount: o.discount,
        staff_id: o.selectedStaff,
      })),
    }),
    allItems: state => [...Object.values(state.productList)?.flat(), ...Object.values(state.serviceList)?.flat(), ...Object.values(state.packageList)?.flat()],
  },
  actions: {
    async fetchSales(page = 1, search = ''): Promise<any> {
      return await posServices.fetchSales(page, search)
    },
    async fetchInvoice(uuid = ''): Promise<any> {
      return await posServices.fetchInvoice(uuid)
    },
    async fetchCheckout(uuid = ''): Promise<any> {
      return await posServices.fetchPosData(uuid).then(({ data }) => data)
    },
    async createPos(payload = ''): Promise<any> {
      return await posServices.createPos(payload).then(({ data }) => data)
    },
    async checkout(uuid = '', payload): Promise<any> {
      const formData = new FormData()
      for (const key in payload) {
        if (payload[key] !== null)
          formData.append(key, payload[key])
      }
      return posServices.checkout(uuid, formData).then(({ data }) => data)
    },
    async checkoutCalculate(payload = {}): Promise<any> {
      return await posServices.checkoutCalculate(payload).then(({ data }) => data)
    },
    async print(id = ''): Promise<any> {
      return await posServices.print(id)
    },
    async getInvoiceHtml(id = ''): Promise<any> {
      return await posServices.getInvoiceHtml(id)
    },
    async fetchPaymentLink(orderId: string): Promise<any> {
      return await posServices.fetchPaymentLink(orderId)
    },
    async previewInvoicePdf(id = ''): Promise<any> {
      return await posServices.previewInvoicePdf(id)
    },
    async downloadInvoicePdf(id = ''): Promise<any> {
      return await posServices.downloadInvoicePdf(id)
    },

    async refundInvoice(id = ''): Promise<any> {
      return await posServices.refundInvoice(id)
    },

    async sendWhatsAppInvoice(id: String): Promise<any> {
      try {
        const res = await posServices.sendWhatsAppInvoice(id)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.whatsappSent'),
        })
        return res
      }
      catch (e) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: e.message,
        })
      }
    },

    async sendInvoicePdf(id = ''): Promise<any> {
      const res = await posServices.sendInvoicePdf(id)
      showNotification({
        title: i18n.global.t('Success'),
        type: 'success',
        message: i18n.global.t('operations.emailSent'),
      })
      return res
    },
    addProduct(product, tab = '') {
      const foundIndex = this.checkoutItems.findIndex(item => item.id === product.id)
      if (foundIndex !== -1) {
        this.checkoutItems[foundIndex].qty += 1
      }
      else {
        const newItem = {
          ...product,
          uuid: uniqueId(),
          subTotal: product.price,
          type: tab,
          discount: 0,
          qty: 1,
        }
        if (tab === 'services') {
          const staffs = product.staff.filter(stf => stf.can_serve)
          newItem.selectedStaff = staffs.length ? staffs[0]?.id : ''
        }
        this.checkoutItems.push(newItem)
      }
    },
    deleteProduct(uuid = '') {
      const { clearCacheErrors } = useServerErrors()
      const foundIndex = this.checkoutItems.findIndex(item => item.uuid === uuid)
      if (foundIndex !== -1)
        this.checkoutItems = this.checkoutItems.filter(item => item.uuid !== uuid)
      clearCacheErrors()
    },
    clearPos() {
      const { clearCacheErrors } = useServerErrors()
      this.paymentMethodList = []
      this.productList = []
      this.serviceList = []
      this.packageList = []
      this.taxList = []
      this.checkoutItems = []
      this.createdOrderDetails = {}
      this.checkoutData = {
        customer: {},
        team_id: '',
        payment_method: '',
      }
      this.invoiceDetail = {}
      this.processing = false
      clearCacheErrors()
    },

  },
})
