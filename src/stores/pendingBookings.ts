import { defineStore } from 'pinia'
import pendingService from '@/services/PendingService'
import type { PendingBooking } from '@/types/pendingBooking'
// import i18n from '@/i18n'
// const { showNotification } = useNotifications()

export const usePendingStore = defineStore({
  id: 'pendingBookings',
  state: () => ({
    pendingBookings: [] as PendingBooking[],
    path: '',
    total: 0,
    per_page: 0,
    to: 0,
  }),
  getters: {
    getPendingBookings: state => state.pendingBookings,
    getPendingBookingsCount: state => state.total,
  },
  actions: {
    fetchPendingBookings() {
      return pendingService.fetchPendingBookings().then((res) => {
        this.pendingBookings = res.data.data
        this.path = res.data.meta.path
        this.per_page = res.data.meta.per_page
        this.to = res.data.meta.to
        this.total = res.data.meta.total
        return res.data
      })
    },
    updateBooking(uuid: string, action: 'confirm' | 'reject') {
      return pendingService.considerPendingBooking(uuid, action).then((data) => {
        return data
      })
    },
  },
})
