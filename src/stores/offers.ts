import { defineStore } from 'pinia'
import offersServices from '../services/Offers'
import type { Offers } from '../types/offers'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

export const useOffers = defineStore({
  id: 'offers',
  state: () => ({
    offers: [] as Offers[],
    processing: false,
  }),
  getters: {
    getOffers: state => state.offers,
  },
  actions: {
    getListOffers() {
      this.processing = true
      return offersServices.getListOffers().then(({ data }) => {
        this.offers = data.data
        return data
      }).finally(() => {
        this.processing = false
      })
    },
    createOffers(offers: Offers) {
      return offersServices.createOffers(offers).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        this.offers.push(data.data)
        return data
      })
    },
    updateOffers(offers: Offers, uuid: string) {
      const payload = { ...offers }
      return offersServices.updateOffers(payload, uuid).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        this.offers = this.offers.map((item) => {
          if (item.uuid === payload.uuid)
            return payload

          return item
        })
        return res
      })
    },
    deleteOffers(offersUuid: string) {
      return offersServices.deleteOffers(offersUuid).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        this.getListOffers()
        return res
      })
    },
  },
})
