import { defineStore } from 'pinia'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'

const { showNotification } = useNotifications()

export const useFeaturesStore = defineStore({
  id: 'Features',
  state: () => ({
    features: [] as string[],
    loader: false,
    featuresLoading: false,

  }),
  getters: {
    getFeatures: state => state.features as string[],
  },
  actions: {

  },
})
