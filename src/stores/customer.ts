import { defineStore } from 'pinia'
import CustomerService from '../services/CustomerService'
import useNotifications from '@/composables/useNotifications'
import type { Attachment, Customer, Notes, Paginate } from '@/types'
import i18n from '@/i18n'

const { showNotification } = useNotifications()
export const useCustomerStore = defineStore({
  id: 'customer',
  state: () => ({}),
  getters: {},
  actions: {
    async createCustomer(payload: Omit<Customer, 'uuid'>): Promise<Customer> {
      return CustomerService.createCustomer(payload)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.created'),
          })
          return data
        })
    },
    async fetchCustomer(page = 1, params: { [key: string]: string } = {}): Promise<Paginate<Customer>> {
      return CustomerService.fetchCustomer(page, params).then(({ data }) => data)
    },
    async fetchCustomerByNameOrPhone(page = 1, search = ''): Promise<Paginate<Customer>> {
      return CustomerService.fetchCustomerByNameOrPhone(page, search).then(({ data }) => data)
    },

    async fetchCustomerById(customerUuid: string): Promise<Customer> {
      return CustomerService.fetchCustomerById(customerUuid)
        .then(({ data }) => {
          return data
        })
    },
    async updateCustomer(customerUuid: string, body: Omit<Customer, 'uuid'>): Promise<Customer> {
      return CustomerService.updateCustomer(customerUuid, { body })
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          this.fetchCustomer()
          return data
        })
    },
    async removeCustomer(customerUuid: string): Promise<boolean> {
      return CustomerService.removeCustomer(customerUuid)
        .then(() => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })
          return true
        })
    },
    async fetchCustomerBookings(customerUuid: string): Promise<Customer> {
      return CustomerService.fetchCustomerBookings(customerUuid).then(({ data }) => data)
    },
    async fetchCustomerInvoices(customerUuid: string): Promise<any> {
      return CustomerService.fetchCustomerInvoices(customerUuid).then(({ data }) => data)
    },
    async getCustomerNotes(customerUuid: string): Promise<Notes> {
      return CustomerService.getCustomerNotes(customerUuid).then(({ data }) => data)
    },
    async createNotes(customerUuid: string, notes: Notes) {
      return CustomerService.createNotes(customerUuid, notes).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
      })
    },
    async deleteNotes(noteUuid: string, customerUuid: string) {
      return CustomerService.deleteNotes(noteUuid, customerUuid).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
      })
    },
    async getCustomerAttachment(customerUuid: string): Promise<Notes> {
      return CustomerService.getCustomerAttachment(customerUuid).then(({ data }) => data)
    },
    async createAttachment(customerUuid: string, attachment: Attachment) {
      return CustomerService.createAttachment(customerUuid, attachment).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
      })
    },
    async deleteAttachment(AttachUuid: string) {
      return CustomerService.deleteAttachment(AttachUuid).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
      })
    },
    async blockCustomer(customerUuid: string) {
      return CustomerService.blockCustomer(customerUuid).then(() => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
      })
    },
  },

})
