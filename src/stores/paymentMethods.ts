import { defineStore } from 'pinia'
import PaymentMethodsService from '@/services/PaymentMethodsService'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()
export const usePaymentMethodsStore = defineStore({
  id: 'PaymentMethods',
  state: () => ({}),
  getters: {},
  actions: {
    async getPaymentMethodsList(): Promise<any> {
      return PaymentMethodsService.getPaymentMethodsList().then(({ data }) => data)
    },
    async getPaymentMethods(): Promise<any> {
      return PaymentMethodsService.getPaymentMethods().then(({ data }) => data)
    },
    async getPaymentMethod(uuid: string): Promise<any> {
      return PaymentMethodsService.getPaymentMethod(uuid).then(({ data }) => data)
    },

    async createPaymentMethods(payload: any): Promise<any> {
      return PaymentMethodsService.createPaymentMethods(payload).then(({ data }) => {
        showNotification({
          title: 'Success',
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return data
      })
    },

    async updatePaymentMethods(payload: any): Promise<any> {
      return PaymentMethodsService.updatePaymentMethods(payload).then(({ data }) => {
        showNotification({
          title: 'Success',
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return data
      })
    },
    async deletePaymentMethods(uuid: string): Promise<any> {
      return PaymentMethodsService.deletePaymentMethods(uuid).then(({ data }) => {
        showNotification({
          title: 'Success',
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        return data
      })
    },
  },
})
