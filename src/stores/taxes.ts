import { defineStore } from 'pinia'
import taxesServices from '../services/taxesServices'
import type { Taxes } from '../types/taxes'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

export const useTaxes = defineStore({
  id: 'taxes',
  state: () => ({
    taxes: [] as Taxes[],
  }),
  getters: {
    getTaxes: state => state.taxes,
  },
  actions: {
    async getAllTaxes() {
      return taxesServices.getAllTaxes().then((res) => {
        this.taxes = res.data.data
        return res
      })
    },
    async createTaxes(taxes: Taxes) {
      return taxesServices.createTaxes(taxes).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        this.getAllTaxes()
        return res
      })
    },
    async updateTaxes(taxes: Taxes, taxesUuid: string) {
      return taxesServices.updateTaxes(taxes, taxesUuid).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        this.getAllTaxes()
        return res
      })
    },
    async deleteTaxes(taxesUuid: string) {
      return taxesServices.deleteTaxes(taxesUuid).then((res) => {
        this.getAllTaxes()
        return res
      })
    },
  },
})
