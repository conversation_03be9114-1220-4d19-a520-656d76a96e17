import { defineStore } from 'pinia'
import MetaDataService from '../services/MetaDataService'
import i18n from '@/i18n'
import useNotifications from '@/composables/useNotifications'
import type { MetaData } from '@/types/metaData'

const { showNotification } = useNotifications()

export const useMetaDataStore = defineStore({
  id: 'MetaData',

  state: () => ({
    metaData: {} as { [key: string]: MetaData[] },
    processing: false,
    teamMetaData: {} as { [key: string]: MetaData[] },
  }),

  getters: {
    getMetaData: state => state.metaData,
    getBookingMetaData: state => state.metaData.bookings,
    getOrderMetaData: state => state.metaData.orders,
    getTeamMetaData: state => state.teamMetaData,
  },

  actions: {
    async fetchMetaData(scoped: Boolean = true): Promise<any> {
      this.processing = true
      return MetaDataService.fetchMetaData(scoped).then(({ data }) => {
        this.metaData = data
        return data
      }).finally(() => {
        this.processing = false
      })
    },
    async fetchTeamMetaData(teamUuid: string): Promise<any> {
      this.processing = true
      return MetaDataService.fetchTeamMetaData(teamUuid).then(({ data }) => {
        this.teamMetaData = data
        return data
      }).finally(() => {
        this.processing = false
      })
    },
    async upsertMetaData(payload: MetaData): Promise<void> {
      return MetaDataService.upsertMetaData(payload).then(({ data }) => {
        const isCreate = !payload.id

        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t(
            isCreate ? 'operations.created' : 'operations.updated',
          ),
        })

        const model = data.model

        if (isCreate) {
          this.metaData[model] = [...this.metaData[model], data]
        }
        else {
          this.metaData[model] = [...this.metaData[model]].map(
            (item: { id: string }) => {
              if (item.id === data.id)
                item = data

              return item
            },
          )
        }
      })
    },
    async deleteMetaData(customfiledUuid: string): Promise<boolean> {
      return MetaDataService.deleteMetaData(customfiledUuid).then(
        ({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })
          this.fetchMetaData()
          return true
        },
      )
    },
  },
})
