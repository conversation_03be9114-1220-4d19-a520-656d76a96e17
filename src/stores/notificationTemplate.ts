import { define<PERSON><PERSON> } from 'pinia'
import type { ActionType, AppNotification, NotficationAction, NotificationTemplates, SendNotification, Template } from '@/types'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
import Notifications from '@/services/Notifications'
const { showNotification } = useNotifications()
export const useNotificationTemplate = defineStore({
  id: 'notificationTemplate',
  state: () => ({
    notificationTemplates: {} as NotificationTemplates,
    sendNotificationsList: [] as SendNotification[],
    processing: false,
    appNotifications: [] as AppNotification[],
    processingNotifications: false,
  }),
  getters: {
    getNotificationTemplates: state => state.notificationTemplates,
    getAppNotifications: state => state.appNotifications,
  },
  actions: {
    fetchNotificationTemplates() {
      this.processing = true
      return Notifications.getNotificationTemplates().then(({ data }: { data: NotificationTemplates }) => {
        this.notificationTemplates = data
        return data
      }).finally(() => {
        this.processing = false
      })
    },
    async deleteNotificationTemplate(uuid: string, currentTab: ActionType) {
      return Notifications.deleteNotificationTemplate(uuid).then((res) => {
        this.removeTemplate(uuid, currentTab)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        return res
      })
    },
    async createNotificationTemplate(payload: Template) {
      return Notifications.createNotificationTemplate(payload).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        this.fetchNotificationTemplates()
        return data
      })
    },
    async updateNotificationTemplate(uuid: string, payload: Template) {
      return Notifications.updateNotificationTemplate(uuid, payload).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        this.fetchNotificationTemplates()
        return res
      })
    },
    async toggleTemplateStatus(uuid: string, isActive: boolean) {
      this.processing = true
      try {
        await Notifications.toggleTemplateStatus(uuid, isActive)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return await this.fetchNotificationTemplates()
      }
      catch (error) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: i18n.global.t('operations.failed'),
        })
        throw error
      }
      finally {
        this.processing = false
      }
    },
    removeTemplate(uuid: string, currentTab: ActionType) {
      this.notificationTemplates.actions[currentTab].forEach((action: NotficationAction) => {
        const index = action.templates.findIndex(
          (template: Template) => template.template_id === uuid,
        )
        if (index > -1)
          action.templates.splice(index, 1)
      })
    },
    async fetchAppNotifications() {
      this.processingNotifications = true
      try {
        const { data } = await Notifications.fetchAppNotifications()
        this.appNotifications = data.data
        return data.data
      }
      finally {
        this.processingNotifications = false
      }
    },
    async readNotification(uuid: string) {
      this.processingNotifications = true
      try {
        await Notifications.readNotification(uuid)
        this.appNotifications = this.appNotifications.filter(notification => notification.uuid !== uuid)
      }
      finally {
        this.processingNotifications = false
      }
    },
  },
})
