import { defineStore } from 'pinia'
import useNotifications from '@/composables/useNotifications'
import WorkingHourService from '@/services/WorkingHourService'
import i18n from '@/i18n'
import type { IntervalHour, WorkingHour } from '@/types'
const { showNotification } = useNotifications()
const DefaultWorkingDays = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat']
export const useWorkingHour = defineStore({
  id: 'Workinghour',
  state: () => ({
    workingHours: [] as WorkingHour[],

  }),
  getters: {
    getWorkingHours: state => state.workingHours,
  },
  actions: {
   setManualWorkingHours(workingHours : DayInterval[]){
    this.workingHours = workingHours;
   },

    getWorkingHourWithDefaultIntervals(intervals : IntervalHour[]): unknown[] {
      let newIntervals = [];
       DefaultWorkingDays.forEach((day) => {
        let dayIsExist = intervals.find((interval) => interval.day == day)?.day;
        if (dayIsExist) {
          newIntervals.push({day : day, intervals : intervals.filter((interval) => interval.day == day)})
        } else {
          newIntervals.push({ day,  intervals: []})
        }
      });
      return newIntervals;
    },
    async fetchWorkingHours(model: string, staffId: string): Promise<any> {
      try {
        const workingHoursResponse = await WorkingHourService.fetchWorkingHours(model, staffId)
        let workingHours = workingHoursResponse.data.data as WorkingHour[]
        // loop over working hours and check if intervals is empty
        workingHours = workingHours.map((workingHour: WorkingHour) => {
          workingHour.intervals = this.getWorkingHourWithDefaultIntervals(workingHour.intervals)
          return workingHour
        })
        this.workingHours = workingHours
        return this.workingHours
      }
      catch (err) {
        return err
      }
    },

    async fetchActiveWorkingHour(model: string, staffId: string): Promise<any> {
      try {
        const response = await WorkingHourService.fetchActiveWorkingHour(model, staffId)
        this.workingHours = this.workingHours.map((workingHour: WorkingHour) => {
          if (workingHour.uuid == response.data.data.uuid)
            workingHour.active = true
          else
            workingHour.active = false

          return workingHour
        },
        )
        return response.data.data
      }
      catch (err) {
        return err
      }
    },
    async createWorkingHour(model: string, staffId: string, payload: object): Promise<any> {
      try {
        const response = await WorkingHourService.createWorkingHour(model, staffId, payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return response.data.data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
        return Promise.reject(err)
      }
    },
    async updateWorkingHour(model: string, staffId: string, workingHourId: string, payload: object): Promise<any> {
      try {
        const response = await WorkingHourService.updateWorkingHour(model, staffId, workingHourId, payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return response.data.data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
        return Promise.reject(err)
      }
    },
    async deleteWorkingHour(model: string, staffId: string, workingHourId: string): Promise<any> {
      try {
        const response = await WorkingHourService.deleteWorkingHour(model, staffId, workingHourId)
        this.workingHours = this.workingHours.filter((workingHour: WorkingHour) => workingHour.uuid != workingHourId)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        return response.data.data
      }
      catch (err) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
        return Promise.reject(err)
      }
    },
  },
})
