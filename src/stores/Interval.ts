import { defineStore } from 'pinia'
import IntervalService from '../services/IntervalService'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

export const useIntervalsStore = defineStore({
  id: 'intervals',
  actions: {
    async createInterval(workingHourId: string, payload: object): Promise<any> {
      try {
        const response = await IntervalService.createInterval(workingHourId, payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return response.data.data
      }
      catch (err) {
        return Promise.reject(err)
      }
    },
    async updateInterval(workingHourId: string, intervalId: string, payload: object): Promise<any> {
      try {
        const response = await IntervalService.updateInterval(workingHourId, intervalId, payload)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return response.data.data
      }
      catch (err) {
        return Promise.reject(err)
      }
    },
    async deleteInterval(workingHourId: string, intervalId: string): Promise<any> {
      try {
        const response = await IntervalService.deleteInterval(workingHourId, intervalId)
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        return response.data.data
      }
      catch (err) {
        return Promise.reject(err)
      }
    },
  },
})
