import { defineStore } from 'pinia'
import rolesServices from '../services/rolesServices'
import type { Roles } from '../types/roles'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

export const useRoles = defineStore({
  id: 'roles',
  state: () => ({
    roles: [] as Roles[],
  }),
  getters: {
    getRoles: state => state.roles,
  },
  actions: {
    async getAllRoles() {
      return rolesServices.getAllRoles().then((res) => {
        this.roles = res.data.data
        return res
      })
    },
    async getAllPermission() {
      return rolesServices.getAllPermission().then((res) => {
        return res
      })
    },
    async createRoles(roles: Roles) {
      return rolesServices.createRoles(roles).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        this.getAllRoles()
        return res
      })
    },
    async updateRoles(roles: Roles, rolesUuid: string) {
      return rolesServices.updateRoles(roles, rolesUuid).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        this.getAllRoles()
        return res
      })
    },
    async deleteRoles(rolesUuid: string) {
      return rolesServices.deleteRoles(rolesUuid).then((res) => {
        this.getAllRoles()

        return res
      })
    },
  },
})
