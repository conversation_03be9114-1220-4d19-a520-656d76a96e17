import { defineStore } from 'pinia'
import labelsServices from '../services/labelsServices'
import type { Labels } from '../types/labels'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

export const useLabels = defineStore({
  id: 'labels',
  state: () => ({
    labels: [] as Labels[],
  }),
  getters: {
    getLabels: state => state.labels,
  },
  actions: {
    async getAllLabels() {
      return labelsServices.getAllLabels().then((res) => {
        this.labels = res.data.data
        return res
      })
    },
    async createLabels(labels: Labels) {
      return labelsServices.createLabels(labels).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        this.getAllLabels()
        return res
      })
    },
    async updateLabels(labels: Labels, labelsUuid: string) {
      return labelsServices.updateLabels(labels, labelsUuid).then((res) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        this.getAllLabels()
        return res
      })
    },
    async deleteLabels(labelsUuid: string) {
      return labelsServices.deleteLabels(labelsUuid).then((res) => {
        this.getAllLabels()

        return res
      })
    },
  },
})
