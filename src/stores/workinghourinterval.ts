import { defineStore } from 'pinia'
import WorkingHourIntervalService from '../services/WorkingHourIntervalsService'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

export const useWorkingHourIntervalStore = defineStore({
  id: 'WorkingHourInterval',
  state: () => ({
    WorkingHourInterval: {},
  }),
  getters: {
    getWorkingHourIntervals: state => state.WorkingHourInterval,
  },
  actions: {
    async createWorkingHourInterval(payload: { workingHourIntervalId: string
      body: {
        day: string
        from: string
        to: string
      } }): Promise<object> {
      return WorkingHourIntervalService.createWorkingHourInterval(payload)
        .then(({ data }) => {
          this.WorkingHourInterval = data.data
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.created'),
          })
          return data.data
        })
    },
    async deleteInterval(payload): Promise<boolean> {
      return WorkingHourIntervalService.deleteInterval(payload)
        .then(() => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.deleted'),
          })
          this.fetchWorkingHourIntervalById(payload.workingHourId)
          return true
        })
    },
    async fetchWorkingHourIntervalById(workingHourIntervalId: string): Promise<boolean> {
      return WorkingHourIntervalService.fetchWorkingHourIntervalById(workingHourIntervalId)
        .then(({ data }) => {
          this.WorkingHourInterval = data.data
          return true
        })
    },
    async updateWorkingHourInterval(payload: {
      workingHourIntervalId: string
      intervalId: string
      body: {
        day: string
        from: string
        to: string
        active: boolean
      } }): Promise<object> {
      return WorkingHourIntervalService.updateWorkingHourInterval(payload)
        .then(({ data }) => {
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return data
        })
    },
  },
})
