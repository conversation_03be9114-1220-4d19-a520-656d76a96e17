import { defineStore } from 'pinia'
import TeamService from '../services/TeamsService'
import useNotifications from '@/composables/useNotifications'
import { useAuthStore } from '@/stores/auth'
import i18n from '@/i18n'
import type { Branch } from '@/types'

const { showNotification } = useNotifications()
const { userInfo } = useAuthStore()
export const useTeamStore = defineStore({
  id: 'Team',
  state: () => ({
    Team: {} as Branch,
    Teams: [],
    processing: false,
    workingHour: {
      name: '',
      uuid: '',
    },
    intervals: [],

  }),
  getters: {
    getTeam: state => state.Team,
    getTeams: state => state.Teams.filter((obj, index) => state.Teams.findIndex(element => element.uuid === obj.uuid) === index),
    getEnabledTeams: state => state.Teams.filter(team => team.enabled).sort((a, b) => Number(b.current) - Number(a.current)),
    getCurrentTeam: state => state.Teams.find(team => team.current),
  },
  actions: {
    async fetchInfo(): Promise<any> {
      this.processing = true

      return TeamService.fetchInfo().then(({ data }) => {
        this.processing = false

        return data
      })
    },
    async fetchTeams(): Promise<any> {
      this.processing = true
      return TeamService.fetchTeams().then(({ data }) => {
        this.Teams = data.data
        this.processing = false
        return data
      })
    },
    async createTeam(payload: {
      name: string
      personal_team: boolean
      industry_id: string
      address: string
      latitude: string
      longitude: string
    }): Promise<object> {
      return TeamService.createTeam(payload)
        .then(({ data }) => {
          this.Team = data.data
          this.Teams = [...this.Teams, data.data]
          userInfo.teams = [...userInfo.teams, data.data]
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.created'),
          })
          return data
        })
    },
    async fetchTeamById(teamId: string, force = false): Promise<Branch> {
      if (!force && this.Team && this.Team.uuid === teamId) {
        return this.Team;
      }
      const { data } = await TeamService.fetchTeamById(teamId)
      this.Team = { ...data }
      return this.Team
    },
    async update(payload: { teamId: string
      body: {
        name: string
        personal_team: string
        industry_id: string
        status: boolean
        timezone: string
        longitude: string
        latitude: string

      } }): Promise<any> {
      const authStore = useAuthStore()

      return TeamService.updateTeam(payload)
        .then(({ data }) => {
          this.Teams = [...this.Teams].map((team) => {
            return team.uuid === data.data.uuid ? data.data : team
          })
          authStore.userInfo.teams = this.Teams
          showNotification({
            title: i18n.global.t('Success'),
            type: 'success',
            message: i18n.global.t('operations.updated'),
          })
          return true
        })
    },
    removeTeam(teamId: string): Promise<any> {
      return TeamService.removeTeam(teamId).then((data) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        return data
      })
    },
    async fetchInterval(staffId: string): Promise<any> {
      return TeamService.fetchHours(staffId).then((data) => {
        this.workingHour = data.data.data.workingHour
        this.intervals = data.data.data.intervals
        return data.data.data
      })
    },
    async updateInterval(payload: object, staffId: string) {
      return TeamService.updateInterval(payload).then((data) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        this.fetchInterval(staffId)
        return data
      })
    },
    deleteIntervals(intervalId: string, modelId: string): Promise<any> {
      return TeamService.deleteIntervals(intervalId, modelId).then((data) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deleted'),
        })
        return data
      })
    },
    createUser(payload: Object): Promise<any> {
      return TeamService.createUser(payload).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return data
      }).catch(({ message }) => {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: message || i18n.global.t('operations.error'),
        })
      })
    },
    updateUser(userId: string, payload: Object): Promise<any> {
      return TeamService.updateUser(userId, payload).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.created'),
        })
        return data
      }).catch(({ message }) => {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: message || i18n.global.t('operations.error'),
        })
      })
    },
    deactiveUser(userId: string): Promise<any> {
      return TeamService.deactivateUser(userId).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.deactivated'),
        })
        return data
      }).catch(({ message }) => {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: message || i18n.global.t('operations.error'),
        })
      })
    },
    changeUserToProvider(userId: string): Promise<any> {
      return TeamService.changeUserToProvider(userId).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return data
      }).catch(({ message }) => {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: message || i18n.global.t('operations.error'),
        })
      })
    },
    changeProviderToUser(userId: string): Promise<any> {
      return TeamService.changeProviderToUser(userId).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return data
      }).catch(({ message }) => {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: message || i18n.global.t('operations.error'),
        })
      })
    },
  },
})
