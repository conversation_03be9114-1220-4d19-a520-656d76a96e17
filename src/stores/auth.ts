import { defineStore } from 'pinia'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import AuthService from '../services/AuthService'
import UserCountry from '../services/UserCountryService'
import { useRoutesStore } from './routes'
import OrderService from '@/services/OrderService'
import { usePluginsStore } from '@/stores/plugins'
import { useFeaturesStore } from '@/stores/features'
import { useNotificationTemplate } from '@/stores/notificationTemplate'
import { AUTH_TOKEN } from '@/constants'
import type {
  AppNotification,
  ForgotPasswordPayload,
  LoginPayload,
  ProfilePayload, RegisterUserPayload,
  TenantSubscription,
  UnreadModules,
  User,
  verifyOtpPayload,
} from '@/types'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

interface UserState {
  userInfo: User
  onTrial: boolean
  showBannerToRenewSubscription: boolean
  userCountry: string
  isAdmin: boolean
  subscriptionAndTrialEnded: boolean
  subscriptionStatus: 'active' | 'past_due' | ''
  forgetLink: Record<string, any>
  unReadModules: UnreadModules
}

interface OnboardingData {
  systemType: 'store' | 'internal' | null
  selectedFeatures: string[]
  selectedEmployees: string | null
  selectedRole: string | null
  businessName: string
  storeUrl: string
  selectedIndustries: string[]
  industryId: string
  serviceType: string
  services: Array<{ name: string; price: string }>
  providers: Array<{ name: string }>
}

export const useAuthStore = defineStore({
  id: 'auth',
  state: (): UserState => ({
    userInfo: {
      email: null,
      name: null,
      profile_photo: null,
      uuid: null,
      tenant: {
        booking_page: {},
        currency: '',
        plugins: [],
        whatsapp_enabled: false,
        whatsapp_status: 'disconnected',
        subscription: {
          status: '',
          show_renew_message: false,
        },
        trail_ends_at: null,
      },
      phone: null,
      phone_country: null,
      lang: null,
      role: [],
      created_at: '',
      teams: [],
      profile_completed: false,
      email_verified: false,
      can_imp: false,
      is_imp: false,
      hash: '',
      permissions: [],
    },
    onTrial: false,
    showBannerToRenewSubscription: false,
    userCountry: '',
    isAdmin: false,
    subscriptionAndTrialEnded: false,
    subscriptionStatus: '',
    forgetLink: {},
    unReadModules: {
      notifications: 0,
      orders: 0,
      transactions: 0,
      comments: 0,
      reviews: 0,
    } as Record<string, number>,
    onboardingData: {
      systemType: null,
      selectedFeatures: [],
      selectedEmployees: null,
      selectedRole: null,
      businessName: '',
      storeUrl: '',
      selectedIndustries: [],
      industryId: '',
      serviceType: '',
      services: [],
      providers: [],
    } as OnboardingData,
  }),
  getters: {
    isUserLoggedIn: state => Boolean(state.userInfo?.uuid),
    getUserInfo: state => reactive(state.userInfo),
    getUserUuid: state => state.userInfo.uuid,
    tenant: state => state.userInfo.tenant,
    getTeams: state => state.userInfo.teams,
    hasUserData: state => Boolean(state.userInfo.uuid),
    getUnReadNotifications: state => state.unReadModules.notifications,
    getUnReadOrders: state => state.unReadModules.orders,
    getUnReadTransactions: state => state.unReadModules.transactions,
    getUnReadComments: state => state.unReadModules.comments,
    getUnReadReviews: state => state.unReadModules.reviews,
    isTenantOnTrial: state => state.onTrial,
    isSubscriptionAndTrialEnded: state => state.subscriptionAndTrialEnded,
    isSubscriptionBannerVisible: state => state.showBannerToRenewSubscription,
    getSubscriptionStatus: state => state.subscriptionStatus,
    tenantActiveteWhatsapp: state => state.userInfo.tenant?.whatsapp_status || 'disconnected',
  },
  actions: {
    getCookie() {
      return AuthService.getCookie()
    },

    async getProfile(): Promise<any> {
      return AuthService.fetchUser().then(async ({ data }) => {
        const { updateRoutePermissions } = useRoutesStore()
        const pluginsStore = usePluginsStore()
        pluginsStore.availablePlugins = data?.tenant?.plugins
        const featuresStore = useFeaturesStore()
        // featuresStore.features = data?.tenant?.features

        this.userInfo = { ...data }
        this.setOnboardingData({
          systemType: data?.tenant?.onboarding?.tenant_type,
          selectedFeatures: data?.tenant?.onboarding?.tenant_benefits || [],
          selectedEmployees: data?.tenant?.onboarding?.number_of_employees,
          selectedRole: data?.tenant?.onboarding?.tenant_creator_role,
          businessName: data?.tenant?.name,
          selectedIndustries: data?.tenant?.industry,
          storeUrl: data?.tenant?.booking_page?.subdomain,
          industryId: data?.tenant?.industry?.uuid,
          serviceType: data?.teams?.[0]?.location,
        })
        localStorage.setItem('user-id', JSON.stringify(this.userInfo.uuid))
        localStorage.setItem('user-phone', JSON.stringify(this.userInfo.phone))
        localStorage.setItem('user-name', JSON.stringify(this.userInfo.name))
        localStorage.setItem('user-email', JSON.stringify(this.userInfo.email))
        localStorage.setItem('user-hash', JSON.stringify(this.userInfo.hash))
        localStorage.setItem(
          'user-created_at',
          JSON.stringify(this.userInfo.created_at),
        )
        if (this.userInfo.profile_completed) {
          const { fetchAppNotifications } = useNotificationTemplate()
          fetchAppNotifications()
          this.fetchUnreadModulesCount()
        }
        const subscriptionEnded = Boolean(!this.userInfo.tenant?.subscription?.status)
        const trialEnded = dayjs().isAfter(dayjs(this.userInfo.tenant?.trail_ends_at))
        const subscriptionActive = this.userInfo.tenant?.subscription?.show_renew_message && this.userInfo.tenant.subscription?.status === 'active'
        this.subscriptionStatus = this.userInfo.tenant?.subscription?.status
        // case one if trial subscition ended
        if (subscriptionEnded && !trialEnded) {
          this.onTrial = true
        }
        else if (subscriptionEnded && trialEnded) {
          // subscription ended and trial ended
          // forceUserToPlansPage
          this.subscriptionAndTrialEnded = true
        }
        else if (subscriptionActive) {
          // show banner to reminder user to renew subscription
          this.showBannerToRenewSubscription = true
        }
        updateRoutePermissions(this.userInfo.permissions)
        return data
      })
    },
    forceRedirectToSubscriptionPage() {
      // redirect to subscription page
      this.$router.push({ name: 'current-plan', query: { renewNow: true } })
    },
    async performLogin(payload: LoginPayload): Promise<any> {
      return AuthService.login(payload).then(({ data }) => data)
    },
    async performRegistration(payload: RegisterUserPayload): Promise<string> {
      return AuthService.register(payload).then(({ data }) => {
        ('data', data)
        const { user, token } = data
        this.userInfo = { ...user }
        return token
      })
    },
    async forgotPassword(payload: ForgotPasswordPayload): Promise<object> {
      return AuthService.forgotPassword(payload).then((res) => {
        this.forgetLink = res
        return res
      })
    },
    async ResetPassword(payload: {
      email: string
      password: string
      token: string
      password_confirmation: string
    }): Promise<string> {
      return AuthService.resetPassword(payload).then(({ data }) => {
        return data.status
      })
    },
    async verifyOtp(payload: verifyOtpPayload): Promise<any> {
      return AuthService.verifyOtp(payload).then(({ data }) => {
        this.userInfo = { ...data }
        return data
      })
    },
    async resendOtp(payload: { email: string }): Promise<any> {
      return AuthService.resendOtp(payload.email)
    },
    logout(): Promise<void> {
      return AuthService.logout().then(() => {
        this.userInfo.uuid = ''
        this.userInfo.name = ''
        this.userInfo.email = ''
        this.userInfo.profile_photo = ''
        localStorage.clear()
      })
    },
    impersonate(payload: object): Promise<any> {
      return AuthService.doImpersonation(payload).then(({ data }) => data)
    },
    stopImpersonate(): Promise<any> {
      return AuthService.stopImpersonate().then(({ data }) => data)
    },
    UpdateProfile(payload: ProfilePayload) {
      return AuthService.UpdateUserProfile(payload).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        this.userInfo = { ...data }
        localStorage.setItem('userAuth', JSON.stringify(true))

        return true
      })
    },
    async updateAccountPassword(payload: {
      current_password: string
      password: string
      password_confirmation: string
    }) {
      const { data } = await AuthService.updateAccountPassword(payload)
      data
        && showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
      return data
    },
    async updateAccountEmail(payload: {
      new_email: string
      current_password: string
    }) {
      const { data } = await AuthService.updateAccountEmail(payload)
      data
        && showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
      return data
    },
    async sendVerificationLink() {
      const { data } = await AuthService.sendVerificationLink()
      showNotification({
        title: i18n.global.t('Success'),
        type: 'success',
        message: data.status,
      })
      return data
    },
    async sendVerificationEmail() {
      const { data } = await AuthService.sendVerificationEmail({ email: this.userInfo?.email })
      showNotification({
        title: i18n.global.t('Success'),
        type: 'success',
        message: data.status,
      })
      return data
    },
    async verifyEmailAddress(link: string): Promise<any> {
      return AuthService.verifyEmailAddress(link)
    },
    getUserCountry() {
      UserCountry.fetchCountry()
        .then((res) => {
          const response = res.json()
          return response
        })
        .then((data) => {
          this.userCountry = data.countryCode
        }).catch((err) => {
          this.userCountry = 'SA'
        })
    },
    async fetchUnreadModulesCount() {
      return OrderService.fetchUnreadModulesCount().then(({ data }) => {
        this.unReadModules = data
      })
    },

    async updateTenantInfo(payload: { tenant_type: string; tenant_creator_role: string; number_of_employees: string; tenant_benefits: string[] }): Promise<any> {
      return AuthService.updateTenantInfo(payload).then(({ data }) => {
        return data
      })
    },
    async updateTenantProfile(payload: { name: string; industry_id: string; location: string; domain: string }): Promise<any> {
      return AuthService.updateTenantProfile(payload).then(({ data }) => {
        return data
      })
    },
    async completeProfile(payload: { staffs: Array<{ name: string }>; services: Array<{ name: string; price: number }> }): Promise<any> {
      return AuthService.completeProfile(payload).then(({ data }) => {
        return data
      })
    },
    setOnboardingData(data: Partial<OnboardingData>) {
      this.onboardingData = { ...this.onboardingData, ...data }
    },
    clearOnboardingData() {
      this.onboardingData = {
        systemType: null,
        selectedFeatures: [],
        selectedEmployees: null,
        selectedRole: null,
        businessName: '',
        storeUrl: '',
        selectedIndustries: [],
        industryId: '',
        serviceType: '',
        services: [],
        providers: [],
      }
    },
    async skipOnboarding() {
      try {
        await AuthService.skipOnboarding()
        await this.getProfile()
        return true
      }
      catch (error) {
        throw error
      }
    },
  },
})
