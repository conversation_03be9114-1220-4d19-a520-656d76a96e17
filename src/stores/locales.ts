import { defineStore } from 'pinia'
import type {
  State,
} from '@/types'
export const useLocalesStore = defineStore({
  id: 'locales',
  state: (): State => ({
    locales: [
      {
        name: 'English',
        id: 'en',
        textSymbol: 'En',
        icon: 'icons/language/en.svg',
        direction: 'ltr',
      },
      {
        name: 'Arabic',
        id: 'ar',
        textSymbol: 'ع',
        icon: 'icons/language/ar.svg',
        direction: 'rtl',
      },
    ],
  }),
  getters: {
    getLocales: state => state.locales,
    getLocale: state => (locale: string) => state.locales.find(availableLocale => availableLocale.id === locale),
  },
})
