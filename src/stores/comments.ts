import { defineStore } from 'pinia'
import CommentService from '@/services/CommentService'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'

const { showNotification } = useNotifications()

export const useCommentsStore = defineStore({
  id: 'Comments',
  state: () => ({}),
  getters: {},
  actions: {
    async fetchComments(page = 1): Promise<any> {
      return CommentService.fetchComments(page).then(({ data }) => data)
    },

    async changeStatus(reviewId: string, published: boolean): Promise<any> {
      return CommentService.changeStatus(reviewId, published).then(({ data }) => {
        showNotification({
          title: 'Success',
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })

        return data
      }).catch((error) => {
        showNotification({
          title: 'Error',
          type: 'error',
          message: error.message,
        })
      })
    },
    async updateReply(comment: any, reply: string): Promise<any> {
      return CommentService.Reply(comment, reply).then(({ data }) => {
        showNotification({
          title: 'Success',
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return data
      }).catch((error) => {
        showNotification({
          title: 'Error',
          type: 'error',
          message: error.message,
        })
      })
    },

  },
})
