/* eslint-disable @typescript-eslint/no-unused-vars */
import { defineStore } from 'pinia'
import type { Booking, Paginate } from '@/types'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
import transactionServices from '@/services/transactionServices'
import type { Transaction } from '@/types/transaction'

const { showNotification } = useNotifications()

export const useTransactionState = defineStore({
  id: 'transaction',
  state: () => ({
    transactions: {},
  }),
  getters: {
    getTransactions: state => state.transactions,
  },
  actions: {
    async fetchTransactions(page = 1, filters = ''): Promise<any> {
      return transactionServices.fetchTransactions(page, filters).then(({ data }) => data)
    },
    async fetchBookingTransactionById(uuid: string): Promise<any> {
      return transactionServices.fetchBookingTransactionById(uuid).then((res) => {
        return res
      })
    },
    async fetchTransactionById(uuid: string): Promise<Transaction> {
      return transactionServices.fetchTransactionById(uuid).then(({ data }) => {
        return data
      })
    },
    async updateTrans(transUuid: string, status: string) {
      return transactionServices.updateTransStatus(transUuid, status).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return data
      })
    },
    async updateTransaction(transUuid: string, formData: { date: string; amount: string }) {
      return transactionServices.updateTransaction(transUuid, formData).then(({ data }) => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.updated'),
        })
        return data
      }).catch((err) => {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
        return Promise.reject(err)
      })
    },

  },
})
