<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import i18n from './i18n'
import ImpersonatingAlert from './components/ImpersonatingAlert.vue'
import useNotifications from '@/composables/useNotifications'
const { showNotification } = useNotifications()

const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const route = useRoute()
const { getUserInfo, isTenantOnTrial, isSubscriptionBannerVisible }
  = storeToRefs(useAuthStore())
const largeSidebarOpen = ref(
  Boolean(JSON.parse(localStorage.getItem('isOpenLargeNav') || 'false')),
)
onMounted(() => {
  const lang = getUserInfo.value?.lang
  if (lang)
    i18n.global.locale.value = lang
  else
    i18n.global.locale.value = 'ar' // Force Arabic as default

  localStorage.setItem('selected-locale', JSON.stringify(i18n.global.locale.value))

  // Force refresh translations
  nextTick(() => {
    i18n.global.locale.value = i18n.global.locale.value
  })
  const echo = inject('$echo')
  const channel = echo.channel('public')

  // Listen for an event on the channel
  channel.listen('.NewUpdate', (data) => {
    showNotification({
      title: i18n.global.t('NewUpdate'),
      type: 'info',
      message: i18n.global.t('NewUpdateMessage'),
    })
    setTimeout(() => {
      // Refresh the page after 7 seconds
      location.reload()
    }, 7000)
  })
})

const sidebarOpen = ref(false)

const routeCheck = computed(() => {
  if (
    route.name === 'settings'
    || route.name === 'profile'
    || route.name === 'bookingPage'
    || route.name == 'onboarding'
  )
    return false

  return true
})
const userAuthComp = computed(() => {
  if (getUserInfo.value.profile_completed && getUserInfo.value.phone_verified)
    return true

  return false
})

const checkDashboard = computed(() => {
  return route.name === 'dashboard'
})
const renderView = ref(true)
async function reRenderView() {
  renderView.value = false
  await nextTick()
  renderView.value = true
}
const { showUpperNav } = useLayout()

const planDetails = computed(() => {
  return {
    start: getUserInfo.value?.tenant?.onTrial ? new Date(getUserInfo.value?.created_at * 1000) : getUserInfo.value?.tenant?.subscription?.start_at,
    end: getUserInfo.value?.tenant?.subscription?.ends_at || getUserInfo.value?.tenant?.trail_ends_at,
    planName: getUserInfo.value?.tenant?.subscription?.plan || 'Trial',
    show_renew_message: getUserInfo.value?.tenant?.subscription?.show_renew_message,
  }
})
</script>

<template>
  <div :dir="getLocale(locale)?.direction" :lang="locale" class="w-full h-full">
    <div v-if="!['posLayout', 'authLayout', 'onboardingLayout'].includes($route.meta.layout)">
      <NavigationBar
        v-if="userAuthComp"
        :showsidebar="sidebarOpen"
        :is-open-large-nav="largeSidebarOpen"
        :plan-details="planDetails"
        @close-nav="sidebarOpen = false"
        @toggle-large-nav="largeSidebarOpen = !largeSidebarOpen"
      />
      <div
        :class="{
          'flex flex-col flex-1 ': userAuthComp,
          'lg:ps-56': largeSidebarOpen,
          'lg:ps-14': !largeSidebarOpen,
        }"
        class="transition-all duration-500 ease-in-out"
      >
        <ImpersonatingAlert v-if="getUserInfo.is_imp" />
        <TenantAlert
          v-if="isTenantOnTrial"
          :plan-details="planDetails"
        />
        <ProviderAlert v-if="getUserInfo.type == 'provider'" />
        <SubscriptionBanner v-if="isSubscriptionBannerVisible" />

        <main class="flex-1">
          <UpperNav
            v-if="showUpperNav"
            @open-side-bar="sidebarOpen = true"
            @language-changed="reRenderView"
          />
          <div
            :class="[routeCheck === false ? 'py-0' : showUpperNav && 'py-6']"
          >
            <div
              :class="[
                routeCheck === false
                  ? ''
                  : 'ps-4 pe-4 ms-auto me-auto max-w-12xl sm:ps-6 sm:pe-6 md:ps-8 md:pe-8',
                !showUpperNav && '!px-0',
              ]"
            >
              <Suspense>
                <RouterView v-if="renderView" />
              </Suspense>
            </div>
          </div>
        </main>
      </div>
    </div>
    <Suspense v-else>
      <RouterView />
    </Suspense>

    <AppAlerts />
  </div>
</template>

<style lang="css" scoped>
.spinner_0XTQ {
  transform-origin: center;
  animation: spinner_y6GP 0.75s linear infinite;
}

.router-link-active.router-link-exact-active {
  @apply bg-primary-800 text-white;
}

.router-link-active.router-link-exact-active > * {
  @apply text-white;
}

@keyframes spinner_y6GP {
  100% {
    transform: rotate(360deg);
  }
}
</style>
