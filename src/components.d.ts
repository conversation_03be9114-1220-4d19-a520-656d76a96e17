// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AddAvatarIcon: typeof import('./components/Icons/AddAvatarIcon.vue')['default']
    AddCouponModal: typeof import('./components/coupons/AddCouponModal.vue')['default']
    AddCustomerModel: typeof import('./components/Customers/AddCustomerModel.vue')['default']
    AddExpenseTypeModal: typeof import('./components/Expenses/AddExpenseTypeModal.vue')['default']
    AddIcon: typeof import('./components/Icons/AddIcon.vue')['default']
    AddImageServiceModal: typeof import('./components/Services/AddImageServiceModal.vue')['default']
    AddPacakagesModal: typeof import('./components/Packages/AddPacakagesModal.vue')['default']
    AddProductsModal: typeof import('./components/Products/AddProductsModal.vue')['default']
    AddServicesModal: typeof import('./components/Services/AddServicesModal.vue')['default']
    AddSingleItemModal: typeof import('./components/Orders/Modals/AddSingleItemModal.vue')['default']
    AddStaffModal: typeof import('./components/Staff/AddStaffModal.vue')['default']
    AlertModal: typeof import('./components/AlertModal.vue')['default']
    AlignCenterIcon: typeof import('./components/Icons/Editor/AlignCenterIcon.vue')['default']
    AlignLeftIcon: typeof import('./components/Icons/Editor/AlignLeftIcon.vue')['default']
    AlignRightIcon: typeof import('./components/Icons/Editor/AlignRightIcon.vue')['default']
    Amount: typeof import('./components/Icons/Amount.vue')['default']
    AnalyticsIcon: typeof import('./components/Icons/AnalyticsIcon.vue')['default']
    AppAlerts: typeof import('./components/Banners/AppAlerts.vue')['default']
    AppCard: typeof import('./components/Apps/AppCard.vue')['default']
    AppNotifications: typeof import('./components/AppNotifications.vue')['default']
    AppoinmentGridDashboard: typeof import('./components/Dashboard/AppoinmentGridDashboard.vue')['default']
    AppointmentRecurringModal: typeof import('./components/Bookings/AppointmentRecurringModal.vue')['default']
    AppSettings: typeof import('./components/Apps/AppSettings.vue')['default']
    AppWideAlert: typeof import('./components/AppWideAlert.vue')['default']
    AvatarInput: typeof import('./components/FormControls/Inputs/AvatarInput.vue')['default']
    BankAccountModal: typeof import('./components/Settings/BankAccountModal.vue')['default']
    BaseButton: typeof import('./components/Buttons/BaseButton.vue')['default']
    BaseComboBox: typeof import('./components/FormControls/Inputs/BaseComboBox.vue')['default']
    BoldIcon: typeof import('./components/Icons/Editor/BoldIcon.vue')['default']
    BookingActionsMenu: typeof import('./components/Dropdowns/BookingActionsMenu.vue')['default']
    BookingActivityLog: typeof import('./components/Bookings/bookingActivityLog.vue')['default']
    BookingFilterForm: typeof import('./components/Bookings/BookingFilterForm.vue')['default']
    BookingModal: typeof import('./components/Calendar/BookingModal.vue')['default']
    BookingServiceFields: typeof import('./components/Bookings/BookingServiceFields.vue')['default']
    BookingServices: typeof import('./components/Bookings/BookingServices.vue')['default']
    BookingsGrid: typeof import('./components/Bookings/BookingsGrid.vue')['default']
    BookingsIcon: typeof import('./components/Icons/BookingsIcon.vue')['default']
    BookingStatus: typeof import('./components/Bookings/BookingStatus.vue')['default']
    BookingStatusMenu: typeof import('./components/Orders/Table/BookingStatusMenu.vue')['default']
    BookingTransaction: typeof import('./components/Bookings/BookingTransaction.vue')['default']
    BookingViewComp: typeof import('./components/Calendar/BookingViewComp.vue')['default']
    BoundariesIcon: typeof import('./components/Icons/BoundariesIcon.vue')['default']
    Branch: typeof import('./components/Icons/Branch.vue')['default']
    BranchDetailsModal: typeof import('./components/Settings/Branch/BranchDetailsModal.vue')['default']
    BranchIcon: typeof import('./components/Icons/BranchIcon.vue')['default']
    BranchInfoCard: typeof import('./components/Settings/Branch/BranchInfoCard.vue')['default']
    BranchModal: typeof import('./components/Settings/Branch/branchModal.vue')['default']
    BranchStatusAction: typeof import('./components/Settings/Branch/BranchStatusAction.vue')['default']
    BranchTabs: typeof import('./components/Settings/Branch/BranchTabs.vue')['default']
    BranchTabWorkingHour: typeof import('./components/Settings/Branch/BranchTabWorkingHour.vue')['default']
    BranchZones: typeof import('./components/Settings/Branch/branchZones.vue')['default']
    BreadCrumb: typeof import('./components/Apps/BreadCrumb.vue')['default']
    BroomIcon: typeof import('./components/Icons/BroomIcon.vue')['default']
    CalendarSettings: typeof import('./components/Settings/AdvancedSettings/CalendarSettings.vue')['default']
    CalendarsIcon: typeof import('./components/Icons/CalendarsIcon.vue')['default']
    CampaignDetailsModal: typeof import('./components/Campaign/CampaignDetailsModal.vue')['default']
    CampaignModal: typeof import('./components/Campaign/CampaignModal.vue')['default']
    CampaignsIcon: typeof import('./components/Icons/CampaignsIcon.vue')['default']
    CancelBookingModal: typeof import('./components/Calendar/CancelBookingModal.vue')['default']
    CardSelector: typeof import('./components/FormControls/Inputs/CardSelector.vue')['default']
    CategoriesIcon: typeof import('./components/Icons/CategoriesIcon.vue')['default']
    CategoryCard: typeof import('./components/Apps/CategoryCard.vue')['default']
    CategoryModal: typeof import('./components/Categories/CategoryModal.vue')['default']
    Checked: typeof import('./components/auth/Checked.vue')['default']
    CheckInput: typeof import('./components/FormControls/Inputs/CheckInput.vue')['default']
    CheckLoad: typeof import('./components/Common/checkLoad.vue')['default']
    CheckoutItem: typeof import('./components/Pos/CheckoutItem.vue')['default']
    CheckoutPayment: typeof import('./components/Pos/CheckoutPayment.vue')['default']
    CheckoutProductSelection: typeof import('./components/Pos/CheckoutProductSelection.vue')['default']
    ClientSelectionModal: typeof import('./components/Pos/ClientSelectionModal.vue')['default']
    CloseIcon: typeof import('./components/Icons/CloseIcon.vue')['default']
    CompLoader: typeof import('./components/Common/CompLoader.vue')['default']
    ConfigCalenderSlide: typeof import('./components/Calendar/ConfigCalenderSlide.vue')['default']
    ConfirmationModal: typeof import('./components/Apps/ConfirmationModal.vue')['default']
    ConfirmModal: typeof import('./components/Common/ConfirmModal.vue')['default']
    ContentCard: typeof import('./components/Common/ContentCard.vue')['default']
    CopyInput: typeof import('./components/FormControls/Inputs/CopyInput.vue')['default']
    CopyLinkIcon: typeof import('./components/Icons/CopyLinkIcon.vue')['default']
    CounterInput: typeof import('./components/FormControls/Inputs/CounterInput.vue')['default']
    CouponDetailsModal: typeof import('./components/coupons/CouponDetailsModal.vue')['default']
    CouponInfoCard: typeof import('./components/coupons/CouponInfoCard.vue')['default']
    Coupons: typeof import('./components/Icons/Coupons.vue')['default']
    CouponScopeCard: typeof import('./components/coupons/CouponScopeCard.vue')['default']
    CouponsGrid: typeof import('./components/coupons/CouponsGrid.vue')['default']
    CouponsIcon: typeof import('./components/Icons/CouponsIcon.vue')['default']
    CouponStatusAction: typeof import('./components/coupons/CouponStatusAction.vue')['default']
    CustomerAndStaff: typeof import('./components/Bookings/CustomerAndStaff.vue')['default']
    CustomerAttachment: typeof import('./components/Customers/CustomerAttachment.vue')['default']
    CustomerAttachmentModal: typeof import('./components/Customers/CustomerAttachmentModal.vue')['default']
    CustomerBookings: typeof import('./components/Customers/CustomerBookings.vue')['default']
    CustomerFilteration: typeof import('./components/Customers/CustomerFilteration.vue')['default']
    CustomerInvoices: typeof import('./components/Customers/CustomerInvoices.vue')['default']
    CustomerNotes: typeof import('./components/Customers/CustomerNotes.vue')['default']
    CustomerNotesModal: typeof import('./components/Customers/CustomerNotesModal.vue')['default']
    CustomersGrid: typeof import('./components/Customers/CustomersGrid.vue')['default']
    CustomersIcon: typeof import('./components/Icons/CustomersIcon.vue')['default']
    CustomersList: typeof import('./components/Customers/CustomersList.vue')['default']
    CustomerTable: typeof import('./components/Customers/CustomerTable.vue')['default']
    CustomField: typeof import('./components/Icons/CustomField.vue')['default']
    CustomForm: typeof import('./components/customForm.vue')['default']
    CustomPagesIcon: typeof import('./components/Icons/CustomPagesIcon.vue')['default']
    DashboardIcon: typeof import('./components/Icons/DashboardIcon.vue')['default']
    DashboardStats: typeof import('./components/DashboardStats.vue')['default']
    DateInput: typeof import('./components/FormControls/Inputs/DateInput.vue')['default']
    DateRangeInput: typeof import('./components/FormControls/Inputs/DateRangeInput.vue')['default']
    DayView: typeof import('./components/Calendar/DayView.vue')['default']
    DisclosureWrapper: typeof import('./components/Common/DisclosureWrapper.vue')['default']
    DiscountIcon: typeof import('./components/Icons/DiscountIcon.vue')['default']
    DiscountInput: typeof import('./components/FormControls/Inputs/DiscountInput.vue')['default']
    DotsVerticalIcon: typeof import('./components/Icons/DotsVerticalIcon.vue')['default']
    DownloadIcon: typeof import('./components/Icons/DownloadIcon.vue')['default']
    DragDropUploadInput: typeof import('./components/FormControls/Inputs/DragDropUploadInput.vue')['default']
    DropConfirmationModal: typeof import('./components/Calendar/DropConfirmationModal.vue')['default']
    DurationInput: typeof import('./components/FormControls/Inputs/DurationInput.vue')['default']
    DynamicMenu: typeof import('./components/Common/DynamicMenu.vue')['default']
    EditBranchInfoModal: typeof import('./components/Settings/Branch/EditBranchInfoModal.vue')['default']
    EditCouponScopeModal: typeof import('./components/coupons/EditCouponScopeModal.vue')['default']
    EditDescriptionModal: typeof import('./components/coupons/EditDescriptionModal.vue')['default']
    EditDescServiceModal: typeof import('./components/Services/EditDescServiceModal.vue')['default']
    EditIcon: typeof import('./components/Icons/EditIcon.vue')['default']
    EditInfoModal: typeof import('./components/coupons/EditInfoModal.vue')['default']
    Editor: typeof import('./components/Editor/index.vue')['default']
    EditServiceInfoModal: typeof import('./components/Services/EditServiceInfoModal.vue')['default']
    EditStaffModal: typeof import('./components/Staff/EditStaffModal.vue')['default']
    EditStaffServiceModal: typeof import('./components/Services/EditStaffServiceModal.vue')['default']
    EmailIcon: typeof import('./components/Icons/EmailIcon.vue')['default']
    EmptyState: typeof import('./components/Common/EmptyState.vue')['default']
    ErrMsg: typeof import('./components/Common/ErrMsg.vue')['default']
    ErrValidations: typeof import('./components/Apps/ErrValidations.vue')['default']
    ExpenseTypesTable: typeof import('./components/Expenses/ExpenseTypesTable.vue')['default']
    ExportIcon: typeof import('./components/Icons/ExportIcon.vue')['default']
    Facebook: typeof import('./components/SoicalMediaIcons/facebook.vue')['default']
    FacebookIcon: typeof import('./components/Icons/FacebookIcon.vue')['default']
    FileInput: typeof import('./components/FormControls/Inputs/FileInput.vue')['default']
    FilterWithTeam: typeof import('./components/FilterationBoxes/FilterWithTeam.vue')['default']
    FirstStep: typeof import('./components/SignUp/FirstStep.vue')['default']
    FormGroup: typeof import('./components/FormGroup/index.vue')['default']
    FourthStep: typeof import('./components/SignUp/FourthStep.vue')['default']
    FullScreenModal: typeof import('./components/Common/FullScreenModal.vue')['default']
    GeneralPanelIcon: typeof import('./components/Icons/GeneralPanelIcon.vue')['default']
    GenericAlert: typeof import('./components/Generic/GenericAlert.vue')['default']
    GenericBoundaryModal: typeof import('./components/Generic/GenericBoundaryModal.vue')['default']
    GenericSchedule: typeof import('./components/Generic/GenericSchedule.vue')['default']
    GenericTable: typeof import('./components/Generic/GenericTable.vue')['default']
    Grid: typeof import('./components/Common/Grid.vue')['default']
    GridTd: typeof import('./components/Common/GridTd.vue')['default']
    GridTh: typeof import('./components/Common/GridTh.vue')['default']
    H1Icon: typeof import('./components/Icons/Editor/H1Icon.vue')['default']
    H2Icon: typeof import('./components/Icons/Editor/H2Icon.vue')['default']
    H3con: typeof import('./components/Icons/Editor/H3con.vue')['default']
    H4Icon: typeof import('./components/Icons/Editor/H4Icon.vue')['default']
    H5Icon: typeof import('./components/Icons/Editor/H5Icon.vue')['default']
    HighlightIcon: typeof import('./components/Icons/Editor/HighlightIcon.vue')['default']
    IconOnlyBtn: typeof import('./components/Buttons/IconOnlyBtn.vue')['default']
    Icons: typeof import('./components/Icons/Icons.vue')['default']
    ImageInput: typeof import('./components/FormControls/Inputs/ImageInput.vue')['default']
    ImagesUploader: typeof import('./components/ImagesUploader.vue')['default']
    ImpersonatingAlert: typeof import('./components/ImpersonatingAlert.vue')['default']
    InfoIcon: typeof import('./components/Icons/InfoIcon.vue')['default']
    Instagram: typeof import('./components/SoicalMediaIcons/instagram.vue')['default']
    Interval: typeof import('./components/WorkingHour/Interval.vue')['default']
    InvoicePreview: typeof import('./components/Pos/InvoicePreview.vue')['default']
    ItalicIcon: typeof import('./components/Icons/Editor/ItalicIcon.vue')['default']
    ItemTypeSelection: typeof import('./components/Orders/ItemTypeSelection.vue')['default']
    LabelInput: typeof import('./components/FormControls/labels/LabelInput.vue')['default']
    LabelsGrid: typeof import('./components/Labels/LabelsGrid.vue')['default']
    LabelsModal: typeof import('./components/Labels/LabelsModal.vue')['default']
    LangIcon: typeof import('./components/Icons/LangIcon.vue')['default']
    LangInput: typeof import('./components/FormControls/Inputs/LangInput.vue')['default']
    LanguageSwitcher: typeof import('./components/Dropdowns/LanguageSwitcher.vue')['default']
    LevelNumber: typeof import('./components/auth/LevelNumber.vue')['default']
    LilActionBtn: typeof import('./components/Buttons/LilActionBtn.vue')['default']
    LinkedIn: typeof import('./components/SoicalMediaIcons/LinkedIn.vue')['default']
    LinkedInIcon: typeof import('./components/Icons/LinkedInIcon.vue')['default']
    LinkIcon: typeof import('./components/Icons/LinkIcon.vue')['default']
    ListNumIcon: typeof import('./components/Icons/Editor/ListNumIcon.vue')['default']
    ListPointIcon: typeof import('./components/Icons/Editor/ListPointIcon.vue')['default']
    LocationIcon: typeof import('./components/Icons/LocationIcon.vue')['default']
    LocationInput: typeof import('./components/FormControls/Inputs/LocationInput.vue')['default']
    MainBookingModal: typeof import('./components/Calendar/MainBookingModal.vue')['default']
    MainOrderModal: typeof import('./components/Bookings/MainOrderModal.vue')['default']
    MainSlider: typeof import('./components/Common/mainSlider.vue')['default']
    Manage: typeof import('./components/Icons/Manage.vue')['default']
    MarkIcon: typeof import('./components/Icons/MarkIcon.vue')['default']
    MasterComponents: typeof import('./components/Preview/MasterComponents.vue')['default']
    MediaIcon: typeof import('./components/Icons/Editor/MediaIcon.vue')['default']
    Menue: typeof import('./components/Apps/Menue.vue')['default']
    MetaDataTable: typeof import('./components/Orders/Table/metaDataTable.vue')['default']
    MiddleLineIcon: typeof import('./components/Icons/Editor/MiddleLineIcon.vue')['default']
    MiniEditor: typeof import('./components/Common/MiniEditor.vue')['default']
    Modal: typeof import('./components/Common/Modal.vue')['default']
    ModalAssignCustomerToOrder: typeof import('./components/Orders/Modals/ModalAssignCustomerToOrder.vue')['default']
    ModalChangeOrderStatus: typeof import('./components/Orders/Modals/ModalChangeOrderStatus.vue')['default']
    ModalOrder: typeof import('./components/Orders/Modals/ModalOrder.vue')['default']
    ModalSetAppointment: typeof import('./components/Orders/Modals/ModalSetAppointment.vue')['default']
    ModalTransactionDetails: typeof import('./components/Orders/Modals/ModalTransactionDetails.vue')['default']
    MonthView: typeof import('./components/Calendar/MonthView.vue')['default']
    MultiFileInput: typeof import('./components/FormControls/Inputs/MultiFileInput.vue')['default']
    MultiSelectInput: typeof import('./components/FormControls/Inputs/MultiSelectInput.vue')['default']
    NavigationBar: typeof import('./components/Apps/NavigationBar.vue')['default']
    NextButton: typeof import('./components/SignUp/NextButton.vue')['default']
    NotDelete: typeof import('./components/Icons/NotDelete.vue')['default']
    NotificationModal: typeof import('./components/Notifications/NotificationModal.vue')['default']
    NotificationSettingsTab: typeof import('./components/Settings/Notifications/notificationSettingsTab.vue')['default']
    NotificationsIcon: typeof import('./components/Icons/NotificationsIcon.vue')['default']
    NumberInput: typeof import('./components/FormControls/Inputs/NumberInput.vue')['default']
    Offers: typeof import('./components/Icons/Offers.vue')['default']
    OffersForm: typeof import('./components/Offers/OffersForm.vue')['default']
    OffersGrid: typeof import('./components/Offers/OffersGrid.vue')['default']
    OffersIcon: typeof import('./components/Icons/OffersIcon.vue')['default']
    OffersModal: typeof import('./components/Offers/OffersModal.vue')['default']
    OnboardingSideBar: typeof import('./components/auth/OnboardingSideBar.vue')['default']
    OptionExplorerModal: typeof import('./components/Calendar/OptionExplorerModal.vue')['default']
    OrderAddressCard: typeof import('./components/Orders/Cards/OrderAddressCard.vue')['default']
    OrderAppointmentTimeCard: typeof import('./components/Orders/Cards/OrderAppointmentTimeCard.vue')['default']
    OrderCloseImages: typeof import('./components/Orders/Cards/orderCloseImages.vue')['default']
    OrderCustomerCard: typeof import('./components/Orders/Cards/orderCustomerCard.vue')['default']
    OrderCustomerInfoCard: typeof import('./components/Orders/Cards/OrderCustomerInfoCard.vue')['default']
    OrderDetailsModal: typeof import('./components/Orders/OrderDetailsModal.vue')['default']
    OrderDiscount: typeof import('./components/Orders/OrderDiscount.vue')['default']
    OrderDiscountModal: typeof import('./components/Orders/Modals/OrderDiscountModal.vue')['default']
    OrderGiftInfoCard: typeof import('./components/Orders/Cards/OrderGiftInfoCard.vue')['default']
    OrderGridDashborad: typeof import('./components/Dashboard/OrderGridDashborad.vue')['default']
    OrderInfoCard: typeof import('./components/Orders/Cards/orderInfoCard.vue')['default']
    OrderInvoiceCard: typeof import('./components/Orders/Cards/orderInvoiceCard.vue')['default']
    OrderItemsTable: typeof import('./components/Orders/Table/orderItemsTable.vue')['default']
    OrderLocationCard: typeof import('./components/Orders/Cards/orderLocationCard.vue')['default']
    OrderLogs: typeof import('./components/Orders/Log/orderLogs.vue')['default']
    OrderMetaDataFields: typeof import('./components/Orders/OrderMetaDataFields.vue')['default']
    OrderNotes: typeof import('./components/Orders/Cards/orderNotes.vue')['default']
    OrderNotifications: typeof import('./components/Orders/Cards/orderNotifications.vue')['default']
    OrderPackage: typeof import('./components/Orders/OrderPackage.vue')['default']
    OrderPaymentCard: typeof import('./components/Orders/Cards/orderPaymentCard.vue')['default']
    OrderProduct: typeof import('./components/Orders/OrderProduct.vue')['default']
    OrderProducts: typeof import('./components/Orders/OrderProducts.vue')['default']
    OrderServiceFields: typeof import('./components/Orders/OrderServiceFields.vue')['default']
    OrderServices: typeof import('./components/Orders/OrderServices.vue')['default']
    OrderSettings: typeof import('./components/Settings/AdvancedSettings/OrderSettings.vue')['default']
    OrdersFilters: typeof import('./components/Orders/Filters/ordersFilters.vue')['default']
    OrdersIcon: typeof import('./components/Icons/OrdersIcon.vue')['default']
    OrderStatusAction: typeof import('./components/Orders/OrderStatusAction.vue')['default']
    OrderSummary: typeof import('./components/Orders/OrderSummary.vue')['default']
    OrderSummaryCard: typeof import('./components/Orders/Cards/OrderSummaryCard.vue')['default']
    OrderTransactionsTable: typeof import('./components/Orders/Table/orderTransactionsTable.vue')['default']
    OverlayLoader: typeof import('./components/Common/OverlayLoader.vue')['default']
    PackageFilteration: typeof import('./components/Packages/PackageFilteration.vue')['default']
    PackagesIcon: typeof import('./components/Icons/PackagesIcon.vue')['default']
    Pagination: typeof import('./components/Common/Pagination.vue')['default']
    PasswordInput: typeof import('./components/FormControls/Inputs/PasswordInput.vue')['default']
    PaymentModal: typeof import('./components/Calendar/PaymentModal.vue')['default']
    PaymentStatus: typeof import('./components/Bookings/PaymentStatus.vue')['default']
    PendingModal: typeof import('./components/Calendar/PendingModal.vue')['default']
    Percentage: typeof import('./components/Icons/Percentage.vue')['default']
    PhoneInput: typeof import('./components/FormControls/Inputs/PhoneInput.vue')['default']
    PieChart: typeof import('./components/PieChart.vue')['default']
    PluginCard: typeof import('./components/Apps/PluginCard.vue')['default']
    PlusIcon: typeof import('./components/Icons/PlusIcon.vue')['default']
    PlusIconBordered: typeof import('./components/Icons/PlusIconBordered.vue')['default']
    POSIcon: typeof import('./components/Icons/POSIcon.vue')['default']
    PosSystemSettings: typeof import('./components/Settings/AdvancedSettings/PosSystemSettings.vue')['default']
    PrevButton: typeof import('./components/SignUp/PrevButton.vue')['default']
    PreviewIcon: typeof import('./components/Icons/PreviewIcon.vue')['default']
    PriceFormat: typeof import('./components/Apps/PriceFormat.vue')['default']
    PricingIcon: typeof import('./components/Icons/PricingIcon.vue')['default']
    ProductList: typeof import('./components/Pos/ProductList.vue')['default']
    ProductsFilteration: typeof import('./components/Products/ProductsFilteration.vue')['default']
    ProductsGrid: typeof import('./components/Products/ProductsGrid.vue')['default']
    ProductsIcon: typeof import('./components/Icons/ProductsIcon.vue')['default']
    ProductsTabs: typeof import('./components/Products/ProductsTabs.vue')['default']
    ProductsTabStaffs: typeof import('./components/Products/ProductsTabStaffs.vue')['default']
    ProviderAlert: typeof import('./components/Banners/ProviderAlert.vue')['default']
    ProviderAppSettings: typeof import('./components/Settings/AdvancedSettings/ProviderAppSettings.vue')['default']
    ProvidersIcon: typeof import('./components/Icons/ProvidersIcon.vue')['default']
    PurchasedPackagesIcon: typeof import('./components/Icons/PurchasedPackagesIcon.vue')['default']
    RangesMenu: typeof import('./components/Dashboard/RangesMenu.vue')['default']
    RedioInput: typeof import('./components/FormControls/Inputs/RedioInput.vue')['default']
    RedoIcon: typeof import('./components/Icons/RedoIcon.vue')['default']
    RefundedOrdersIcon: typeof import('./components/Icons/RefundedOrdersIcon.vue')['default']
    RefundOrderItemsModal: typeof import('./components/Orders/Modals/refundOrderItemsModal.vue')['default']
    RefundsOrderRef: typeof import('./components/Orders/Cards/refundsOrderRef.vue')['default']
    RepeatIcon: typeof import('./components/Icons/RepeatIcon.vue')['default']
    ReportsIcon: typeof import('./components/Icons/ReportsIcon.vue')['default']
    ReviewsIcon: typeof import('./components/Icons/ReviewsIcon.vue')['default']
    Role: typeof import('./components/Icons/Role.vue')['default']
    RolesGrid: typeof import('./components/Roles/RolesGrid.vue')['default']
    RolesModal: typeof import('./components/Roles/RolesModal.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SaleFilteration: typeof import('./components/Sale/SaleFilteration.vue')['default']
    SaleGrid: typeof import('./components/Sale/SaleGrid.vue')['default']
    SaleStatus: typeof import('./components/Sale/SaleStatus.vue')['default']
    SearchInput: typeof import('./components/FormControls/Inputs/SearchInput.vue')['default']
    SelectInput: typeof import('./components/FormControls/Inputs/SelectInput.vue')['default']
    SentNotificationModal: typeof import('./components/Settings/Notifications/sentNotificationModal.vue')['default']
    SentNotificationsTab: typeof import('./components/Settings/Notifications/sentNotificationsTab.vue')['default']
    ServiceDescTable: typeof import('./components/Services/ServiceDescTable.vue')['default']
    ServiceFilteration: typeof import('./components/Services/ServiceFilteration.vue')['default']
    ServiceImageTable: typeof import('./components/Services/Table/ServiceImageTable.vue')['default']
    ServiceInfoCard: typeof import('./components/Services/ServiceInfoCard.vue')['default']
    ServicesGrid: typeof import('./components/Services/ServicesGrid.vue')['default']
    ServicesIcon: typeof import('./components/Icons/ServicesIcon.vue')['default']
    ServicesTabs: typeof import('./components/Services/ServicesTabs.vue')['default']
    ServicesTabStaffs: typeof import('./components/Services/ServicesTabStaffs.vue')['default']
    ServiceStaffTable: typeof import('./components/Services/Table/ServiceStaffTable.vue')['default']
    ShareBox: typeof import('./components/ShareBox.vue')['default']
    ShareIcon: typeof import('./components/Icons/ShareIcon.vue')['default']
    ShowServiceModal: typeof import('./components/Services/ShowServiceModal.vue')['default']
    ShowStaffModal: typeof import('./components/Staff/ShowStaffModal.vue')['default']
    ShowTransaction: typeof import('./components/Transaction/showTransaction.vue')['default']
    SignUpForm: typeof import('./components/SignUp/SignUpForm.vue')['default']
    SkeletonLoader: typeof import('./components/SkeletonLoader.vue')['default']
    SmallUploadInput: typeof import('./components/FormControls/Inputs/SmallUploadInput.vue')['default']
    StaffAndCustomerTab: typeof import('./components/Settings/Notifications/staffAndCustomerTab.vue')['default']
    StaffApps: typeof import('./components/Staff/Breaks/StaffApps.vue')['default']
    StaffBreaks: typeof import('./components/Staff/Breaks/StaffBreaks.vue')['default']
    StaffBreaksModal: typeof import('./components/Staff/Breaks/StaffBreaksModal.vue')['default']
    StaffBreaksTable: typeof import('./components/Staff/Breaks/staffBreaksTable.vue')['default']
    StaffGrid: typeof import('./components/Staff/StaffGrid.vue')['default']
    StaffSlide: typeof import('./components/Calendar/StaffSlide.vue')['default']
    StaffTabBookings: typeof import('./components/Staff/StaffTabBookings.vue')['default']
    StaffTabBoundaries: typeof import('./components/Staff/StaffTabBoundaries.vue')['default']
    StaffTabs: typeof import('./components/Staff/StaffTabs.vue')['default']
    StaffTabServices: typeof import('./components/Staff/StaffTabServices.vue')['default']
    StaffTabWorkingHours: typeof import('./components/Staff/StaffTabWorkingHours.vue')['default']
    StaffTimeOff: typeof import('./components/Staff/TimeOffs/StaffTimeOff.vue')['default']
    StaffTimeOffModal: typeof import('./components/Staff/TimeOffs/StaffTimeOffModal.vue')['default']
    StaffTimeTable: typeof import('./components/Staff/TimeOffs/staffTimeTable.vue')['default']
    StaffZones: typeof import('./components/Staff/StaffZones.vue')['default']
    StatusFilter: typeof import('./components/Orders/Filters/statusFilter.vue')['default']
    StorePanelIcon: typeof import('./components/Icons/StorePanelIcon.vue')['default']
    SubscribeStatus: typeof import('./components/auth/SubscribeStatus.vue')['default']
    SubscriptionBanksModal: typeof import('./components/Settings/Subscriptions/SubscriptionBanksModal.vue')['default']
    SubscriptionBanner: typeof import('./components/Banners/SubscriptionBanner.vue')['default']
    SummaryIcon: typeof import('./components/Icons/SummaryIcon.vue')['default']
    SwitchInput: typeof import('./components/FormControls/Inputs/SwitchInput.vue')['default']
    Tab: typeof import('./components/Common/Tab.vue')['default']
    TagsComp: typeof import('./components/TagsComp.vue')['default']
    TaxesGrid: typeof import('./components/Taxes/TaxesGrid.vue')['default']
    TaxesModal: typeof import('./components/Taxes/TaxesModal.vue')['default']
    TeamSelectInput: typeof import('./components/FormControls/Inputs/TeamSelectInput.vue')['default']
    TenantAlert: typeof import('./components/Banners/TenantAlert.vue')['default']
    TextareaInput: typeof import('./components/FormControls/Inputs/TextareaInput.vue')['default']
    TextColorIcon: typeof import('./components/Icons/Editor/TextColorIcon.vue')['default']
    TextInput: typeof import('./components/FormControls/Inputs/TextInput.vue')['default']
    TheLoader: typeof import('./components/TheLoader.vue')['default']
    TheSkeleton: typeof import('./components/TheSkeleton.vue')['default']
    ThirdStep: typeof import('./components/SignUp/ThirdStep.vue')['default']
    ThreeColumnView: typeof import('./components/Common/ThreeColumnView.vue')['default']
    TimeSlotsGrid: typeof import('./components/Bookings/TimeSlotsGrid.vue')['default']
    TransactionsIcon: typeof import('./components/Icons/TransactionsIcon.vue')['default']
    TransactionStatus: typeof import('./components/Transaction/TransactionStatus.vue')['default']
    TrashIcon: typeof import('./components/Icons/TrashIcon.vue')['default']
    TrashOrder: typeof import('./components/Trash/TrashOrder.vue')['default']
    TrashTransaction: typeof import('./components/Trash/TrashTransaction.vue')['default']
    TwoColumnView: typeof import('./components/Common/TwoColumnView.vue')['default']
    UndoIcon: typeof import('./components/Icons/UndoIcon.vue')['default']
    UploadFile: typeof import('./components/Icons/UploadFile.vue')['default']
    UpperLineIcon: typeof import('./components/Icons/Editor/UpperLineIcon.vue')['default']
    UpperNav: typeof import('./components/Apps/UpperNav.vue')['default']
    UpsertAppModal: typeof import('./components/Apps/UpsertAppModal.vue')['default']
    UpsertServicesForm: typeof import('./components/Services/UpsertServicesForm.vue')['default']
    UpsertStaffForm: typeof import('./components/Staff/UpsertStaffForm.vue')['default']
    UrlInput: typeof import('./components/FormControls/Inputs/UrlInput.vue')['default']
    ViewBookingModal: typeof import('./components/Calendar/ViewBookingModal.vue')['default']
    WeekView: typeof import('./components/Calendar/WeekView.vue')['default']
    Whatsapp: typeof import('./components/SoicalMediaIcons/whatsapp.vue')['default']
    WhatsappIcon: typeof import('./components/Icons/WhatsappIcon.vue')['default']
    WizerdForm: typeof import('./components/SignUp/WizerdForm.vue')['default']
    WorkingHourControlModal: typeof import('./components/WorkingHour/WorkingHourControlModal.vue')['default']
    WorkingHourIntervals: typeof import('./components/WorkingHour/WorkingHourIntervals.vue')['default']
    X: typeof import('./components/SoicalMediaIcons/x.vue')['default']
    XIcon: typeof import('./components/Icons/XIcon.vue')['default']
  }
}
