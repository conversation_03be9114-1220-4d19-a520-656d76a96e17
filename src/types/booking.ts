import type { Customer } from './customer'
import type { Service } from './service'
import type { Staff } from './staff'
import type { Tag } from './tags'
import type { Transaction } from './transaction'

export interface BookingService {
  id: string
  price: number
}

export interface BookingV1 {
  uuid: string
  start: string
  customer_id: string
  staff_id: string
  services: BookingService[]
  source: string
  note: string
  tags: string[]
  cf_birthdate_date?: string
}

export interface BookingForm {
  customer_id: string
  staff_id: string
  service_id: string
  note: string
  tags: string[]
  start: string
  end: string
  source: 'booking-page' | 'calendar'
}

export interface CustomField {
  value: string
  label: string
  path: string
  type: string
}

export interface Booking {
  activity_log: any
  parsedStart: any
  customer: Customer
  staff: Staff
  service: any
  invoice?: null | string
  uuid: string
  booking_number: number
  start: string
  end: string
  duration: number
  price: number
  currency: string
  source: string
  type: string
  is_editable: boolean
  note: string
  status: string
  created_at: string
  parsedEnd: string
  payment_status: string
  invoice_url: string
  tags: Tag[]
  custom_fields: CustomField[]
  date: string
  transactions: Transaction[]
  is_recurring: boolean | null
  recurring_appointemnts: Booking[]
}

export interface Event {
  uuid: string
  name: string
  start_time: string
  end_time: string
  date: string
  start: string
  end: string
  href: string
  routeName: string
  [key: string]: any
}

export interface EventData {
  info: any
  uuid: string
  start: Date | number
  end: Date | number
}
