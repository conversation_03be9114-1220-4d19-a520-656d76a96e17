import type { Booking } from './booking'

export interface OrderStatus {
  status: string
  count: number
  label: string
  bg_color: string
  text_color: string
}

export interface OrderItems {
  id: string
  name: string
  price: number
  quantity: number
  total_amount: number
  service_type?: string
  discount_amount?: number
  discount_type?: 'fixed' | 'percentage'
  booking: Booking
  uuid: string
  staff_id: number
  photo: string
  provider: {
    uuid: string
    name: string
  }
}

export interface OrderParams {
  status: string
  page: number
  from: string
  to: string
  team: string
  source: string
  payment_status: string
  start_from?: string
  start_to?: string
  customer?: string
  invoiced?: string
  orderNum?: string
  staffId?: string
}

export interface OrderActivityLog {
  user: string
  created_at: string
  description: string | null
}

export interface OrderCustomer {
  uuid?: string
  first_name: string
  last_name: string
  email?: string
  phone?: string
  orders_count?: number
  photo?: string | null

}
export interface OrderTranscations {
  id: string
  status: string
  date: string
  currency: string
  payment_method: {
    name: string
    slug: string
  }
  transaction_no: string
  amount: number
  customer: string
  note: string
  recipt_file: string
}
export interface OrderPayments {
  paid_amount: number
  remaining_unpaid_amount: number
  payment_link: string | null
  transcations: OrderTranscations[]
  status: string
}

export interface OrderSummary {
  sub_total: number
  total_amount: number
  discount_amount: number
  discount_type?: 'fixed' | 'percentage'
  coupon?: {
    code: string
    value: number
    type: 'fixed' | 'percentage'
  }
}
export interface Branch {
  id: number
  name: string
}
export interface OrderAppointment {
  booking_number: number
  created_at: string
  currency: string
  end: string
  is_editable: boolean
  note: null
  payment_status: string
  source: string
  start: string
  price: number
  type: string
  uuid: string
  status: string
  services: any[]
}

export interface Order {
  id: string
  OrderNum: number
  customer: OrderCustomer
  items: OrderItems[]
  total: string
  created_at: string
  payments: OrderPayments

  currency?: string;
  note: string | null;
  activity_log: OrderActivityLog[];
  summary: OrderSummary;
  branch : Branch;
  status : OrderStatus;
  source: string;
  is_read_it:number;
  appointments: Booking[];
  start: string;
  end: string;
  showStatusDropdown?: boolean;
  address?: string;
  payment_status?: string;
  has_invoice?: boolean;
  type?: string;
  refundable?: boolean;
  pin_code?: string;
  gift_card?: any;
  longitude?: number;
  latitude?: number;
  invoices?: { id: string };
  images?: any[];
  meta_data?: any;
  refunded_orders?: any[];
  notifications?: any[];
}

export interface BookingService {
  id: string
  price: number
  quantity: number
  unit_amount: number
  discount_amount: number
  total_amount: number
  staffId?: string
  duration?: number
  type?: 'service' | 'product' | 'package'
  key?: string
  showDiscount?: boolean
  applyDiscount?: boolean
}

export interface OrderDiscountItem {
  id: string
  name: string
  price: number
  quantity: number
  discount_amount?: number
  discount_type?: 'fixed' | 'percentage'
  total_amount: number
}
