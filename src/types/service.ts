import type { HoursMinutes } from '.'
export interface Category {
  name: String
  type: String
  uuid: String
}
export interface Service {
  uuid: string
  name: string
  slug?: string | null
  imageLink: string | null
  image: string | null
  description: string
  color: string
  duration: HoursMinutes
  price: number
  // deposit: number
  services: PackageServices[]
  category_id: string | null
  // buffer_after: number
  display_on_booking_page: boolean | number
  // enable_deposit: boolean | number
  location: string
  team_id: string
  [key: string]: any
}
export interface Services {
  id: string
  quantity: string | number
}
export interface PackageServices {
  id: string
  quantity: string | number
}
