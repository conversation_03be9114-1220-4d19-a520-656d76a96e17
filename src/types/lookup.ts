export interface Lookups {
  customers: Customer[]
  status: string[]
  staff: Staff[]
  services: Services[]
  workdays: Workday[]
}

export interface Customer {
  uuid: string
  first_name: string
  last_name: string
}

export interface Staff {
  name: string
  phone: string
  phone_country: string
  imageLink: any
  email: string
  uuid: string
  services: Service[]
  has_workingHour: boolean
  has_timeoff: boolean
  has_breaks: boolean
  clients_in_same_time: number
}

export interface Service {
  uuid: string
  name: string
  slug: string
  description: string
  imageLink: any
  location: string
  color: string
  duration: number
  buffer_after: number
  display_on_booking_page: number
  price: number
  category_id: string
  category_name: string
}

export interface Services {
  uuid: string
  name: string
  slug: string
  description: string
  imageLink: any
  location: string
  color: string
  duration: number
  buffer_after: number
  display_on_booking_page: number
  price: number
  category_id: string
  category_name: string
  staffs: Staffs[]
}

export interface Staffs {
  name: string
  phone: string
  phone_country: string
  imageLink: any
  email: string
  uuid: string
  has_workingHour: boolean
  has_timeoff: boolean
  has_breaks: boolean
  clients_in_same_time: number
}

export interface Workday {
  id: string
  workdays: string[]
}
export interface RecurringAppointment {
  end_date: string | Date
  type: 'daily' | 'weekly' | 'monthly'
}
