import type { PaymentMethod } from './checkout'
import type { Provider } from './providers'
import type { Service } from './services'

export interface AppointmentsResponseData {
  booking_page_updated_at: Date
  data: Appointment[]
  msg: string
  result: boolean
}

export interface Appointment {
  t: string
  u: number
  d: string
}

export interface GetAppointmentsInterface {
  branchId: string
  serviceId: string
  providerId: string
  date: string
  timezone: string
}

export interface MyAppointmentsApiResponse {
  result: boolean
  msg: string
  data: MyAppointment[]
  booking_page_updated_at: string
}

export interface MyAppointment {
  id: string
  start: Start
  provider: Provider
  date?: string
  time?: string
  services: Service[]
  created_at: string
}

export interface Start {
  u: number
  t: string
  d: string
}

export interface AppointmentDetailApiResponse {
  result: boolean
  msg: string
  data: AppointmentDetail[]
  booking_page_updated_at: string
}

export interface AppointmentDetail {
  id: string
  start: Start
  provider: Provider
  services: Service[]
  payment_method: PaymentMethod
}

export interface AppointmentDetailPaymentMethod {
  type: string
  total: string
}

export interface CheckAvailabilityInterface {
  branchId: string
  services: string[]
  providerId: string
  slot: string
  timezone: string
}

export interface CheckSlotApiResponse {
  result: boolean
  msg: string
  data: Availability
  booking_page_updated_at: string
}

export interface Availability {
  result: string
  payment: boolean
  total_price: number
  currency: string
}
