export interface PluginType {
  uuid: string
  name: {
    ar: string
    en: string
  }
  description?: {
    ar?: string
    en?: string
  }
  category: string
  banner_url?: string | null
  props?: string[]
  soon: boolean
  long_description?: {
    ar?: string
    en?: string
  }
  slug: string
  installed?: {
    props_value?: string
    status?: number
  }
  name_localized: string
  description_localized: string
  recommended: boolean | null
  order: number
}
