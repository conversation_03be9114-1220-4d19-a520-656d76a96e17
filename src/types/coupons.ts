export interface Coupons {
  uuid?: string
  code: string
  discount_amount: string
  discount_type: string
  start_date: string | Date
  status: number
  end_date: string | Date
  usage_limit: string | number
  usage_limit_per_user: string | number
  description_localized?: {
    ar: string
    en: string
  }
  products?: any[]
  categories?: any[]
  branches?: any[]
}
export interface CouponInfoPayload {
  code: string;
  discount_amount: string;
  discount_type: string;
  start_date: string;
  end_date: string;
  usage_limit: string;
  usage_limit_per_user: string;
}

