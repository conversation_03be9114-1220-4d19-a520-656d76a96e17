export interface Transaction {
  amount: string
  created_at: string
  date: string
  currency: string
  customer: string
  note: string
  id: string
  recipt_file: string | null
  status: string
  transaction_no: number
  payment_method?: {
    id: string
    name: string
    slug: string | null
  }
  attached_file: string | null
  payment_gateway_id: string | null
  payment_gateway_link: string | null
  order?: {
    id: string
    orderNum: number
  }
}
