export type ActionType = "tenant-staff" | "tenant-customer" | "tenant-gift-receiver" | "sentNotifications" | "setting" | "failedNotifications";


export interface Block {
  type: string;
  data: {
    text: string;
  };
}
export interface NotficationAction {
  actionId: string;
  name: string;
  actionType: ActionType;
  variables: {
    [key: string]: string;
  };
  templates: Template[];
}
export interface Template {
  template_id?: string;
  title: string;
  content: {blocks?: Block[] , [key: string]?: any} | string;
  channel: string;
  active?: boolean;
  [key: string]: any;
}
export interface NotificationTemplates {
  actions: {
    [key in ActionType]: NotficationAction[];
  };
  channel: string[];
}
