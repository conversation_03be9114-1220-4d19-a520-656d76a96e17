export type TDay = 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat' | 'sun'

export interface WorkingHour {
  uuid: string
  name: string
  start: string | null
  end: string | null
  intervals: DayInterval[] | []
  active: boolean
}

export interface DayInterval {
  day?: TDay
  intervals?: IntervalHour[]
}

export interface IntervalHour {
  uuid?: string
  day?: TDay
  to?: string
  from?: string
  name?: string
  error?: string
}
