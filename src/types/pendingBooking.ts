export interface PendingService {
  uuid: string
  name: string
  quantity: number
  price: string
  duration: number
  color: string
  [key: string]: any
}

export interface PendingBooking {
  uuid: string
  booking_number: number
  date: string
  start: string
  end: string
  customer: {
    first_name: string
    last_name: string
    [key: string]: any
  }
  services: PendingService[]
  staff: {
    name: string
    imageLink?: string | null
    [key: string]: any
  }
  currency: string
  price: number
  source: string
  type: string
  is_editable: boolean
  status: string
  created_at: string
  payment_status: string
}
