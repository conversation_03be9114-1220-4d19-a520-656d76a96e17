export interface GetProfileApiResponse {
  booking_page_updated_at: Date
  data: User
  msg: string
  result: boolean
}

export interface UpdateProfileApiResponse {
  booking_page_updated_at: Date
  data: User
  msg: string
  result: boolean
}
interface tenant {
  booking_page: {}
  currency: string
  plugins: string[]
}
export interface Teams {
  name: string
  uuid: string

}
export interface User {
  uuid?: string
  name: string | null
  email: string
  profile_photo: string | null
  role: string
  phone: string
  phone_country: string
  lang: string
  created_at: string
  hash: string
  tenant: tenant
  teams: Teams[]
  email_verified?: boolean
  profile_completed?: boolean
  can_imp: boolean
  is_imp: boolean
  permissions: string[]
  trial_ends_at: string
  subscription: {
    status: string
    show_renew_message: boolean
    billing_type: string
    plan: string
    trial_ends_at: string

  }
}

export interface ProfilePayload {
  name: string
  profile_photo: File | null
  phone: string
  phone_country: string
}
