export interface receiver {
  name: string
  phone: string
  email: string
  id: string

}
export interface SendNotification {
  id: string
  receiver: receiver
  title: string
  body: string
  full_body: string
  app: string
  channel: 'email' | 'sms' | 'whatsapp'
  created_at: string
}
export interface AppNotification {
  uuid: string
  description: string
  url: string
  isRead: boolean | 1 | 0
  created_at: string
}
