export * from './appointments'
export * from './auth'
export * from './branches'
export * from './checkout'
export * from './locale'
export * from './providers'
export * from './review'
export * from './setting'
export * from './user'
export * from './pagination'
export * from './staff'
export * from './service'
export * from './booking'
export * from './customer'
export * from './bookingFilters'
export * from './tags'
export * from './timeSlot'
export * from './pendingBooking'
export * from './apps'
export * from './products'
export * from './staffTimeOffs'
export * from './sale'
export * from './table'
export * from './notfication'
export * from './sendNotifications'
export * from './offers'
export * from './orders'
export * from './workingHour'
export * from './plugin'
export * from './metaData'
export * from './serviceMetrics'

export interface AboutUsResponseData {
  booking_page_updated_at: Date
  data: AboutUsPage
  msg: string
  result: boolean
}

export interface AboutUsPage {
  title: string
  image: string
  last_updated: string
  content: string
}

export interface StepProp {
  key: string
  title: string
  completed: boolean
}

export interface HoursMinutes {
  hours: number
  minutes: number
}

export type ExportableModels = 'Booking' | 'Staff' | 'Transaction' | 'Service' | 'Page' | 'Customer' | 'Products'

export interface AppNotification {
  id: string
  title: string
  message: string
  type: string
}

export interface LoginPayload {
  email: string
  password: string
}

export interface RegisterUserPayload {
  email: string
  password: string
  name: string
}

export interface ForgotPasswordPayload {
  email: string
}

export interface ProfilePayload {
  name: string
  phone: string
  phone_country: string
  profile_photo: File | null
  lang: string
}

export interface TenantSubscription {
  status: 'active' | 'past_due'
  show_renew_message: boolean
}

export interface UnreadModules {
  notifications: number
  orders: number
  transactions: number
  comments: number
  reviews: number
}

export interface User {
  email: string | null
  name: string | null
  profile_photo: string | null
  uuid: string | null
  tenant: {
    booking_page: Record<string, any>
    currency: string
    plugins: string[]
    whatsapp_enabled: boolean
    whatsapp_status?: 'disconnected' | 'connected'
    subscription: TenantSubscription | null
    trail_ends_at: string | null
  }
  phone: string | null
  phone_country: string | null
  lang: string | null
  role: string[]
  created_at: string | null
  teams: any[]
  profile_completed: boolean
  email_verified: boolean
  can_imp: boolean
  is_imp: boolean
  hash: string
  permissions: string[]
}

export interface ExpenseType {
  uuid: string
  title: string
}

export interface Expense {
  uuid: string
  title: string
  note?: string
  expense_type_id: string
  type?: ExpenseType
  file?: string
  amount: number
  reference_no?: string
  taxable?: boolean
  payment_method_id?: string
  payment_method?: { id: string; name: string }
  team_id?: string
  team?: { uuid: string; name: string }
}

export interface DomainsCollection {
  suggestion_domains: string[]
}

export interface TimeSlot {
  u: number
  t: string
  d: string
  date: string
  booked: boolean
  time: string
  fullDate: string
}

export interface DaySlot {
  date: string
  slots: TimeSlot[]
  isHoliday: boolean
  count?: number
}
