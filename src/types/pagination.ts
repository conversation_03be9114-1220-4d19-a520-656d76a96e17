export interface PaginationMeta {
  current_page: number
  from: number
  last_page: number
  links: {
    url: string | null
    label: string
    active: boolean
  }[]
  path: string
  per_page: number
  to: number
  total: number
}

export interface PaginationLinks {
  first: string
  last: string
  prev: string | null
  next: string | null
}

export interface Paginate<T> {
  data: T[]
  links: PaginationLinks
  meta: PaginationMeta
}
