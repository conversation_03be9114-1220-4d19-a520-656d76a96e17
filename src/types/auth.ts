export interface RegisterUserPayload {
  name: string
  email: string
  password: string
}
export interface ForgotPasswordPayload {
  email: string
}
export interface LoginPayload {
  email: string
  password: string
  remember: boolean
}
export interface ResetPasswordPayload {
  email: string
  password: string
  token: string
}
export interface verifyOtpPayload {
  email: string
  otp: string
}
export interface IndustryCollection {
  id: string
  uuid: string
  name: string
}
