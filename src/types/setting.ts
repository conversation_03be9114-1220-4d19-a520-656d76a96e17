import type { PaymentMethod } from './checkout'

export interface SettingsApiResponse {
  booking_page_updated_at: Date
  data: SettingsData
  msg: string
  result: boolean
}

export interface SettingsData {
  default_locale: string
  contact: Contact
  html_meta: HTMLMeta
  locale: LocaleSettings
  menu: Menu
  payment_methods: PaymentMethod[]
  props: Props
  theme: Theme
  url: string
}

export interface Contact {
  call: string
  social_accounts: SocialAccounts
}

export interface SocialAccounts {
  facebook: string
  instagram: string
  twitter: string
  whatsapp: string
  youtube: string
}

export interface HTMLMeta {
  booking_page_updated_at: Date
  description: string
  msapplication: Msapplication
  og: Og
  sitemap: string
  title: string
  twitter: Twitter
}

export interface Msapplication {
  'msapplication-TileColor': string
  'msapplication-TileImage': string
}

export interface Og {
  description: string
  image: string
  title: string
  type: string
  url: string
}

export interface Twitter {
  card: string
  description: string
  image: string
  title: string
}

export interface LocaleSettings {
  ar: string
  en: string
}

export interface Menu {
  about_us: boolean
  reviews: boolean
  services: boolean
}

export interface Props {
  powerd_by: boolean
}

export interface Theme {
  background_color: string
  cover_image_url: string
  favicon: string
  logo_url: string
  primary_color: string
  secondary_color: string
}

export interface GeneralSettings {
  id: string
  type: string
  value: string
  rules: string
  label: string
  help: string
  options?: { [key: string]: string }
  htmlTag: string
}
