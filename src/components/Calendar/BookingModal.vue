<script setup lang="ts">
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { useVuelidate } from '@vuelidate/core'
import dayjs from 'dayjs'
import { useAppsStore } from '@/stores/apps'
import type { Booking, BookingForm, Staff, TimeSlot } from '@/types'
import { required } from '@/utils/i18n-validators'
import Modal from '@/components/Common/Modal.vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  eventId: {
    type: String,
    default: '',
  },
})

const emits = defineEmits(['closed', 'updated'])

const { getLocale } = storeToRefs(useLocalesStore())
const bookingNumber = ref('')

const { locale } = useI18n()
const eventsStore = useEventStore()
const { fetchEventById, updateEvent, fetchTimeSlots } = eventsStore
const staffList = ref<Staff[]>([])
const formData = reactive<Omit<BookingForm, 'uuid'> & Record<string, unknown>>({
  customer_id: '',
  staff_id: '',
  service_id: '',
  note: '',
  tags: [],
  start: '',
  end: '',
  source: 'calendar',
})

const formRules = {
  staff_id: {
    required,
  },
}
const vFrom$ = useVuelidate(formRules, formData)

const state = reactive<{
  timeSlots: TimeSlot[]
  loading: boolean
  processing: boolean
  booking?: Booking
  timeSlotProcessing: boolean
  pickDate: string
  startDate: string
  isHoliday: boolean
  timeSlotsByDay: TimeSlot[][]
}>({
  timeSlots: [],
  loading: false,
  processing: false,
  timeSlotProcessing: false,
  pickDate: '',
  isHoliday: false,
  startDate: '',
  timeSlotsByDay: [],
})

const rules = {
  startDate: {
    required,
  },
}
const v$ = useVuelidate(rules, state)

const setBookingDate = (date: string) => {
  state.pickDate = dayjs(date).format('YYYY/MM/DD HH:mm').toString() || ''
}

const setBookingTimeSlot = (slot: TimeSlot) => {
  state.startDate = slot.fullDate
}

const bookingDate = computed(() => state.pickDate)

watch(
  () => [bookingDate.value, formData.staff_id],
  (value, oldValue) => {
    const [date, staff] = value
    const [oldDate, oldStaff] = oldValue
    if (!date || !staff.length || (!oldDate && !oldStaff))
      return

    const totalDuration = state.booking?.duration
    const service = state.booking?.service.uuid

    fetchSlots(
      { startDate: dayjs(date).format('YYYY-MM-DD') },
      staff,
      service,
      totalDuration,
    ).then((hasSlots) => {
      state.isHoliday = !hasSlots
      state.startDate = ''
    })
  },
)

const disableDateSelection = computed(
  () => !(formData.staff_id.length && state.pickDate.length),
)

const totalDuration = computed(() => {
  if (!state.startDate)
    return ''
  return state.booking?.duration
})

const handleSubmit = async () => {
  v$.value.$touch()
  vFrom$.value.$touch()
  if (v$.value.$invalid || vFrom$.value.$invalid || state.processing)
    return false
  state.processing = true
  if (state.booking?.uuid) {
    const payload = {
      ...formData,
      customer_id: state.booking.customer.uuid,
      duration: totalDuration.value,
      service_id: state.booking.service.uuid,
      startDate: state.startDate,
      end: dayjs(state.startDate)
        .clone()
        .add(Number(totalDuration.value), 'minutes')
        .format('YYYY/MM/DD HH:mm'),
      start: state.startDate,
      bookingId: state.booking.uuid,
    }
    return updateEvent(payload as unknown as any)
      .then((res) => {
        v$.value.$reset()
        vFrom$.value.$reset()
        emits('updated', res.data)
        emits('closed')
      })
      .finally(() => {
        state.processing = false
      })
  }
}
const { fetchServicesStaff, toggleServicesStaff, fetchServiceById } = useServicesStore()
const fetchBookingDetails = async () => {
  try {
    state.loading = true
    const res = await fetchEventById(props.eventId as string)
    state.pickDate = res.start
      ? dayjs(res.start).format('YYYY-MM-DD')
      : dayjs().format('YYYY-MM-DD')
    formData.staff_id = res.staff.uuid
    formData.start = res.start
    state.booking = res
    const service = res.service.uuid
    const durations = res.service.duration
    try {
      const serviceWithStaff = await fetchServiceById(res.service.uuid)
      staffList.value = serviceWithStaff.staff || []
    }
    catch (error) {
      console.error('Failed to fetch services staff:', error)
    }

    try {
      await fetchSlots(
        { startDate: state.pickDate },
        formData.staff_id,
        service,
        durations,
      )
      if (
        !staffList.value.map(item => item.id).includes(formData.staff_id)
      )
        staffList.value.push(state.booking.staff)
    }
    catch (error) {
      console.error('Failed to fetch time slots:', error)
    }
  }
  finally {
    state.loading = false
  }
}

const VISIBLE_DAYS = ref(2)
const SCREEN_BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1991,
}
const updateVisibleDays = () => {
  const width = window.innerWidth
  if (width < SCREEN_BREAKPOINTS.sm)
    VISIBLE_DAYS.value = 1
  else
    VISIBLE_DAYS.value = 2
}

onMounted(async () => {
  await fetchBookingDetails()
  updateVisibleDays()
  window.addEventListener('resize', updateVisibleDays)
})
onUnmounted(() => {
  window.removeEventListener('resize', updateVisibleDays)
})

const fetchSlots = async (options: { nextAvailability?: boolean; startDate?: string } = {}, staffId?, serviceId?, duration?) => {
  try {
    let startDateToFetch: string
    const { nextAvailability = false, startDate } = options
    state.timeSlotProcessing = true

    if (nextAvailability)
      startDateToFetch = dayjs().add(1, 'day').format('YYYY-MM-DD')
    else if (startDate)
      startDateToFetch = startDate
    else
      startDateToFetch = dayjs().format('YYYY-MM-DD')

    const queries = {
      staffIds: [staffId || formData.staff_id],
      itemIds: [serviceId || state.booking?.service?.uuid],
      durations: [duration || state.booking?.duration],
    }

    if (!queries.staffIds[0] || !queries.itemIds[0])
      return false

    const { data } = await fetchTimeSlots(
      startDateToFetch,
      queries.staffIds,
      queries.itemIds,
      queries.durations,
      undefined, // quantities not needed for booking modal
      VISIBLE_DAYS.value,
    )

    state.timeSlotsByDay = Object.values(data.data)
    return Object.values(state.timeSlotsByDay).some(slots => slots.length > 0)
  }
  catch (error) {
    console.error('Error fetching time slots:', error)
    state.timeSlotsByDay = []
    return false
  }
  finally {
    state.timeSlotProcessing = false
  }
}

const timeSlotsList = computed(() => {
  return state.timeSlotsByDay
})

const showBookedSlots = ref(false)
</script>

<template>
  <Modal
    :open="isOpen"
    :dir="getLocale(locale)?.direction"
    panel-classes="relative w-full  max-w-2xl p-0 text-left align-middle bg-white rounded-2xl shadow-xl transition-all transform sm:px-6 px-2"
    @close="$emit('closed')"
  >
    <overlay-loader v-if="state.loading" :full-screen="false" />
    <button
      class="absolute top-4 left-4 z-10"
      aria-label="Close"
      @click="$emit('closed')"
    >
      <img src="@/assets/icons/actions/close.svg" alt="Close" class="w-9 h-9">
    </button>
    <div class="px-2 pt-8 pb-2 sm:px-8">
      <h3
        v-if="!state.loading"
        class="mb-6 text-2xl font-bold leading-6 text-center text-gray-900"
      >
        {{
          eventId
            ? `${$t('modalHeader.updateBooking')} #${state.booking?.booking_number ?? ''}`
            : $t('modalHeader.createBooking')
        }}
      </h3>
      <form class="flex flex-col gap-6 items-center">
        <form-group :validation="vFrom$" name="staff_id">
          <template #default="{ attrs }">
            <SelectInput
              id="staff"
              v-model="formData.staff_id"
              :label="$t('booking.staff')"
              required
              v-bind="attrs"
            >
              <option
                v-for="staff of staffList || []"
                :key="staff.id"
                :value="staff.id"
              >
                {{ staff.name }}
              </option>
            </SelectInput>
          </template>
        </form-group>
        <div class="w-full  bg-[#F8F8F8] rounded-lg p-6 ">
          <form-group :validation="v$" name="startDate">
            <template #default="{ attrs }">
              <TimeSlotsGrid
                v-bind="attrs"
                :time-slots-list="timeSlotsList"
                :loading="state.timeSlotProcessing"
                :selected-time="state.startDate"
                :show-booked="showBookedSlots"
                :visible-days="VISIBLE_DAYS"
                @update-time="setBookingTimeSlot"
                @week-change="fetchSlots({ startDate: $event })"
              />
            </template>
          </form-group>
        </div>
        <BaseButton
          class="mb-4 w-full"
          :processing="state.processing"
          type="button"
          @click="handleSubmit"
        >
          <span>{{ $t('form.update') }}</span>
        </BaseButton>
      </form>
    </div>
  </Modal>
</template>
