<script setup lang="ts">
import {
  ClockIcon,
} from '@heroicons/vue/20/solid'
import {
  eachDayOfInterval,
  endOfMonth,
  endOfWeek,
  format, isSameDay,
  isSameMonth, isToday,
  parse, parseISO,
  startOfWeek,
} from 'date-fns'
import { storeToRefs } from 'pinia'
import type { PropType } from 'vue'
import type { Event } from '@/types'

const props = defineProps({
  events: {
    type: Array as PropType<Event[]>,
    default() {
      return []
    },
  },
  currentDate: {
    type: Date,
    default: () => new Date(),
  },
})
const router = useRouter()
const authStore = useAuthStore()
const { getUserInfo } = storeToRefs(authStore)

const currentMonth = computed(() => format(props.currentDate, 'MMM-yyyy'))
const firstDayOfMonth = computed(() => parse(currentMonth.value, 'MMM-yyyy', new Date()))
const selectedDay = null

const weekdays = computed(() => eachDayOfInterval({
  start: startOfWeek(props.currentDate, { weekStartsOn: getUserInfo.value.tenant.start_of_week }),
  end: endOfWeek(props.currentDate, { weekStartsOn: getUserInfo.value.tenant.start_of_week }),
}))

const monthDays = computed(() => eachDayOfInterval({
  start: startOfWeek(firstDayOfMonth.value, { weekStartsOn: getUserInfo.value.tenant.start_of_week }),
  end: endOfWeek(endOfMonth(firstDayOfMonth.value), { weekStartsOn: getUserInfo.value.tenant.start_of_week }),
}))

const days = computed(() => monthDays.value.map(day => ({
  date: format(day, 'yyyy-MM-dd'),
  isCurrentMonth: isSameMonth(day, firstDayOfMonth.value),
  isToday: isToday(day),
  isSelected: isSameDay(selectedDay?.date, day),
  events: props.events.filter(event =>
    isSameDay(parseISO(event.start_time), day),
  ),
})))

const openEventDetail = (event) => {
  router.push({ name: 'booking', params: { id: event.uuid } })
}
</script>

<template>
  <div>
    <div class="shadow ring-1 ring-black ring-opacity-5 lg:flex lg:flex-auto lg:flex-col">
      <div class="grid grid-cols-7 gap-px border-b border-gray-300 bg-gray-200 text-center text-xs font-semibold leading-6 text-gray-700 lg:flex-none">
        <div v-for="day in weekdays" class="bg-white py-2">
          {{ $t(`calendar.day_short.${format(day, 'EEE')}`) }}
        </div>
      </div>
      <div class="flex bg-gray-200 text-xs leading-6 text-gray-700 lg:flex-auto">
        <div class="hidden w-full lg:grid lg:grid-cols-7 lg:grid-rows-6 lg:gap-px">
          <div
            v-for="day in days"
            :key="day.date"
            class="lg:h-0 lg:min-h-[110px] relative px-3 py-2"
            :class="[day.isCurrentMonth ? 'bg-white' : 'bg-gray-50 text-gray-500']"
            @click="selectedDay = day"
          >
            <time :datetime="day.date" :class="day.isToday ? 'flex h-6 w-6 items-center justify-center rounded-full bg-indigo-600 font-semibold text-white' : undefined">{{ day.date.split('-').pop().replace(/^0/, '') }}</time>
            <ol v-if="day.events.length > 0" class="mt-2">
              <li v-for="event in day.events.slice(0, 2)" :key="event.uuid" @click="openEventDetail(event)">
                <a :href="event.href" class="group flex">
                  <p class="flex-auto truncate font-medium text-gray-900 group-hover:text-indigo-600">
                    {{ event.name }}
                  </p>
                  <time :datetime="event.start_time" class="ml-3 hidden flex-none text-gray-500 group-hover:text-indigo-600 xl:block">{{ event.start }}</time>
                </a>
              </li>
              <li v-if="day.events.length > 2" class="text-gray-500">
                + {{ day.events.length - 2 }} more
              </li>
            </ol>
          </div>
        </div>
        <div class="isolate grid w-full grid-cols-7 grid-rows-6 gap-px lg:hidden">
          <button
            v-for="day in days"
            :key="day.date"
            type="button"
            class="flex h-14 flex-col px-3 py-2 hover:bg-gray-100 focus:z-10" :class="[day.isCurrentMonth ? 'bg-white' : 'bg-gray-50', (day.isSelected || day.isToday) && 'font-semibold', day.isSelected && 'text-white', !day.isSelected && day.isToday && 'text-indigo-600', !day.isSelected && day.isCurrentMonth && !day.isToday && 'text-gray-900', !day.isSelected && !day.isCurrentMonth && !day.isToday && 'text-gray-500']"
          >
            <time :datetime="day.date" class="ml-auto" :class="[day.isSelected && 'flex h-6 w-6 items-center justify-center rounded-full', day.isSelected && day.isToday && 'bg-indigo-600', day.isSelected && !day.isToday && 'bg-gray-900']">{{ day.date.split('-').pop().replace(/^0/, '') }}</time>
            <span class="sr-only">{{ day.events.length }} events</span>
            <span v-if="day.events.length > 0" class="-mx-0.5 mt-auto flex flex-wrap-reverse">
              <span v-for="event in day.events" :key="event.uuid" class="mx-0.5 mb-1 h-1.5 w-1.5 rounded-full bg-gray-400" @click="openEventDetail(event)" />
            </span>
          </button>
        </div>
      </div>
    </div>
    <div v-if="selectedDay?.events.length > 0" class="px-4 py-10 sm:px-6 lg:hidden">
      <ol class="divide-y divide-gray-100 overflow-hidden rounded-lg bg-white text-sm shadow ring-1 ring-black ring-opacity-5">
        <li v-for="event in selectedDay.events" :key="event.id" class="group flex p-4 pr-6 focus-within:bg-gray-50 hover:bg-gray-50">
          <div class="flex-auto">
            <p class="font-semibold text-gray-900">
              {{ event.name }}
            </p>
            <time :datetime="event.start_time" class="mt-2 flex items-center text-gray-700">
              <ClockIcon class="mr-2 h-5 w-5 text-gray-400" aria-hidden="true" />
              {{ event.time }}
            </time>
          </div>
          <a :href="event.href" class="ml-6 flex-none self-center rounded-md bg-white px-3 py-2 font-semibold text-gray-900 opacity-0 shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400 focus:opacity-100 group-hover:opacity-100">Edit<span class="sr-only">, {{ event.name }}</span></a>
        </li>
      </ol>
    </div>
  </div>
</template>
