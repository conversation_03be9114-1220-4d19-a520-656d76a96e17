<script setup lang="ts">
import type { PropType } from 'vue'
import 'vue-multiselect/dist/vue-multiselect.css'
import {
  ArrowTopRightOnSquareIcon,
  CalendarIcon,
  ClipboardDocumentIcon,
  ComputerDesktopIcon,
  GlobeAltIcon,
} from '@heroicons/vue/24/solid'

import { PlusIcon } from '@heroicons/vue/24/outline'
import { formatDate, formatTime } from '@/composables/dateFormat'
import type { Booking } from '@/types'
const props = defineProps({
  booking: {
    type: Object as PropType<Booking>,
    default: () => ({}),
  },
  hidePrice: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'closed',
  'openPaymentModal',
  'openCancelModal',
  'refresh',
  'showRecurtingAppointmentModal',
  'openMainRecurringAppointmentModal',
])

const { updateBookingStatus } = useEventStore()

const { booking, hidePrice } = toRefs(props)
const { t } = useI18n()

const router = useRouter()

const currentTab = ref('details')

const processingUpdateBookingStatus = ref(false)

const tabs = ref([
  {
    label: t('details'),
    value: 'details',
    href: '#',
    icon: 'InformationCircleIcon',
    show: true,
  },
  {
    label: t('recurring_appointments'),
    value: 'recurringAppointments',
    href: '#',
    icon: 'CalendarDaysIcon',
    show: true,
  },
])
const hasRecurringAppointemnts = computed(
  () =>
    Boolean(!booking.value.is_recurring)
    && booking.value.recurring_appointemnts.length > 0,
)
const updateTabVisibility = (tabValue, condition) => {
  tabs.value = tabs.value.map(tab =>
    tab.value === tabValue ? { ...tab, show: condition } : tab,
  )
}

watch(
  hasRecurringAppointemnts,
  newValue => updateTabVisibility('recurringAppointments', newValue),
  { immediate: true },
)

const recurringTableHeader = computed(() => {
  return [
    {
      title: t('booking.booking_number'),
    },
    {
      title: t('status'),
    },
    {
      title: t('dashboard.booking.date'),
    },
    {
      title: t('price'),
    },
    {
      title: t('source'),
    },
  ]
})

const changeStatus = async (status: 'confirmed' | 'completed' | 'no-show') => {
  if (status === booking.value.status)
    return
  try {
    processingUpdateBookingStatus.value = true
    await updateBookingStatus(booking.value.uuid, status)
    emit('refresh', booking.value.uuid)
  }
  finally {
    processingUpdateBookingStatus.value = false
  }
}
function showRecurtingAppointment(item: Booking) {
  emit('showRecurtingAppointmentModal', item.uuid)
}
const copyToClipboard = (text: string) => {
  if (navigator?.clipboard)
    navigator.clipboard.writeText(text)
}
</script>

<template>
  <div>
    <tab
      v-if="booking"
      v-model="currentTab"
      :tabs="tabs.filter((tab) => tab.show)"
    />
    <template v-if="currentTab === 'details'">
      <overlay-loader
        v-if="processingUpdateBookingStatus"
        :full-screen="false"
      />

      <div>
        <div class="flex gap-2 items-center py-3 border-b border-stone-100">
          <label
            for="staff"
            class="block text-sm font-bold text-neutral-500 w-15"
          >{{ $t("booking.staff") }}
          </label>
          <div class="relative items-center focus-within:z-10">
            {{ booking?.staff.name }}
          </div>
        </div>
        <div class="flex gap-2 items-center py-3 border-b border-stone-100">
          <label
            for="staff"
            class="block text-sm font-bold text-neutral-500 w-15"
          >{{ $t("booking.service") }}
          </label>
          <div class="flex relative gap-3 items-center focus-within:z-10">
            <p>{{ booking?.service.name }}</p>
            <span
              :style="
                `background-color:${booking?.service.color}`
              " class="block w-3 h-3 rounded"
            />
          </div>
        </div>
        <div class="flex gap-2 items-center py-3 border-b border-stone-100">
          <label
            for="staff"
            class="block text-sm font-bold text-neutral-500 w-15"
          >{{ $t("booking.time") }}
          </label>
          <div class="relative items-center focus-within:z-10" dir="auto">
            {{ booking ? formatDate(booking?.start) : "-" }}
            <span class="font-semibold text-primary-800">
              {{ formatTime(booking?.start) }}
            </span>
            :
            {{ booking ? formatDate(booking?.end) : "-" }}
            <span class="font-semibold text-primary-800">
              {{ formatTime(booking?.end) }}
            </span>
          </div>
        </div>

        <div
          v-if="!!booking?.google_meet_link"
          class="flex gap-2 items-center py-3 border-b border-stone-100"
        >
          <label
            for="staff"
            class="block text-sm font-bold text-neutral-500 w-15"
          >{{ $t("booking.meeting_link") }}
          </label>
          <div class="relative items-center focus-within:z-10">
            <a :href="booking.google_meet_link" target="_blank">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                aria-hidden="true"
                class="w-5 h-5 text-primary"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244"
                />
              </svg>
            </a>
          </div>
        </div>

        <div
          v-if="booking?.is_recurring"
          class="flex gap-2 items-center py-3 border-b border-stone-100"
        >
          <label
            for="staff"
            class="block text-sm font-bold text-neutral-500 w-15"
          >{{ $t("main_appointment") }}
          </label>
          <div class="relative items-center focus-within:z-10">
            <button
              class="flex gap-1 items-center font-semibold"
              @click="
                $emit('openMainRecurringAppointmentModal', booking.is_recurring)
              "
            >
              {{ $t("main_appointment_details") }}
              <span>
                <ArrowTopRightOnSquareIcon
                  class="w-5 h-5 font-semibold text-primary"
                />
              </span>
            </button>
          </div>
        </div>
      </div>
      <div
        class="flex mb-3 justify-center items-center px-10 mt-2 sm:flex-row flex-col content-center min-w-[100px] gap-1"
      >
        <BaseButton
          class="p-3 bg-green-200 text-green-900 font-semibold flex-1 duration-150 border-2 !m-0 !rounded-none hover:bg-green-400 w-full"
          custome-bg="bg-[#4CAF5080]"
          :class="{
            'outline outline-[#4CAF50]': booking.status == 'confirmed',
          }"
          :default-style="false"
          @click="changeStatus('confirmed')"
        >
          {{ $t("confirmed") }}
        </BaseButton>

        <BaseButton
          class="p-3 text-blue-800 font-semibold flex-1 duration-150 border-2 !m-0 !rounded-none w-full hover:bg-blue-400"
          custome-bg="bg-[#2196f380]"
          :class="{
            'outline outline-[#2196F3] ': booking.status == 'completed',
          }"
          :default-style="false"
          @click="changeStatus('completed')"
        >
          {{ $t("completed") }}
        </BaseButton>
        <BaseButton
          class="p-3 text-red-900 font-semibold flex-1 duration-150 border-2 !m-0 !rounded-none w-full hover:bg-red-400"
          custome-bg="bg-[#F4433680]"
          :class="{
            '  outline outline-[#F44336]': booking.status == 'no-show',
          }"
          :default-style="false"
          @click="changeStatus('no-show')"
        >
          {{ $t("no-show") }}
        </BaseButton>
      </div>
      <div class="flex">
        <!-- <button
          v-if="['confirmed', 'completed'].includes(booking.status) && booking.model_type !=='package'"
          class="p-4 w-2/3 w-full font-semibold text-white rounded-sm duration-150 rtl:ml-1 bg-primary-600 hover:bg-primary-700" @click="paymentHandler"
        >
          {{ !booking?.invoice ? $t('pos.checkout') : $t('order_page') }}
        </button> -->

        <button
          v-if="booking.status == 'confirmed'"
          :class="[
            booking.model_type !== 'package'
              ? 'w-full p-4 font-semibold text-red-600 bg-white border border-red-600 rounded-sm hover:border-red-700 hover:text-red-700'
              : 'w-full p-4 font-semibold text-red-600 bg-white border border-red-600 rounded-sm hover:border-red-700 hover:text-red-700',
          ]"
          @click="$emit('openCancelModal')"
        >
          <span>{{ $t("bookingItems.cancel") }}</span>
        </button>
      </div>
      <div v-if="booking.reference_id" class="text-xs text-gray-500">
        <div class="flex items-center">
          <span>{{ $t("reference_id") }}: {{ booking.reference_id }}</span>
          <button
            class="p-2 text-gray-400 hover:text-gray-600"
            title="Copy reference ID"
            @click="copyToClipboard(booking.reference_id)"
          >
            <ClipboardDocumentIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </template>

    <template
      v-if="
        currentTab === 'recurringAppointments'
          && !Boolean(booking.is_recurring)
          && booking.recurring_appointemnts.length > 0
      "
    >
      <div
        v-if="booking?.recurring_appointemnts"
      >
        <generic-table
          :headers="recurringTableHeader"
          :data="booking.recurring_appointemnts"
          tr-class="cursor-pointer"
          :on-row-click="showRecurtingAppointment"
        >
          <template #row="{ item }">
            <grid-td> # {{ item?.booking_number }} </grid-td>
            <grid-td :default-style="false" class="flex items-center py-2">
              <booking-status
                class="flex items-center px-2 py-1 rounded-md"
                :book-status="item?.status"
              />
            </grid-td>
            <grid-td>
              <div class="text-gray-900">
                {{ formatDate(item.date) }}
              </div>
            </grid-td>
            <grid-td>
              <price-format
                :form-data="{
                  price: item?.price,
                  currency: item?.currency || '',
                }"
              />
            </grid-td>

            <grid-td>
              <ComputerDesktopIcon
                v-if="item.source == 'pos'"
                class="inline-block w-5 h-5"
              />
              <GlobeAltIcon
                v-else-if="item.source == 'booking-page'"
                class="inline-block w-5 h-5"
              />
              <CalendarIcon
                v-else-if="item.source == 'calendar'"
                class="inline-block w-5 h-5"
              />
            </grid-td>
          </template>
        </generic-table>
      </div>
    </template>

    <template v-if="currentTab === 'history'">
      <booking-activity-log :activities="booking?.activity_log" />
    </template>
  </div>
</template>
