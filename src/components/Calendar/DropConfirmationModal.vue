<script setup lang="ts">
import dayjs from 'dayjs'
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import type { PropType } from 'vue'
import { formatDateAndTime } from '@/composables/dateFormat'
import type { EventData } from '@/types'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  eventData: {
    type: Object as PropType<EventData>,
    default: () => ({}),
  },
})
const emit = defineEmits(['closed'])

const eventsStore = useEventStore()
const { moveEvent } = eventsStore

const processing = ref(false)
const target = computed(() => props.eventData.start ? 'start' : 'end')

const nope = () => {
  props.eventData.info.revert()
  emit('closed')
}
const confirm = async () => {
  processing.value = true
  const payload = {
    start: dayjs(props.eventData.start).format('YYYY/MM/DD HH:mm'),
    end: dayjs(props.eventData.end).format('YYYY/MM/DD HH:mm'),
    staffId: props.eventData.staffId,
    bookingId: props.eventData.uuid,
  }
  const res = await moveEvent(payload)
    .then((_) => {
      emit('closed')
    }).finally(() => {
      processing.value = false
    })
}
</script>

<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" class="relative z-10" @close="$emit('closed')">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div
          class="flex min-h-full items-center justify-center p-4 text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-xl transform overflow-hidden rounded-2xl bg-white p-6   align-middle shadow-xl transition-all"
            >
              <DialogTitle
                as="h3"
                class="text-2xl text-center font-medium leading-6 text-gray-900"
              >
                {{ $t('modalHeader.moving_confirmation') }}
              </DialogTitle>
              <div class="mt-2">
                <overlay-loader v-if="processing" />

                <div class="mt-5">
                  {{ $t(`Confirm re-schedule booking's time to`) }} <b>{{ formatDateAndTime(eventData[target]) }}</b>
                </div>
                <div class="flex gap-2 items-center justify-end w-full mt-5">
                  <button
                    type="button"
                    class="inline-flex justify-center rounded-md border border-transparent bg-red-100 px-4 py-2 text-sm font-medium text-red-900 hover:bg-red-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2"
                    @click="nope"
                  >
                    {{ $t('cancel') }}
                  </button>
                  <button
                    type="button"
                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                    @click="confirm"
                  >
                    {{ $t('btn_confirm') }}
                  </button>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
