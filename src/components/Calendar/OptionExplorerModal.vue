<script setup lang="ts">
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { XCircleIcon } from '@heroicons/vue/24/outline'
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['close', 'create-appointment', 'create-order'])
const { t } = useI18n()
const authStore = useAuthStore()
const { getUserInfo } = storeToRefs(authStore)
const buttons = computed(() => {
  return markRaw([
    {
      title: t('new_order'),
      action: () => emits('create-order'),
      tag: t('multi_service'),
    },
    ...(getUserInfo.value?.tenant?.allowed_booking_function
      ? [
          {
            title: t('new_appointment'),
            action: () => emits('create-appointment'),
            tag: t('multi_service'),
          },
        ]
      : []),
  ])
})
</script>

<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" class="relative z-20" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="overflow-y-auto fixed inset-0">
        <div
          class="flex justify-center items-center p-4 min-h-full text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="overflow-hidden relative p-6 w-full max-w-xl text-left align-middle bg-white rounded-2xl shadow-xl transition-all transform"
            >
              <XCircleIcon
                class="fixed top-4 left-4 w-9 h-9 text-red-400 cursor-pointer close ms-auto"
                aria-hidden="true"
                @click="$emit('close')"
              />
              <DialogTitle class="text-lg font-bold text-center text-gray-900">
                <!-- Which one do you want to create? -->
                {{ $t("which_one_do_you_want_to_create") }}
              </DialogTitle>
              <div>
                <button
                  v-for="btn in buttons"
                  class="flex relative flex-col gap-2 justify-center items-center px-4 py-4 mx-auto mt-4 w-full max-w-md font-bold text-white bg-gray-700 rounded transition-all ease-in-out hover:bg-gray-700 hover:scale-105 duration-400"
                  @click="btn.action"
                >
                  {{ btn.title }}
                  <span
                    class="flex absolute bottom-1 left-1 justify-start items-center px-4 py-1 text-xs text-white rounded-md bg-primary-800 w-fit"
                  >
                    {{ btn.tag }}
                  </span>
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
