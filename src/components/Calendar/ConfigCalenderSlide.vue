<script setup lang="ts">
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  configCalendarInputs: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['close', 'refresh'])
const { updateGeneralCustomeSettings } = useAccountSettingStore()
const processing = ref(false)
const submit = async (formData: {}) => {
  try {
    const payload = {
      setting: {
        calendar: {
          ...formData,
        },
      },
    }
    processing.value = true
    await updateGeneralCustomeSettings(payload, 'calendar')
    emit('refresh')
    emit('close')
  }
  finally {
    processing.value = false
  }
}

const { locale } = useI18n()

const { getLocale } = storeToRefs(useLocalesStore())
</script>

<template>
  <TransitionRoot as="template" :show="isOpen">
    <Dialog as="div" class="relative z-10" @close="$emit('close')">
      <div class="fixed inset-0" />

      <div class="fixed inset-0 overflow-hidden">
        <div class="absolute inset-0 overflow-hidden">
          <div
            class="pointer-events-none fixed inset-y-0 flex max-w-md end-0 ps-10"
            :dir="getLocale(locale)?.direction"
          >
            <TransitionChild
              as="template"
              enter="transform transition ease-in-out duration-500 sm:duration-700"
              enter-to="translate-x-0"
              leave="transform transition ease-in-out duration-500 sm:duration-700"
              leave-from="-translate-x-0"
              :enter-from="
                getLocale(locale)?.direction === 'rtl'
                  ? ' -translate-x-full'
                  : ' translate-x-full'
              "
              :leave-to="
                getLocale(locale)?.direction === 'rtl'
                  ? '-translate-x-full'
                  : ' translate-x-full'
              "
            >
              <DialogPanel class="pointer-events-auto w-screen max-w-md">
                <div class="flex h-full flex-col bg-white shadow-xl">
                  <div class="bg-primary-900 px-4 py-6 sm:px-6">
                    <div class="flex items-center justify-between">
                      <DialogTitle
                        class="text-base font-semibold leading-6 text-white"
                      >
                        {{ $t("calendar_configuration") }}
                      </DialogTitle>
                      <div class="ml-3 flex h-7 items-center">
                        <button
                          type="button"
                          class="rounded-md bg-black-700 text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-white"
                          @click="$emit('close')"
                        >
                          <span class="sr-only">Close panel</span>
                          <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="relative flex-1 px-4 py-6 sm:px-6">
                    <div class="flex flex-col h-full relative">
                      <overlay-loader v-if="processing" :full-screen="false" />
                      <customForm
                        :inputs="configCalendarInputs"
                        error-object-name="setting.calendar"
                        class="h-full"
                        @submit="submit"
                      />
                    </div>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
