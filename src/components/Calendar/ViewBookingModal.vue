<script setup lang="ts">
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { LinkIcon, PencilIcon, XCircleIcon } from '@heroicons/vue/24/outline'
import { PhoneArrowUpRightIcon } from '@heroicons/vue/24/solid'
import { ArrowPathIcon } from '@heroicons/vue/20/solid'
import type { Booking, Staff, TimeSlot } from '@/types'
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  booking: {
    type: Object as PropType<Booking>,
    required: true,
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
  hidePrice: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  'closed',
  'openEditModal',
  'openPaymentModal',
  'openCancelModal',
  'refresh',
  'openRecurringModal',
  'openRecurringAppointmentModal',
  'openMainRecurringAppointmentModal',
  'showOrderModal',
])
const { loading } = toRefs(props)
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const { booking, isOpen } = toRefs(props)

// const hasOrder = computed(() => Boolean(booking.value.invoice));

const openPaymentModal = (uuid: string) => {
  emits('openPaymentModal', uuid)
}
const completeButtonRef = ref(null)

const cancelModal = ref(false)
const openCancelModal = () => {
  cancelModal.value = true
}
const recurringModal = ref(false)
</script>

<template>
  <TransitionRoot :show="isOpen" as="template">
    <Dialog as="div" class="relative z-50" :initial-focus="completeButtonRef">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="overflow-y-auto fixed inset-0">
        <div
          class="flex justify-center items-center p-4 min-h-full text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="relative w-full max-w-xl p-6 pb-4 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl min-h-[200px] rounded-2xl"
            >
              <overlay-loader v-if="loading" :full-screen="false" />
              <DialogTitle
                v-if="!!booking.uuid"
                as="h3"
                class="text-2xl font-medium leading-6 text-gray-900"
              >
                <div
                  class="flex justify-between"
                  :class="`${$i18n.locale === 'ar' ? 'flex-row-reverse' : ''}`"
                >
                  <p v-if="booking.customer">
                    <router-link
                      :to="{
                        name: 'customer',
                        params: { id: booking.customer.uuid },
                      }"
                      class="flex flex-row gap-1 items-center bg-transparent rtl:flex-row-reverse focus:outline-none focus:ring-0"
                    >
                      {{ booking.customer.first_name }}
                      {{ booking.customer.last_name }}
                      <span>
                        <LinkIcon class="w-5 h-5 text-primary" />
                      </span>
                    </router-link>
                  </p>
                  <p v-else>
                    {{ $t("customer_deleted") }}
                  </p>
                  <div>
                    <XCircleIcon
                      class="w-9 h-9 text-red-400 cursor-pointer close ms-auto"
                      aria-hidden="true"
                      @click="$emit('closed')"
                    />
                  </div>
                </div>
                <div
                  v-if="booking.customer?.phone"
                  :class="`text-base	flex items-center gap-3  flex-wrap ${
                    $i18n.locale === 'ar' ? 'flex-row-reverse' : ''
                  }`"
                >
                  <a
                    class="flex items-center py-1"
                    :href="`tel:${booking.customer.phone}`"
                  >
                    <PhoneArrowUpRightIcon class="mx-2 w-5 h-5" />
                    {{ booking.customer?.phone }}</a>
                </div>
                <div
                  :class="`text-sm flex items-center gap-3 flex-wrap ${
                    $i18n.locale === 'ar' ? 'flex-row-reverse' : ''
                  }`"
                >
                  <p>
                    {{
                      `${$t("modalHeader.viewBooking")} #${
                        booking.booking_number
                      } `
                    }}
                  </p>

                  <ArrowPathIcon
                    v-if="booking.is_recurring"
                    class="block w-5 h-5 text-black"
                  />
                  <button
                    v-if="
                      ['confirmed', 'completed', 'unapproved'].includes(
                        booking.status,
                      )
                    "
                    class="flex gap-1 p-2 text-xs text-white rounded-md duration-150 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-300"
                    @click="$emit('openEditModal')"
                  >
                    <div class="flex gap-2 justify-center items-center">
                      <span>{{ $t("form.edit") }}</span>
                      <PencilIcon class="w-[1rem]" />
                    </div>
                  </button>
                  <button
                    v-if="
                      !Boolean(booking.is_recurring)
                        && !(booking.recurring_appointemnts?.length > 0)
                    "
                    class="flex gap-1 p-2 text-xs text-white bg-green-700 rounded-md duration-150 hover:bg-green-800"
                    @click="recurringModal = true"
                  >
                    <div class="flex gap-2 justify-center items-center">
                      <ArrowPathIcon class="block w-5 h-5" />
                      <span>{{ $t("recurring_appointment") }}</span>
                    </div>
                  </button>
                </div>
                <!-- <button
                  class="block px-3 py-2 mt-2 text-sm bg-gray-300 rounded-lg cursor-pointer text-end w-fit ms-auto"
                  v-if="hasOrder"
                  @click="emits('closed')"
                >
                  {{ `${$t("order_booking")}` }}
                </button> -->
              </DialogTitle>
              <button
                ref="completeButtonRef"
                className="h-0 w-0 overflow-hidden"
              />
              <div>
                <booking-view-comp
                  v-if="!!booking.uuid"
                  :hide-price="hidePrice"
                  :dir="getLocale(locale)?.direction"
                  :booking="booking"
                  :class="[
                    getLocale(locale)?.direction === 'rtl'
                      ? 'text-right'
                      : 'text-left',
                  ]"
                  @closed="$emit('closed')"
                  @open-payment-modal="openPaymentModal"
                  @open-cancel-modal="openCancelModal"
                  @refresh="(ev: any) => $emit('refresh', ev)"
                  @show-recurting-appointment-modal="
                    $emit('openRecurringAppointmentModal', $event)
                  "
                  @open-main-recurring-appointment-modal="
                    $emit('openMainRecurringAppointmentModal', $event)
                  "
                />
              </div>
              <cancel-booking-modal
                v-if="cancelModal"
                :is-open="cancelModal"
                :booking="booking"
                @closed="cancelModal = false"
                @refresh="$emit('refresh')"
              />
              <Appointment-recurring-modal
                v-if="recurringModal"
                :is-open="recurringModal"
                :appointment-id="booking?.uuid"
                @close="recurringModal = false"
                @refresh="$emit('refresh')"
              />
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
