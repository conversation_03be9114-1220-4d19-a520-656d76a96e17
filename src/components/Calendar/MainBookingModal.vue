<script setup lang="ts">
import type { PropType } from 'vue'
import type { Booking, TimeSlot } from '@/types'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: String,
    required: true,
  },
  orderId: {
    type: String,
    default: false,
  },
  selectedSlot: {
    type: Object as PropType<TimeSlot>,
    default: () => ({}),
  },
  hidePrice: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  'refreshEvents',
  'closed',
  'startLoading',
  'endLoading',
  'showOrderModal',
])
const eventsStore = useEventStore()
const { tableData } = useStaff()
const { fetchEventById } = eventsStore
const { fetchSingleOrder } = useOrder()

const { itemId, orderId } = toRefs(props)

const processing = ref(false)
const state = reactive<{
  booking?: Booking
  startDate?: string
  selectedTransaction?: any
}>({})

const bookingModalOpen = reactive({
  bookingModal: false,
  requestModal: false,
  cancelModal: false,
  paymentModal: false,
  orderModal: false,
  setAppointmentModal: false,
  transactionModal: false,
})

const fetchItemData = (
  item_id = '',
  { refreshOrder }: { refreshOrder: boolean } = { refreshOrder: false },
) => {
  processing.value = true
  return fetchEventById(item_id || itemId.value)
    .then(async (res) => {
      state.booking = res
      if (refreshOrder && orderId.value) {
        console.log('refreshOrder', refreshOrder)
        await fetchSingleOrder(orderId.value)
      }
    })
    .finally(() => {
      processing.value = false
    })
}

watch(
  () => [itemId.value, orderId.value],
  ([itemId, orderId]) => {
    if (itemId && orderId) {
      bookingModalOpen.orderModal = true
      fetchItemData()
    }
    else if (itemId) {
      bookingModalOpen.bookingModal = true
      bookingModalOpen.orderModal = false
      fetchItemData()
    }
    else if (orderId) {
      bookingModalOpen.orderModal = true
      // fetchSingleOrder(orderId);
    }
    else {
      state.booking = undefined
      bookingModalOpen.bookingModal = false
    }
  },
  { immediate: true },
)

const closeModal = () => {
  emits('closed')
  bookingModalOpen.bookingModal = false
  bookingModalOpen.requestModal = false
  bookingModalOpen.cancelModal = false
  bookingModalOpen.paymentModal = false
  bookingModalOpen.orderModal = false
}

const reOpenDetailModal = () => {
  bookingModalOpen.requestModal = false
  bookingModalOpen.bookingModal = true
  bookingModalOpen.cancelModal = false
  bookingModalOpen.paymentModal = false
}

const closePaymentModal = () => {
  emits('closed')
  bookingModalOpen.bookingModal = false
  bookingModalOpen.requestModal = false
  bookingModalOpen.cancelModal = false
  bookingModalOpen.paymentModal = false
  if (orderId.value)
    bookingModalOpen.orderModal = true
}
const openEditModal = () => {
  bookingModalOpen.orderModal = false
  bookingModalOpen.bookingModal = false
  bookingModalOpen.cancelModal = false
  bookingModalOpen.paymentModal = false
  bookingModalOpen.requestModal = true
}
const openCancelModal = () => {
  bookingModalOpen.cancelModal = true
  bookingModalOpen.requestModal = false
  bookingModalOpen.paymentModal = false
}
const openPaymentModal = () => {
  bookingModalOpen.requestModal = false
  bookingModalOpen.bookingModal = false
  bookingModalOpen.cancelModal = false
  bookingModalOpen.orderModal = false
  bookingModalOpen.paymentModal = true
}
const openRecurringModal = () => {
  bookingModalOpen.requestModal = false
  bookingModalOpen.bookingModal = false
  bookingModalOpen.cancelModal = false
  bookingModalOpen.paymentModal = false
}
const openOrderModal = () => {
  bookingModalOpen.bookingModal = false
  bookingModalOpen.orderModal = true
}
const reOpenDetailsModalWithNewBooking = (id: string) => {
  processing.value = true
  bookingModalOpen.bookingModal = true
  bookingModalOpen.requestModal = false
  bookingModalOpen.cancelModal = false
  bookingModalOpen.paymentModal = false
  state.booking = {}
  fetchItemData(id)
    .then(() => {
      bookingModalOpen.orderModal = false
    })
    .catch(() => {
      bookingModalOpen.bookingModal = false
    })
    .finally(() => {
      processing.value = false
    })
}

const openAppointmentById = async (id) => {
  bookingModalOpen.orderModal = false
  bookingModalOpen.bookingModal = true

  await fetchItemData(id)
}

const openRecurringAppointmentModal = (id: string) => {
  reOpenDetailsModalWithNewBooking(id)
}
const selectedItemToSetAppointment = ref<Booking | null>(null)
const openSetAppointmentModal = (item) => {
  bookingModalOpen.setAppointmentModal = true
  bookingModalOpen.orderModal = false
  selectedItemToSetAppointment.value = item
}
const openMainRecurringAppointmentModal = (id: string) => {
  reOpenDetailsModalWithNewBooking(id)
}
const closeCancelModal = () => {
  bookingModalOpen.cancelModal = false
}
const closeBookingModal = () => {
  bookingModalOpen.bookingModal = false
  state.booking = {}
  if (!orderId.value)
    emits('closed')
  else
    bookingModalOpen.orderModal = true
}
const openTransactionDetailsModal = (transaction) => {
  state.selectedTransaction = transaction
  bookingModalOpen.orderModal = false

  bookingModalOpen.transactionModal = true
}
const refreshAfterUpdateBooking = async (id) => {
  await fetchItemData(id, { refreshOrder: bookingModalOpen.orderModal })
  emits('refreshEvents')
}
</script>

<template>
  <div>
    <ModalTransactionDetails
      :show-modal="bookingModalOpen.transactionModal"
      :transaction-details="state.selectedTransaction"
      @close="
        bookingModalOpen.transactionModal = false;
        bookingModalOpen.orderModal = true;
        $emit('refreshEvents');
      "
    />
    <main-order-modal
      v-if="bookingModalOpen.orderModal"
      :is-open="bookingModalOpen.orderModal"
      :order-id="orderId"
      :item-id="itemId"
      :loading="processing"
      @closed="closeModal"
      @open-appointment-modal="openAppointmentById"
      @open-set-appointment-modal="openSetAppointmentModal"
      @refresh="$emit('refreshEvents')"
      @open-transaction-details-modal="openTransactionDetailsModal"
    />
    <modal-set-appointment
      v-if="bookingModalOpen.setAppointmentModal"
      :is-open="bookingModalOpen.setAppointmentModal"
      :order-id="orderId"
      :selected-item="selectedItemToSetAppointment"
      @close="
        bookingModalOpen.setAppointmentModal = false;
        bookingModalOpen.orderModal = true;
        $emit('refreshEvents');
      "
    />
    <!-- @close="closeSetAppointment" -->
    <!-- :selectedItem="" -->

    <view-booking-modal
      v-if="bookingModalOpen.bookingModal"
      :hide-price="hidePrice"
      :is-open="bookingModalOpen.bookingModal"
      :booking="state.booking"
      :loading="processing"
      @show-order-modal="openOrderModal"
      @closed="closeBookingModal"
      @open-edit-modal="openEditModal"
      @open-payment-modal="openPaymentModal"
      @refresh="refreshAfterUpdateBooking"
      @open-recurring-appointment-modal="openRecurringAppointmentModal"
      @open-main-recurring-appointment-modal="openMainRecurringAppointmentModal"
    />

    <payment-modal
      v-if="bookingModalOpen.paymentModal"
      :is-open="bookingModalOpen.paymentModal"
      :booking="state.booking"
      :is-from-booking="false"
      @closed="closePaymentModal"
      @refresh="fetchItemData(itemId)"
    />
    <!-- note in the body request -->

    <booking-modal
      v-if="bookingModalOpen.requestModal"
      :is-open="bookingModalOpen.requestModal"
      :event-id="state.booking?.uuid"
      :staff-list="tableData.staffList"
      :slot-data="props.selectedSlot"
      @closed="closeModal"
      @open-booking-details="reOpenDetailModal"
      @deleted="$emit('refreshEvents')"
      @created="$emit('refreshEvents')"
    />
  </div>
</template>
