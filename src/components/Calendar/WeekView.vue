<script setup lang="ts">
import { onMounted, ref } from 'vue'
import {
  eachDayOfInterval,
  endOfWeek,
  format,
  isSameDay,
  parse,
  startOfWeek,
} from 'date-fns'
import { storeToRefs } from 'pinia'
import { orderBy } from 'lodash'
import type { PropType } from 'vue'
import type { Event } from '@/types'

const props = defineProps({
  events: {
    type: Array as PropType<Event[]>,
    default() {
      return []
    },
  },
  currentDate: {
    type: Date,
    default: () => new Date(),
  },
})

const router = useRouter()
const authStore = useAuthStore()
const { getUserInfo } = storeToRefs(authStore)

const container = ref(null)
const containerNav = ref(null)
const containerOffset = ref(null)

const weekDays = computed(() => eachDayOfInterval({
  start: startOfWeek(props.currentDate, { weekStartsOn: getUserInfo.value.tenant.start_of_week }),
  end: endOfWeek(props.currentDate, { weekStartsOn: getUserInfo.value.tenant.start_of_week }),
}))

const getColClass = (event) => {
  const idx = weekDays.value.findIndex((day) => {
    return isSameDay(parse(event.start_time, 'yyyy-MM-dd HH:mm', new Date()), day)
  })

  return idx ? `sm:col-start-${idx + 1} z-[${idx + 1}]` : ''
}

const getEventStyle = (event) => {
  const start = event.start.split(':')
  const startToInt = Number(start[0]) + Number(start[1]) / 60

  const end = event.end.split(':')
  const endToInt = Number(end[0]) + Number(end[1]) / 60

  const duration = Number(endToInt) - Number(startToInt)

  return startToInt ? `grid-row: ${(startToInt * 12) + 2} / span ${duration * 12}` : ''
}

const openEventDetail = (event) => {
  router.push({ name: 'booking', params: { id: event.uuid } })
}

const sameTimeSlotEvent = (event) => {
  return orderBy(props.events.filter((e) => {
    return e.start_time === event.start_time
  }), ['start_time', 'asc'])
}
onMounted(() => {
  // Set the container scroll position based on the current time.
  const currentMinute = new Date().getHours() * 60
  container.value.scrollTop
    = ((container.value.scrollHeight - containerNav.value.offsetHeight - containerOffset.value.offsetHeight)
      * currentMinute)
    / 1440
})
</script>

<template>
  <div ref="container" class="isolate flex flex-auto flex-col overflow-auto bg-white">
    <div style="width: 165%" class="flex max-w-full flex-none flex-col sm:max-w-none md:max-w-full">
      <div ref="containerNav" class="sticky top-0 z-30 flex-none bg-white shadow ring-1 ring-black ring-opacity-5 ltr:sm:pr-8 rtl:sm:pl-8">
        <div class="grid grid-cols-7 text-sm leading-6 text-gray-500 sm:hidden">
          <button v-for="(day, index) in weekDays" :key="index" type="button" class="flex flex-col items-center pb-3 pt-2">
            {{ format(day, 'EEEEE') }}
            <span class="mt-1 flex h-8 w-8 items-center justify-center font-semibold text-gray-900">{{ format(day, 'dd') }}</span>
          </button>
        </div>

        <div class="-mr-px hidden grid-cols-7 divide-x divide-gray-100 rtl:border-l ltr:border-r border-gray-100 text-sm leading-6 text-gray-500 sm:grid">
          <div class="col-end-1 w-14" />
          <div v-for="(day, index) in weekDays" :key="index" class="flex items-center justify-center py-3">
            <span>{{ $t(`calendar.day_short.${format(day, 'E')}`) }}  <span class="items-center justify-center font-semibold text-gray-900">{{ format(day, 'dd') }}</span></span>
          </div>
        </div>
      </div>
      <div class="flex flex-auto">
        <div class="sticky ltr:left-0 rtl:right-0 z-10 w-14 flex-none bg-white ring-1 ring-gray-100" />
        <div class="grid flex-auto grid-cols-1 grid-rows-1">
          <!-- Horizontal lines -->
          <div class="col-start-1 col-end-2 row-start-1 grid divide-y divide-gray-100" style="grid-template-rows: repeat(48, minmax(3.5rem, 1fr))">
            <div ref="containerOffset" class="row-end-1 h-7" />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                12{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                1{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                2{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                3{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                4{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                5{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                6{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                7{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                8{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                9{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                10{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                11{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                12{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                1{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                2{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                3{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                4{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                5{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                6{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                7{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                8{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                9{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                10{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 z-20 ltr:-ml-14 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                11{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
          </div>

          <!-- Vertical lines -->
          <div class="col-start-1 col-end-2 row-start-1 hidden grid-cols-7 grid-rows-1 divide-x divide-gray-100 sm:grid sm:grid-cols-7">
            <div class="col-start-1 row-span-full" />
            <div class="col-start-2 row-span-full" />
            <div class="col-start-3 row-span-full" />
            <div class="col-start-4 row-span-full" />
            <div class="col-start-5 row-span-full" />
            <div class="col-start-6 row-span-full" />
            <div class="col-start-7 row-span-full" />
            <div class="col-start-8 row-span-full w-8" />
          </div>

          <!-- Events -->
          <ol class="col-start-1 col-end-2 row-start-1 grid grid-cols-1 sm:grid-cols-7 rtl:sm:pl-8 ltr:sm:pr-8" style="grid-template-rows: 1.75rem repeat(288, minmax(0, 1fr)) auto">
            <li v-for="event in events" :key="`event-${event.uuid}`" class="relative mt-px flex" :class="[getColClass(event)]" :style="getEventStyle(event)" @click.self="openEventDetail(event)">
              <a href="#" class="group absolute inset-1 flex flex-col overflow-y-auto rounded-lg bg-blue-50 p-2 text-xs leading-5 hover:bg-blue-100">
                <template v-for="(slot, index) in sameTimeSlotEvent(event)" :key="index">
                  <p class="text-blue-500 group-hover:text-blue-700">
                    <time datetime="2022-01-12T06:00">{{ slot.start }} - {{ slot.end }}</time>
                  </p>
                  <p class="font-semibold text-blue-700 hover:text-rose-900" @click="openEventDetail(slot)">{{ slot.name }}</p>
                </template>
              </a>
            </li>
          </ol>
        </div>
      </div>
    </div>
  </div>
</template>
