<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { XCircleIcon } from '@heroicons/vue/24/outline'
import type { PropType } from 'vue'
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { useI18n } from 'vue-i18n'
import type { Booking } from '@/types'
import Reasons from '@/services/Reasons'
import { useEventStore } from '@/stores/event'
import { useLocalesStore } from '@/stores/locales'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  booking: {
    type: Object as PropType<Booking>,
    default: () => ({}),
  },
})
const emits = defineEmits(['closed', 'refresh'])
// import useBooking from '@/composables/useBooking'
const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()

const { cancelBooking } = useEventStore()

const { showNotification } = useNotifications()
const selectedReason = ref('')
const otherReason = ref('')
const reasons = ref<any>([])
const processing = ref(false)
onMounted(async () => {
  try {
    processing.value = true
    const res = await Reasons.getReasons('booking-cancel')
    reasons.value = [...res.data.data]
  }
  finally {
    processing.value = false
  }
})

// add cancel async function
const cancelBookingHandler = async () => {
  try {
    processing.value = true
    await cancelBooking(props.booking.uuid, selectedReason.value)
    showNotification({
      title: t('Success'),
      type: 'success',
      message: t('bookingCancelledSuccessfully'),
    })
    emits('refresh')
    emits('closed')
  }
  catch (err) {
    showNotification({
      title: t('Error'),
      type: 'error',
      message: t('bookingCancelledError'),
    })
  }
  finally {
    processing.value = false
  }
}
const completeButtonRef = ref(null)
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="$t('cancelConfirmation')"
    :param="booking?.services?.[0]?.name || booking?.staff?.name || ''"
    @closed="console.log('closed');emits('closed')"
  >
    <div class="flex flex-col gap-4">
      <!-- Reason select -->
      <div class="text-right">
        <label for="reasons" class="block mb-1 text-base font-medium text-gray-700">
          {{ $t('bookingItems.cancelReason') || 'سبب الإلغاء' }}
          <span class="text-red-600">*</span>
        </label>
        <select
          id="reasons"
          v-model="selectedReason"
          class="block px-3 py-2 w-full text-base rounded-md border border-gray-300 focus:border-primary-500 focus:ring-primary-500"
        >
          <option value="">
            {{ $t('form.select') || 'اختر السبب' }}
          </option>
          <option
            v-for="reason in reasons"
            :key="reason?.uuid"
            :value="reason?.uuid"
          >
            {{ reason?.name }}
          </option>
        </select>
      </div>
      <!-- Other reason textarea -->
      <div class="text-right">
        <label for="other-reason" class="block mb-1 text-base font-medium text-gray-700">
          {{ $t('bookingItems.otherReason') || 'لديك سبب آخر؟ شاركنا به' }}
        </label>
        <textarea
          id="other-reason"
          v-model="otherReason"
          rows="3"
          class="block px-3 py-2 w-full text-base rounded-md border border-gray-300 resize-none focus:border-primary-500 focus:ring-primary-500"
          :placeholder="$t('bookingItems.otherReasonPlaceholder') || 'اكتب هنا...'"
        />
      </div>
      <!-- Cancel button -->
      <button
        class="py-3 mt-2 w-full text-base font-bold text-white bg-red-600 rounded-lg transition hover:bg-red-700"
        :disabled="!selectedReason"
        @click="cancelBookingHandler"
      >
        {{ $t('bookingItems.cancelAppointment') || 'إلغاء الموعد' }}
      </button>
    </div>
  </Modal>
</template>
