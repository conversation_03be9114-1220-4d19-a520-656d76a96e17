<script setup lang="ts">
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { CheckCircleIcon, EllipsisVerticalIcon, XCircleIcon } from '@heroicons/vue/20/solid'
import { storeToRefs } from 'pinia'
import type { PropType } from 'vue'
import type { PendingBooking } from '@/types'
defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  pendingBookings: {
    type: Array as PropType<PendingBooking[]>,
    default: () => [],
  },
})

defineEmits(['close', 'takePendingAction', 'openBookingDetails'])
const { locale } = useI18n()
const { loader } = storeToRefs(useAppsStore())

// const handlePendingAction = () => {
//   emit('')
// }
</script>

<template>
  <TransitionRoot appear :show="isOpen && !!pendingBookings.length" as="template">
    <Dialog as="div" class="relative z-10" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div
          class="flex min-h-full items-center justify-center p-4 text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-3xl relative transform overflow-hidden rounded-2xl bg-white p-6 rtl:text-right ltr:text-left align-middle shadow-xl transition-all"
            >
              <overlay-loader v-if="loader" />
              <DialogTitle
                as="h3"
                class="text-2xl text-center font-medium leading-6 text-gray-900"
              >
                {{ $t('form.pendingBookings') }}
              </DialogTitle>

              <ul role="list" class="divide-y divide-gray-100">
                <li v-for="pendingBooking in pendingBookings" :key="pendingBooking.uuid" class="flex items-center justify-between gap-x-6 py-5">
                  <div class="min-w-0">
                    <div class="flex items-start gap-x-3">
                      <p class="text-md font-semibold leading-6">
                        <a
                          class="cursor-pointer text-gray-900 hover:text-blue-500"
                          @click="$emit('openBookingDetails', { extendedProps: { uuid: pendingBooking.uuid } })"
                        >
                          {{ pendingBooking.customer.first_name }} {{ pendingBooking.customer.last_name }}
                        </a>
                      </p>
                    </div>
                    <div class="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500">
                      <p class="whitespace-nowrap">
                        <time :datetime="pendingBooking.date">{{ `${$t('from')} ${pendingBooking.start} ${$t('to')} ${pendingBooking.end}` }}</time>
                      </p>
                      <svg viewBox="0 0 2 2" class="h-0.5 w-0.5 fill-current">
                        <circle cx="1" cy="1" r="1" />
                      </svg>
                      <p class="truncate">
                        {{ pendingBooking.services?.map(service => service.name).join(", ") }} ({{ pendingBooking.staff.name }})
                      </p>
                    </div>
                  </div>

                  <div class="flex flex-none items-center gap-x-4">
                    <button
                      class="flex items-center p-2.5 text-white bg-green-500 hover:bg-green-600 rounded-full rtl:mr-1 ltr:ml-1"
                      @click="$emit('takePendingAction', pendingBooking.uuid, 'confirm')"
                    >
                      <CheckCircleIcon class="h-5 w-5" aria-hidden="true" />
                      <span>{{ $t('confirm') }}</span>
                    </button>
                    <button
                      class="flex items-center p-2.5 text-white bg-red-500 hover:bg-red-600 rounded-full rtl:mr-1 ltr:ml-1"
                      @click="$emit('takePendingAction', pendingBooking.uuid, 'reject')"
                    >
                      <XCircleIcon class="h-5 w-5" aria-hidden="true" />
                      <span>{{ $t('reject') }}</span>
                    </button>
                  </div>
                </li>
              </ul>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
