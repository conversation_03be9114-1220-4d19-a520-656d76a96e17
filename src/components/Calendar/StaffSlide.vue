<script setup lang="ts">
import { CheckCircleIcon, XCircleIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  canSelectMultiple: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['close', 'refresh', 'updateSelectedStaffs'])
const { tableData } = useStaff()

const selectedStaffs = computed(() =>
  tableData.staffList.filter(item => item.selected),
)
const allSelected = computed(() =>
  tableData.staffList.every(item => item.selected),
)
const toggleStaff = async (staff) => {
  if (
    selectedStaffs.value.length == 1
    && selectedStaffs.value[0].uuid === staff.uuid
  )
    return

  if (!props.canSelectMultiple) {
    tableData.staffList = tableData.staffList.map((item) => {
      if (item.uuid === staff.uuid)
        item.selected = !item.selected
      else
        item.selected = false

      return item
    })
    emit('updateSelectedStaffs', selectedStaffs.value)
  }
  else {
    tableData.staffList = tableData.staffList.map((item) => {
      if (item.uuid === staff.uuid)
        item.selected = !item.selected

      return item
    })
    emit('updateSelectedStaffs', selectedStaffs.value)
  }
}
const toggleAll = () => {
  const selected = tableData.staffList.every(item => item.selected)
  tableData.staffList = tableData.staffList.map((item) => {
    item.selected = !selected
    return item
  })
  emit('updateSelectedStaffs', selectedStaffs.value)
}
</script>

<template>
  <div
    class="transition-all duration-200 ease-in w-[300px]"
    :class="isOpen ? 'flex flex-col' : 'hidden'"
  >
    <overlay-loader v-if="tableData.processing" :full-screen="false" />
    <div
      class="flex overflow-y-auto relative flex-col gap-y-5 px-6 h-full text-black bg-gray-100 border-l border-gray-200 grow"
    >
      <button class="absolute top-3 end-4" @click="emit('close')">
        <XCircleIcon
          class="w-9 h-9 text-red-400 cursor-pointer close ms-auto"
          aria-hidden="true"
        />
      </button>
      <h3
        class="py-4 text-lg font-semibold text-black border-b border-gray-200"
      >
        {{ $t("staffs") }}
      </h3>
      <ul class="flex flex-col gap-1">
        <li
          class="flex justify-between items-center px-3 py-1 text-sm rounded-lg transition-transform duration-200 ease-in-out cursor-pointer hover:scale-105"
          role="button"
          aria-label="All Providers"
          tabindex="0"
          :class="
            allSelected
              ? 'bg-primary-500  text-white'
              : 'bg-primary-700 text-white'
          "
          @click="toggleAll"
        >
          <div
            class="flex gap-3 justify-start items-center px-1 py-1 w-full text-lg font-semibold text-white rounded-lg"
          >
            <span>
              {{ allSelected ? $t("cancel_selected_all") : $t("selected_all") }}
              ({{ tableData.staffList.length }})</span>
          </div>
          <CheckCircleIcon
            v-if="tableData.selectAllStaff"
            class="w-6 text-primary-500"
          />
        </li>
        <li
          v-for="item in tableData.staffList"
          :key="item.id"
          class="flex justify-between items-center px-3 py-1 text-sm rounded-lg cursor-pointer hover:bg-gray-300"
          role="button"
          :aria-label="item.name"
          tabindex="0"
          :class="
            item.selected ? 'bg-gray-500 hover:!bg-gray-500 text-white' : ''
          "
          @click="toggleStaff(item)"
        >
          <div class="flex gap-3 justify-start items-center w-full">
            <img
              v-if="item.image"
              :src="item.image"
              class="w-8 h-8 rounded-full"
            >
            <svg
              v-else
              xmlns="http://www.w3.org/2000/svg"
              class="w-8"
              viewBox="0 0 24 24"
            >
              <path
                fill="#e1e1e1"
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z"
              />
            </svg>
            <span class="">{{ item.name }}</span>
          </div>
          <CheckCircleIcon v-if="item.selected" class="w-6 text-primary-500" />
        </li>
      </ul>
    </div>
  </div>
</template>
