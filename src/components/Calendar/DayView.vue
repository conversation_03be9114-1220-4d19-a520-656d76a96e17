<script setup lang="ts">
import type { PropType } from 'vue'
import { onMounted, ref } from 'vue'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/20/solid'
import {
  eachDayOfInterval,
  endOfMonth,
  endOfWeek,
  format,
  isSameDay,
  isSameMonth,
  isToday, parse,
  parseISO,
  startOfWeek,
} from 'date-fns'
import { storeToRefs } from 'pinia'
import type { Event } from '@/types'

const props = defineProps({
  events: {
    type: Array as PropType<Event[]>,
    default() {
      return []
    },
  },
  currentDate: {
    type: Date,
    default: () => new Date(),
  },
})

const emit = defineEmits(['customPrev', 'customNext', 'setCurrentDay'])
const router = useRouter()
const authStore = useAuthStore()
const { getUserInfo } = storeToRefs(authStore)

const container = ref(null)
const containerNav = ref(null)
const containerOffset = ref(null)

const currentMonth = computed(() => format(props.currentDate, 'MMM-yyyy'))
const firstDayOfMonth = computed(() => parse(currentMonth.value, 'MMM-yyyy', new Date()))

const monthDays = computed(() => eachDayOfInterval({
  start: startOfWeek(firstDayOfMonth.value, { weekStartsOn: getUserInfo.value.tenant.start_of_week }),
  end: endOfWeek(endOfMonth(firstDayOfMonth.value), { weekStartsOn: getUserInfo.value.tenant.start_of_week }),
}))

const days = computed(() => monthDays.value.map(day => ({
  date: format(day, 'yyyy-MM-dd'),
  isCurrentMonth: isSameMonth(day, firstDayOfMonth.value),
  isToday: isToday(day),
  isSelected: isSameDay(day, props.currentDate),
  events: props.events.filter(event =>
    isSameDay(parseISO(event.start_time), day),
  ),
})))

const getEventStyle = (event) => {
  const start = event.start.split(':')
  const startToInt = Number(start[0]) + Number(start[1]) / 60

  const end = event.end.split(':')
  const endToInt = Number(end[0]) + Number(end[1]) / 60

  const duration = Number(endToInt) - Number(startToInt)

  return startToInt ? `grid-row: ${(startToInt * 12) + 2} / span ${duration * 12}` : ''
}

const setCurrentDate = (dateObj) => {
  emit('currentDateSet', parse(dateObj.date, 'yyyy-MM-dd', new Date()))
}

const openEventDetail = (event) => {
  router.push({ name: 'booking', params: { id: event.uuid } })
}

onMounted(() => {
  // Set the container scroll position based on the current time.
  const currentMinute = new Date().getHours() * 60
  container.value.scrollTop
    = ((container.value.scrollHeight - containerNav.value.offsetHeight - containerOffset.value.offsetHeight)
      * currentMinute)
    / 1440
})
</script>

<template>
  <div class="isolate flex flex-auto overflow-hidden bg-white">
    <div ref="container" class="flex flex-auto flex-col overflow-auto">
      <div ref="containerNav" class="sticky top-0 z-10 grid flex-none grid-cols-7 bg-white text-xs text-gray-500 shadow ring-1 ring-black ring-opacity-5 md:hidden">
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>W</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-gray-900">19</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>T</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-indigo-600">20</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>F</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-gray-900">21</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>S</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full bg-gray-900 text-base font-semibold text-white">22</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>S</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-gray-900">23</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>M</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-gray-900">24</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>T</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-gray-900">25</span>
        </button>
      </div>
      <div class="flex w-full flex-auto">
        <div class="w-14 flex-none bg-white ring-1 ring-gray-100" />
        <div class="grid flex-auto grid-cols-1 grid-rows-1">
          <!-- Horizontal lines -->
          <div class="col-start-1 col-end-2 row-start-1 grid divide-y divide-gray-100" style="grid-template-rows: repeat(48, minmax(3.5rem, 1fr))">
            <div ref="containerOffset" class="row-end-1 h-7" />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                12{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                1{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                2{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                3{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                4{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                5{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                6{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                7{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                8{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                9{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                10{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                11{{ $t(`calendar.AM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                12{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                1{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                2{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                3{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                4{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                5{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                6{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                7{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                8{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                9{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                10{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
            <div>
              <div class="sticky ltr:left-0 ltr:-ml-14 rtl:right-0 rtl:-mr-14 -mt-2.5 w-14 pr-2 text-right text-xs leading-5 text-gray-400">
                11{{ $t(`calendar.PM`) }}
              </div>
            </div>
            <div />
          </div>

          <!-- Events -->
          <ol class="col-start-1 col-end-2 row-start-1 grid grid-cols-1" style="grid-template-rows: 1.75rem repeat(288, minmax(0, 1fr)) auto">
            <li v-for="event in events" :key="`event-${event.uuid}`" class="relative mt-px flex" :style="getEventStyle(event)" @click="openEventDetail(event)">
              <a href="#" class="group absolute inset-1 flex flex-col overflow-y-auto rounded-lg bg-blue-50 p-2 text-xs leading-5 hover:bg-blue-100">
                <p class="order-1 font-semibold text-blue-700">{{ event.name }}</p>
                <p class="text-blue-500 group-hover:text-blue-700">
                  <time datetime="2022-01-22T06:00">{{ event.start }}</time>
                </p>
              </a>
            </li>
          </ol>
        </div>
      </div>
    </div>
    <div class="hidden w-1/2 max-w-md flex-none border-l border-gray-100 px-8 py-10 md:block">
      <div class="flex items-center text-center text-gray-900">
        <button type="button" class="-m-1.5 flex flex-none items-center justify-center p-1.5 text-gray-400 hover:text-gray-500" @click="emit('customPrev', 'months')">
          <span class="sr-only">Previous month</span>
          <ChevronLeftIcon class="h-5 w-5" aria-hidden="true" />
        </button>
        <div class="flex-auto text-sm font-semibold">
          {{ format(firstDayOfMonth, 'MMM yyyy') }}
        </div>
        <button type="button" class="-m-1.5 flex flex-none items-center justify-center p-1.5 text-gray-400 hover:text-gray-500" @click="emit('customNext', 'months')">
          <span class="sr-only">Next month</span>
          <ChevronRightIcon class="h-5 w-5" aria-hidden="true" />
        </button>
      </div>
      <div class="mt-6 grid grid-cols-7 text-center text-xs leading-6 text-gray-500">
        <div>M</div>
        <div>T</div>
        <div>W</div>
        <div>T</div>
        <div>F</div>
        <div>S</div>
        <div>S</div>
      </div>
      <div class="isolate mt-2 grid grid-cols-7 gap-px rounded-lg bg-gray-200 text-sm shadow ring-1 ring-gray-200">
        <button
          v-for="(day, dayIdx) in days"
          :key="day.date"
          type="button"
          class="py-1.5 hover:bg-gray-100 focus:z-10" :class="[
            day.isCurrentMonth
              ? 'bg-white'
              : 'bg-gray-50', (day.isSelected || day.isToday)
              && 'font-semibold', day.isSelected
              && 'text-white', !day.isSelected
              && day.isCurrentMonth
              && !day.isToday
              && 'text-gray-900', !day.isSelected
              && !day.isCurrentMonth && !day.isToday
              && 'text-gray-400', day.isToday
              && !day.isSelected
              && 'text-indigo-600', dayIdx === 0
              && 'rounded-tl-lg', dayIdx === 6
              && 'rounded-tr-lg', dayIdx === days.length - 7
              && 'rounded-bl-lg', dayIdx === days.length - 1
              && 'rounded-br-lg',
          ]"
          @click="setCurrentDate(day)"
        >
          <time :datetime="day.date" class="mx-auto flex h-7 w-7 items-center justify-center rounded-full" :class="[day.isSelected && day.isToday && 'bg-indigo-600', day.isSelected && !day.isToday && 'bg-gray-900']">{{ day.date.split('-').pop().replace(/^0/, '') }}</time>
        </button>
      </div>
    </div>
  </div>
</template>
