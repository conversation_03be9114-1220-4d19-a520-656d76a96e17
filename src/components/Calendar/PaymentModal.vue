<script setup lang="ts">
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import useNotifications from '@/composables/useNotifications'
import type { Booking, PaymentMethod } from '@/types'
import posServices from '@/services/posServices'
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  booking: {
    type: Object as PropType<Booking>,
    default: () => ({}),
  },
  invoice: {
    type: Object,
    default: () => ({}),
  },
  unPaidAmount: {
    type: Number,
    default: 0,
    required: true,
  },
  formBtn: {
    type: Function,
    default: 'pos.checkout',
    required: false,
  },
})
const emits = defineEmits(['closed', 'refresh'])
const { t } = useI18n()
const i18n = useI18n()
watch(
  () => props.isOpen,
  (val) => {
    if (val)
      amount.value = props.unPaidAmount
  },
)

const errHandle = ref<any[]>([])

const hasApiError = computed(() => {
  return errHandle.value.length
})

const { showNotification } = useNotifications()
const { getLocale } = storeToRefs(useLocalesStore())
// const { paymentMethods } = storeToRefs(usePosStore())
const paymentMethods = ref<any[]>([])
const { fetchCheckoutPage } = usePos()
const { checkout } = usePosStore()
const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)

const amount = ref(0)
const selectedPaymentMethod = ref('')
const note = ref('')

const processing = ref(false)
const formData = ref({
  referenceNumber: '',
  attachedFile: null,
  date: new Date(),
})
onMounted(() => {
  processing.value = true
  posServices
    .getPaymentMethods()
    .then((res) => {
      paymentMethods.value = [...res.data.data]
      selectedPaymentMethod.value = paymentMethods.value[0].id
    })
    .finally(() => {
      processing.value = false
    })
})

const isNumber = (evt: any) => {
  evt = evt || window.event
  const charCode = evt.which ? evt.which : evt.keyCode
  if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46)
    evt.preventDefault()
  else return true
}
const hitCheckout = async () => {
  errHandle.value = []
  processing.value = true
  if (props.invoice.id || props.booking.invoice) {
    const invoiceId = props.invoice.id || props.booking.invoice
    checkout(invoiceId, {
      amount: amount.value,
      payment_method: selectedPaymentMethod.value,
      reference_number: formData.value.referenceNumber,
      attached_file: formData.value.attachedFile,
      date: formatDate(formData.value.date),
      note: note.value,
    })
      .then(() => {
        showNotification({
          title: t('Success'),
          type: 'success',
          message: t('pos.amount_was_paid_successfully'),
        })
        emits('refresh')
        emits('closed')
      })
      .catch((err) => {
        if (err.errors)
          for (const prop in err.errors) errHandle.value.push(err.errors[prop])
      })
      .finally(() => {
        processing.value = false
      })
  }
}
const renderImageDependSlug = (slug) => {
  const imageUrl = new URL(`../../assets/${slug}.png`, import.meta.url).href
  return imageUrl
}
const { locale } = useI18n()
const link = ref('')
</script>

<template>
  <div>
    <Modal
      appear
      :show="isOpen"
      as="template"
      panel-classes="w-full max-w-3xl transform overflow-hidden rounded-2xl bg-white p-6 md:!p-8 text-left align-middle shadow-xl transition-all text-center"
      title="add_payment"
      :dir="getLocale(locale)?.direction"
      @close="emits('closed')"
    >
      <overlay-loader v-if="processing" :full-screen="false" />
      <ul
        v-if="hasApiError"
        class="relative px-3 py-2 mb-2 text-sm text-red-700 bg-red-100 rounded border border-red-400"
        role="alert"
      >
        <li
          v-for="(err, index) in errHandle"
          :key="index"
          class="font-bold [&>*:not(:first-child)]:mt-2"
        >
          {{ err.join() }}
        </li>
      </ul>
      <div class="mt-2">
        <span class="font-bold">{{ $t("pos.pay") }}</span>
        <p class="text-sm text-gray-500 text-start">
          {{ $t("choose_payment_method") }}
        </p>
        <div class="flex flex-col items-center mt-4">
          <!-- Payment Methods Grid -->
          <div
            class="grid grid-cols-2 gap-3 w-full md:grid-cols-3 lg:grid-cols-5"
          >
            <div
              v-for="method in paymentMethods"
              :key="method.name"
              class="flex flex-col items-center p-3 rounded-lg border-2 transition-colors cursor-pointer"
              :class="[
                method.id === selectedPaymentMethod
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-blue-300',
              ]"
              @click="selectedPaymentMethod = method.id"
            >
              <img
                :src="renderImageDependSlug(method.slug)"
                class="object-contain w-10 h-10"
                :alt="method.name"
              >
              <span class="mt-2 text-xs font-medium">{{ method.name }}</span>
            </div>
          </div>
          <!-- Payment Note -->
        </div>
        <div class="flex flex-col items-center mt-5 w-full">
          <div class="flex justify-center items-start w-full">
            <div class="flex relative flex-col items-start w-full">
              <div class="flex items-center w-full">
                <TextInput
                  id="amount"
                  v-model="amount"
                  :label="$t('enter_your_amount')"
                  autofocus
                  :placeholder="$t('enter_your_amount')"
                  name="amount"
                  @keypress="isNumber($event)"
                >
                  <template #suffix>
                    <div class="flex items-center">
                      <span class="absolute top-1/2 z-10 text-sm font-semibold text-gray-700 -translate-y-1/2 end-3">
                        {{ $t(`currenices.${userInfo.tenant?.currency}`) }}
                      </span>
                    </div>
                  </template>
                </TextInput>
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-col items-start mt-4 w-full">
          <DateInput
            id="date"
            v-model="formData.date"
            :label="$t('date')"
            :placeholder="$t('enter_date')"
            :is24hr="true"
          />
        </div>
        <div class="flex flex-col items-start mt-4 w-full">
          <TextInput
            id="referenceNumber"
            v-model="formData.referenceNumber"
            :label="$t('reference_number')"
            rows="2"
            :placeholder="$t('enter_reference_number')"
          />
        </div>
        <div class="flex flex-col items-start mt-4 w-full">
          <LabelInput for="attached_file" class="mb-2">
            {{
              $t("attached_file")
            }}
          </LabelInput>
          <ImageInput
            id="attached_file"
            v-model="formData.attachedFile"
            :show-clear-btn="true"
            :link="link"
            @image-cleared="link = ''"
            @update:link="link = $event"
          />
        </div>
        <div class="mt-4 w-full">
          <TextareaInput
            v-model="note"
            rows="2"
            :placeholder="$t('booking.notes')"
          />
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-3 mt-4 w-full">
          <button
            class="flex-1 py-2.5 text-sm font-medium text-white bg-blue-500 rounded-lg transition-colors hover:bg-blue-600"
            @click="hitCheckout"
          >
            {{ $t(formBtn) }}
          </button>
          <button
            class="flex-1 py-2.5 text-sm font-medium text-white bg-gray-400 rounded-lg transition-colors hover:bg-gray-500"
            @click="$emit('closed')"
          >
            {{ $t("form.cancel") }}
          </button>
        </div>
      </div>
    </Modal>
  </div>
</template>
