<script setup lang="ts">
import type { Ref } from 'vue'
import { cloneDeep, findIndex, forEach } from 'lodash'
import {
  PencilSquareIcon,
  PhotoIcon,
  PlusIcon,
  TrashIcon,
} from '@heroicons/vue/24/outline'
interface MediaFile {
  name: string
  path: string
  highlight: number
  formData: FormData
}
const props = defineProps({
  dragText: {
    type: String,
    default: 'Drag your images here',
  },
  browseText: {
    type: String,
    default: 'choose images to upload',
  },
  dropText: {
    type: String,
    default: 'Drop your images here',
  },
  accept: {
    type: String,
    default: 'image/gif,image/jpeg,image/png,image/bmp,image/jpg',
  },
  dataImages: {
    type: Array,
    default: () => {
      return []
    },
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  showImageName: {
    type: Boolean,
    default: false,
  },
  maxImage: {
    type: Number,
    default: 10,
  },
  idUpload: {
    type: String,
    default: 'image-upload',
  },
  idEdit: {
    type: String,
    default: 'image-edit',
  },
  showEdit: {
    type: Boolean,
    default: true,
  },
  showDelete: {
    type: Boolean,
    default: true,
  },
  showAdd: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  imageContainerClasses: {
    type: String,
    default: '',
  },
  smallImagesClasses: {
    type: String,
    default: '',
  },
  smallImagesContainerClasses: {
    type: String,
    default: '',
  },
  showControlsForSingleImage: {
    type: Boolean,
    default: false,
  },
  addButtonClasses: {
    type: String,
    default: '',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showCustomIcons: {
    type: Boolean,
    default: false,
  },
  idAction: {
    type: String,
    default: 'image-action',
  },

})
const emit = defineEmits([
  'before-upload',
  'edit-image',
  'before-remove',
  'limit-exceeded',
  'custom-action',
])
const currentIndexImage = ref(0)
const images: Ref<MediaFile[]> = ref([])
const isDragover = ref(false)
const fireCustomAction = (currentIndex: number) => {
  emit('custom-action', currentIndex, images.value[currentIndex])
}
const getImageName = computed(() => {
  const image = images.value[currentIndexImage.value]
  return image?.name
    ? `${image.name.substring(0, 20)}.${image.name.split('.').pop()}`
    : `${image.path.replace(/^.*[\\\/]/, '').slice(0, 10)}-` + 'Mahjoz'
})

const imagePreview = computed(() => {
  const index = findIndex(images.value, { highlight: 1 })
  if (index > -1)
    return images.value[index].path
  else
    return images.value.length ? images.value[0].path : ''
})

function preventEvent(e: Event) {
  e.preventDefault()
  e.stopPropagation()
}

function onDrop(e: DragEvent) {
  isDragover.value = false
  preventEvent(e)
  const files = (e.dataTransfer as DataTransfer).files
  if (!files.length)
    return false

  if (!isValidNumberOfImages(files.length))
    return false

  forEach(files, (value, index) => {
    createImage(value)
    if (!props.multiple)
      return false
  })
  if (document.getElementById(props.idUpload))
    (document.getElementById(props.idUpload) as HTMLInputElement).value = ''
}

function onDragover() {
  isDragover.value = true
}

function createImage(file: File) {
  if (props.disabled)
    return
  const reader = new FileReader()
  const formData = new FormData()
  formData.append('image', file)
  reader.onload = (e: ProgressEvent<FileReader>) => {
    const dataURI = (e.target as FileReader).result as string
    if (dataURI) {
      if (!images.value.length) {
        currentIndexImage.value = 0
      }
      else {
        images.value.map((item, index) => {
          item.highlight = 0
          return item
        })
      }
      emit(
        'before-upload',
        formData,
        images.value.length,
        images.value,
        (id: string) => {
          images.value.push({
            name: file.name,
            path: dataURI,
            highlight: 1,
            formData,
            id,
          })
        },
      )
    }
  }
  reader.readAsDataURL(file)
}

function editImage(file: File) {
  if (props.disabled)
    return
  const reader = new FileReader()
  const formData = new FormData()
  formData.append('file', file)
  reader.onload = (e: ProgressEvent<FileReader>) => {
    const dataURI = (e.target as FileReader).result as string
    if (dataURI) {
      if (images.value.length && images.value[currentIndexImage.value]) {
        images.value[currentIndexImage.value].path = dataURI
        images.value[currentIndexImage.value].name = file.name
        images.value[currentIndexImage.value].formData = formData
      }
    }
  }
  reader.readAsDataURL(file)
  emit('edit-image', formData, currentIndexImage.value, images.value)
}

function uploadFieldChange(e: DragEvent) {
  const files
    = (e.target as HTMLInputElement).files
    || (e.dataTransfer as DataTransfer).files
  if (!files.length)
    return false

  if (!isValidNumberOfImages(files.length))
    return false

  forEach(files, (value, index) => {
    createImage(value)
  })
  if (document.getElementById(props.idUpload))
    (document.getElementById(props.idUpload) as HTMLInputElement).value = ''
}

function editFieldChange(e: DragEvent) {
  const files
    = (e.target as HTMLInputElement).files
    || (e.dataTransfer as DataTransfer).files
  if (!files.length)
    return false

  if (!isValidNumberOfImages(files.length))
    return false

  forEach(files, (value, index) => {
    editImage(value)
  })
  if (document.getElementById(props.idEdit))
    (document.getElementById(props.idEdit) as HTMLInputElement).value = ''
}

function changeHighlight(currentIndex: number) {
  currentIndexImage.value = currentIndex
  const arr = images.value
  images.value = []
  arr.map((item, index) => {
    if (currentIndex === index)
      item.highlight = 1
    else
      item.highlight = 0

    return item
  })
  images.value = arr
}

function deleteImage(currentIndex: number) {
  emit(
    'before-remove',
    currentIndex,
    images.value[currentIndex],
    () => {
      images.value.splice(currentIndex, 1)
      currentIndexImage.value = 0
      if (images.value.length)
        images.value[0].highlight = 1
    },
    images.value,
  )
}

function isValidNumberOfImages(amount: number) {
  if (amount > props.maxImage) {
    emit('limit-exceeded', amount)
    return false
  }
  else {
    return true
  }
}
const { dataImages, loading } = toRefs(props)
watch(
  () => dataImages.value,
  (newVal) => {
    images.value = cloneDeep(newVal)
  },
  { deep: true },
)

onMounted(() => {
  document.body.addEventListener('dragleave', (event) => {
    event.stopPropagation()
    event.preventDefault()
    isDragover.value = false
  })
  images.value = []
  images.value = cloneDeep(props.dataImages)
})
</script>

<template>
  <div
    style="outline: none"
    class="relative"
    :class="[disableUpload ? '!cursor-not-allowed' : '']"
    @drag="preventEvent"
    @dragstart="preventEvent"
    @dragend="preventEvent"
    @dragover="preventEvent"
    @dragenter="preventEvent"
    @dragleave="preventEvent"

    @drop="preventEvent"
  >
    <overlay-loader v-if="loading" :full-screen="false" />
    <div
      v-if="!images.length"
      class="w-[190px] h-[180px] relative rounded-md border-dashed border-2 border-gray-300 flex items-center justify-center"
      :class="[{ 'cursor-pointer': !disabled }, imageContainerClasses]"
    >
      <div v-if="isDragover" class="drag-upload-cover absolute w-full h-full flex items-center justify-center" @drop="onDrop">
        <div
          class="w-full text-center text-primary flex flex-col justify-center items-center"
        >
          <PhotoIcon class="w-10 h-10" />
          <h4>
            <b>{{ dropText }}</b>
          </h4>
        </div>
      </div>
      <div
        v-else
        class=" flex flex-col justify-center items-center w-100 h-100"
        @dragover.prevent="onDragover"
      >
        <div>
          <PhotoIcon class="w-10 h-10" />
        </div>
        <div class="text-center">
          <label class="text-sm pt-1 font-semibold text-gray-600">{{
            dragText
          }}</label>
          <br>
          <p class="text-md text-primary">
            {{ browseText }}
          </p>
        </div>
        <div class="image-input absolute w-full h-full left-0 top-0">
          <label
            :for="idUpload" class="w-full h-full  block"
            :class="[disabled ? 'cursor-not-allowed' : 'cursor-pointer']"
          />
        </div>
      </div>
    </div>
    <div
      v-else-if="!showControlsForSingleImage"
      class="w-[190px] h-[180px] relative rounded-md border-2 border-gray-300 flex relative text-center border border-gray-300 rounded-md items-between justify-between"
      :class="[imageContainerClasses, { 'cursor-pointer': !disabled }]"
    >
      <div
        v-if="isDragover"
        class="absolute top-0 left-0 right-0 bottom-0 z-10 bg-gray-200 bg-opacity-50 border border-dashed border-primary-300 flex justify-center items-center"
        @drop="onDrop"
      >
        <div
          class="w-full text-center text-primary flex flex-col justify-center items-center"
        >
          <PhotoIcon class="w-10 h-10" />
          <h4>
            <b>{{ dropText }}</b>
          </h4>
        </div>
      </div>
      <div
        v-else
        class="flex flex-col gap-1 w-full"
        @dragover.prevent="onDragover"
      >
        <div
          class="w-full relative group h-[140px] rounded-md opacity-100" :class="[
            { '!h-full': !showImageName },
          ]"
        >
          <div
            class="w-full h-full flex justify-center items-center gap-2 absolute top-0 left-0 z-10 opacity-0 bg-gray-200 bg-opacity-50 group-hover:opacity-100 transition-all duration-300"
          >
            <a
              v-if="showDelete"
              class="flex cursor-pointer"
              @click.prevent="deleteImage(currentIndexImage)"
            >
              <TrashIcon class="w-10 h-10 text-red-700" />
            </a>
            <label v-if="showEdit" class="flex cursor-pointer" :for="idEdit">
              <PencilSquareIcon class="w-10 h-10" />
            </label>
          </div>
          <div
            class="h-full w-full overflow-hidden flex justify-center items-center"
          >
            <img
              class="object-fit max-h-full max-w-full rounded-md"
              :src="imagePreview"
            >
          </div>
        </div>
        <div v-if="showImageName">
          <span class="mx-auto px-2 py-1 bg-gray-200 rounded-md text-[11px]">
            {{ getImageName }}
          </span>
        </div>
      </div>
    </div>

    <div
      v-if="images.length && multiple"
      class="flex flex-wrap mt-2 max-w-[250px] min-h-[90px] overflow-auto"
      :class="smallImagesContainerClasses"
    >
      <div
        v-for="(image, index) in images" :key="index"
        class=" w-[32px] h-[32px] relative cursor-pointer rounded-sm border border-1 border-gray-300 mx-1 group"
        :class="[
          { 'border border-primary-200 ': image.highlight },
          smallImagesClasses,
        ]"
      >
        <div class="flex justify-center items-center h-full w-full">
          <img
            class="object-cover h-auto max-w-full max-h-full"
            :src="image?.path || image.image"
          >
        </div>
        <div
          v-if="showControlsForSingleImage"
          class="w-full h-full flex justify-center items-center gap-2 absolute top-0 left-0 z-10 opacity-0 bg-gray-200 bg-opacity-50 group-hover:opacity-100 transition-all duration-300"
        >
          <a
            v-if="showDelete"
            class="flex cursor-pointer"
            @click.prevent.stop="deleteImage(index)"
          >
            <TrashIcon class="w-10 h-10 text-red-700" />
          </a>
          <label v-if="showEdit" class="flex cursor-pointer" :for="idEdit">
            <PencilSquareIcon class="w-10 h-10" />
          </label>
          <a
            v-if="showCustomIcons"
            class="flex cursor-pointer"
            @click.prevent="fireCustomAction(index)"
          >
            <slot name="custom-button" :image="image" />

          </a>
        </div>
      </div>
      <div
        v-if="images.length < maxImage && showAdd"
        class="w-[32px] h-[32px] relative cursor-pointer flex justify-center items-center"
        :class="addButtonClasses"
        @drop="onDrop"
      >
        <PlusIcon class="w-10 h-8" />
        <div class="absolute h-full w-full">
          <label
            :for="idUpload" class="block w-full h-full"
            :class="[disabled ? 'cursor-not-allowed' : 'cursor-pointer']"
          />
        </div>
      </div>
    </div>
    <div>
      <input
        :id="idUpload"
        class="hidden"
        name="images"
        :multiple="multiple"
        :accept="accept"
        type="file"
        :disabled="disabled"
        @change="uploadFieldChange"
      >
      <input
        :id="idEdit"
        class="hidden"
        name="image"
        :accept="accept"
        type="file"
        :disabled="disabled"
        @change="editFieldChange"
      >
    </div>
  </div>
</template>
