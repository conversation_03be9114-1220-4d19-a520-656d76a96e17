<script lang="ts" setup>
import type { PropType } from 'vue'
import type { Staff } from '@/types'
const prop = defineProps({
  staff: {
    type: Object as PropType<Staff>,
    required: true,
  },
})

const { staff } = toRefs(prop)
</script>

<template>
  <div
    class="'relative overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
  >
    <generic-schedule :item-id="staff.uuid" model="staff" />
  </div>
</template>

<style></style>
