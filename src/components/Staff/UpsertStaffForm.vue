<script setup lang="ts">
import type { PropType } from '@vue/runtime-core'
import { TrashIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import Multiselect from 'vue-multiselect'
import { required } from '@/utils/i18n-validators'
import type { Staff } from '@/types'
import 'vue-multiselect/dist/vue-multiselect.css'
import TextInput from '../FormControls/Inputs/TextInput.vue'

const props = defineProps({
  staff: {
    type: Object as PropType<Staff>,
    default: null,
  },
  tags: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['created', 'updated', 'deleted'])

const formData = reactive<Omit<Staff, 'uuid'>>({
  name: '',
  email: '',
  phone: '',
  phone_country: '',
  image: null,
  imageLink: '',
  clients_in_same_time: '',
  tags: [],
})

const state = reactive({
  processing: false,
})
const errHandle = {
  name: [],
  image: [],
}
const { staff } = toRefs(props)
const rules = {
  name: {
    required,
  },
  clients_in_same_time: {
    required,
  },
}
const v$ = useVuelidate(rules, formData)
watch(staff, (value: Staff) => {
  formData.name = value?.name || ''
  formData.email = value?.email || ''
  formData.phone = value?.phone || ''
  formData.phone_country = value?.phone_country || ''
  formData.imageLink = value?.imageLink || ''
  formData.clients_in_same_time = value?.clients_in_same_time || 1
  formData.image = ''
  formData.tags = value?.tags || []
})

const { createStaff, updateStaff, removeStaff } = useStaffStore()
const diplayForm = useStaffStore()
const setPhoneNumber = (phoneNumber: string, phoneObject: { countryCode: string }) => {
  formData.phone = phoneNumber
  formData.phone_country = phoneObject.countryCode
}
const create = async () => {
  for (const prop in errHandle)
    errHandle[prop] = []
  v$.value.$touch()
  if (state.processing || v$.value.$invalid)
    return
  state.processing = true
  if (formData.image === '' || formData.image === null)
    delete formData.image

  const payload = { ...formData, tags: formData.tags!.map(tag => tag.id).join(',') }

  return updateStaff(staff.value.uuid, payload).then((res) => {
    emit('updated', res.data)
  }).catch((e) => {
    if (e.errors) {
      for (const prop in e.errors)
        errHandle[prop] = e.errors[prop]
    }
  }).finally(() => {
    state.processing = false
  })
}

const deleteStaff = () => {
  if (!staff.value?.uuid || state.processing)
    return
  state.processing = true
  removeStaff(staff.value.uuid).then(() => {
    emit('deleted', staff.value)
    diplayForm.showForm = true
  }).finally(() => {
    state.processing = false
  })
}
</script>

<template>
  <form class="w-full my-4 lg:basis-2/4" @submit.prevent="create">
    <div class="grid sm:grid-cols-2 grid-cols-1 gap-4">
      <div class="">
        <TextInput
          id="grid-first-name"
          v-model="formData.name"
          :label="$t('form.name')"
          :placeholder="$t('form.name')"
          required
        />
        <div v-if="errHandle.name.length > 0">
          <ErrMsg v-for="err in errHandle.name" :key="err" :msg="err" />
        </div>
        <p v-for="error of v$.name.$errors" :key="error.$uid" class="error-message">
          {{ $t(error.$message) }}
        </p>
      </div>
      <div class=" w-full">
        <div class="">
          <NumberInput
            id="grid-last-name"
            v-model="formData.clients_in_same_time" :label="$t('form.clientSaameTime')" class="
                block
                w-full
                py-3
                mb-3
                leading-tight
                text-gray-700
                rounded
                appearance-none
                px-4
                focus:outline-none focus:bg-white
              "
            min="1" :placeholder="$t('formPlaceHolder.clientSameTime')"
          />
          <p v-for="error of v$.clients_in_same_time.$errors" :key="error.$uid" class="error-message">
            {{ $t(error.$message) }}
          </p>
        </div>
      </div>
    </div>
    <div class="grid lg:grid-cols-2 grid-cols-1  gap-5 mt-5">
      <div class=" w-full">
        <TextInput
          id="grid-last-name"
          v-model="formData.email"
          :label="$t('form.email')"
          :placeholder="$t('email.enter')"
        />
      </div>
      <PhoneInput
        :model-value="formData.phone" class="w-full " label-text="form.phone"
        @update:model-value="setPhoneNumber"
      />
    </div>
    <div class="w-full mt-2 mb-6">
      <label for="image" class="block mb-2 text-xs font-bold tracking-wide text-gray-700  ">{{ $t('form.tags')
      }}</label>
      <Multiselect
        v-model="formData.tags" :multiple="true"
        track-by="id" :clear-on-select="false" :close-on-select="false" label="name" :options="tags"
      />
    </div>
    <div class="w-full mt-5 mb-6">
      <label for="image" class="block mb-2 text-xs font-bold tracking-wide text-gray-700  ">{{ $t('form.image')
      }}</label>
      <ImageInput id="image" v-model="formData.image" :link="staff?.imageLink" />
      <div v-if="errHandle.image.length > 0">
        <ErrMsg v-for="err in errHandle.image" :key="err" :msg="err" />
      </div>
    </div>
    <div class="flex justify-between w-full ms-auto sm:w-1/2">
      <BaseButton
        v-if="staff" custome-bg="bg-red-600" class="inline-flex hover:bg-red-700 disabled:bg-red-300 me-2"
        :processing="state.processing" @click="deleteStaff"
      >
        {{ $t('form.delete') }}
        <TrashIcon class="ms-2 -me-0.5 h-4 w-4" aria-hidden="true" />
      </BaseButton>
      <BaseButton class="inline-flex hover:bg-gray-800" show-icon :processing="state.processing" custome-bg="bg-gray-700">
        {{ $t('form.update') }}
      </BaseButton>
    </div>
  </form>
</template>
