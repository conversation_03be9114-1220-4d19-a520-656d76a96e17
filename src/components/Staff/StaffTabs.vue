<script setup lang="ts">
import { ref, watch } from 'vue'
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/vue'
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import type { Staff } from '@/types'

const props = defineProps({
  staff: {
    type: Object as PropType<Staff>,
    default: null,
  },
})
const { getUserInfo } = storeToRefs(useAuthStore())
const selectedTab = ref()
const { hasBoundaries } = storeToRefs(usePluginsStore())
const tabs = ['bookings', 'services', ...(hasBoundaries.value ? ['zones'] : []), 'workingHours', 'TimeOff']

const isOpenOrderModal = ref(false)
const orderId = ref('')

const openBookingModal = (order_id: string) => {
  console.log(order_id)
  isOpenOrderModal.value = true
  orderId.value = order_id
}

const closeOrderModal = () => {
  isOpenOrderModal.value = false
  orderId.value = ''
}

watch(() => props.staff, (staff) => {
  staff && (selectedTab.value = tabs[0])
})
</script>

<template>
  <TabGroup v-if="staff">
    <TabList class="flex p-1 space-x-1 bg-slate-700">
      <Tab
        v-for="(tab, idx) in tabs"
        :key="idx"
        v-slot="{ selected }"
        as="template"
      >
        <button
          class="py-2.5 w-full text-sm font-medium leading-5 text-white focus:outline-none" :class="[
            selected
              ? 'bg-slate-600 shadow'
              : 'text-white hover:bg-slate-300/[0.12]',
          ]"
        >
          {{ $t(`staff.${tab}`) }}
        </button>
      </Tab>
    </TabList>

    <TabPanels class="relative mt-2 w-full">
      <TabPanel
        v-for="(tab, idx) in tabs"
        :key="idx"
        class="bg-white focus:outline-none focus:ring-2"
      >
        <StaffTabBookings v-if="tab === 'bookings'" :staff="staff" @open-booking-modal="openBookingModal" />

        <StaffTabServices v-else-if="tab === 'services'" :staff="staff" />
        <staff-tab-working-hours v-else-if="tab === 'workingHours'" :staff="staff" />
        <staff-time-off v-else-if="tab === 'TimeOff'" :staff="staff" type="timeoff" />
        <!-- <StaffBreaks v-else-if="tab === 'break' " :staff="staff" type="break" /> -->
        <StaffTabBoundaries v-else-if="tab === 'zones'" :staff="staff" />

        <div v-else class="p-4">
          {{ $t(`staff.${tab}`) }}
        </div>
      </TabPanel>
    </TabPanels>

    <order-details-modal
      v-if="isOpenOrderModal"
      :is-open="isOpenOrderModal"
      :order-id="orderId"
      @close="closeOrderModal"
    />
  </TabGroup>
</template>

