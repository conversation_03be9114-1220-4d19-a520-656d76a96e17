<script setup lang="ts">
import type { PropType } from 'vue'
import type { Booking, Staff } from '@/types'
import useBooking from '@/composables/useBooking'

const props = defineProps({
  staff: {
    type: Object as PropType<Staff>,
    default: null,
  },
})

const emits = defineEmits(['openBookingModal'])

const { tableData, fetchEventpage } = useBooking()
const route = useRoute()
const fetchBookings = (page = 1) => {
  fetchEventpage(false, page, { staff: props.staff.uuid, perPage: 8 })
}
const router = useRouter()
const i18n = useI18n()
const headers = computed(() => {
  return [
    {
      title: i18n.t('booking.booking_number'),
    },
    {
      title: i18n.t('dashboard.booking.customer'),
    },
    {
      title: i18n.t('dashboard.booking.staff'),
    },
    {
      title: i18n.t('dashboard.booking.service'),
    },
    {
      title: i18n.t('dashboard.booking.date'),
    },
    {
      title: i18n.t('dashboard.booking.start'),
    },
    {
      title: i18n.t('dashboard.booking.end'),
    },
    {
      title: i18n.t('price'),
    },
    {
      title: i18n.t('booking.status'),
    },
  ]
})
const redirectToBooking = (item: Booking) => {
  emits('openBookingModal', item.order_id)
}
onMounted(() => {
  fetchBookings()
})
</script>

<template>
  <div>
    <div class="overflow-x-auto -mx-4 -my-2 sm:-mx-6 lg:-mx-8">
      <div class="inline-block py-2 min-w-full align-middle md:px-6 lg:px-8">
        <div
          class="overflow-hidden ring-1 ring-black ring-opacity-5 shadow md:rounded-lg"
        >
          <bookings-grid
            :booking-list="tableData.bookingList"
            :is-loading="tableData.processing"
            :on-row-click="redirectToBooking"
          />
        </div>
      </div>
    </div>

    <div class="px-4">
      <Pagination
        v-if="tableData.bookingList.length"
        :pagination-meta="tableData.paginationMeta"
        :pagination-links="tableData.paginationLinks"
        @change="fetchBookings"
      />
    </div>
  </div>
</template>
