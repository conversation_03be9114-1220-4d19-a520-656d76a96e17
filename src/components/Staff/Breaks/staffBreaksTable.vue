<script setup lang="ts">
import type { PropType } from 'vue'
import { PencilSquareIcon, TrashIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import type { Staff } from '@/types/staff'
import { useStaffStore } from '@/stores/staff'
// import {} from "@/stores/staff"
import type { ComputedRef, header } from '@/types'
import type { StaffTimeOff } from '@/types/staffTimeOffs'
import i18n from '@/i18n'
const props = defineProps({
  staffTimeOffs: {
    type: Array as PropType<StaffTimeOff[]>,
  },
  staff: {
    type: Object as PropType<Staff>,
  },
  title: {
    type: String,
  },
})
const emit = defineEmits(['editTimeOffs', 'deleteTime'])
const { t } = useI18n()
const deleteBreaks = (timeOffsUuid: string) => {
  emit('deleteTime', props.staff?.uuid, timeOffsUuid)
}
const editBreaks = (timeOffs: StaffTimeOff) => {
  emit('editTimeOffs', props.staff?.uuid, timeOffs)
}
const timeOffsVar = ref<StaffTimeOff[]>()
onMounted(() => {
  timeOffsVar.value = props.staffTimeOffs
})
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('dashboard.booking.day'),
    },
    {
      title: t('dashboard.booking.start'),
    },
    {
      title: t('dashboard.booking.end'),
    },
    {
      title: '#',
      className: 'text-center',
    },

  ]
})
</script>

<template>
  <div class="">
    <generic-table
      :data="staffTimeOffs"
      item-key="id"
      :headers="headers"
    >
      <template #row="{ item }">
        <grid-td
          class="py-4 pr-3 pl-4 text-sm whitespace-nowrap sm:pl-6"
          :default-style="false"
        >
          <div class="flex items-center">
            <div class="">
              <div class="font-medium text-gray-900">
                {{ $t(`days.${item.day}`) }}
              </div>
              <div class="text-gray-500" />
            </div>
          </div>
        </grid-td>
        <grid-td>
          {{ item.start_time?.slice(0, 5) }}
        </grid-td>
        <grid-td>
          {{ item.end_time?.slice(0, 5) }}
        </grid-td>
        <grid-td class="flex gap-2" :class="[i18n.global.locale.value === 'ar' ? 'justify-start' : 'justify-center']">
          <BaseButton class="hover:bg-gray-800 w-fit" custome-bg="bg-gray-700" @click="editBreaks(item)">
            <PencilSquareIcon
              class="w-4 h-4 cursor-pointer"
              aria-hidden="true"
            />
          </BaseButton>
          <BaseButton class="hover:bg-red-700 w-fit" custome-bg="bg-red-600" @click="deleteBreaks(item.id)">
            <TrashIcon class="mx-1 w-4 h-4" aria-hidden="true" />
          </BaseButton>
        </grid-td>
      </template>
    </generic-table>
  </div>
</template>
