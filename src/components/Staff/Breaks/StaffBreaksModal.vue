<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import type { PropType } from 'vue'

import { useStaffStore } from '@/stores/staff'
import { required, requiredIf } from '@/utils/i18n-validators'
import type { Staff } from '@/types'
import fetchStaffPage from '@/composables/useStaff'
import type { StaffTimeOff } from '@/types/staffTimeOffs'
import timeForms from '@/composables/timeForms'

const prop = defineProps({
  showModal: {
    type: Boolean,
  },
  staff: {
    type: Object as PropType<Staff>,
  },
  timeOffsModal: {
    type: Object as PropType<StaffTimeOff | null>,
    required: false,
  },
  title: {
    type: String,
  },

})
const emit = defineEmits(['close', 'active', 'updateTimeOffs'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { addBreaks } = useStaffStore()
const processing = ref(false)
const loader = ref(false)

const formData2 = reactive({
  day: '',
  start_time: { hours: '00', minutes: '00' },
  end_time: { hours: '00', minutes: '00' },
})
const rules2 = {
  day: {
    required,
  },
  start_time: {
    required: requiredIf((val) => {
      return !(formData2.start_time || formData2.start_time?.hours == '00' || formData2.start_time?.minutes == '00')
    }),
  },
  end_time: {
    required: requiredIf((val) => {
      return !(formData2.end_time || formData2.end_time?.hours == '00' || formData2.end_time?.minutes == '00')
    }),

  },
}
const v1$ = useVuelidate(rules2, formData2)

const closeModal = () => {
  emit('close')
}
const days = ref<string[]>(['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'])
const create = () => {
  v1$.value.$touch()
  if (v1$.value.$invalid)
    return
  loader.value = true
  processing.value = true
  addBreaks(prop.staff?.uuid, formData2)
    .then(() => {
      fetchStaffPage()
      closeModal()
      emit('active')
    }).finally(() => {
      loader.value = false
      processing.value = false
    })
}
const modalTitle = ref('break')
const { timeOffsModal } = toRefs(prop)
watch(timeOffsModal, (val) => {
  if (val?.id?.length > 0) {
    formData2.day = val?.day || ''
    formData2.start_time = {
      hours: `${val?.start_time?.slice(0, 2)}` || '00',
      minutes: `${val?.start_time?.slice(3, 5)}` || '00',
    }
    formData2.end_time = {
      hours: `${val?.end_time?.slice(0, 2)}` || '00',
      minutes: `${val?.end_time?.slice(3, 5)}` || '00',
    }
  }
  else {
    formData2.day = val?.day || ''
    formData2.start_time = {
      hours: '00',
      minutes: '00',
    }
    formData2.end_time = {
      hours: '00',
      minutes: '00',
    }
  }
  if (formData2.day.length > 0) {
    modalTitle.value = 'breakEdit'
  }
  else {
    modalTitle.value = 'break'
    v1$.value.$reset()
  }
})
const updateTimeOffs = (timeOffsModal: StaffTimeOff) => {
  formData2.id = timeOffsModal.value.id
  emit('updateTimeOffs', formData2)
}
const save = () => {
  if (timeOffsModal?.value?.id)
    updateTimeOffs(timeOffsModal)
  else
    create()
}
const { timesFroms, timesTo } = timeForms()
</script>

<template>
  <Modal
    v-if="showModal"
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="modalTitle"
    @close="closeModal"
  >
    <overlay-loader v-if="loader" />
    <form
      class="w-full mb-4 mt-10 lg:basis-2/4 text-start"
      @submit.prevent="save"
    >
      <div class="">
        <div class="grid lg:grid-cols-3 grid-cols-1 gap-4 mb-6">
          <div class="">
            <form-group :validation="v1$" name="day">
              <template #default="{ attrs }">
                <SelectInput
                  id="grid-last-name"
                  v-bind="attrs"
                  v-model="formData2.day"
                  :label="$t('form.day')"
                  :placeholder="${$t('form.day')}"
                  class="block w-full text-gray-700 py-2 focus:border-primary-500 focus:ring-primary-500 rounded-md focus:outline-none focus-visible:ring--gray-300  sm:text-sm border-gray-300"
                >
                  <option selected value="">
                    {{ $t("form.selectday") }}
                  </option>
                  <option v-for="day in days" :key="day" :value="day">
                    {{ $t(`days.${day}`) }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
          <div class="">
            <form-group :validation="v1$" name="start_time" error-name="start">
              <template #default="{ attrs }">
                <SelectInput
                  v-bind="attrs"
                  id="grid-start_time"
                  v-model="formData2.start_time"
                  :label="$t('form.startWith')"
                  :placeholder="${$t('form.startWith')}"
                  class="block w-full text-gray-700 py-2 focus:border-primary-500 focus:ring-primary-500 rounded-md focus:outline-none focus-visible:ring--gray-300  sm:text-sm border-gray-300"
                >
                  <option selected value="">
                    {{ $t("form.select") }}
                  </option>
                  <option
                    v-for="(time, index) in timesFroms"
                    :key="index"
                    :value="{
                      hours: time.slice(0, 2),
                      minutes: time.slice(3, 5),
                    }"
                  >
                    {{ time
                    }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
          <div class="">
            <form-group :validation="v1$" name="end_time" error-name="end">
              <template #default="{ attrs }">
                <SelectInput
                  v-bind="attrs"
                  id="grid-endWith"
                  v-model="formData2.end_time"
                  :label="$t('form.endWith')"
                  :placeholder="${$t('form.endWith')}"
                  class="block w-full text-gray-700 py-2 focus:border-primary-500 focus:ring-primary-500 rounded-md focus:outline-none focus-visible:ring--gray-300  sm:text-sm border-gray-300"
                >
                  <option selected value="">
                    {{ $t("form.select") }}
                  </option>

                  <option
                    v-for="(time, index) in timesTo"
                    :key="index"
                    :value="{
                      hours: time.slice(0, 2),
                      minutes: time.slice(3, 5),
                    }"
                  >
                    {{ time }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
        </div>
      </div>
      <BaseButton
        v-if="timeOffsModal?.id?.length === 0"
        class="hover:bg-green-800 w-fit ms-auto"
        show-icon
        :processing="processing"
        type="submit"
        custome-bg="bg-green-600"
      >
        {{ $t("form.create") }}
      </BaseButton>
      <BaseButton
        v-else
        class="hover:bg-gray-800 w-fit ms-auto"
        show-icon
        :processing="processing"
        custome-bg="bg-gray-700"
        type="submit"
      >
        {{ $t("form.update") }}
      </BaseButton>
    </form>
  </Modal>
</template>
