<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import i18n from '@/i18n'
import type { Staff } from '@/types'
import { useAppsStore } from '@/stores/apps'
const prop = defineProps<{
  staff: Staff
}>()
const { showNotification } = useNotifications()
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())

const loader = ref(false)
const apps = ref([])

const { fetchStaffApps, unSyncApp } = useAppsStore()
const { staff } = toRefs(prop)

onBeforeMount(() => {
  loader.value = true
  fetchStaffApps(staff.value?.uuid).then((res: any) => {
    apps.value = res.data
  }).finally(() => {
    loader.value = false
  })
})

const selectedApp = ref(null)

const showConfirmModal = computed(() => !!selectedApp.value?.uuid)

const handleClick = (app) => {
  if (!app.installed) {
    window.open(app.oauthLink, '_blank')
    return
  }

  selectedApp.value = app
}

const unSyncSelectedApp = () => {
  unSyncApp(staff.value?.uuid, selectedApp.value?.uuid).then((res: any) => {
    if (res) {
      selectedApp.value = null
      apps.value = [...apps.value].map((app) => {
        if (app.uuid === selectedApp.value?.uuid)
          app.installed = false

        return app
      })

      showNotification({
        title: i18n.global.t('Success'),
        type: 'success',
        message: i18n.global.t('operations.uninstalled'),
      })
    }
  })
}
</script>

<template>
  <div class="relative">
    <overlay-loader v-if="loader" :full-screen="false" />
    <confirmation-modal
      v-if="showConfirmModal"
      :is-open="showConfirmModal"
      :dir="getLocale(locale)?.direction"
      @closed="selectedApp = null"
      @removed="unSyncSelectedApp"
    >
      <p class="leading-7 text-start">
        هل انت متأكد من إلفاء المزامنة؟
      </p>
    </confirmation-modal>
    <div class="relative grid grid-cols-1 gap-8 py-5 lg:grid-cols-3 md:grid-cols-2 auto-rows-fr">
      <div
        v-for="app of apps" :key="app.id"
        class="bg-white rounded-lg p-4 border shadow-neutral-300 relative shadow-3xl hover:shadow transition-all duration-300 cursor-pointer"
        :class="[!!app?.installed ? 'border-teal-500' : 'border-gray-200']"
      >
        <a href="#" @click.prevent="handleClick(app)">
          <div class="p-5 flex flex-row gap-6">
            <img
              class="w-9 h-9"
              :src="
                app?.banner_url
                  || 'https://www.freeiconspng.com/thumbs/message-icon-png/message-icon-png-17.png'
              "
              :alt="app.name"
            >
            <div>
              <h5 class="mb-2 text-1xl font-bold tracking-tight text-gray-900">
                {{ app?.name }}
              </h5>
              <p class="text-sm text-neutral-400">
                {{ app?.description }}
              </p>
              <span />
            </div>
          </div>
          <span
            v-if="installed"
            class="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-700 absolute bottom-2 right-3 rtl:left-3 rtl:right-auto "
          >
            {{ $t("installed") }}
          </span>
        </a>
      </div>
    </div>
  </div>
</template>
