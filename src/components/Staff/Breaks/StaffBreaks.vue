<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import type { PropType } from 'vue'
import component from 'vue-tailwind-datepicker'
import { useStaffStore } from '@/stores/staff'
import type { Staff } from '@/types'
import type { StaffTimeOff } from '@/types/staffTimeOffs'

const prop = defineProps({
  staff: {
    type: Object as PropType<Staff>,
  },
  type: {
    type: String,
  },
})
const emit = defineEmits([''])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { addBreaks, getBreaks, deleteBreaks, updateBreaks } = useStaffStore()
const { staff } = toRefs(prop)
const showModal = ref(false)
const user_has_timeoff = ref(true)
const loader = ref(false)
const showHours = () => {
  showModal.value = true
}

const create = () => {

}
const staffTimeOffs = ref([])
onMounted(() => {
  loader.value = true

  getBreaks(prop.staff?.uuid).then((res) => {
    if (res.data.length === 0) {
      user_has_timeoff.value = false
    }
    else {
      staffTimeOffs.value = res.data
      loader.value = false
    }
    loader.value = false
  })
})
watch(staff, (val: Staff) => {
  loader.value = true

  prop.staff?.has_breaks ? user_has_timeoff.value = true : user_has_timeoff.value = false
  if (prop.staff?.has_breaks) {
    getBreaks(prop.staff?.uuid).then((res) => {
      staffTimeOffs.value = res.data
      loader.value = false
    })
  }
  else {
    loader.value = false
  }
})
const active = () => {
  user_has_timeoff.value = true
  loader.value = true
  getBreaks(prop.staff?.uuid).then((res) => {
    staffTimeOffs.value = res.data
    loader.value = false
  })
}
const fetchTimeOffs = () => {
  loader.value = true
  return getBreaks(prop.staff?.uuid).then((res) => {
    staffTimeOffs.value = res.data
  }).finally(() => {
    loader.value = false
  })
}
const deleteTime = (staffUuid: string, timeOffsUuid: string) => {
  loader.value = true
  deleteBreaks(staffUuid, timeOffsUuid).finally(() => {
    loader.value = false
  })
  staffTimeOffs.value = staffTimeOffs.value.filter((time) => {
    return time.id !== timeOffsUuid
  })
  if (staffTimeOffs.value.length === 0)
    user_has_timeoff.value = false
}
const timeOffsModal = ref<StaffTimeOff | null>({ name: '', start_at: '', end_at: '', uuid: '' })
const editTimeOffs = (staffUuid: string, timeOffs: StaffTimeOff) => {
  timeOffsModal.value = timeOffs
  showModal.value = true
}
const closeModal = () => {
  showModal.value = false
  timeOffsModal.value = { name: '', start_at: '', end_at: '', uuid: '' }
}
const start_time = ref([])
const end_time = ref([])
const updateTimeOffsFun = (selectedTimeOffs: any) => {
  start_time.value = []
  end_time.value = []
  updateBreaks(prop.staff?.uuid, selectedTimeOffs).then(() => {
    fetchTimeOffs().then(() => {
      showModal.value = false
    })
  }).catch((err) => {
    if (err.errors.start)
      start_time.value = err.errors.start

    if (err.errors.end)
      end_time.value = err.errors.end
  })
}
</script>

<template>
  <div class="relative">
    <generic-alert type="warning" custom-classes="w-full" :message="$t('warning_break_tab')" />
    <overlay-loader v-if="loader" :full-screen="false" />
    <StaffBreaksModal :show-modal="showModal" :time-offs-modal="timeOffsModal" :staff="staff" :start-err="start_time" :end-err="end_time" @close="closeModal()" @active="active" @update-time-offs="updateTimeOffsFun" />
    <BaseButton v-if="user_has_timeoff" class=" hover:bg-green-700 w-fit ms-auto my-6" custome-bg="bg-green-600" @click="showModal = true">
      {{ $t('form.breaks') }}
    </BaseButton>
    <empty-state v-if="!user_has_timeoff" :text="prop.type" @show-hours="showHours" />
    <StaffBreaksTable v-if="user_has_timeoff" :staff-time-offs="staffTimeOffs" :staff="staff" :title="type" @delete-time="deleteTime" @edit-time-offs="editTimeOffs" />
  </div>
</template>
