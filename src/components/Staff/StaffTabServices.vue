<script setup lang="ts">
import type { PropType } from '@vue/runtime-core'
import { Switch } from '@headlessui/vue'
import type { ComputedRef } from 'vue'
import type { Service, Staff, header } from '@/types'
import useStaff from '@/composables/useStaff'
const props = defineProps({
  staff: {
    type: Object as PropType<Staff>,
    default: null,
  },
})

const { t } = useI18n()

const { tableServicData, fetchServices, toggleService, currentToggle } = useStaff()

watch(
  () => props.staff,
  (staff) => {
    props.staff && fetchServices(staff)
  },
)

const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('serviceProvider.tabs.services.toggle'),
    },
    {
      title: t('serviceProvider.tabs.services.name'),
    },
  ]
})

onMounted(() => {
  fetchServices(props.staff)
})
</script>

<template>
  <div class="">
    <div
      v-if="tableServicData.processing"
      class="flex relative justify-between items-center p-7 my-6"
    >
      <overlay-loader :full-screen="false" />
    </div>
    <generic-table
      v-else
      :data="tableServicData.ServiceList"
      item-key="uuid"
      :headers="headers"
    >
      <template #row="{ item }">
        <grid-td
          class="py-4 pr-3 pl-4 text-sm whitespace-nowrap sm:pl-6"
          :default-style="false"
        >
          <div class="flex items-center">
            <the-loader v-if="currentToggle === item.uuid" />
            <Switch
              v-else
              v-model="item.selected"
              class="inline-flex relative flex-shrink-0 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              :class="[item.selected ? 'bg-primary-600' : 'bg-gray-200']"
              @update:model-value="() => toggleService(staff, item)"
            >
              <span class="sr-only">Service Status</span>
              <span
                aria-hidden="true"
                class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
                :class="[
                  item.selected
                    ? 'translate-x-5 rtl:-translate-x-5'
                    : 'translate-x-0',
                ]"
              />
            </Switch>
          </div>
        </grid-td>
        <grid-td>
          {{ item.name_with_category }}
        </grid-td>
      </template>
    </generic-table>
  </div>

  <div class="px-4">
    <Pagination
      v-if="tableServicData.ServiceList.length"
      :pagination-meta="tableServicData.paginationMeta"
      :pagination-links="tableServicData.paginationLinks"
      @change="(page) => fetchServices(staff, page)"
    />
  </div>
</template>
