<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import type { PropType } from 'vue'
import { useBoundaries } from '@/stores/boundaries'
import type { Staff } from '@/types'
const prop = defineProps({
  staff: {
    type: Object as PropType<Staff>,
    required: true,
  },
})
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())

const { staff } = toRefs(prop)
const proccessing = ref(false)
const { fetchBoundaries, deleteBoundary } = useBoundaries()
const { getBoundaries } = storeToRefs(useBoundaries())

const fetchStaffBoundaries = async () => {
  proccessing.value = true
  try {
    await fetchBoundaries('staff', staff.value.uuid as string)
  }
  finally {
    proccessing.value = false
  }
}
const boundaryId = ref<string | null>(null)
watch(
  () => staff,
  async (staff) => {
    if (staff)
      await fetchStaffBoundaries()
  },
  { immediate: true },
)
const showModal = ref(false)
const openCreateModel = () => {
  boundaryId.value = null
  showModal.value = true
}

const i18n = useI18n()
const headers = computed(() => {
  return [
    {
      title: i18n.t('form.name'),
    },
    {
      title: '#',
    },
  ]
})
const openEditModel = (item: any) => {
  showModal.value = true
  boundaryId.value = item.id
}

const showConfirmModal = ref(false)
const openConfirmModal = (item) => {
  boundaryId.value = item.id
  showConfirmModal.value = true
}
const deleteRecord = async () => {
  try {
    proccessing.value = true
    await deleteBoundary('staff', staff.value.uuid, boundaryId.value as string)
    showConfirmModal.value = false
  }
  finally {
    proccessing.value = false
  }
}
</script>

<template>
  <div class="'relative">
    <overlay-loader v-if="proccessing" :full-screen="false" />
    <confirmation-modal
      v-if="showConfirmModal"
      :is-open="showConfirmModal"
      :dir="getLocale(locale)?.direction"
      @closed="showConfirmModal = false"
      @removed="deleteRecord"
    />
    <generic-boundary-modal
      v-model:show-modal="showModal"
      model="staff"
      :item-id="staff.uuid"
      :boundary-id="boundaryId"
      ,@close="boundaryId = null"
      @updated="fetchStaffBoundaries"
      @created="fetchStaffBoundaries"
    />
    <BaseButton
      v-if="getBoundaries?.length"
      class="my-6 hover:bg-green-700 w-fit ms-auto"
      custome-bg="bg-green-600"
      @click="openCreateModel"
    >
      {{ $t("new_zone") }}
    </BaseButton>
    <div
      v-if="getBoundaries?.length"
      class=""
    >
      <generic-table
        :headers="headers"
        :data="getBoundaries"
      >
        <template #row="{ item }">
          <grid-td
            class="px-4 py-4 text-sm whitespace-nowrap sm:pl-6"
            :default-style="false"
          >
            {{ item.name }}
          </grid-td>
          <grid-td
            class="flex gap-2"
            :class="[
              i18n.locale.value === 'ar'
                ? 'justify-start'
                : 'justify-center',
            ]"
          >
            <BaseButton
              class="hover:bg-gray-800 w-fit"
              custome-bg="bg-gray-700"
              @click="openEditModel(item)"
            >
              {{ $t("form.edit") }}
            </BaseButton>
            <BaseButton
              class="hover:bg-red-700 w-fit"
              custome-bg="bg-red-600"
              @click="openConfirmModal(item)"
            >
              {{ $t("form.delete") }}
            </BaseButton>
          </grid-td>
        </template>
      </generic-table>
    </div>
    <empty-state
      v-else
      text="zones"
      btn-text="customize_zone"
      @show-hours="openCreateModel"
    />
  </div>
</template>

<style></style>
