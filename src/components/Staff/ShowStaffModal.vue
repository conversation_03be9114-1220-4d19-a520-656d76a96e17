<script setup lang="ts">
import { storeToRefs } from "pinia";
import type { PropType } from "@vue/runtime-core";
import useVuelidate from "@vuelidate/core";
import Multiselect from "vue-multiselect";
import { required, minValue, requiredIf } from "@/utils/i18n-validators";
import type { Staff, Tag, Service } from "@/types";
import type { StaffTimeOff } from "@/types/staffTimeOffs";
import { useBoundaries } from "@/stores/boundaries";
import useStaff from "@/composables/useStaff";
import useBooking from "@/composables/useBooking";
import { usePluginsStore } from "@/stores/plugins";
import { useLocalesStore } from "@/stores/locales";
import { useAuthStore } from "@/stores/auth";
import { useServicesStore } from "@/stores/services";
import { useStaffStore } from "@/stores/staff";
import { PlusIcon, PencilSquareIcon, TrashIcon } from "@heroicons/vue/24/outline";
import { Switch } from '@headlessui/vue';
import MultiSelectInput from "../FormControls/Inputs/MultiSelectInput.vue";

const boundariesStore = useBoundaries();
const { getBoundaries } = storeToRefs(boundariesStore);
const { hasCommission } = storeToRefs(usePluginsStore());

const props = defineProps({
  staff: {
    type: Object as PropType<Staff>,
    default: null,
  },
  showModal: {
    type: Boolean,
    default: false,
  },
  tags: {
    type: Array as PropType<Tag[]>,
    default: () => [],
  },
});

const emit = defineEmits(["created", "closed", "updated", "close-show-modal"]);
const { locale, t } = useI18n();
const { getLocale } = storeToRefs(useLocalesStore());
const processing = ref(false);

const localStaff = ref<Staff | null>(null);

// Update local staff data when props change
watch(() => props.staff, (newStaff) => {
  if (newStaff) {
    localStaff.value = { ...newStaff };
  }
}, { immediate: true, deep: true });
// Staff Services composable

const { fetchStaffBoundariesList, currentStaffBoundary, toggleStaffBoundary , tableServicData, fetchServices, currentToggle, toggleService } = useStaff();

// Staff Bookings composable
const { tableData: bookingTableData, fetchEventpage } = useBooking();

// Staff TimeOffs state
const { getTimeOffs, addTimeOffs, updateTimeOffs, deleteTimeOffs, updateStaff } = useStaffStore();
const staffTimeOffs = ref<StaffTimeOff[]>([]);
const timeOffsLoading = ref(false);

// Service Assignment Modal State
const showServiceModal = ref(false);
const serviceModalLoading = ref(false);

const showBoundariesModal = ref(false);
const boundariesModalLoading = ref(false);
// TimeOff Management Modal State
const showTimeOffModal = ref(false);
const timeOffModalLoading = ref(false);
const editingTimeOff = ref<StaffTimeOff | null>(null);

// Basic Info Edit Modal State
const showBasicInfoModal = ref(false);
const basicInfoLoading = ref(false);

// TimeOff Form Data
const timeOffFormData = reactive({
  name: "",
  start_at: "",
  end_at: "",
});

// Basic Info Form Data
const userInfo = useAuthStore();
const teamsList = computed(() => userInfo.getUserInfo.teams);
const link = ref("");

const basicInfoFormData = reactive<Omit<Staff, "uuid">>({
  name: "",
  email: "",
  phone: "",
  phone_country: "",
  phone2: "",
  phone_country2: "",
  image: "",
  clients_in_same_time: 1,
  commission: "",
  team_id: "",
  tags: [],
});

// Basic Info Form Validation
const basicInfoValidationData = computed(() => ({
  name: basicInfoFormData.name,
  clients_in_same_time: basicInfoFormData.clients_in_same_time,
  team_id: basicInfoFormData.team_id,
}));

const basicInfoRules = {
  name: {
    required
  },
  clients_in_same_time: {
    required,
    minValue: minValue(1),
  },
  team_id: {
    required,
  },
};

const v$ = useVuelidate(basicInfoRules, basicInfoValidationData);

const servicesHeaders = computed(() => {
  return [
    { title: t("serviceProvider.tabs.services.name") },
    { title: t("form.actions") },
  ];
});

const boundariesHeaders = computed(() => {
  return [
    { title: t("serviceProvider.tabs.zones.zone") },
    { title: t("form.actions") },
  ];
});

// Computed filtered lists with proper typing
const assignedServices = computed(() => {
  return (tableServicData.ServiceList as Service[]).filter((item) => (item as any).selected);
});

const servicesFormData = reactive<{ services: (string | number)[] }>({ services: [] });
const boundariesFormData = reactive<{ boundaries: (string | number)[] }>({ boundaries: [] });

// Transform services to have the correct structure for MultiSelectInput
const serviceOptions = computed(() => {
  return (tableServicData.ServiceList as Service[]).map(service => ({
    id: service.uuid,
    name: service.name || service.name_with_category || 'Unnamed Service'
  }));
});

const assignedBoundaries = computed(() => {
  return (tableServicData.BoundaryList as any[]).filter((item) => (item as any).selected);
});


watch(assignedServices, (services) => {
  servicesFormData.services = services.map(service => service.uuid);
}, { immediate: true });

watch(assignedBoundaries, (boundaries) => {
  boundariesFormData.boundaries = boundaries.map(boundary => boundary.uuid);
}, { immediate: true });

const fetchStaffTimeOffs = async () => {
  if (!props.staff?.uuid) return;
  
  timeOffsLoading.value = true;
  try {
    const response = await getTimeOffs(props.staff.uuid);
    staffTimeOffs.value = response.data || [];
  } catch (error) {
    console.error('Error fetching timeoffs:', error);
    staffTimeOffs.value = [];
  } finally {
    timeOffsLoading.value = false;
  }
};

const fetchStaffBookings = () => {
  if (!props.staff?.uuid) return;
  fetchEventpage(false, 1, { staff: props.staff.uuid, perPage: 8 });
};

watch(() => props.staff, async (staff) => {
  if (staff?.uuid) {
    fetchServices(staff);
    await fetchStaffBoundariesList(staff, '*');
    fetchStaffBookings();
    fetchStaffTimeOffs();
  }
}, { immediate: true });

const closeModal = () => {
  emit("updated");
  emit("closed");
};

// Service Modal Functions
const openServiceModal = () => {
  showServiceModal.value = true;
};

const closeServiceModal = () => {
  showServiceModal.value = false;
};

const handleServiceToggle = async (service: any) => {
  if (!props.staff) return;
  
  await toggleService(props.staff, service);
};

const handleServiceSave = async () => {
  if (!props.staff) return;
  
  serviceModalLoading.value = true;
  try {
    const currentlyAssigned = assignedServices.value.map(s => s.uuid);
    const newlySelected = servicesFormData.services as string[];
    
    const servicesToAdd = newlySelected.filter(id => !currentlyAssigned.includes(id));
    const servicesToRemove = currentlyAssigned.filter(id => !newlySelected.includes(id));
    
    for (const serviceId of servicesToAdd) {
      const service = (tableServicData.ServiceList as Service[]).find(s => s.uuid === serviceId);
      if (service) {
        await toggleService(props.staff, service);
      }
    }
    
    for (const serviceId of servicesToRemove) {
      const service = (tableServicData.ServiceList as Service[]).find(s => s.uuid === serviceId);
      if (service) {
        await toggleService(props.staff, service);
      }
    }
    
    await fetchServices(props.staff);
    
    closeServiceModal();
  } catch (error) {
    console.error('Error updating service assignments:', error);
  } finally {
    serviceModalLoading.value = false;
  }
};

// Boundaries Modal Functions
const openBoundariesModal = async () => {
  if (!props.staff?.uuid) return;
  
  // // Refresh staff boundaries data first
  // await fetchStaffBoundariesList(props.staff, '*');
  
  // boundariesFormData.boundaries = assignedBoundaries.value.map(boundary => boundary.uuid);
  
  showBoundariesModal.value = true;
};

const closeBoundariesModal = () => {
  showBoundariesModal.value = false;
};

const handleBoundariesSave = async () => {
  if (!props.staff) return;
  
  boundariesModalLoading.value = true;
  try {
    // Get current assignments
    await toggleStaffBoundary(props.staff, boundariesFormData.boundaries.join(','));
    
    await fetchStaffBoundariesList(props.staff, '*');
    
    closeBoundariesModal();
  } catch (error) {
    console.error('Error updating boundary assignments:', error);
  } finally {
    boundariesModalLoading.value = false;
  }
};

const handleServiceDelete = async (service: Service) => {
  if (!props.staff) return;
  
  if (confirm(t('confirmModal.removeServiceAssignment'))) {
    await toggleService(props.staff, service);
  }
};

const handleBoundaryDelete = async (boundary: any) => {
  if (!props.staff) return;
  
  if (confirm(t('confirmModal.removeBoundaryAssignment'))) {
    const boundaryId = boundary.uuid || boundary.id;
    await toggleStaffBoundary(props.staff, boundaryId);
  }
};

const openAddTimeOffModal = () => {
  editingTimeOff.value = null;
  timeOffFormData.name = "";
  timeOffFormData.start_at = "";
  timeOffFormData.end_at = "";
  showTimeOffModal.value = true;
};

const openEditTimeOffModal = (timeOff: StaffTimeOff) => {
  editingTimeOff.value = timeOff;
  timeOffFormData.name = timeOff.name;
  timeOffFormData.start_at = timeOff.start_at;
  timeOffFormData.end_at = timeOff.end_at;
  showTimeOffModal.value = true;
};

const closeTimeOffModal = () => {
  showTimeOffModal.value = false;
  editingTimeOff.value = null;
  timeOffFormData.name = "";
  timeOffFormData.start_at = "";
  timeOffFormData.end_at = "";
};

const handleTimeOffSave = async () => {
  if (!props.staff?.uuid) return;
  
  timeOffModalLoading.value = true;
  try {
    if (editingTimeOff.value) {
      const updateData = {
        ...timeOffFormData,
        uuid: editingTimeOff.value.uuid,
      };
      await updateTimeOffs(props.staff.uuid, updateData);
    } else {
      await addTimeOffs(props.staff.uuid, timeOffFormData);
    }
    await fetchStaffTimeOffs();
    closeTimeOffModal();
  } catch (error) {
    console.error('Error saving timeoff:', error);
  } finally {
    timeOffModalLoading.value = false;
  }
};

const handleTimeOffDelete = async (timeOff: StaffTimeOff) => {
  if (!props.staff?.uuid || !timeOff.uuid) return;
  
  if (confirm(t('confirmModal.deleteTimeOff'))) {
    try {
      await deleteTimeOffs(props.staff.uuid, timeOff.uuid);
      await fetchStaffTimeOffs();
    } catch (error) {
      console.error('Error deleting timeoff:', error);
    }
  }
};

// Form validation
const isTimeOffFormValid = computed(() => {
  return timeOffFormData.name.trim() !== '' && 
         timeOffFormData.start_at !== '' && 
         timeOffFormData.end_at !== '';
});

// Defensive modal logic: ensure only one modal is open at a time
watch(() => props.showModal, (val) => {
  if (val) {
    emit('close-show-modal');
  }
});

// Format functions
const formatTime = (time: string) => {
  if (!time) return '-';
  return new Date(time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

const formatDate = (date: string) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString();
};

const openBookingModal = (bookingId: string) => {
  console.log('Open booking modal for:', bookingId);
};

// Basic Info Modal Functions
const openBasicInfoModal = () => {
  if (!localStaff.value) return;
  
  const staff = localStaff.value;
  basicInfoFormData.name = staff.name || '';
  basicInfoFormData.email = staff.email || '';
  basicInfoFormData.phone = staff.phone || '';
  basicInfoFormData.phone_country = staff.phone_country || '';
  basicInfoFormData.phone2 = staff.phone2 || '';
  basicInfoFormData.phone_country2 = staff.phone_country2 || '';
  basicInfoFormData.clients_in_same_time = staff.clients_in_same_time || 1;
  basicInfoFormData.commission = staff.commission || '';
  basicInfoFormData.team_id = staff.team?.uuid || '';
  link.value = staff.imageLink || '';
  basicInfoFormData.image = staff.image || null;
  
  showBasicInfoModal.value = true;
};

const closeBasicInfoModal = () => {
  showBasicInfoModal.value = false;
  v$.value.$reset();
};

const handleBasicInfoSave = async () => {
  v$.value.$touch();
  if (v$.value.$invalid || !props.staff?.uuid) return;
  
  basicInfoLoading.value = true;
  try {
    const payload = { ...basicInfoFormData };
    if (link.value === props.staff.imageLink) {
      delete payload.image;
    }
    
    const updatedStaff = await updateStaff(props.staff.uuid, payload);
    
    // Update local staff data immediately
    if (localStaff.value && updatedStaff) {
      localStaff.value = { ...localStaff.value, ...payload };
      if (link.value) {
        localStaff.value.imageLink = link.value;
      }
    }
    
    emit("updated");
    closeBasicInfoModal();
  } catch (error) {
    console.error('Error updating basic info:', error);
  } finally {
    basicInfoLoading.value = false;
  }
};

// Phone number handlers
const setPhoneNumber = (phoneNumber: string, phoneObject: { countryCode: string }) => {
  basicInfoFormData.phone = phoneNumber;
  basicInfoFormData.phone_country = phoneObject.countryCode;
};

const setPhoneNumber2 = (phoneNumber: string, phoneObject: { countryCode: string }) => {
  basicInfoFormData.phone2 = phoneNumber;
  basicInfoFormData.phone_country2 = phoneObject.countryCode;
};

</script>

<template>
  <FullScreenModal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="$t('provider') + ' ' + localStaff?.name"
    @close="closeModal"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    
    <div class="space-y-6">
      <!-- 1. Basic Info with Image -->
      <div class="p-6 bg-white rounded-lg border border-gray-200">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">{{ $t('staff.basicInfo') }}</h3>
          <button
            @click="openBasicInfoModal"
            class="inline-flex items-center px-3 py-2 text-sm font-medium leading-4 text-white rounded-md border border-transparent bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PencilSquareIcon class="mr-1 w-4 h-4" />
            {{ $t('form.editBasicInfo') }}
          </button>
        </div>
        
        <div class="flex flex-col gap-6 items-center md:flex-row md:items-start">
          <!-- Staff Image -->
          <div class="flex-shrink-0">
            <img
              v-if="localStaff?.imageLink"
              :src="localStaff.imageLink"
              class="object-cover w-24 h-24 rounded-full"
              :alt="localStaff.name"
            />
            <div
              v-else
              class="flex justify-center items-center w-24 h-24 bg-gray-200 rounded-full"
            >
              <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z"/>
              </svg>
            </div>
          </div>
          
          <!-- Staff Details -->
          <div class="grid flex-1 grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label class="block text-sm font-medium text-gray-500">{{ $t("booking.staff") }}</label>
              <p class="mt-1 text-sm font-semibold text-gray-900">{{ localStaff?.name || '-' }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-500">{{ $t("form.email") }}</label>
              <p class="mt-1 text-sm font-semibold text-gray-900">{{ localStaff?.email || '-' }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-500">{{ $t("form.phone") }}</label>
              <p class="mt-1 text-sm font-semibold text-gray-900">{{ localStaff?.phone || '-' }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-500">{{ $t("form.clientSaameTime") }}</label>
              <p class="mt-1 text-sm font-semibold text-gray-900">{{ localStaff?.clients_in_same_time || '-' }}</p>
            </div>
            
            <div v-if="hasCommission">
              <label class="block text-sm font-medium text-gray-500">{{ $t("form.commission") }}</label>
              <p class="mt-1 text-sm font-semibold text-gray-900">{{ localStaff?.commission ? localStaff.commission + ' %' : '-' }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-500">{{ $t("modalPlacholder.branch") }}</label>
              <p class="mt-1 text-sm font-semibold text-gray-900">{{ localStaff?.team?.name || '-' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 2. Staff Bookings Table -->
      <div class="p-6 bg-white rounded-lg border border-gray-200">
        <h3 class="mb-4 text-lg font-semibold text-gray-900">{{ $t('staff.bookings') }}</h3>
        
        <StaffTabBookings :staff="staff" @open-booking-modal="openBookingModal" />
      </div>

      <!-- 3. Assigned Services Table -->
      <div class="p-6 bg-white rounded-lg border border-gray-200">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">{{ $t('staff.services') }}</h3>
          <button
            @click="openServiceModal"
            class="inline-flex items-center px-3 py-2 text-sm font-medium leading-4 text-white rounded-md border border-transparent bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon class="mr-1 w-4 h-4" />
            {{ $t('form.addServices') }}
          </button>
        </div>
        
        <div class="overflow-hidden ring-1 ring-black ring-opacity-5 shadow md:rounded-lg">
          <overlay-loader v-if="tableServicData.processing" :fullScreen="false" />
          <generic-table
            v-if="!tableServicData.processing"
            :data="assignedServices"
            itemKey="uuid"
            :headers="servicesHeaders"
          >
            <template #row="{ item }">
              <grid-td class="py-4 pr-3 pl-4 text-sm sm:pl-6">
                <div class="flex gap-2 items-center">
                  <span 
                    v-if="item.color"
                    :style="{ backgroundColor: item.color }" 
                    class="block w-3 h-3 rounded"
                  ></span>
                  <span class="font-medium text-gray-900">{{ item.name_with_category || item.name }}</span>
                </div>
              </grid-td>
              <grid-td class="py-4 pr-4 pl-3 text-sm text-right sm:pr-6">
                <button
                  @click="handleServiceDelete(item)"
                  class="inline-flex items-center p-2 text-red-600 rounded-md transition-colors hover:text-red-800 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                  :title="$t('form.removeAssignment')"
                >
                  <TrashIcon class="w-4 h-4" />
                </button>
              </grid-td>
            </template>
          </generic-table>
        </div>
      </div>

      <!-- 4. Assigned Boundaries Table -->
      <div class="p-6 bg-white rounded-lg border border-gray-200">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">{{ $t('staff.zones') }}</h3>
          <button
            @click="openBoundariesModal"
            class="inline-flex items-center px-3 py-2 text-sm font-medium leading-4 text-white rounded-md border border-transparent bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon class="mr-1 w-4 h-4" />
            {{ $t('form.addBoundaries') }}
          </button>
        </div>
        
        <div class="overflow-hidden ring-1 ring-black ring-opacity-5 shadow md:rounded-lg">
          <overlay-loader v-if="tableServicData.processing" :fullScreen="false" />
          <generic-table
            v-if="!tableServicData.processing"
            :data="tableServicData.BoundaryList.filter(boundary => boundary.selected)"
            itemKey="uuid"
            :headers="boundariesHeaders"
          >
            <template #row="{ item }">
              <grid-td class="py-4 pr-3 pl-4 text-sm sm:pl-6">
                <span class="font-medium text-gray-900">{{ item.name }}</span>
              </grid-td>
              <grid-td class="py-4 pr-4 pl-3 text-sm text-right sm:pr-6">
                <button
                  @click="handleBoundaryDelete(item)"
                  class="inline-flex items-center p-2 text-red-600 rounded-md transition-colors hover:text-red-800 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                  :title="$t('form.removeAssignment')"
                >
                  <TrashIcon class="w-4 h-4" />
                </button>
              </grid-td>
            </template>
          </generic-table>
        </div>
      </div>

      <!-- 5. Generic Schedule -->
      <div class="p-6 bg-white rounded-lg border border-gray-200">
        <h3 class="mb-4 text-lg font-semibold text-gray-900">{{ $t('staff.workingHours') }}</h3>
        
        <div class="overflow-hidden ring-1 ring-black ring-opacity-5 shadow md:rounded-lg">
          <generic-schedule 
            v-if="localStaff?.uuid"
            :itemId="localStaff.uuid" 
            :model="'staff'"
          />
        </div>
      </div>

      <!-- 6. TimeOffs Table -->
      <div class="p-6 bg-white rounded-lg border border-gray-200">
        <StaffTimeOff :staff="staff" type="timeoff" />
      </div>
    </div>

    <!-- Basic Info Edit Modal -->
    <Modal
      :dir="getLocale(locale)?.direction"
      :open="showBasicInfoModal"
      :title="$t('form.editBasicInfo')"
      @close="closeBasicInfoModal"
    >
      <div class="p-6">
        <overlay-loader v-if="basicInfoLoading" :fullScreen="false" />
        
        <form @submit.prevent="handleBasicInfoSave" class="space-y-4">
          <!-- Image Upload -->
          <div class="flex justify-center mb-6">
            <div class="relative w-32 h-32">
              <label class="block mb-2 text-sm font-medium text-center text-gray-700">{{ $t("form.image") }}</label>
              <ImageInput 
                id="basic-image" 
                v-model="basicInfoFormData.image" 
                :link="link" 
                :showClearBtn="true" 
                @image-cleared="link = ''" 
                @update:link="link = $event" 
                class="object-cover w-full h-full rounded-full border border-gray-300" 
              />
              <div class="mt-2 text-xs text-center text-gray-400">{{ $t('form.image_note') }}</div>
            </div>
          </div>

          <!-- Name -->
          <div>
            <form-group :validation="v$" name="name">
              <template #default="{ attrs }">
                <TextInput 
                  required 
                  v-bind="attrs" 
                  id="basic-name" 
                  v-model="basicInfoFormData.name" 
                  :label="$t('form.staffName')" 
                  :placeholder="$t('modalPlacholder.staffName')" 
                />
              </template>
            </form-group>
          </div>
          
          <!-- Email -->
          <div>
            <form-group :validation="v$" name="email">
              <template #default="{ attrs }">
                <TextInput 
                  v-bind="attrs" 
                  id="basic-email" 
                  v-model="basicInfoFormData.email" 
                  :label="$t('email.enter')" 
                  :placeholder="$t('email.enter')" 
                />
              </template>
            </form-group>
          </div>
          
          <!-- Phone -->
          <div>
            <PhoneInput 
              class="w-full" 
              :model-value="basicInfoFormData.phone" 
              label="form.phone" 
              @update:model-value="setPhoneNumber" 
            />
          </div>

          <!-- Branch -->
          <div>
            <form-group :validation="v$" name="team_id">
              <template #default="{ attrs }">
                <SelectInput 
                  v-bind="attrs" 
                  id="basic-team" 
                  v-model="basicInfoFormData.team_id" 
                  :label="$t('modalPlacholder.branch')"
                >
                  <option hidden selected value="">
                    {{ $t("form.select") }}
                  </option>
                  <option v-for="team in teamsList" :key="team?.uuid" :value="team?.uuid">
                    {{ team.name }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
          
          <!-- Client Capacity -->
          <div>
            <form-group :validation="v$" name="clients_in_same_time">
              <template #default="{ attrs }">
                <NumberInput 
                  :label="$t('form.clientSaameTime')" 
                  v-bind="attrs" 
                  id="basic-capacity" 
                  v-model="basicInfoFormData.clients_in_same_time" 
                  min="1" 
                  :placeholder="$t('formPlaceHolder.clientSameTime')" 
                />
              </template>
            </form-group>
          </div>
          
          <!-- Commission -->
          <div v-if="hasCommission">
            <form-group :validation="v$" name="commission">
              <template #default="{ attrs }">
                <NumberInput 
                  v-bind="attrs" 
                  id="basic-commission" 
                  v-model="basicInfoFormData.commission" 
                  min="1" 
                  :placeholder="$t('formPlaceHolder.commission')" 
                  :label="$t('form.commission')" 
                />
              </template>
            </form-group>
          </div>

          <!-- Second Phone -->
          <div>
            <PhoneInput 
              class="w-full" 
              :model-value="basicInfoFormData.phone2" 
              label="form.phone2" 
              @update:model-value="setPhoneNumber2" 
            />
          </div>
          
          <!-- Footer -->
          <div class="flex gap-3 justify-end mt-6">
            <button
              type="button"
              @click="closeBasicInfoModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              {{ $t('form.cancel') }}
            </button>
            <button
              type="submit"
              :disabled="v$.$invalid || basicInfoLoading"
              class="px-4 py-2 text-sm font-medium text-white rounded-md border border-transparent bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ $t('form.update') }}
            </button>
          </div>
        </form>
      </div>
    </Modal>

    <!-- Service Assignment Modal -->
    <Modal
      :dir="getLocale(locale)?.direction"
      :open="showServiceModal"
      :title="$t('form.assignServices')"
      @close="closeServiceModal"
    >
      <div class="p-6">
        <overlay-loader v-if="serviceModalLoading" :fullScreen="false" />
        
        <div class="space-y-4">
          <p class="mb-4 text-sm text-gray-600">
            {{ $t('form.selectServicesToAssign') }}
          </p>
          
          <!-- Available Services List -->
          <div class="max-h-96 rounded-lg border border-gray-200">
            <MultiSelectInput  
              :label="$t('form.services')"
              id="service-select"
              v-model="servicesFormData.services"
              :options="serviceOptions"
              :placeholder="$t('form.select')"
            />
          </div>
          
          <!-- Footer -->
          <div class="flex gap-3 justify-end mt-6">
            <button
              type="button"
              @click="closeServiceModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              {{ $t('form.cancel') }}
            </button>
            <button
              type="button"
              @click="handleServiceSave"
              :disabled="serviceModalLoading"
              class="px-4 py-2 text-sm font-medium text-white rounded-md border border-transparent bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ $t('form.save') }}
            </button>
          </div>
        </div>
      </div>
    </Modal>

    <!-- TimeOff Management Modal -->
    <Modal
      :dir="getLocale(locale)?.direction"
      :open="showTimeOffModal"
      :title="editingTimeOff ? $t('form.editTimeOff') : $t('form.addTimeOff')"
      @close="closeTimeOffModal"
    >
      <div class="p-6">
        <overlay-loader v-if="timeOffModalLoading" :fullScreen="false" />
        
        <form @submit.prevent="handleTimeOffSave" class="space-y-4">
          <div>
            <label for="timeoff-name" class="block mb-1 text-sm font-medium text-gray-700">
              {{ $t('form.TimeOffslabel') }}
            </label>
            <input
              id="timeoff-name"
              v-model="timeOffFormData.name"
              type="text"
              class="px-3 py-2 w-full rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :placeholder="$t('modalPlacholder.staffDes')"
              required
            />
          </div>
          
          <div>
            <label for="timeoff-start" class="block mb-1 text-sm font-medium text-gray-700">
              {{ $t('form.startWith') }}
            </label>
            <input
              id="timeoff-start"
              v-model="timeOffFormData.start_at"
              type="date"
              class="px-3 py-2 w-full rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              required
            />
          </div>
          
          <div>
            <label for="timeoff-end" class="block mb-1 text-sm font-medium text-gray-700">
              {{ $t('form.endWith') }}
            </label>
            <input
              id="timeoff-end"
              v-model="timeOffFormData.end_at"
              type="date"
              class="px-3 py-2 w-full rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              required
            />
          </div>
          
          <!-- Footer -->
          <div class="flex gap-3 justify-end mt-6">
            <button
              type="button"
              @click="closeTimeOffModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              {{ $t('form.cancel') }}
            </button>
            <button
              type="submit"
              :disabled="!isTimeOffFormValid || timeOffModalLoading"
              class="px-4 py-2 text-sm font-medium text-white rounded-md border border-transparent bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ editingTimeOff ? $t('form.update') : $t('form.create') }}
            </button>
          </div>
        </form>
      </div>
    </Modal>

    <!-- Boundaries Assignment Modal -->
    <Modal
      :dir="getLocale(locale)?.direction"
      :open="showBoundariesModal"
      :title="$t('form.assignBoundaries')"
      @close="closeBoundariesModal"
    >
      <div class="p-6">
        <overlay-loader v-if="boundariesModalLoading" :fullScreen="false" />
        
        <div class="space-y-4">
          <p class="mb-4 text-sm text-gray-600">
            {{ $t('form.selectBoundariesToAssign') }}
          </p>
          
          <!-- Available Boundaries List -->
          <div class="max-h-96 rounded-lg border border-gray-200">
            {{boundariesFormData.boundaries}}
            <MultiSelectInput  
              :label="$t('form.boundaries')"
              id="boundaries-select"
              v-model="boundariesFormData.boundaries"
              :options="tableServicData.BoundaryList.map(boundary => ({
                id: boundary.uuid,
                name: boundary.name
              }))"
              :placeholder="$t('form.select')"
            />
          </div>
          
          <!-- Footer -->
          <div class="flex gap-3 justify-end mt-6">
            <button
              type="button"
              @click="closeBoundariesModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              {{ $t('form.cancel') }}
            </button>
            <button
              type="button"
              @click="handleBoundariesSave"
              :disabled="boundariesModalLoading"
              class="px-4 py-2 text-sm font-medium text-white rounded-md border border-transparent bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ $t('form.save') }}
            </button>
          </div>
        </div>
      </div>
    </Modal>
  </FullScreenModal>
</template>

<style scoped>
.grid > div:not(:last-child) {
  flex: 1 0 48%;
}
.grid > div.full {
  flex: 1 0 100%;
}
@media (max-width: 768px) {
  .grid > div:not(:last-child) {
    flex: 1 0 100% !important;
  }
}
</style>
