<script setup lang="ts">
import { storeToRefs } from "pinia";
import type { PropType } from "@vue/runtime-core";
import useVuelidate from "@vuelidate/core";
import { required, minValue, requiredIf } from "@/utils/i18n-validators";
import type { Staff, Tag } from "@/types";
import "vue-multiselect/dist/vue-multiselect.css";
import MultiSelectInput from "@/components/FormControls/Inputs/MultiSelectInput.vue";
import GenericSchedule from '@/components/Generic/GenericSchedule.vue';
import { useBoundaries } from "@/stores/boundaries";
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue';
import { ChevronUpIcon } from '@heroicons/vue/20/solid';
import FullScreenModal from '@/components/Common/FullScreenModal.vue';
import {convertPyloadToFormdata} from "@/utils/index";
const { fetchBoundaries } = useBoundaries();
const { getBoundaries } = storeToRefs(useBoundaries());
const { createStaff, updateStaff } = useStaffStore();
const { fetchServices } = useServicesStore();
const { hasCommission } = storeToRefs(usePluginsStore());

const props = defineProps({
  staff: {
    type: Object as PropType<Staff>,
    default: null,
  },
  showModal: {
    type: Boolean,
    default: false,
  },
  tags: {
    type: Array as PropType<Tag[]>,
    default: () => [],
  },
});
const userInfo = useAuthStore();

const emit = defineEmits(["created", "closed", "updated"]);
const { locale, t } = useI18n();

const { getLocale } = storeToRefs(useLocalesStore());
const processing = ref(false);
const editMode = ref(false);
const link = ref("");
const fileInput = ref(null);

const teamsList = computed(() => userInfo.getUserInfo.teams);
const boundariesList = ref([]);
const ServicesList = ref([]);
const resetIntervals = ref([
  {
    "day": "sun",
    "from": "09:00",
    "to": "17:00"
  },
  {
    "day": "mon",
    "from": "09:00",
    "to": "17:00"
  },
  {
    "day": "tue",
    "from": "09:00",
    "to": "17:00"
  },
  {
    "day": "wed",
    "from": "09:00",
    "to": "17:00"
  },
  {
    "day": "thu",
    "from": "09:00",
    "to": "17:00"
  },
  {
    "day": "sat",
    "from": "09:00",
    "to": "17:00"
  }
]);

const formData = reactive<Omit<Staff, "uuid">>({
  name: "",
  email: "",
  phone: "",
  phone_country: "",
  phone2: "",
  phone_country2: "",
  image: "",
  tags: [],
  clients_in_same_time: 1,
  commission: '',
  team_id: teamsList.value[0]?.uuid,
  boundaries: [],
  services: [],
  intervals: resetIntervals,
});
const { staff } = toRefs(props);
const rules = {
  name: {
    required,
  },
  clients_in_same_time: {
    required,
    minValue: minValue(1),
  },
  team_id: {
    required,
  },
}
const v$ = useVuelidate(rules, formData)
watch(staff, (value: Staff) => {
  if (!value)
    return
  const {
    name,
    email,
    phone,
    phone_country,
    phone2,
    phone_country2,
    clients_in_same_time,
    commission,
    team,
    imageLink,
    tags,
    boundaries,
    services,
    intervals,
  } = value;
  formData.name = name || ''
  formData.email = email || ''
  formData.phone = phone || ''
  formData.phone_country = phone_country || ''
  formData.phone2 = phone2 || ''
  formData.phone_country2 = phone_country2 || ''
  formData.clients_in_same_time = clients_in_same_time;
  formData.commission = commission;
  formData.team_id = team.uuid;
  formData.tags = tags || [];
  link.value = imageLink || "";
  formData.boundaries = boundaries || [];
  formData.services = services || [];
  formData.intervals = intervals || resetIntervals;
  editMode.value = true;
},
{ immediate: true },
)


watch(
  () => formData.team_id,
  (teamId) => {
    if (teamId) {
      fetchServices("*", { "team": teamId }).then((res) => {
        ServicesList.value = res.data.map((service) => ({
          id: service.uuid,
          name: service.name,
        }));
      });

      fetchBoundaries("team", teamId as string);
    }
  },
  { immediate: true }
);
const setPhoneNumber = (
  phoneNumber: string,
  phoneObject: { countryCode: string; nationalNumber: string },
) => {
  formData.phone = phoneNumber
  formData.phone_country = phoneObject.countryCode
}
const setPhoneNumber2 = (
  phoneNumber: string,
  phoneObject: { countryCode: string; nationalNumber: string },
) => {
  formData.phone2 = phoneNumber
  formData.phone_country2 = phoneObject.countryCode
}
const resetForm = () => {
  formData.name = "";
  formData.email = "";
  formData.phone = "";
  formData.phone_country = "";
  formData.phone2 = "";
  formData.phone_country2 = "";
  formData.clients_in_same_time = 1;
  formData.commission = "";
  formData.team_id = teamsList.value[0]?.uuid;
  formData.tags = [];
  formData.boundaries = [];
  formData.services = [];
  formData.intervals = resetIntervals;
  formData.image = "";
  link.value = "";
  editMode.value = false;
  v$.value.$reset();
};
const createRecord = async (payload) => {
  return createStaff(payload).then((res) => {
    resetForm();
    emit("created", res.data);
  });
};

const closeModal = () => {
  emit('closed')
}

const saveStaff = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return false
  processing.value = true
  try {
    if (link.value === staff?.value?.imageLink)
      delete formData.image
    const payload = {
      ...formData,
      tags: formData.tags.map((tag) => tag.id).join(",") as string[],
    };
    
      payload['intervals'] = payload['intervals'][0];
      await createRecord(convertPyloadToFormdata(payload));
    

  } finally {
    processing.value = false;
  }
}
</script>

<template>
  <FullScreenModal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="$t('modalHeader.employee')"
    :subtitle="$t('modalHeader.employee_subtitle')"
    @close="closeModal"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    <form
      class="grid grid-cols-3 gap-4 mb-4 w-full text-start"
      @submit.prevent="saveStaff"
    >
      <!-- Image Upload - Full Row -->
      <div class="flex col-span-3 justify-center my-6">
        <div class="relative w-32 h-32">
          <LabelInput for="image" class="mb-2 text-center">{{ $t("form.image") }}</LabelInput>
          <ImageInput 
            id="image" 
            v-model="formData.image" 
            :link="link" 
            :showClearBtn="true" 
            @image-cleared="link = ''" 
            @update:link="link = $event" 
            class="object-cover w-full h-full rounded-full border border-gray-300" 
          />
          <div class="mt-2 text-xs text-center text-gray-400">{{$t('form.image_note')}}</div>
        </div>
      </div>

      <!-- First Row: Name, Email, Phone -->
      <!-- Name -->
      <div class="col-span-1">
        <form-group :validation="v$" name="name">
          <template #default="{ attrs }">
            <TextInput 
              required 
              v-bind="attrs" 
              id="staffName" 
              v-model="formData.name" 
              :label="$t('form.staffName')" 
              :placeholder="$t('modalPlacholder.staffName')" 
            />
          </template>
        </form-group>
      </div>
      
      <!-- Email -->
      <div class="col-span-1">
        <form-group :validation="v$" name="email">
          <template #default="{ attrs }">
            <TextInput 
              v-bind="attrs" 
              id="email" 
              v-model="formData.email" 
              :label="$t('email.enter')" 
              :placeholder="$t('email.enter')" 
            />
          </template>
        </form-group>
      </div>
      
      <!-- Phone -->
      <div class="col-span-1">
        <PhoneInput 
          class="w-full" 
          :model-value="formData.phone" 
          label="form.phone" 
          @update:model-value="setPhoneNumber" 
        />
      </div>

      <!-- Second Row: Branch, Client Capacity, Commission/Phone2 -->
      <!-- Branch -->
      <div class="col-span-1">
        <form-group :validation="v$" name="team_id">
          <template #default="{ attrs }">
            <SelectInput 
              v-bind="attrs" 
              id="team_id" 
              v-model="formData.team_id" 
              :label="$t('modalPlacholder.branch')"
            >
              <option hidden selected value="">
                {{ $t("form.select") }}
              </option>
              <option v-for="team in teamsList" :key="team?.uuid" :value="team?.uuid">
                {{ team.name }}
              </option>
            </SelectInput>
          </template>
        </form-group>
      </div>
      
      <!-- Client Capacity -->
      <div class="col-span-1">
        <form-group :validation="v$" name="clients_in_same_time">
          <template #default="{ attrs }">
            <NumberInput 
              :label="$t('form.clientSaameTime')" 
              v-bind="attrs" 
              id="clients_in_same_time" 
              v-model="formData.clients_in_same_time" 
              min="1" 
              :placeholder="$t('formPlaceHolder.clientSameTime')" 
            />
          </template>
        </form-group>
      </div>
      
      <!-- Commission or Second Phone -->
      <div class="col-span-1">
        <div v-if="hasCommission">
          <form-group :validation="v$" name="commission">
            <template #default="{ attrs }">
              <NumberInput 
                v-bind="attrs" 
                id="commission" 
                v-model="formData.commission" 
                min="1" 
                :placeholder="$t('formPlaceHolder.commission')" 
                :label="$t('form.commission')" 
              />
            </template>
          </form-group>
        </div>
        <div v-else>
          <PhoneInput 
            class="w-full" 
            :model-value="formData.phone2" 
            label="form.phone2" 
            @update:model-value="setPhoneNumber2" 
          />
        </div>
      </div>

      <!-- Third Row: Services, Coverage Areas, Additional Field -->
      <!-- Services -->
      <div class="col-span-1">
        <form-group :validation="v$" name="services">
          <template #default>
            <LabelInput for="services" class="mb-2">{{ $t("altNav.services") }}</LabelInput>
            <Multiselect 
              v-model="formData.services" 
              :multiple="true" 
              track-by="id" 
              class="w-full text-gray-700 py-2 border-gray-300 rounded-md !py-0" 
              id="services" 
              :clear-on-select="false" 
              :close-on-select="false" 
              label="name" 
              :options="ServicesList" 
              :placeholder="$t('altNav.services')" 
              :select-label="$t('tags.selectLabel')" 
              :selected-label="$t('tags.selectedLabel')" 
              :deselect-label="$t('tags.deselectLabel')" 
            />
          </template>
        </form-group>
      </div>
      
      <!-- Coverage Areas -->
      <div class="col-span-1">
        <form-group :validation="v$" name="boundaries">
          <template #default>
            <LabelInput for="boundaries" class="mb-2">{{ $t("staff.zones") }}</LabelInput>
            <Multiselect 
              v-model="formData.boundaries" 
              :multiple="true" 
              track-by="id" 
              class="w-full text-gray-700 py-2 border-gray-300 rounded-md !py-0" 
              id="boundaries" 
              :clear-on-select="false" 
              :close-on-select="false" 
              label="name" 
              :options="getBoundaries" 
              :placeholder="$t('staff.zones')" 
              :select-label="$t('tags.selectLabel')" 
              :selected-label="$t('tags.selectedLabel')" 
              :deselect-label="$t('tags.deselectLabel')" 
            />
          </template>
        </form-group>
      </div>
      
      <!-- Additional field placeholder or second phone if commission exists -->
      <div class="col-span-1">
        <div v-if="hasCommission">
          <PhoneInput 
            class="w-full" 
            :model-value="formData.phone2" 
            label="form.phone2" 
            @update:model-value="setPhoneNumber2" 
          />
        </div>
        <!-- You can add another field here if needed -->
      </div>

      <!-- Work Schedule Section - Full Row -->
      <div class="col-span-3 w-full">
        <div class="px-2 md:px-6 py-4 bg-white rounded-lg outline outline-1 outline-offset-[-1px] outline-gray-200 overflow-x-auto">
          <Disclosure as="div" v-slot="{ open }" default-open>
            <DisclosureButton 
              :class="open ? 'border-b border-gray-500 pb-4' : ''" 
              class="flex justify-between items-center w-full cursor-pointer select-none"
            >
              <div class="flex gap-2 items-center text-base font-normal text-neutral-800">
                <span class="text-base font-normal text-neutral-800">{{ $t('staff.workingHours') }}</span>
              </div>
              <ChevronUpIcon 
                :class="open ? 'transform rotate-180' : ''" 
                class="w-5 h-5 text-gray-500 transition-transform duration-200" 
              />
            </DisclosureButton>
            <DisclosurePanel>
              <!-- Inline work schedule section -->
              <div class="mt-4">
                <GenericSchedule 
                  :item-id="formData.team_id" 
                  model="team" 
                  withoutServer
                  @update:workingHours="formData.intervals = $event"
                  
                />
              </div>
            </DisclosurePanel>
          </Disclosure>
        </div>
      </div>

      <!-- Submit Button - Full Row -->
      <div class="flex col-span-3 justify-center mt-6">
        <BaseButton 
          type="submit" 
          class="w-1/2 hover:bg-green-700" 
          custome-bg="bg-green-600" 
          show-icon 
          :processing="processing"
        >
          <span>{{ editMode ? $t("form.update") : $t("form.create") }}</span>
        </BaseButton>
      </div>
    </form>
  </FullScreenModal>
</template>

<style scoped>
form > div:not(:last-child) {
  flex: 1 0 48%;
}
form > div.full {
  flex: 1 0 100%;
}
@media (max-width: 768px) {
  form > div:not(:last-child) {
    flex: 1 0 100% !important;
  }
}
</style>
