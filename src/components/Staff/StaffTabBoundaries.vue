<script setup lang="ts">
import type { PropType } from '@vue/runtime-core'
import { Switch } from '@headlessui/vue'
import type { ComputedRef } from 'vue'
import type { Service, Staff, header } from '@/types'
import useStaff from '@/composables/useStaff'
const props = defineProps({
  staff: {
    type: Object as PropType<Staff>,
    default: null,
  },
})

const { t } = useI18n()

const { tableServicData, fetchStaffBoundariesList, toggleStaffBoundary, currentStaffBoundary } = useStaff()

watch(
  () => props.staff,
  (staff) => {
    props.staff && fetchStaffBoundariesList(staff)
  },
)

const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('serviceProvider.tabs.zones.toggle'),
    },
    {
      title: t('serviceProvider.tabs.zones.zone'),
    },
  ]
})

onMounted(() => {
  fetchStaffBoundariesList(props.staff)
})
</script>

<template>
  <div>
    <generic-table
      :data="tableServicData.BoundaryList"
      item-key="uuid"
      :headers="headers"
      :is-loading="tableServicData.processing"
    >
      <template #row="{ item }">
        <grid-td
          class="py-4 pr-3 pl-4 text-sm whitespace-nowrap sm:pl-6"
          :default-style="false"
        >
          <div class="flex items-center">
            <the-loader v-if="currentStaffBoundary === item.uuid" />
            <Switch
              v-else
              v-model="item.selected"
              class="inline-flex relative flex-shrink-0 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              :class="[item.selected ? 'bg-primary-600' : 'bg-gray-200']"
              @update:model-value="() => toggleStaffBoundary(staff, item.uuid)"
            >
              <span class="sr-only">Service Status</span>
              <span
                aria-hidden="true"
                class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
                :class="[
                  item.selected
                    ? 'translate-x-5 rtl:-translate-x-5'
                    : 'translate-x-0',
                ]"
              />
            </Switch>
          </div>
        </grid-td>
        <grid-td>
          {{ item.name }}
        </grid-td>
      </template>
    </generic-table>
    <div class="px-4">
      <Pagination
        v-if="tableServicData.ServiceList.length"
        :pagination-meta="tableServicData.paginationMeta"
        :pagination-links="tableServicData.paginationLinks"
        @change="(page) => fetchStaffBoundariesList(staff, page)"
      />
    </div>
  </div>
</template>
