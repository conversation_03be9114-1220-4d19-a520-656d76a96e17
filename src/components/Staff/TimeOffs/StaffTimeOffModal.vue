<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import type { PropType } from 'vue'
import { useStaffStore } from '@/stores/staff'
import { required } from '@/utils/i18n-validators'
import type { Staff } from '@/types'
import fetchStaffPage from '@/composables/useStaff'
import type { StaffTimeOff } from '@/types/staffTimeOffs'
import DateInput from '@/components/FormControls/Inputs/DateInput.vue'
const prop = defineProps({
  showModal: {
    type: Boolean,
  },
  staff: {
    type: Object as PropType<Staff>,
  },
  timeOffsModal: {
    type: Object as PropType<StaffTimeOff | null>,
    required: false,
  },
})
const emit = defineEmits(['close', 'active', 'updateTimeOffs'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { addTimeOffs } = useStaffStore()
const processing = ref(false)
const loader = ref(false)
const formData = reactive({
  name: '',
  start_at: '',
  end_at: '',
})
const rules = {
  name: {
    required,
  },
  start_at: {
    required,
  },
  end_at: {
    required,
  },
}
const closeModal = () => {
  emit('close')
}

const modalTitle = ref('timeoff')
const days = ref<string[]>(['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'])
const v$ = useVuelidate(rules, formData)
const create = () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return
  loader.value = true
  processing.value = true
  addTimeOffs(prop.staff?.uuid, formData)
    .then(() => {
      v$.value.$reset()
      emit('active')
    })
    .finally(() => {
      loader.value = false
      processing.value = false
    })
}
const { timeOffsModal } = toRefs(prop)
watch(timeOffsModal, (val) => {
  formData.name = val?.name || ''
  formData.start_at = val?.start_at.replace(/[/]/g, '-') || ''
  formData.end_at = val?.end_at.replace(/[/]/g, '-') || '' || ''

  if (formData.name.length > 0) {
    modalTitle.value = 'timeoffEdit'
  }
  else {
    modalTitle.value = 'timeoff'
    v$.value.$reset()
  }
})
const updateTimeOffs = (timeOffsModal: StaffTimeOff) => {
  formData.uuid = timeOffsModal.value.uuid
  emit('updateTimeOffs', formData)
}
const save = () => {
  if (timeOffsModal.value.uuid)
    updateTimeOffs(timeOffsModal)
  else
    create()
}
</script>

<template>
  <Modal
    v-if="showModal"
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="modalTitle"
    @close="$emit('close')"
  >
    <form
      class="w-full mb-4 mt-10 lg:basis-2/4 text-start relative"
      @submit.prevent="save"
    >
      <overlay-loader v-if="loader" :full-screen="false" />
      <div class="">
        <div class="grid lg:grid-cols-3 grid-cols-1 gap-4 mb-6">
          <div>
            <form-group :validation="v$" name="name">
              <template #default="{ attrs }">
                <TextInput
                  id="name"
                  v-bind="attrs"
                  v-model="formData.name"
                  :label="$t('form.TimeOffslabel')"
                  class="block w-full text-gray-700 py-2 focus:border-primary-500 focus:ring-primary-500 rounded-md focus:outline-none focus-visible:ring--gray-300 sm:text-sm border-gray-300"
                  :placeholder="$t('modalPlacholder.staffDes')"
                  required
                />
              </template>
            </form-group>
          </div>
          <div class="">
            <form-group :validation="v$" name="start_at">
              <template #default="{ attrs }">
                <DateInput
                  v-bind="attrs"
                  id="start_at"
                  v-model="formData.start_at"
                  :label="$t('form.startWith')"
                  class="block w-full text-gray-700 py-2 focus:border-primary-500 focus:ring-primary-500 rounded-md focus:outline-none focus-visible:ring--gray-300 sm:text-sm border-gray-300"
                  pattern="\d{4}-\d{2}-\d{2}"
                  placeholder="select day"
                  :lang="$i18n.locale"
                />
              </template>
            </form-group>
          </div>
          <div class="">
            <form-group :validation="v$" name="end_at">
              <template #default="{ attrs }">
                <DateInput
                  v-bind="attrs"
                  id="end_at"
                  v-model="formData.end_at"
                  :label="$t('form.endWith')"
                  class="block w-full text-gray-700 py-2 focus:border-primary-500 focus:ring-primary-500 rounded-md focus:outline-none focus-visible:ring--gray-300 sm:text-sm border-gray-300"
                  pattern="\d{4}-\d{2}-\d{2}"
                  placeholder="select day"
                  :lang="$i18n.locale"
                />
              </template>
            </form-group>
          </div>
        </div>
      </div>
      <BaseButton
        class="hover:bg-green-800 w-fit ms-auto"
        show-icon
        :processing="processing"
        type="submit"
        custome-bg="bg-green-600"
      >
        {{ timeOffsModal.uuid ? $t("form.update") : $t("form.create") }}
      </BaseButton>
    </form>
  </Modal>
</template>
