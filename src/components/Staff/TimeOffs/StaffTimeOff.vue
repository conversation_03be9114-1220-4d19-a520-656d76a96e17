<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import type { PropType } from 'vue'
import component from 'vue-tailwind-datepicker'
import { useStaffStore } from '@/stores/staff'
import type { Staff } from '@/types'
import type { StaffTimeOff } from '@/types/staffTimeOffs'

const prop = defineProps({
  staff: {
    type: Object as PropType<Staff>,
  },
  type: {
    type: String,
  },
})
const emit = defineEmits([''])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { addTimeOffs, getTimeOffs, deleteTimeOffs, updateTimeOffs } = useStaffStore()
const { staff } = toRefs(prop)
const showModal = ref(false)
const user_has_timeoff = ref(true)
const loader = ref(false)
const showHours = () => {
  showModal.value = true
}

const create = () => {

}
const staffTimeOffs = ref([])
onMounted(() => {
  loader.value = true
  getTimeOffs(prop.staff?.uuid).then((res) => {
    if (res.data.length === 0) {
      user_has_timeoff.value = false
    }
    else {
      staffTimeOffs.value = res.data
      loader.value = false
    }
    loader.value = false
  })
})
watch(staff, (val: Staff) => {
  loader.value = true
  prop.staff?.has_timeoff ? user_has_timeoff.value = true : user_has_timeoff.value = false
  if (prop.staff?.has_timeoff) {
    getTimeOffs(prop.staff?.uuid).then((res) => {
      staffTimeOffs.value = res.data
      loader.value = false
    })
  }
  else {
    loader.value = false
  }
})
const active = () => {
  user_has_timeoff.value = true
  loader.value = true
  getTimeOffs(prop.staff?.uuid).then((res) => {
    staffTimeOffs.value = res.data
    loader.value = false
  })
}
const fetchTimeOffs = () => {
  loader.value = true
  return getTimeOffs(prop.staff?.uuid).then((res) => {
    staffTimeOffs.value = res.data
    loader.value = false
  })
}
const deleteTime = (staffUuid: string, timeOffsUuid: string) => {
  loader.value = true
  deleteTimeOffs(staffUuid, timeOffsUuid).finally(() => {
    loader.value = false
  })
  staffTimeOffs.value = staffTimeOffs.value.filter((time) => {
    return time.uuid !== timeOffsUuid
  })
  if (staffTimeOffs.value.length === 0)
    user_has_timeoff.value = false
}
const start_at = ref([])
const end_at = ref([])
const timeOffsModal = ref<StaffTimeOff | null>({ name: '', start_at: '', end_at: '', uuid: '' })
const editTimeOffs = (staffUuid: string, timeOffs: StaffTimeOff) => {
  timeOffsModal.value = timeOffs
  start_at.value = []
  end_at.value = []
  showModal.value = true
}
const closeModal = () => {
  showModal.value = false
  timeOffsModal.value = { name: '', start_at: '', end_at: '', uuid: '' }
}
const updateTimeOffsFun = (selectedTimeOffs: any) => {
  start_at.value = []
  end_at.value = []
  updateTimeOffs(prop.staff?.uuid, selectedTimeOffs).then(() => {
    fetchTimeOffs().then(() => {
      showModal.value = false
    })
  }).catch((err) => {
    if (err.errors.start_at)
      start_at.value = err.errors.start_at

    if (err.errors.end_at)
      end_at.value = err.errors.end_at
  })
}
</script>

<template>
  <div class="relative">
    <overlay-loader v-if="loader" :fullScreen="false"/>
    <StaffTimeOffModal :show-modal="showModal" :time-offs-modal="timeOffsModal" :staff="staff"  @close="closeModal()" @active="active" @update-time-offs="updateTimeOffsFun" />
    <BaseButton v-if="user_has_timeoff"
      class="sm:flex-1 px-2 py-1 mb-1 rounded-md bg-white border border-[#0F2C3F] !text-[#0F2C3F] hover:bg-gray-100 hover:!text-white transition"
      @click="showModal = true"
    >
      {{ $t('form.timeOffs') }}
    </BaseButton>
    <empty-state v-if="!user_has_timeoff" :text="prop.type" @show-hours="showHours" />
    <staff-time-table v-if="user_has_timeoff" :staff-time-offs="staffTimeOffs" :staff="staff" :title="prop.type" @delete-time="deleteTime" @edit-time-offs="editTimeOffs" />
  </div>
</template>

<style>

</style>
