<script setup lang="ts">
import type { ComputedRef, PropType } from 'vue'
import { PencilSquareIcon, TrashIcon } from '@heroicons/vue/24/outline'
import type { Staff, StaffTimeOff, header } from '@/types'

import i18n from '@/i18n'
const props = defineProps({
  staffTimeOffs: {
    type: Array as PropType<StaffTimeOff[]>,
    default: () => [],
  },
  staff: {
    type: Object as PropType<Staff>,
    default: () => ({}),
  },
  title: {
    type: String,
  },
})
const emit = defineEmits(['editTimeOffs', 'deleteTime'])
const deleteTimeOfss = (timeOffsUuid: string) => {
  emit('deleteTime', props.staff?.uuid, timeOffsUuid)
}
const editTimeOffset = (timeOffs: StaffTimeOff) => {
  emit('editTimeOffs', props.staff?.uuid, timeOffs)
}
const timeOffsVar = ref<StaffTimeOff[]>()
onMounted(() => {
  timeOffsVar.value = props.staffTimeOffs
})
const { t } = useI18n()
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('dashboard.booking.des'),
    },
    {
      title: t('dashboard.booking.start'),
    },
    {
      title: t('dashboard.booking.end'),
    },
    {
      title: '#',
      className: 'text-center',
    },
  ]
})
</script>

<template>
  <div class="">
    <generic-table :data="staffTimeOffs" item-key="uuid" :headers="headers">
      <template #row="{ item }">
        <grid-td
          class="py-4 pr-3 pl-4 text-sm whitespace-nowrap sm:pl-6"
          :default-style="false"
        >
          <div class="flex items-center">
            <div class="">
              <div class="font-medium text-gray-900">
                {{ item.name }}
              </div>
            </grid-td>
            <grid-td>
              {{ item.start_at }}
            </grid-td>
            <grid-td>
              {{ item.end_at }}
            </grid-td>
            <grid-td
              class="flex gap-2"
              :class="[
                i18n.global.locale.value === 'ar' ? 'justify-start' : 'justify-center',
              ]"
            >
              <BaseButton
                class="hover:bg-gray-800 w-fit"
                custome-bg="bg-gray-700"
                @click="editTimeOffset(item)"
              >
                <PencilSquareIcon class="w-4 h-4 cursor-pointer" aria-hidden="true" />
              </BaseButton>
              <IconOnlyBtn
                :icon="TrashIcon"
                @click="deleteTimeOfss(item.uuid)"
                class="text-rose-500 outline-rose-200"
              />
          </grid-td>
        </template>
      </generic-table>
  </div>
</template>
