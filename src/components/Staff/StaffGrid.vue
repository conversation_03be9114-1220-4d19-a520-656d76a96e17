<script setup lang="ts">
import type { ComputedRef, PropType } from 'vue'
import type { Staff, header } from '@/types'

const props = defineProps({
  staffList: {
    type: Array as PropType<Staff[]>,
    default: () => [],
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
})

const { t } = useI18n()
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.name'),
    },
    {
      title: t('form.phone'),
    },
    {
      title: t('email.title'),
    },
    {
      title: t('modalPlacholder.branch'),
    },
  ]
})
const router = useRouter()
const emit = defineEmits(['click'])
const redirectToStaff = (item: Staff) => {
  emit('click', item);
  // router.push({ name: 'staff', params: { id: item.uuid } })
}
</script>

<template>
  <generic-table
    :headers="headers"
    :data="staffList"
    tr-class="cursor-pointer"
    item-key="uuid"
    :is-loading="isLoading"
    :on-row-click="redirectToStaff"
  >
    <template #row="{ item }">
      <grid-td
        class="whitespace-nowrap flex items-center gap-1 py-2 pl-2 pr-2 text-sm sm:pl-6"
        :default-style="false"
      >
        <div>
          <svg v-if="item.imageLink === null" xmlns="http://www.w3.org/2000/svg" class="w-10" viewBox="0 0 24 24">
            <path fill="#e1e1e1" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z" />
          </svg>

          <img v-else id="image" class="w-10 h-10 rounded-full" :src="item.imageLink" :link="null" alt="">
        </div>
        <span>
          {{ item?.name }}
        </span>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.phone">
            {{ item?.phone }}
          </span>
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.email">
            {{ item?.email }}
          </span>
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.team?.uuid">
            {{ item?.team.name }}
          </span>
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
    </template>
  </generic-table>
</template>
