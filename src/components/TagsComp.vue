<script lang="ts" setup>
import type { PropType } from 'vue'

interface Tags {
  name: string
  id: string
  model: string
  color: string
}
const props = defineProps({
  tags: {
    type: Array as PropType<Tags[]>,
    default: () => ([]),
  },
})
</script>

<template>
  <div v-if="tags.length > 0" class="   flex items-center ">
    <div class="flex mt-1 rounded-md font-semibold py-2">
      <div v-for="tag in tags" :key="tag.id" class="text-xs mx-1 cursor-pointer inline-flex items-center font-bold leading-sm uppercase px-3 bg-white py-1  text-primary-700 border-primary border rounded-full" :style="[{ color: tag.color }, { border: `1px solid ${tag.color}` }]">
        {{ tag.name }}
      </div>
    </div>
  </div>
</template>

<style>

</style>
