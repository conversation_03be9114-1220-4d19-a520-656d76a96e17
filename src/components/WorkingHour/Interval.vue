<script setup lang="ts">
import type { PropType } from 'vue'
import {
  DocumentDuplicateIcon,
  PlusIcon,
  XMarkIcon,
  ChevronUpIcon,
} from "@heroicons/vue/20/solid";
import { Switch, Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue';

const { createInterval, updateInterval, deleteInterval } = useIntervalsStore();

const emit = defineEmits<{
  (e: 'interval-updated', interval: IntervalHour): void
  (e: 'interval-created', interval: IntervalHour): void
  (e: 'interval-deleted', intervalId: string): void
}>();

const props = defineProps({
  dayInterval: {
    type: Object as PropType<DayInterval>,
    required: true,
  },
  workingHourId: {
    type: String,
    required: true,
  },
  withoutServer: {
    type: Boolean,
    default: false
  }
});

const DefaultInterval = {
  from: '09:30',
  to: '17:00',
  day: props.dayInterval.day,
};

const { timesFroms, timesTo } = timeForms();
const { locale } = useI18n();
const loading = ref(false);
const dayEnabled = ref(true);

// Watch for day enabled state
watch(dayEnabled, (enabled) => {
  if (!enabled) {
    // When disabled, we might want to remove all intervals or just hide them
    // For now, we'll just hide them
  }
});

const addInterval = async (interval?: IntervalHour) => {
  if (props.withoutServer) {
    const newInterval = {
      ...DefaultInterval,
      uuid: crypto.randomUUID(),
      ...(interval || {})
    };
    props.dayInterval.intervals?.push(newInterval);
    emit('interval-created', newInterval);
    return;
  }

  loading.value = true;
  try {
    const newInterval = await createInterval(
      props.workingHourId,
      interval?.to ? interval : DefaultInterval
    );
    if (interval?.to) {
      interval.uuid = newInterval.uuid;
    }
    if (interval && 'error' in interval) {
      delete interval.error;
    }
    if (interval?.to) return;
    props.dayInterval.intervals?.push(newInterval);
  } catch (error: any) {
    if (interval?.to) return;
    props.dayInterval.intervals?.push({
      ...DefaultInterval,
      error: error?.message || 'Unknown error',
    });
  } finally {
    loading.value = false;
  }
};

const editInterval = async (interval: IntervalHour) => {
  if (!interval.uuid) {
    addInterval(interval)
    return
  }

  if (props.withoutServer) {
    emit('interval-updated', interval);
    return;
  }

  loading.value = true;
  try {
    await updateInterval(props.workingHourId, interval.uuid, {...interval, active: true});
    if ('error' in interval) {
      delete interval.error;
    }
  } catch (error: any) {
    (interval as any).error = error?.message || 'Unknown error';
  } finally {
    loading.value = false;
  }
}

const removeInterval = async (interval: IntervalHour) => {
  if (props.withoutServer) {
    props.dayInterval.intervals = props.dayInterval.intervals?.filter(
      (item: IntervalHour) => item.uuid !== interval.uuid
    );
    emit('interval-deleted', interval.uuid as string);
    return;
  }

  loading.value = true;
  try {
    await deleteInterval(props.workingHourId, interval.uuid as string)
    props.dayInterval.intervals = props.dayInterval.intervals?.filter(
      (item: IntervalHour) => item.uuid !== interval.uuid
    );
  } catch (error: any) {
    console.error('Error removing interval:', error);
  } finally {
    loading.value = false;
  }
};

// Computed summary time
const timeSummary = computed(() => {
  if (!dayEnabled.value || !props.dayInterval.intervals?.length) return '';
  const firstInterval = props.dayInterval.intervals[0];
  const lastInterval = props.dayInterval.intervals[props.dayInterval.intervals.length - 1];
  return `${firstInterval.from} - ${lastInterval.to}`;
});
</script>

<template>
  <div class="bg-white rounded-lg border border-gray-200">
    <Disclosure as="div" v-slot="{ open }" :default-open="dayEnabled">
      <DisclosureButton class="flex justify-between items-center px-6 py-4 w-full cursor-pointer select-none hover:bg-gray-50">
        <div class="flex items-center space-x-4">
          <!-- Day Toggle Switch -->
          <Switch 
            v-model="dayEnabled"
            class="inline-flex relative flex-shrink-0 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            :class="[
              dayEnabled ? 'bg-primary-600' : 'bg-gray-200',
            ]"
            @click.stop
          >
            <span aria-hidden="true"
              class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
              :class="[
                dayEnabled ? 'translate-x-5' : 'translate-x-0',
              ]" />
          </Switch>
          
          <!-- Day Name -->
          <span class="text-base font-medium text-gray-900">{{ $t(`days.${dayInterval?.day}`) }}</span>
          
          <!-- Time Summary -->
          <span v-if="dayEnabled && timeSummary" class="text-sm text-gray-500">
            {{ timeSummary }}
          </span>
          <span v-else-if="!dayEnabled" class="px-3 py-1 text-sm text-gray-400 bg-gray-100 rounded-md">
            {{ $t('closed') }}
          </span>
          <span v-else class="text-sm text-gray-400">
            {{ $t("settings.workinghours.empty") }}
          </span>
        </div>
        
        <!-- Disclosure Arrow -->
        <ChevronUpIcon 
          v-if="dayEnabled"
          :class="open ? 'transform rotate-180' : ''" 
          class="w-5 h-5 text-gray-500 transition-transform duration-200" 
        />
      </DisclosureButton>
      
      <!-- Day Time Settings Panel -->
      <DisclosurePanel v-if="dayEnabled" class="px-6 pb-4 border-t border-gray-100">
        <div class="pt-4 space-y-4">
          <overlay-loader v-if="loading && !withoutServer" :fullScreen="false" />
          
          <!-- Time Intervals -->
          <div v-if="dayInterval?.intervals?.length" class="space-y-3">
            <div
              v-for="(interval, idx) of dayInterval?.intervals"
              :key="interval.uuid || idx"
              class="flex items-center p-4 space-x-4 bg-gray-50 rounded-lg"
            >
              <div class="flex items-center space-x-2">
                <span class="text-sm font-medium text-gray-600">{{ $t('from') }}</span>
                <div class="w-36">
                  <select-input
                    :model-value="interval.from || ''"
                    @update:model-value="(value) => { interval.from = value; editInterval(interval); }"
                    class="text-sm"
                  >
                    <option
                      v-for="(time, index) in timesFroms"
                      :key="index"
                      :value="time"
                    >
                      {{ time }}
                    </option>
                  </select-input>
                </div>
              </div>
              
              <span class="text-gray-400">-</span>
              
              <div class="flex items-center space-x-2">
                <span class="text-sm font-medium text-gray-600">{{ $t('to') }}</span>
                <div class="w-36">
                  <select-input
                    :model-value="interval.to || ''"
                    @update:model-value="(value) => { interval.to = value; editInterval(interval); }"
                    class="text-sm"
                  >
                    <option
                      v-for="(time, index) in timesTo"
                      :key="index"
                      :value="time"
                    >
                      {{ time === 'end_of_day' ? $t(time) : time }}
                    </option>
                  </select-input>
                </div>
              </div>
              
              <!-- Remove interval button -->
              <button 
                v-if="dayInterval?.intervals && dayInterval.intervals.length > 1"
                @click="interval?.uuid ? removeInterval(interval) : dayInterval.intervals?.splice(idx, 1)"
                class="p-2 text-red-600 rounded-md transition-colors hover:text-red-800 hover:bg-red-50"
                type="button"
              >
                <XMarkIcon class="w-4 h-4" />
              </button>
              
              <!-- Error Message -->
              <div v-if="interval && 'error' in interval" class="ml-2 text-sm text-red-500">
                {{ interval.error }}
              </div>
            </div>
          </div>
          
          <!-- Empty State -->
          <div v-else class="py-8 text-center text-gray-500">
            {{ $t("settings.workinghours.empty") }}
          </div>
          
          <!-- Add Interval Button -->
          <div class="flex justify-center pt-4">
            <button
              type="button"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              @click="addInterval()"
            >
              <PlusIcon class="mr-2 w-4 h-4" />
              {{ $t('add_time_interval') }}
            </button>
          </div>
        </div>
      </DisclosurePanel>
    </Disclosure>
  </div>
</template>
