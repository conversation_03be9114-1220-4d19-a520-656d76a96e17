<script setup lang="ts">
import { storeToRefs } from "pinia";
const { getLocale } = storeToRefs(useLocalesStore());
const { locale } = useI18n();
import type { PropType } from "vue";
import type { IntervalHour } from "@/types";

const emit = defineEmits<{
  (e: 'update:intervals', intervals: IntervalHour[]): void
  (e: 'intervalCreated', interval: IntervalHour): void
  (e: 'intervalUpdated', interval: IntervalHour): void
  (e: 'intervalDeleted', intervalId: string): void
}>();

const props = defineProps({
  workingHourIntervals: {
    type: Array as PropType<IntervalHour[]>,
    required: true,
  },
  workingHourId: {
    type: String,
    required: true,
  },
  withoutServer: {
    type: Boolean,
    default: false
  }
});

const handleIntervalUpdate = (interval: IntervalHour) => {
  if (props.withoutServer) {
    emit('intervalUpdated', interval);
  }
};

const handleIntervalCreate = (interval: IntervalHour) => {
  if (props.withoutServer) {
    emit('intervalCreated', interval);
  }
};

const handleIntervalDelete = (intervalId: string) => {
  if (props.withoutServer) {
    emit('intervalDeleted', intervalId);
  }
};
</script>

<template>
  <div class="text-black">
    <div class="relative py-4 mx-auto sm:px-6 lg:py-2 lg:px-8">
      <div
        v-for="(dayInt, ind) of workingHourIntervals"
        :key="ind"
        class="py-4 border-b basis-1"
      >
        <Interval 
          :dayInterval="dayInt" 
          :workingHourId="workingHourId"
          :withoutServer="withoutServer"
          @interval-updated="handleIntervalUpdate"
          @interval-created="handleIntervalCreate"
          @interval-deleted="handleIntervalDelete"
        />
      </div>
    </div>
  </div>
</template>
