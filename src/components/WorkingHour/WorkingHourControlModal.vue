<script setup lang="ts">
import type { PropType, Ref } from 'vue'
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import VueTailwindDatepicker from 'vue-tailwind-datepicker'
import dayjs from 'dayjs'
import { minLength, required } from '@/utils/i18n-validators'
import type { WorkingHour } from '@/types'
import { locatizedOption } from '@/composables/useDatePicker'

interface OperationsConfig {
  create: (model: string, id: string, formDate: any) => Promise<void>
  update: (model: string, id: string, workingHourId: string, formDate: any) => Promise<void>
}
const props = defineProps({
  id: {
    type: String as PropType<string>,
    default: '',
    required: true,
  },
  isOpen: {
    type: Boolean as PropType<boolean>,
    required: true,
  },
  workingHour: {
    type: Object as PropType<WorkingHour>,
    default: null,
    required: false,
  },
  operationsConfig: {
    type: Object as PropType<OperationsConfig>,
    default: () => ({}),
    required: false,
  },
  model: {
    type: String as PropType<'staff' | 'team'>,
    default: '',
    required: true,
  },

})
const emit = defineEmits(['closed', 'addedNewWorkingHour'])
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const editMode = ref(false)
const closeModal = () => {
  emit('closed')
}
const processing = ref(false)

const formatDate = ref({
  date: 'YYYY-MM-DD',
  month: 'MMM',
})
const range = ref([]) as Ref<string[]>

const formDate = reactive({
  name: '',
  start: '',
  end: '',
})
const rules = {
  name: {
    required,
  },
}
const $v = useVuelidate(rules, formDate)

const saveWorkingHour = async () => {
  processing.value = true
  try {
    $v.value.$touch()
    if ($v.value.$invalid)
      return
    if (editMode.value)
      await props.operationsConfig.update(props.model, props.id, props.workingHour.uuid, formDate)
    else
      await props.operationsConfig.create(props.model, props.id, formDate)

    emit('addedNewWorkingHour')
    emit('closed')
  }
  finally {
    processing.value = false
  }
}
watch(
  range,
  (val) => {
    const [start, end] = val
    formDate.start = dayjs(start).format('YYYY-MM-DD')
    formDate.end = dayjs(end).format('YYYY-MM-DD')
  },
  { deep: true },
)

watch(
  () => props.workingHour,
  (val) => {
    if (val) {
      editMode.value = true
      formDate.name = val.name
      range.value = val.start ? [dayjs(val.start)?.format('MM-DD-YYYY'), dayjs(val.end).format('MM-DD-YYYY')] : []
    }
  },
  { deep: true, immediate: true },
)
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="editMode ? 'edit_working_hour' : 'add_working_hour'"
    panel-classes="max-w-xl"
    @close="closeModal"
  >
    <form class="text-start relative" @submit.prevent="saveWorkingHour">
      <div class="grid gap-4 lg:grid-cols-2">
        <div class="">
          <LabelInput for="" class="mb-2">
            {{
              $t("name_of_working_hour")
            }}
          </LabelInput>
          <form-group :validation="$v" name="name" :label="$t('form.name')">
            <template #default="{ attrs }">
              <TextInput
                v-bind="attrs"
                v-model="formDate.name"
                :placeholder="$t('form.name')"
              />
            </template>
          </form-group>
        </div>
        <div>
          <LabelInput for="range_of_working_hour" class="mb-2">
            {{
              $t("range_of_working_hour")
            }}
          </LabelInput>
          <form-group :validation="$v" error-name="start" :label="$t('form.name')">
            <template #default="{ attrs }">
              <VueTailwindDatepicker
                id="range_of_working_hour"
                v-bind="attrs"
                v-model="range"
                :dir="getLocale(locale)?.direction === 'rtl' ? 'ltr' : 'rtl'"
                :options="locatizedOption"
                :formatter="formatDate"
                input-classes="block w-full border-gray-300 rounded-md    focus:border-primary-500 focus:ring-primary-500 sm:text-sm py-3"
                separator=" to "
                :placeholder="$t('form.date_rage')"
                use-range
                :i18n="getLocale(locale)?.id === 'ar' ? 'ar-sa' : 'en'"
                :disable-date="(date) => date < new Date()"
              />
            </template>
          </form-group>
        </div>
      </div>
      <div class="flex w-full gap-4 justify-center mt-1">
        <BaseButton
          class="md:col-span-2 col-span-4 mt-5"
          :customebg="[editMode ? 'bg-gray-700' : 'bg-green-600']"
          show-icon
          type="submit"
          :processing="processing"
        >
          {{ editMode ? $t("form.update") : $t("form.create") }}
        </BaseButton>
      </div>
    </form>
  </Modal>
</template>
