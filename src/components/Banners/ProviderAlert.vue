<script setup lang="ts">
import { MegaphoneIcon } from '@heroicons/vue/24/outline'
</script>

<template>
  <div class="bg-blue-100">
    <div class="px-3 py-3 mx-auto max-w-7xl sm:px-6 lg:px-16">
      <div class="flex flex-wrap justify-center items-center">
        <div
          class="flex flex-wrap flex-1 gap-3 justify-center items-center mx-auto w-0"
        >
          <span class="flex p-2 rounded-lg">
            <MegaphoneIcon class="w-6 h-6 text-blue-700" aria-hidden="true" />
          </span>
          <p class="font-medium text-blue-700">
            {{ $t("provider_mobile_only") }}
          </p>
          <div class="flex gap-2">
            <a
              href="https://play.google.com/store/apps/details?id=app.mahjoz.io&hl=ar"
              target="_blank"
              class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-700 rounded-md hover:bg-blue-800"
            >
              Android
            </a>
            <a
              href="https://apps.apple.com/us/app/mahjoz-%D9%85%D8%AD%D8%AC%D9%88%D8%B2/id6481259819"
              target="_blank"
              class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-700 rounded-md hover:bg-blue-800"
            >
              iOS
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
