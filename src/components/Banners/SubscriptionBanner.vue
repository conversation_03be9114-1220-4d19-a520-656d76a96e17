<script setup lang="ts">
import {
  MegaphoneIcon,
  ShieldExclamationIcon,
} from '@heroicons/vue/24/outline'
</script>

<template>
  <div class="bg-gradient-to-r from-yellow-200 to-yellow-300 shadow-md">
    <div class="px-4 py-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
      <div class="flex justify-center items-center text-center">
        <div class="flex items-center space-x-3">
          <ShieldExclamationIcon
            class="w-8 h-8 text-yellow-600"
            aria-hidden="true"
          />
          <span class="text-lg font-semibold text-gray-800">
            {{ $t("subscription_banner_title") }}
          </span>
        </div>
        <router-link
          :to="{ name: 'current-plan', query: { renewNow: true } }"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md border border-transparent transition duration-150 ease-in-out animate-pulse bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 hover:animate-none"
        >
          {{ $t("to_renew_subscription_click_here") }}
        </router-link>
      </div>
    </div>
  </div>
</template>
