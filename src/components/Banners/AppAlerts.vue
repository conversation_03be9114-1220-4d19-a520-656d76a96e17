<script setup lang="ts">
import { XMarkIcon } from '@heroicons/vue/24/solid'

const { hideNotification, notifications } = useNotifications()
const route = useRoute()
</script>

<template>
  <div aria-live="assertive" class="flex fixed inset-0 z-[100] items-end py-6 pointer-events-none ps-4 pe-4 sm:p-6 sm:items-start">
    <div class="flex flex-col items-center space-y-4 w-full sm:items-end">
      <transition
        v-for="notification in notifications"
        :key="notification.id"
        enter-active-class="transition duration-300 ease-out transform"
        enter-from-class="opacity-0 translate-y-2 sm:translate-y-0 sm:translate-x-2"
        enter-to-class="opacity-100 translate-y-0 sm:translate-x-0"
        leave-active-class="transition duration-100 ease-in"
        leave-from-class="opacity-100"
        leave-to-class="opacity-0"
      >
        <div
          class="overflow-hidden w-full max-w-sm rounded-lg ring-1 ring-black ring-opacity-5 shadow-lg pointer-events-auto"
          :class="notification.style"
        >
          <div class="p-4">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Component :is="notification.icon" class="w-6 h-6" aria-hidden="true" />
              </div>
              <div class="flex-1 pt-0.5 w-0 ms-3">
                <p class="font-bold text-md">
                  {{ notification.title }}
                </p>
                <p class="mt-1 text-sm">
                  {{ notification.message }}
                </p>
              </div>
              <div class="flex flex-shrink-0 ms-4">
                <button
                  type="button"
                  class="inline-flex text-black rounded-md hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" @click="hideNotification(notification.id)"
                >
                  <span class="sr-only">{{ $t('close') }}</span>
                  <XMarkIcon class="w-5 h-5" aria-hidden="true" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>
