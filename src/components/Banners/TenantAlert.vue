<script setup lang="ts">
import {
  MegaphoneIcon,
  ShieldExclamationIcon,
} from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
const props = defineProps({
  planDetails: {
    type: Object,
  },
})
const { getUserInfo } = storeToRefs(useAuthStore())
const days = ref(0)
const { extendTrail } = useAccountSettingStore()
const udpateDate = () => {
  const newDdate = new Date().getTime()
  const trailTime = new Date(props.planDetails?.end).getTime()
  const gap = trailTime - newDdate
  const second = 1000
  const minute = second * 60
  const hours = minute * 60
  const day = hours * 24
  const newDay = Math.floor(gap / day)
  days.value = day
  return newDay
}
</script>

<template>
  <div class="bg-yellow-200">
    <div class="px-3 py-3 mx-auto max-w-7xl sm:px-6 lg:px-16">
      <div class="flex flex-wrap justify-center items-center">
        <div
          class="flex flex-wrap flex-1 gap-3 justify-center items-center mx-auto w-0"
        >
          <span class="flex p-2 rounded-lg">
            <ShieldExclamationIcon
              class="w-6 h-6 text-black"
              aria-hidden="true"
            />
          </span>
          <span
            v-if="udpateDate() >= 0"
            class="font-medium text-center text-black"
          >{{
             $t("trail", {
               day: udpateDate() == 0 ? "" : udpateDate(),
               user: getUserInfo.name,
             })
           }}
            <router-link to="/settings/subscriptions-panel/plans">
              <button class="p-2 text-base font-bold leading-6 capitalize bg-gradient-to-l from-sky-400 to-green-300 rounded-md transition-colors  router-link-exact-active text-secondary hover:bg-primary-200 hover:text-white">
                <span class="text-sm text-white">{{
                  $t("subscribe_banner")
                }}</span>
              </button>
            </router-link>
          </span>
          <template v-else-if="udpateDate() < 0">
            <router-link
              to="/settings/subscriptions-panel/plans"
              class="ml-3 font-medium text-black truncate bg-inherit"
            >
              <p class="">
                {{ $t("extend_trial") }}
                <button
                  class="p-2 text-black bg-indigo-600 rounded-md"
                  @click="extendTrail()"
                >
                  <span class="text-sm text-white">{{ $t("Click_here") }}</span>
                </button>
              </p>
            </router-link>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
