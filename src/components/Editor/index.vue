<script lang="ts" setup>
import EditorJS from '@editorjs/editorjs'
import Header from '@editorjs/header'
import List from '@editorjs/list'
import SimpleImage from '@editorjs/simple-image'
import Quote from '@editorjs/quote'
import Warning from '@editorjs/warning'
import Marker from '@editorjs/marker'
import CodeTool from '@editorjs/code'
import Delimiter from '@editorjs/delimiter'
import InlineCode from '@editorjs/inline-code'
import LinkTool from '@editorjs/link'
import Embed from '@editorjs/embed'
import Table from '@editorjs/table'
import Paragraph from '@editorjs/paragraph'
import ColorPlugin from 'editorjs-text-color-plugin'
import AlignmentTuneTool from 'editorjs-text-alignment-blocktune'

import { storeToRefs } from 'pinia'
import { useServerErrors } from '@/stores/errors'

import i18n from '@/i18n'
const props = defineProps({
  content: {
    type: Object,
    required: false,
  },
  editorVars: {
    type: Array,
    required: false,
  },
  reRenderEditor: {
    type: Boolean,
    required: false,
  },
  form: {
    type: Object,
    required: true,
  },
  validation: {
    type: Object,
    required: false,
  },
})

const { getServerErrors } = storeToRefs(useServerErrors())

const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { content, editorVars, reRenderEditor, form, validation } = toRefs(props)
const editor = new EditorJS({
  holder: 'editorjs',
  i18n: {
    direction: getLocale.value(locale.value)?.direction || 'ltr',
  },
  tools: {
    AlignmentTuneTool: {
      class: AlignmentTuneTool,
      inlineToolbar: true,
      config: {
        default:
          (getLocale.value(locale.value)?.direction || 'ltr') == 'ltr' ? 'left' : 'right',
      },
    },

    header: {
      class: Header,
      tunes: ['AlignmentTuneTool'],
      shortcut: 'CMD+SHIFT+H',
      config: {
        level: 1,
      },
      inlineToolbar: true,
    },
    list: {
      class: List,
      inlineToolbar: true,
      shortcut: 'CMD+SHIFT+L',
      config: {
        placeholder: 'Enter a list',
        types: ['ordered', 'unordered'],
        defaultType: 'unordered',
      },
      tunes: ['AlignmentTuneTool'],
    },
    image: SimpleImage,
    quote: {
      class: Quote,
      inlineToolbar: true,
      shortcut: 'CMD+SHIFT+O',
      config: {
        quotePlaceholder: 'Enter a quote',
        captionPlaceholder: 'Quote\'s author',
      },
    },
    warning: Warning,
    marker: Marker,
    code: CodeTool,
    delimiter: Delimiter,
    inlineCode: {
      class: InlineCode,
      shortcut: 'CMD+SHIFT+C',
      tunes: ['AlignmentTuneTool'],
    },
    linkTool: LinkTool,
    embed: Embed,
    table: {
      class: Table,
      inlineToolbar: true,
      config: {
        row: 10,
        col: 10,
      },
      tunes: ['AlignmentTuneTool'],
    },
    paragraph: {
      class: Paragraph,
      inlineToolbar: true,
      tunes: ['AlignmentTuneTool'],
    },
    Color: {
      class: ColorPlugin, // if load from CDN, please try: window.ColorPlugin
      config: {
        colorCollections: [
          '#EC7878',
          '#9C27B0',
          '#673AB7',
          '#3F51B5',
          '#0070FF',
          '#03A9F4',
          '#00BCD4',
          '#4CAF50',
          '#8BC34A',
          '#CDDC39',
          '#FFF',
        ],
        defaultColor: '#FF1300',
        type: '#f03',
        customPicker: false, // add a button to allow selecting any colour
      },
    },
  },
  onReady: async () => {
    if (content?.value?.blocks?.length)
      await editor.blocks.render(content.value)
      // make editor direaction text rtl
  },
  onChange: () => {
    editor.save().then((outputData) => {
      form.value.content = outputData
    })
  },
})

watch(
  () => editorVars.value,
  (val) => {
    editor.blocks.insert('paragraph', {
      text: val[val.length - 1],
    })
  },
  { deep: true },
)
watch(
  () => reRenderEditor.value,
  (val) => {
    editor.blocks.clear()
  },
)
</script>

<template>
  <div id="editorjs" class="" />
  <p v-if="getServerErrors?.content?.length" class="error-message">
    {{ getServerErrors.content[0] }}
  </p>
</template>

<style lang="scss">
#editorjs {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: revert;
  }
}
</style>
