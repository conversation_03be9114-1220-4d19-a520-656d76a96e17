<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useAuthStore } from '@/stores/auth'

defineProps({
  checkoutData: {
    type: Object,
    required: true,
    default: () => ({
      discount: 0,
      subTotalBeforeTax: 0,
      taxes: 0,
      total: 0,
    }),
  },
})

const { getUserInfo } = storeToRefs(useAuthStore())
</script>

<template>
  <div class="bg-[#F6F7FB] rounded-lg p-6 flex flex-col gap-3 border border-secondary shadow-sm">
    <h3 class="pb-2 text-xl font-semibold text-black text-start">
      {{ $t("ملخص الطلب") }}
    </h3>
    <div class="flex flex-col gap-2 mt-4">
      <div class="flex justify-between items-center">
        <span class="text-gray-600">{{ $t("الخصم") }}</span>
        <span class="flex gap-1 items-center font-medium tabular-nums text-black">
          <price-format
            :form-data="{
              price: checkoutData.discount || 0,
              currency: getUserInfo?.tenant?.currency || '',
            }"
          />
        </span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-gray-600">
          {{ $t("subtotal") }}
        </span>
        <span class="flex gap-1 items-center font-medium tabular-nums text-black">
          <price-format
            :form-data="{
              price: (checkoutData.subTotalBeforeTax ) || 0,
              currency: getUserInfo?.tenant?.currency || '',
            }"
          />
        </span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-gray-600">{{ $t("vat") }}</span>
        <span class="flex gap-1 items-center font-medium tabular-nums text-black">
          <price-format
            :form-data="{
              price: checkoutData.taxes || 0,
              currency: getUserInfo?.tenant?.currency || '',
            }"
          />
        </span>
      </div>
    </div>
    <div class="my-3 border-t border-gray-300" />
    <div class="flex justify-between items-center">
      <span class="font-bold text-black">
        <span v-if="checkoutData.discount">{{ $t("الإجمالي بعد الخصم") }}</span>
        <span v-else>{{ $t("الإجمالي") }}</span>
        <span class="text-xs font-normal text-gray-400">({{ $t("شامل الضريبة") }})</span>
      </span>
      <span class="flex gap-1 items-center text-xl font-extrabold tabular-nums text-primary-600">
        <price-format
          :form-data="{
            price: checkoutData.total || 0,
            currency: getUserInfo?.tenant?.currency || '',
          }"
        />
      </span>
    </div>
  </div>
</template>
