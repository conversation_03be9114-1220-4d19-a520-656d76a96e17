<script lang="ts" setup>
import type { PropType } from 'vue'
import dayjs from 'dayjs'
import type { OrderActivityLog } from '@/types'
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue'

defineProps({
  orderLog: {
    type: Array as PropType<OrderActivityLog[]>,
    required: true,
  },
  withOutHeader: {
    type: Boolean,
    default: false,
  },
})
</script>

<template>
  <DisclosureWrapper :title="$t('order_log')">
    <!-- No header actions in this case -->
    <template #default>
      <div class="flex flex-col py-2 w-full rounded-xl">
        <div class="py-2 mx-auto w-full">
          <div class="relative">
            <!-- Timeline line (runs through all dots) -->
            <ul class="flex relative flex-col gap-6 pl-4 border-r border-neutral-300">
              <li
                v-for="(log, index) in orderLog"
                :key="index"
                class="flex relative gap-3 items-start"
              >
                <!-- Dot -->
                <span
                  class="absolute z-10 w-2 h-2 rounded-full -start-1"
                  :class="[
                    index === 0
                      ? 'bg-neutral-800'
                      : index === orderLog.length - 1
                        ? 'bg-white border border-neutral-800'
                        : 'bg-neutral-500 border border-neutral-800',
                  ]"
                />

                <!-- Content -->
                <div class="flex-1 pr-4">
                  <div v-if="log.user" class="text-sm font-normal text-right text-primary-800">
                    {{ log.user }}
                  </div>
                  <div class="text-sm font-medium text-right text-neutral-800">
                    {{ log.description }}
                  </div>
                  <div class="text-xs text-right text-neutral-500">
                    {{ dayjs(log.created_at).format('DD MMMM YYYY | hh:mm:ss A') }}
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </template>
  </DisclosureWrapper>
</template>
