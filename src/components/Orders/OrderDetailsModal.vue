<script setup lang="ts">
import { storeToRefs } from 'pinia'
import type { PropType } from 'vue'
import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/vue'
import { CheckIcon, ClipboardIcon, LinkIcon, PencilSquareIcon, PhoneIcon } from '@heroicons/vue/24/outline'
import OrderDiscountModal from './Modals/OrderDiscountModal.vue'
import type { Order } from '@/types'
import { useOrder } from '@/stores/order'
import ServicesIcon from '@/components/Icons/ServicesIcon.vue'

import { usePosStore } from '@/stores/pos'
import { useLocalesStore } from '@/stores/locales'
import ExportIcon from '@/components/Icons/ExportIcon.vue'
import SummaryIcon from '@/components/Icons/SummaryIcon.vue'
import PreviewIcon from '@/components/Icons/PreviewIcon.vue'
import EmailIcon from '@/components/Icons/EmailIcon.vue'
import WhatsappIcon from '@/components/Icons/WhatsappIcon.vue'
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['close'])
const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const orderStore = useOrder()
const posStore = usePosStore()
const router = useRouter()
const { fetchSingleOrder, fetchStatus, getOrderNotifications, deleteOrder } = orderStore
const { generalCustomeSetting } = useAccountSettingStore()
const { getOrderDetails } = storeToRefs(useOrder())

const processing = ref(false)
const invoiceProcessing = ref(false)
const showConfirmInvoiceModal = ref(false)

const refundItems = ref([])
const showRefundModal = ref(false)

const copiedPin = ref(false)
const copiedAll = ref(false)
const copiedEmail = ref(false)
const showDiscountModal = ref(false)

const fetchOrderDetails = async (id: string = props.orderId) => {
  processing.value = true
  try {
    await fetchSingleOrder(id)
  }
  finally {
    processing.value = false
  }
}
const printOptions = computed(() => {
  if (getOrderDetails.value?.invoices) {
    return [
      {
        name: t('order_summary'),
        id: 'order_summary',
        icon: SummaryIcon,
        event: openOrderSummary,
      },
      {
        name: t('ord_details.export_invoice'),
        id: 'export_invoice',
        icon: ExportIcon,
        event: downloadPdfInvoice,
      },
      {
        name: t('ord_details.preview_invoice'),
        id: 'preview_invoice',
        icon: PreviewIcon,
        event: openInvoicePdf,
      },
      {
        name: t('ord_details.send_invoice_email'),
        id: 'send_invoice_email',
        icon: EmailIcon,
        event: sendInvoice,
      },
      {
        name: t('ord_details.send_invoice_whatsapp'),
        id: 'send_invoice_whatsapp',
        icon: WhatsappIcon,
        event: sendInvoiceWhatsApp,
      },
    ]
  }
  else {
    return [
      {
        name: t('order_summary'),
        id: 'order_summary',
        icon: SummaryIcon,
        event: openOrderSummary,
      },
    ]
  }
})

// Add this for Listbox open/close state
const selectedPrintOption = ref(null)

const customerActions = computed(() => {
  const c = getOrderDetails.value?.customer || {}
  return [
    {
      icon: WhatsappIcon,
      type: 'link',
      href: c.phone ? `https://wa.me/${c.phone.replace(/[^0-9]/g, '')}` : null,
      title: t('whatsapp'),
    },
    {
      icon: EmailIcon,
      type: 'copyEmail',
      onClick: copyEmail,
      title: t('email'),
      active: copiedEmail.value,
    },
    {
      icon: PhoneIcon,
      type: 'link',
      href: c.phone ? `tel:${c.phone}` : null,
      title: t('phone'),
    },
    {
      icon: ClipboardIcon,
      type: 'copy',
      onClick: copyAllCustomerInfo,
      title: t('ord_details.copy_all_info'),
      active: copiedAll.value,
    },
    {
      icon: LinkIcon,
      type: 'profile',
      onClick: goToCustomerProfile,
      title: t('ord_details.go_to_profile'),
    },
  ]
})

function formatDate(dateStr: string | undefined | null) {
  if (!dateStr)
    return '-'
  // Try to parse as ISO, fallback to raw string
  const d = new Date(dateStr)
  if (isNaN(d.getTime()))
    return dateStr
  return `${d.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' })
    } | ${d.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' })}`
}

async function openRefundOrderModal() {
  if (!getOrderDetails.value)
    return
  try {
    processing.value = true
    const items = await orderStore.getRefundItems(getOrderDetails.value.id)
    refundItems.value = items.data
    showRefundModal.value = true
  }
  finally {
    processing.value = false
  }
}

function closeRefundModal(orderId = null) {
  showRefundModal.value = false
  if (orderId)
    window.open(`/orders/?order_id=${orderId}`, '_self')
}

async function createOrderInvoice() {
  if (!getOrderDetails.value)
    return
  try {
    invoiceProcessing.value = true
    await orderStore.createOrderInvoice(getOrderDetails.value.id)
    const response = await fetchSingleOrder(getOrderDetails.value.id)
    getOrderDetails.value = {
      ...response,
      notifications: response.notifications || [],
      images: response.images || [],
    }
  }
  finally {
    invoiceProcessing.value = false
    showConfirmInvoiceModal.value = false
  }
}

function downloadPdfInvoice() {
  if (!getOrderDetails.value?.invoices?.id)
    return
  invoiceProcessing.value = true
  posStore.previewInvoicePdf(getOrderDetails.value.invoices.id).finally(() => {
    invoiceProcessing.value = false
  })
}

function openInvoicePdf() {
  if (getOrderDetails.value?.invoices?.id)
    window.open(`/preview-invoices/${getOrderDetails.value.id}`, '_blank')
}

function sendInvoice() {
  if (!getOrderDetails.value?.invoices?.id)
    return
  invoiceProcessing.value = true
  posStore.sendInvoicePdf(getOrderDetails.value.invoices.id).finally(() => {
    invoiceProcessing.value = false
  })
}

function sendInvoiceWhatsApp() {
  if (!getOrderDetails.value?.invoices?.id)
    return
  invoiceProcessing.value = true
  posStore.sendWhatsAppInvoice(getOrderDetails.value.invoices.id).finally(() => {
    invoiceProcessing.value = false
  })
}

function openOrderSummary() {
  const btn = document.getElementById('add-transaction-button')
  const copy_btn = document.getElementById('copy-payment-link')
  btn?.style.setProperty('visibility', 'hidden')
  copy_btn?.style.setProperty('visibility', 'hidden')
  const prtHtml = (document.getElementById('order') as HTMLElement).innerHTML
  // Get all stylesheets HTML
  let stylesHtml = ''
  const stylesheets = document.querySelectorAll(
    'link[rel="stylesheet"], style',
  )
  stylesheets.forEach((node) => {
    stylesHtml += node.outerHTML
  })
  // Open the print window
  const WinPrint = window.open(
    '',
    '',
    'left=0,top=0,width=800,height=900,toolbar=0,scrollbars=0,status=0',
  ) as Window & { document: Document }

  WinPrint.document.write(`<!DOCTYPE html>
  <html dir="rtl">
    <head>
      ${stylesHtml}
    </head>
    <body>
      ${prtHtml}
    </body>
  </html>`)

  WinPrint.document.close()
  WinPrint.focus()
  WinPrint.addEventListener('load', async () => {
    copy_btn?.style.setProperty('visibility', 'visible')
    btn?.style.setProperty('visibility', 'visible')
    await WinPrint.print()
  })
}

// Map modal logic from OrderLocationCard
const showMapModal = ref(false)
const mapFormData = reactive({
  longitude: 0,
  latitude: 0,
})
function openMapEdit() {
  showMapModal.value = true
  if (getOrderDetails.value?.longitude && getOrderDetails.value?.latitude) {
    mapFormData.longitude = getOrderDetails.value.longitude
    mapFormData.latitude = getOrderDetails.value.latitude
  }
  else {
    navigator.geolocation.getCurrentPosition((position) => {
      mapFormData.longitude = position.coords.longitude
      mapFormData.latitude = position.coords.latitude
    })
  }
}
function closeMapModal() {
  showMapModal.value = false
}
const mapProcessing = ref(false)
async function updateMapLocation() {
  if (!getOrderDetails.value)
    return
  mapProcessing.value = true
  await orderStore.updateOrderLocation(getOrderDetails.value.id, mapFormData)
  showMapModal.value = false
  await fetchSingleOrder(getOrderDetails.value.id)
  mapProcessing.value = false
}
function markMap(event) {
  mapFormData.latitude = event.latLng.lat()
  mapFormData.longitude = event.latLng.lng()
}

function copyPinCode(pin) {
  if (!pin)
    return
  navigator.clipboard.writeText(pin.toString())
  copiedPin.value = true
  setTimeout(() => copiedPin.value = false, 1200)
}

function copyAllCustomerInfo() {
  if (!getOrderDetails.value?.customer)
    return
  const c = getOrderDetails.value.customer
  const info = `Name: ${c.first_name || ''} ${c.last_name || ''}\nPhone: ${c.phone || ''}\nEmail: ${c.email || ''}`
  navigator.clipboard.writeText(info)
  copiedAll.value = true
  setTimeout(() => copiedAll.value = false, 1200)
}

function goToCustomerProfile() {
  if (getOrderDetails.value?.customer?.uuid)
    router.push({ name: 'customer', params: { id: getOrderDetails.value.customer.uuid } })
}

function copyEmail() {
  if (!getOrderDetails.value?.customer?.email)
    return
  navigator.clipboard.writeText(getOrderDetails.value.customer.email)
  copiedEmail.value = true
  setTimeout(() => copiedEmail.value = false, 1200)
}
onMounted(async () => {
  await fetchOrderDetails(props.orderId)
})

const route = useRoute()
const notifications = ref([])
const settings = ref([])
onBeforeMount(async () => {
  try {
    processing.value = true
    getOrderNotifications(props.orderId as string).then((data) => {
      notifications.value = data
    })
    await fetchOrderDetails(props.orderId)
    await generalCustomeSetting('order').then((res) => {
      settings.value = res
    })
  }
  catch (error) {
    emit('close')
  }
  finally {
    processing.value = false
  }
})

const showConfModal = ref(false)
const processingbtn = ref(false)
function deleteOrderModal() {
  showConfModal.value = true
  processingbtn.value = true
}

async function creatorderInvoice(id: string) {
  try {
    showConfirmInvoiceModal.value = false
    processing.value = true
    await createOrderInvoice(id)
    await fetchOrderDetails()
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <FullScreenModal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="getOrderDetails ? t('ord_details.order_num') + getOrderDetails?.OrderNum ?? '' : t('ord_details.order_num')"
    :subtitle="getOrderDetails ? formatDate(getOrderDetails.created_at) : ''"
    :icon="ServicesIcon"
    panel-classes="w-full  bg-white rounded-xl shadow-[0px_8px_8px_-4px_rgba(10,13,18,0.04)] shadow-[0px_20px_24px_-4px_rgba(10,13,18,0.10)] flex flex-col overflow-hidden sm:w-full sm:mx-20 h-full my-20"
    @close="$emit('close')"
  >
    <overlay-loader v-if="processing" :full-screen="false" />

    <div v-else-if="getOrderDetails" id="order" class="flex flex-col gap-6 p-4 pb-6">
      <RefundOrderItemsModal
        :is-open="showRefundModal"
        :order_id="getOrderDetails.id"
        :items="refundItems"
        @close="closeRefundModal"
      />
      <confirm-modal
        v-if="showConfirmInvoiceModal"
        :is-open="showConfirmInvoiceModal"
        :title="$t('create_invoice')"
        @cancel="showConfirmInvoiceModal = false"
        @confirm="creatorderInvoice(getOrderDetails.id)"
      >
        <template #body>
          <p
            class="flex gap-2 justify-end items-center text-base font-medium text-red-500 text-start"
          >
            {{ $t("create_invoice_body") }}
            <ExclamationTriangleIcon class="w-5 h-5" />
          </p>
        </template>
      </confirm-modal>
      <!-- Status and Actions -->
      <div class="flex flex-col gap-4 justify-between items-start w-full sm:flex-row sm:items-center">
        <OrderStatusAction
          :order="getOrderDetails"
          @refresh="fetchOrderDetails"
        />
        <div class="flex flex-wrap gap-3 items-center">
          <button
            v-if="getOrderDetails.refundable"
            class="p-2.5 rounded-lg outline outline-1 outline-offset-[-1px] outline-rose-200 text-rose-500 flex items-center gap-2 cursor-pointer text-start focus:outline-none focus:ring-2 focus:ring-rose-500 s sm:text-sm"
            @click="openRefundOrderModal"
          >
            {{ t('refund_order') }}
            <img class="block w-5 h-5" src="@/assets/icons/actions/refund.svg" alt="Refund">
          </button>
          <button
            v-if="!getOrderDetails.invoices"
            class="p-2.5 rounded-lg outline  outline-offset-[-1px] outline-primary-800 outline-2 flex items-center gap-2 cursor-pointer text-start focus:outline-none focus:ring-2 focus:ring-primary-500 sm:text-sm disabled:cursor-not-allowed"
            :disabled="invoiceProcessing"
            @click="showConfirmInvoiceModal = true"
          >
            {{ t('create_invoice') }}
            <img class="block w-5 h-5" src="@/assets/icons/actions/bill.svg" alt="Create">
          </button>
          <div class="flex font-bold text-white rounded-lg">
            <Listbox v-model="selectedPrintOption" as="div" class="relative w-full">
              <div>
                <ListboxButton
                  class="p-2.5 rounded-lg outline  outline-offset-[-1px] outline-primary-800 outline-2 flex items-center gap-2 cursor-pointer text-start focus:outline-none focus:ring-2 focus:ring-primary-500 sm:text-sm disabled:cursor-not-allowed"
                  :disabled="invoiceProcessing"
                >
                  <span v-if="invoiceProcessing" class="text-primary-600">Loading...</span>
                  <span v-else class="flex items-center rtl:left-auto rtl:right-0">
                    <img class="block w-5 h-5" src="@/assets/icons/actions/print.svg" alt="Print">
                    <img class="block w-5 h-5" src="@/assets/icons/arrows/list.svg" alt="Options">
                  </span>
                </ListboxButton>
                <transition
                  enter-active-class="transition duration-100 ease-out"
                  enter-from-class="opacity-0 transform scale-95"
                  enter-to-class="opacity-100 transform scale-100"
                  leave-active-class="transition duration-75 ease-out"
                  leave-from-class="opacity-100 transform scale-100"
                  leave-to-class="opacity-0 transform scale-95"
                >
                  <ListboxOptions
                    class="overflow-auto absolute z-10 py-1 mt-2 w-60 max-h-80 text-base bg-white rounded-lg ring-1 ring-black ring-opacity-5 shadow-lg focus:outline-none sm:text-sm"
                    :style="getLocale(locale)?.direction === 'ltr' ? 'left: 0;' : 'right: 0;'"
                  >
                    <ListboxOption
                      v-for="option in printOptions"
                      :key="option.id"
                      v-slot="{ active }"
                      as="template"
                      :value="option"
                      @click="option.event()"
                    >
                      <li
                        class="relative cursor-pointer select-none py-2.5 px-3" :class="[
                          active ? 'bg-gray-100 text-gray-900' : 'text-gray-900',
                        ]"
                      >
                        <div class="flex gap-3 justify-start items-center">
                          <component
                            :is="option.icon"
                            v-if="option.icon && typeof option.icon !== 'string'"
                            class="block w-5 h-5"
                          />
                          <img v-else-if="option.icon === 'invoice'" src="@/assets/icons/actions/print.svg" class="block w-5 h-5" alt="Invoice">
                          <img v-else-if="option.icon === 'summary'" src="@/assets/icons/actions/bill.svg" class="block w-5 h-5" alt="Summary">
                          <span class="text-sm" :class="[selected ? 'font-semibold' : 'font-normal']">
                            {{ option.name }}
                          </span>
                        </div>
                        <span
                          v-if="selected"
                          class="relative inset-y-0 right-0 flex items-center pr-4" :class="[
                            active ? 'text-gray-900' : 'text-indigo-600',
                          ]"
                        >
                          <CheckIcon class="w-5 h-5" aria-hidden="true" />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
          </div>
        </div>
      </div>

      <!-- Order Information Cards -->
      <div class="grid gap-6 p-4 w-full bg-gray-50 rounded-xl">
        <div class="grid grid-cols-1 gap-6 w-full md:grid-cols-1 lg:grid-cols-4">
          <!-- Customer Info Card -->
          <OrderCustomerInfoCard
            :customer="getOrderDetails.customer"
            :pin_code="getOrderDetails.pin_code"
            class="w-full min-w-0 bg-white rounded-lg shadow-sm transition-shadow duration-200 hover:shadow-md"
            :editable="getOrderDetails.source != 'bookingPage'"
            :order-id="getOrderDetails.id"
          />

          <!-- Order Summary Card -->
          <OrderSummaryCard
            :summary="getOrderDetails.summary"
            :branch="getOrderDetails.branch"
            :currency="getOrderDetails.currency"
            :source="getOrderDetails.source"
            class="w-full min-w-0 bg-white rounded-lg shadow-sm transition-shadow duration-200 hover:shadow-md"
          />

          <!-- Gift Card Info -->
          <OrderGiftInfoCard
            v-if="getOrderDetails.gift_card"
            :gift_card="getOrderDetails.gift_card"
            class="w-full min-w-0 bg-white rounded-lg shadow-sm transition-shadow duration-200 hover:shadow-md"
          />

          <!-- Delivery Address Card -->
          <OrderAddressCard
            :address="getOrderDetails.address"
            :longitude="getOrderDetails.longitude"
            :latitude="getOrderDetails.latitude"
            :order-id="getOrderDetails.id"
            class="w-full min-w-0 bg-white rounded-lg shadow-sm transition-shadow duration-200 hover:shadow-md"
            @refresh="fetchOrderDetails"
          />
        </div>
      </div>

      <div class="flex relative flex-col w-full text-black rounded-lg text-md">
        <!-- Items Table -->
        <OrderItemsTable
          :processing="processing"
          :items="getOrderDetails.items"
          @refresh="fetchOrderDetails"
        />
      </div>

      <!-- Payments Section -->
      <div v-if="getOrderDetails.type !== 'refund' && getOrderDetails.items?.length > 0" class="w-full">
        <div
          v-if="Number(getOrderDetails.summary?.total_amount)"
          class="flex relative flex-col text-black rounded-lg text-md"
        >
          <orderTransactionsTable
            :transcations="getOrderDetails.payments.transcations"
            :order-id="getOrderDetails.id"
            :payment-status="getOrderDetails.payments.status"
            :remaining-amount="getOrderDetails.payments.remaining_amount"
            :currency="getOrderDetails.currency"
            @refresh="fetchOrderDetails"
          />
        </div>
      </div>

      <!-- Notifications Section -->
      <div v-if="notifications?.length > 0" class="w-full">
        <orderNotifications :notifications="notifications" />
      </div>
      <dis
        v-if="settings.require_upload_image_to_close_order?.value == true && getOrderDetails.images?.length > 0"
        class="grid grid-cols-1 mb-6"
      >
        <orderCloseImages :images="getOrderDetails.images" />
      </dis>
      <!-- Refunded orders -->
      <div
        v-if="
          getOrderDetails.type !== 'refund'
            && getOrderDetails?.refunded_orders?.length > 0
        "
        class="w-full"
      >
        <RefundsOrderRef :orders="getOrderDetails?.refunded_orders" />
      </div>

      <!-- Notes -->
      <div
        v-if="getOrderDetails.meta_data?.length > 0"
        class="grid grid-cols-1 mb-8"
      >
        <div
          class="flex relative flex-col text-black rounded-lg border border-gray-200 text-md"
        >
          <metaDataTable
            :processing="processing"
            :items="getOrderDetails.meta_data"
            :order-id="getOrderDetails?.id"
            :team-id="getOrderDetails?.branch?.uuid"
            @update="fetchOrderDetails(getOrderDetails?.id)"
          />
        </div>
      </div>
      <div
        v-if="getOrderDetails.type !== 'refund' "
        class="w-full"
      >
        <OrderNotes
          :note="getOrderDetails.note"
          :order-id="getOrderDetails?.id"
          @update="fetchOrderDetails"
        />
      </div>

      <!-- Order Logs -->
      <div v-if="getOrderDetails.activity_log?.length > 0" class="w-full">
        <orderLogs :order-log="getOrderDetails.activity_log" />
      </div>

      <div
        class="flex gap-y-8 justify-center items-center w-full rounded-md sm:w-auto"
      >
        <confirmation-modal
          v-if="showConfModal"
          :dir="getLocale(locale)?.direction"
          :is-open="showConfModal"
          redirect-url="/orders"
          :api-call="deleteOrder"
          :record-id="getOrderDetails?.id"
          @closed="showConfModal = false; $emit('close')"
        >
          <p class="leading-7 text-center">
            {{ $t("confirmModal.deleteOder") }}
          </p>
        </confirmation-modal>
        <BaseButton
          v-if="!getOrderDetails.invoices"
          class="flex col-span-4 justify-center items-center px-4 py-2 text-sm font-medium text-white rounded-md transition duration-100 ease-in cursor-pointer md:col-span-2 focus:transform active:scale-95"
          custome-bg="bg-red-600"
          show-icon
          type="button"
          :processing="processingbtn"
          @click="showConfModal = true"
        >
          {{ $t("form.deleteOrder") }}
        </BaseButton>
      </div>
    </div>
    <OrderDiscountModal
      :is-open="showDiscountModal"
      :order-id="orderId"
      :items="getOrderDetails?.items || []"
      @close="showDiscountModal = false"
      @refresh="fetchOrderDetails"
    />
  </FullScreenModal>
</template>

<style scoped>
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #7f7f80 #e5e7eb;
}

@media (max-width: 768px) {
  .custom-scrollbar {
    max-height: 120px;
  }
}
</style>
