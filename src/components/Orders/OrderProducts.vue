<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { ChevronUpIcon, PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'
import type { PropType } from 'vue'
import { uniqueId } from 'lodash'
import { useVuelidate } from '@vuelidate/core'
import { Disclosure, DisclosureButton, DisclosurePanel, Switch } from '@headlessui/vue'
import {
  maxValue,
  minValue,
  required,
} from '@/utils/i18n-validators'
import type { BookingService } from '@/types'
const props = defineProps({
  modelValue: {
    type: Object as PropType<BookingService>,
    default: () => ({
      id: '',
      quantity: 0,
      price: 0,
      unit_amount: 0,
      total_amount: 0,
    }),
  },
  team: {
    type: String as PropType<string>,
    default: '',
  },
  canRemove: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue', 'set-processing'])
const { modelValue } = toRefs(props)
const rules = {
  id: {
    required,
  },
  price: {
    required,
    minValue: minValue(0),
  },
  quantity: {
    required,
    minValue: minValue(1),
  },

  total_amount: {
    required,
    minValue: minValue(0),
  },
}

const v$ = useVuelidate(rules, modelValue)

const updateValue = (value: BookingService) => {
  emit('update:modelValue', value)
}

const { fetchProducts } = useProductStore()
const productsOptions = ref([])
const processing = ref(false)

watch(
  () => props.team,
  async (val) => {
    if (val) {
      try {
        processing.value = true
        const { data } = await fetchProducts('*', {
          team: val,
        })
        console.log(data)
        productsOptions.value = data.map(product => ({
          ...product,
          value: product.uuid,
          label: product.name,
        }))
      }
      finally {
        processing.value = false
      }
    }
  },
  { immediate: true },
)

const selectProduct = (uuid: string) => {
  const selectedProduct = productsOptions.value.find(
    product => product.uuid === uuid,
  )

  if (selectedProduct?.uuid == modelValue.value.id)
    return
  updateValue({
    ...modelValue.value,
    id: selectedProduct?.uuid ?? '',
    quantity: selectedProduct?.quantity || 1,
    price: selectedProduct?.price || 0,
    unit_amount: selectedProduct?.price || 0,
    total_amount:
      (selectedProduct?.price || 0) * (selectedProduct?.quantity || 1) || 0,
  })
}

const changeQuantity = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...modelValue.value,
    quantity: +modelValue.value.quantity,
    total_amount:
      +modelValue.value.quantity * (modelValue.value?.unit_amount || 0),
  })
}

const changePricePerUnit = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...modelValue.value,
    unit_amount: +event.target.value,
    price: +event.target.value,
    total_amount:
      +event.target.value * (modelValue.value?.quantity || 0),
  })
}
</script>

<template>
  <div class="space-y-4">
    <div class="relative p-4 bg-white rounded-lg border shadow-sm">
      <Disclosure v-slot="{ open }" as="div" default-open>
        <DisclosureButton

          :class="open ? 'border-b border-gray-200 pb-4' : ''"
          class="flex justify-between items-center w-full cursor-pointer select-none"
        >
          <div class="flex gap-2 justify-between items-center w-full">
            <span class="text-base font-normal text-neutral-800">{{ $t("product") }} </span>
            <div class="flex gap-2 justify-end items-center">
              <span class="flex gap-2 items-center">
                <Switch
                  :id="`discount-applying-${modelValue.id}`"
                  v-model="modelValue.applyDiscount"
                  class="inline-flex relative flex-shrink-0 mt-1 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  :class="[
                    modelValue.applyDiscount
                      ? 'bg-primary-600'
                      : 'bg-gray-200',
                  ]"
                >
                  <span
                    aria-hidden="true"
                    class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
                    :class="[
                      modelValue.applyDiscount
                        ? 'translate-x-5 rtl:-translate-x-5'
                        : 'translate-x-0',
                    ]"
                  />
                </Switch>
                <span class="text-sm text-gray-700">  {{ $t('apply_discount') }} </span>
              </span>
              <button
                v-if="canRemove"
                type="button"
                class="flex justify-center items-center w-9 h-9 text-red-500 bg-white rounded-full border border-gray-200 shadow-sm transition hover:bg-red-50 hover:text-red-700"
                aria-label="حذف المنتج"
                :title="$t('deleteProduct')"
                @click.stop.prevent="$emit('remove')"
              >
                <TrashIcon class="w-5 h-5" />
              </button>
              <ChevronUpIcon
                :class="open ? 'transform rotate-180' : ''"
                class="w-5 h-5 text-gray-500 transition-transform duration-200"
              />
              <!--  -->
            </div>
          </div>
        </DisclosureButton>

        <DisclosurePanel>
          <div class="grid overflow-visible gap-2.5 items-start grid-cols-1 sm:[grid-template-columns:repeat(auto-fit,minmax(180px,1fr))]">
            <!-- Product select -->
            <div class="col-span-1 w-full min-w-0 text-start">
              <form-group :validation="v$" name="id">
                <template #default="{ attrs }">
                  <BaseComboBox
                    v-bind="attrs"
                    :model-value="modelValue.id"
                    :id="`product-${uniqueId()}`"
                    place-holder="productName"
                    required
                    arial-label="Search"
                    :disabled="!productsOptions.length"
                    :error="v$.$dirty && v$.id.$error"
                    :options="productsOptions"
                    :label="$t('form.product')"
                    class="w-full min-w-[120px]"
                    @update:model-value="selectProduct($event)"
                  >
                    {{ $t('dashboard.booking.product') }}
                  </BaseComboBox>
                </template>
              </form-group>
              <p v-if="!productsOptions.length" class="error-message">
                {{ $t('dashboard.booking.no_product') }}
              </p>
            </div>
            <!-- Quantity -->
            <div class="col-span-1 w-full min-w-0 text-start">
              <form-group :validation="v$" name="quantity">
                <template #default="{ attrs }">
                  <NumberInput
                    v-bind="attrs"
                    :id="`quantity-${uniqueId()}`"
                    v-model="modelValue.quantity"
                    :label="$t('quantity')"
                    min="1"
                    :placeholder="$t('quantity')"
                    @change="changeQuantity"
                  />
                </template>
              </form-group>
            </div>
            <!-- Price -->
            <div class="col-span-1 w-full min-w-0 text-start">
              <form-group
                :validation="v$"
                name="price"
                :error-name="`services.${index}.unit_amount`"
              >
                <template #default="{ attrs }">
                  <NumberInput
                    v-bind="attrs"
                    :id="`pricePerUnit-${name}`"
                    v-model="modelValue.price"
                    :label="$t('price')"
                    :hint="$t('pricePerUnit')"
                    min="1"
                    :placeholder="$t('pricePerUnit')"
                    @change="changePricePerUnit"
                  />
                </template>
              </form-group>
            </div>
            <!-- Discount -->

            <!-- Total -->
            <div class="col-span-1 w-full min-w-0 text-start">
              <form-group :validation="v$" name="total_amount">
                <template #default="{ attrs }">
                  <NumberInput
                    v-bind="attrs"
                    :id="`productPrice-${uniqueId()}`"
                    :key="`productPrice-${uniqueId()}`"
                    v-model="modelValue.total_amount"
                    readonly
                    :label="$t('total')"
                    @blur="attrs.onblur"
                  />
                </template>
              </form-group>
            </div>
          </div>
        </DisclosurePanel>
      </Disclosure>
      <!-- Trash icon absolute top-left -->

      <!-- Apply Discount Checkbox -->
    </div>
  </div>
</template>
