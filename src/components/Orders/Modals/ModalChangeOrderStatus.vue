<script lang="ts" setup>
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { Switch } from '@headlessui/vue'
import type { Order } from '@/types'
import { required } from '@/utils/i18n-validators'
import OrderService from '@/services/OrderService'
import Reasons from '@/services/Reasons'
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  order: {
    type: Object as PropType<Order | null>,
    default: () => ({}),
  },
  status: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['close', 'refresh'])
const { updateOrderStatus, fetchStatus } = useOrder()
const { getOrderStatuses } = storeToRefs(useOrder())
const orderStore = useOrder()
const { locale } = useI18n()
const { order } = toRefs(props)
const { getLocale } = storeToRefs(useLocalesStore())

const formData = reactive({
  note_for_status: '',
  id: '',
  status: '',
  sms: true,
  cancel_reason_id: '',
})
const rules = {
  status: {
    required,
  },
}

const processing = ref(false)
const statusFilters = ref([])
const statusChanged = ref(false)
const $v = useVuelidate(rules, formData)
const reasons = ref<any>([])
onMounted(async () => {
  Promise.all([
    fetchStatus(),
    Reasons.getReasons('invoice-refund').then((res) => {
      reasons.value = [...res.data.data]
    }),
  ])
  formData.status = props.status
  formData.id = order.value?.id
})

const submitForm = async () => {
  $v.value.$touch()
  if ($v.value.$invalid)
    return
  processing.value = true
  const payload = {
    id: formData.id,
    status: formData.status,
    note_for_status: formData.note_for_status,
    sendSms: formData.sms,
    ...(formData.status === 'canceled' && {
      cancel_reason_id: formData.cancel_reason_id,
    }),
  }
  try {
    await updateOrderStatus(payload)
    emits('refresh')
    emits('close')
  }
  finally {
    processing.value = false
  }
}
const removedStatus = [
  'failed',
  'waiting-for-payment',
  'waiting-for-bank-transfer-confirmation',
  'fully-refunded',
  'partial-refund',
  'canceled',
  'pending',
]
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    title="status_order_no"
    :param="order.OrderNum"
    @close="emits('close')"
  >
    <overlay-loader v-if="processing" :full-screen="processing" />
    <form class="text-start" @submit.prevent="submitForm">
      <div class="grid grid-cols-1 gap-4">
        <div>
          <form-group :validation="$v" name="status">
            <template #default="{ attrs }">
              <SelectInput
                v-bind="attrs"
                id="status"
                v-model="formData.status"
                :label="$t('choose_order_status')"
                @change="statusChanged = true"
              >
                <option
                  v-for="status in getOrderStatuses"
                  :key="status.id"
                  :value="status.status"
                  :hidden="removedStatus.includes(status.status)"
                >
                  {{ status.label }}
                </option>
              </SelectInput>
            </template>
          </form-group>
        </div>
        <generic-alert
          v-if="statusChanged && formData.status === 'canceled'"
          type="warning"
          :message="
            $t(
              'when_change_order_status_to_canceled_will_change_all_appointments_status_to_canceled',
            )
          "
        />
        <generic-alert
          v-if="statusChanged && formData.status === 'refunded'"
          type="info"
          :message="
            $t(
              'when_change_order_status_to_refunded_system_will_create_credit_note_invoice',
            )
          "
        />
        <div v-if="formData.status === 'canceled'">
          <form-group :validation="$v" name="cancel_reason_id">
            <template #default="{ attrs }">
              <SelectInput
                id="reasons"
                v-bind="attrs"
                v-model="formData.cancel_reason_id"
                :label="$t('reasonsForCancellation')"
              >
                <option value="" selected disabled>
                  {{ $t("form.select") }}
                </option>
                <option
                  v-for="reason in reasons"
                  :key="reason?.uuid"
                  :value="reason?.uuid"
                >
                  {{ reason?.name }}
                </option>
              </SelectInput>
            </template>
          </form-group>
        </div>
        <div>
          <form-group error-name="note_for_status">
            <template #default="{ attrs }">
              <TextareaInput
                v-bind="attrs"
                id="notes"
                v-model="formData.note_for_status"
                :label="$t('additionals_notes_for_customer')"
                :placeholder="$t('enter_notes')"
                class="mt-1"
              />
            </template>
          </form-group>
        </div>
      </div>
      <div class="mt-8">
        <BaseButton
          type="submit"
          show-icon
          class="w-full"
        >
          {{ $t("change_status_order") }}
        </BaseButton>
      </div>
    </form>
  </Modal>
</template>
