<script setup lang="ts">
import { computed, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { Switch } from '@headlessui/vue'
import type { PropType } from 'vue'
import { useLocalesStore } from '@/stores/locales'
import { useOrder } from '@/stores/order'
import DiscountInput from '@/components/FormControls/Inputs/DiscountInput.vue'
import MultiSelectInput from '@/components/FormControls/Inputs/MultiSelectInput.vue'

interface OrderItem {
  id: string
  uuid: string
  name: string
  price: number
  discount?: number
}

interface FormData {
  discountAmount: string
  discountType: 'fixed' | 'percentage'
  applyAll: boolean
  selectedItems: string[]
}

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  orderId: {
    type: String,
    required: true,
  },
  items: {
    type: Array as PropType<OrderItem[]>,
    required: true,
  },
})

const emit = defineEmits(['close', 'refresh'])

const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const { updateOrderDiscount } = useOrder()

const processing = ref(false)

// Initialize with items that have discounts
const initialSelectedItems = computed(() => {
  return props.items
    .filter(item => item.discount !== undefined && item.discount > 0)
    .map(item => item.uuid)
})

const formData = reactive<FormData>({
  discountAmount: '',
  discountType: 'fixed',
  applyAll: false,
  selectedItems: initialSelectedItems.value,
})

// Transform items for MultiSelectInput with discount indication
const itemOptions = computed(() => {
  return props.items.map(item => ({
    id: item.uuid,
    name: item.discount
      ? `${item.name} - ${item.price} (${$t('discount')}: ${item.discount})`
      : `${item.name} - ${item.price}`,
  }))
})

// Watch for changes in applyAll to update selectedItems
watch(
  () => formData.applyAll,
  (newValue) => {
    if (newValue)
      formData.selectedItems = props.items.map((item: OrderItem) => item.uuid)
    else
      formData.selectedItems = initialSelectedItems.value
  },
)

// Reset selection when modal opens
watch(
  () => props.isOpen,
  (newValue) => {
    if (newValue) {
      formData.selectedItems = initialSelectedItems.value
      formData.applyAll = false
    }
  },
)

const save = async () => {
  try {
    processing.value = true

    const payload = {
      discount_type: formData.discountType,
      discount_amount: Number(formData.discountAmount),
      apply_all: formData.applyAll,
      items: formData.selectedItems,
    }

    await updateOrderDiscount(props.orderId, payload)
    emit('refresh')
    emit('close')
  }
  catch (error) {
    console.error('Error updating discount:', error)
  }
  finally {
    processing.value = false
  }
}
const discountObject = ref({
  amount: 0,
  type: 'fixed',
})
const updateDiscount = (discountObject: {
  amount: number
  type: 'fixed' | 'percentage'
}) => {
  formData.discountAmount = Number(discountObject.amount)
  formData.discountType
    = discountObject.type === 'fixed' ? 'fixed' : 'percentage'
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="$t('discount_modal')"
    @close="emit('close')"
  >
    <div class="flex flex-col gap-4 p-4">
      <overlay-loader v-if="processing" :full-screen="false" />

      <!-- Discount Amount -->
      <div class="w-full">
        <DiscountInput
          id="discount_amount"
          v-model="discountObject"
          :label="$t('discount_amount')"
          :placeholder="$t('enter_discount_amount')"
          :hint="$t('hints.discount')"
          :min="0"
          :max="formData.discountType === 'percentage' ? 100 : undefined"
          @update:model-value="updateDiscount"
        />
      </div>

      <!-- Item Selection -->
      <div v-if="!formData.applyAll" class="w-full">
        <MultiSelectInput
          v-model="formData.selectedItems"
          :options="itemOptions"
          :label="$t('fields.select_items_for_discount')"
          :placeholder="$t('form.select')"
        />
      </div>

      <!-- Apply to All Items -->
      <div class="flex justify-between items-center py-2">
        <label class="text-sm font-medium text-gray-700">
          {{ $t("apply_discount_to_all_items") }}
        </label>
        <Switch
          id="apply_discount_all"
          v-model="formData.applyAll"
          class="inline-flex relative flex-shrink-0 mt-1 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          :class="[formData.applyAll ? 'bg-primary-600' : 'bg-gray-200']"
          @update:model-value="
            formData.applyAll
              ? (formData.selectedItems = props.items.map(
                (item: OrderItem) => item.uuid,
              ))
              : (formData.selectedItems = initialSelectedItems.value)
          "
        >
          <span
            aria-hidden="true"
            class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
            :class="[
              formData.applyAll
                ? 'translate-x-5 rtl:-translate-x-5'
                : 'translate-x-0',
            ]"
          />
        </Switch>
      </div>

      <!-- Save Button -->
      <BaseButton class="mt-4 w-full" :processing="processing" @click="save">
        {{ $t("save") }}
      </BaseButton>
    </div>
  </Modal>
</template>
