<script setup lang="ts">
import type { PropType } from 'vue'

import {
  LinkIcon,
  MinusIcon,
  PlusIcon,
  TrashIcon,
} from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import type { OrderTranscations } from '@/types'
import { formatDateAndTime } from '@/composables/dateFormat'
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  transactionDetails: {
    type: Object as PropType<OrderTranscations>,
    default: () => ({}),
  },
})
const emits = defineEmits(['close', 'refresh'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { updateTransactionStatus, updateTransaction, deleteTransaction }
  = useOrder()
const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)
const processing = ref(false)
const isNumber = (evt: any) => {
  evt = evt || window.event
  const charCode = evt.which ? evt.which : evt.keyCode
  if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46)
    evt.preventDefault()
  else return true
}
watch(
  () => props.transactionDetails,
  (val) => {
    if (val) {
      formData.uuid = val.id
      formData.amount = val.amount
      formData.note = val.note
      formData.recipt_file = val.recipt_file
      formData.attached_file = val.attached_file
      formData.date = val.date
      formData.reference_number = val.reference_number
    }
  },
)
const { transactionDetails } = toRefs(props)
const formData = reactive({
  uuid: '',
  amount: 0,
  note: '',
  recipt_file: '',
  attached_file: '',
  date: '',
  reference_number: '',
})
function submitForm() {}
function updateTransStatus(status: string) {
  processing.value = true
  updateTransactionStatus(formData.uuid, status).finally(() => {
    processing.value = false
  })
}
async function deleteRecord() {
  try {
    processing.value = true
    await deleteTransaction(formData.uuid)
    emits('close')
    emits('refresh')
  }
  finally {
    processing.value = false
  }
}
async function updateRecord() {
  try {
    processing.value = true
    await updateTransaction(formData.uuid, formData)
    emits('close')
    emits('refresh')
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    title="transaction_no"
    :param="transactionDetails.transaction_no"
    panel-classes="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 align-middle shadow-xl transition-all  sm:w-2xl md:px-8 lg:px-8 "
    @close="$emit('close')"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    <div class="mt-2">
      <div class="flex gap-3 justify-start items-center w-full">
        <div class="w-full">
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label class="block text-sm font-bold text-neutral-500">{{ $t("form.customerName") }}
            </label>
            <div class="relative items-center focus-within:z-10">
              {{ transactionDetails?.customer ?? "--" }}
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label class="block text-sm font-bold text-neutral-500">{{ $t("Date") }}
            </label>
            <div class="flex relative gap-3 items-center focus-within:z-10">
              {{ formatDateAndTime(transactionDetails?.date) ?? "--" }}
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label class="block text-sm font-bold text-neutral-500">{{ $t("transaction.payment_method") }}
            </label>
            <div class="flex relative gap-6 items-center focus-within:z-10">
              {{ transactionDetails?.payment_method?.name ?? "--" }}
              <a
                v-if="
                  transactionDetails?.payment_method?.slug == 'bank_transfer'
                "
                :href="transactionDetails.recipt_file"
                target="_blank"
                class="text-blue-500 hover:underline"
              >
                <LinkIcon class="inline-block w-4 h-4" />
                {{ $t("recipt_file") }}</a>
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label class="block text-sm font-bold text-neutral-500">{{ $t("reference_number") }}
            </label>
            <p class="flex relative items-center focus-within:z-10">
              {{ transactionDetails?.reference_number ?? "--" }}
            </p>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label class="block text-sm font-bold text-neutral-500">{{ $t("attached_file") }}
            </label>
            <p class="flex relative items-center focus-within:z-10">
              {{
                transactionDetails?.attached_file ? $t("attached_file") : "--"
              }}
            </p>
            <a
              v-if="transactionDetails?.attached_file"
              :href="transactionDetails.attached_file"
              target="_blank"
              class="text-blue-500 hover:underline"
            >
              <LinkIcon class="inline-block w-4 h-4" />
              {{ $t("download") }}
            </a>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label class="block text-sm font-bold text-neutral-500">{{ $t("booking.status") }}
            </label>
            <div
              v-if="transactionDetails.status"
              class="relative items-center focus-within:z-10"
            >
              <booking-status :book-status="transactionDetails.status" />
            </div>
            <span v-else> -- </span>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-2">
      <form class="text-start" @submit.prevent="submitForm">
        <div class="grid grid-cols-1 gap-4">
          <div class="w-full">
            <form-group error-name="amount">
              <template #default="{ attrs }">
                <div class="flex flex-col items-start w-full">
                  <div class="flex relative items-center w-full">
                    <TextInput
                      v-bind="attrs"
                      id="amount"
                      v-model="formData.amount"
                      :label="$t('dashboard.booking.amount')"
                      autofocus
                      :placeholder="$t('enter_your_amount')"
                      name="amount"
                      @keypress="isNumber($event)"
                    >
                      <template #suffix>
                        <div class="flex items-center">
                          <span class="absolute top-1/2 z-10 text-sm font-semibold text-gray-700 -translate-y-1/2 end-3">
                            {{ $t(`currenices.${userInfo.tenant?.currency}`) }}
                          </span>
                        </div>
                      </template>
                    </TextInput>
                  </div>
                </div>
              </template>
            </form-group>
          </div>
          <div class="w-full">
            <form-group error-name="note">
              <template #default="{ attrs }">
                <TextareaInput
                  v-bind="attrs"
                  id="note"
                  v-model="formData.note"
                  :label="$t('note')"
                  :placeholder="$t('note')"
                />
              </template>
            </form-group>
          </div>
        </div>
      </form>
    </div>

    <div
      v-if="transactionDetails.status === 'pending'"
      class="flex gap-4 my-6 w-full"
    >
      <BaseButton
        class="inline-flex hover:bg-green-700"
        custome-bg="bg-green-600"
        @click="updateTransStatus('confirmed')"
      >
        {{ $t("accepted") }}
      </BaseButton>
      <BaseButton
        class="inline-flex hover:bg-red-700"
        custome-bg="bg-red-600"
        :processing="processing"
        @click="updateTransStatus('refused')"
      >
        {{ $t("decliend") }}
      </BaseButton>
      <div v-if="transactionDetails.recipt_file" class="flex flex-col">
        <a
          :href="transactionDetails.recipt_file"
          class="flex gap-1 items-center p-2 w-full text-sm text-start"
        >
          {{ $t("form.downloadReceipt") }}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-4"
            viewBox="0 0 16 16"
          >
            <path
              fill="#888888"
              fill-rule="evenodd"
              d="M8 0a5.53 5.53 0 0 0-3.594 1.342c-.766.66-1.321 1.52-1.464 2.383C1.266 4.095 0 5.555 0 7.318C0 9.366 1.708 11 3.781 11H7.5V5.5a.5.5 0 0 1 1 0V11h4.188C14.502 11 16 9.57 16 7.773c0-1.636-1.242-2.969-2.834-3.194C12.923 1.999 10.69 0 8 0zm-.354 15.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 14.293V11h-1v3.293l-2.146-2.147a.5.5 0 0 0-.708.708l3 3z"
            />
          </svg>
        </a>
      </div>
    </div>
    <div class="flex gap-4 justify-center items-center mt-6 w-full">
      <BaseButton
        class="inline-flex hover:bg-primary-700"
        custome-bg="bg-primary-600"
        :processing="processing"
        @click="updateRecord()"
      >
        {{ $t("form.update") }}
      </BaseButton>
      <BaseButton
        class="inline-flex w-auto hover:bg-red-700"
        :processing="processing"
        custome-bg="bg-red-600"
        @click="deleteRecord()"
      >
        {{ $t("form.delete") }}
        <TrashIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </div>
  </Modal>
</template>
