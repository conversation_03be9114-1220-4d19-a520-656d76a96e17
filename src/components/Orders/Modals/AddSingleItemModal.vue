<script lang="ts" setup>
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { computed, reactive, ref, watch } from 'vue'
import { useVuelidate } from '@vuelidate/core'
import { uniqueId } from 'lodash'
import { minValue, required, requiredIf } from '@/utils/i18n-validators'
import type { Service, Staff } from '@/types'
import { useStaffStore } from '@/stores/staff'
import { useProductStore } from '@/stores/products'

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  orderId: {
    type: String,
    required: true,
  },
})
const emit = defineEmits(['close', 'refresh'])
const { getLocale } = storeToRefs(useLocalesStore())
const { getOrderDetails } = storeToRefs(useOrder())
const { locale } = useI18n()
const { addOrderItem } = useOrder()
const { getUserInfo } = storeToRefs(useAuthStore())
const { fetchStaff, fetchStaffServices } = useStaffStore()
const productStore = useProductStore()

const { t } = useI18n()

const formData = reactive({
  order_id: props.orderId,
  team_id: getOrderDetails.value.branch?.id || '',
  item_type: 'service', // default to service, can switch to product
  id: '',
  staff_id: '',
  quantity: 1,
  discount_amount: 0,
  discount_type: 'amount', // Add discount type field
  price: 0,
  unit_amount: 0,
  total_amount: 0,
  showDiscount: false,
})

const rules = {
  item_type: { required },
  quantity: { required, minValue: minValue(1) },
  staff_id: {
    required: requiredIf(() => formData.item_type === 'service'),
  },
  id: {
    required,
  },

  total_amount: { minValue: minValue(0.01) },
}

const vFrom$ = useVuelidate(rules, formData)
const processingBtn = ref(false)
const servicesOptions = ref([])
const productsOptions = ref([])
const staffMembers = ref<Staff[]>([])
const isLoading = ref(false)

const fetchStaffList = async () => {
  try {
    isLoading.value = true
    const response = await fetchStaff(1, { team_id: formData.team_id })
    staffMembers.value = response.data || []
  }
  catch (error) {
    console.error('Failed to load staff:', error)
  }
  finally {
    isLoading.value = false
  }
}

const fetchProductsList = async () => {
  try {
    isLoading.value = true
    const response = await productStore.fetchProducts(1, { branch_id: formData.team_id })
    productsOptions.value = response.data?.map(product => ({
      ...product,
      value: product.uuid,
      label: product.name,
    })) || []
    console.log(productsOptions.value)
  }
  catch (error) {
    console.error('Failed to load products:', error)
  }
  finally {
    isLoading.value = false
  }
}

onBeforeMount(async () => {
  await fetchStaffList()
  await fetchProductsList()
})

const staffsOptions = computed(() => {
  return staffMembers.value.map((staff) => {
    return {
      ...staff,
      id: staff.uuid,
      name: staff.name,
    }
  })
})

const selectStaff = (uuid) => {
  formData.staff_id = uuid
  formData.service_id = ''

  if (uuid) {
    isLoading.value = true
    fetchStaffServices(uuid, '*')
      .then((data) => {
        servicesOptions.value = data.data
          .filter(service => service.canServe)
          .map((service) => {
            return {
              ...service,
              value: service.uuid,
              label: service.name,
            }
          })
      })
      .finally(() => {
        isLoading.value = false
      })
  }
  else {
    servicesOptions.value = []
  }
}

const selectService = (uuid) => {
  const selectedService = servicesOptions.value.find(
    service => service.uuid === uuid,
  )

  if (!selectedService)
    return

  formData.id = selectedService.uuid
  formData.quantity = 1
  formData.discount_amount = 0
  formData.unit_amount = selectedService.price || 0
  calculateTotal()
}

const selectProduct = (uuid) => {
  const selectedProduct = productsOptions.value.find(
    product => product.value === uuid,
  )

  if (!selectedProduct)
    return

  formData.id = selectedProduct.value
  formData.quantity = 1
  formData.discount_amount = 0
  formData.unit_amount = selectedProduct.price || 0
  calculateTotal()
}

const changeQuantity = (event) => {
  formData.quantity = +event.target.value
  calculateTotal()
}

const changePricePerDiscount = (event) => {
  let discount = event.target.value
  const discountType = formData.discount_type || 'amount'
  let total = 0

  if (discountType === 'percent') {
    discount = Math.min(Number(discount), 100) // max 100%
    total = (formData.unit_amount || 0) * (formData.quantity || 0)
    total = total - (total * discount / 100)
  }
  else {
    total = (formData.unit_amount || 0) * (formData.quantity || 0) - discount
  }

  formData.discount_amount = discount
  formData.total_amount = total
}

const calculateTotal = () => {
  formData.total_amount = (formData.unit_amount * formData.quantity) - formData.discount_amount
}

const save = async () => {
  try {
    processingBtn.value = true
    vFrom$.value.$touch()

    if (vFrom$.value.$invalid)
      return

    const itemData = {
      staffId: formData.item_type === 'service' ? formData.staff_id : '',
      id: formData.id,
      quantity: formData.quantity,
      discount_amount: formData.discount_amount,
      price: formData.unit_amount,
      unit_amount: formData.unit_amount,
      unit_price: formData.unit_amount,
      total_amount: formData.total_amount,
      type: formData.item_type,
    }

    await addOrderItem(props.orderId, itemData)
    emit('refresh')
    emit('close')
  }
  catch (error) {
    console.error('Error saving item:', error)
  }
  finally {
    processingBtn.value = false
  }
}

const showDiscount = () => {
  formData.showDiscount = true
}

const hideDiscount = () => {
  formData.discount_amount = 0
  formData.discount_type = 'amount'
  formData.showDiscount = false
  calculateTotal()
}

watch(() => formData.item_type, (val) => {
  if (val === 'product')
    fetchProductsList()
})
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    title="add_item"
    @close="emit('close')"
  >
    <div class="relative w-full text-start">
      <overlay-loader v-if="isLoading" :full-screen="false" />

      <form class="text-start" @submit.prevent="save">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 gap-y-6">
          <!-- Item Type Selection -->
          <div class="md:col-span-2">
            <form-group :validation="vFrom$" name="item_type">
              <template #default="{ attrs }">
                <div class="flex w-full border border-[#0C2D48] rounded-md overflow-hidden">
                  <label
                    class="flex-1 py-2 px-4 text-center cursor-pointer transition-colors border-0" :class="[
                      formData.item_type === 'service'
                        ? 'bg-[#0C2D48] text-white font-semibold'
                        : 'bg-white text-[#0C2D48] font-medium',
                    ]"
                  >
                    <input
                      id="service"
                      v-model="formData.item_type"
                      type="radio"
                      value="service"
                      class="sr-only"
                    >
                    {{ $t("service") }}
                  </label>
                  <label
                    class="flex-1 py-2 px-4 text-center cursor-pointer transition-colors border-0" :class="[
                      formData.item_type === 'product'
                        ? 'bg-[#0C2D48] text-white font-semibold'
                        : 'bg-white text-[#0C2D48] font-medium',
                    ]"
                  >
                    <input
                      id="product"
                      v-model="formData.item_type"
                      type="radio"
                      value="product"
                      class="sr-only"
                    >
                    {{ $t("product") }}
                  </label>
                </div>
              </template>
            </form-group>
          </div>

          <!-- Staff Selection (only for services) -->
          <div v-if="formData.item_type === 'service'" class="w-full">
            <form-group :validation="vFrom$" name="staff_id">
              <template #default="{ attrs }">
                <SelectInput
                  v-bind="attrs"
                  id="staff_id"
                  :model-value="formData.staff_id"
                  :label="$t('form.staff')"
                  required
                  @update:model-value="selectStaff($event)"
                >
                  <option
                    v-for="staff in staffsOptions"
                    :key="staff?.id"
                    :value="staff?.id"
                  >
                    {{ staff.name }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>

          <!-- Service Selection -->
          <div v-if="formData.item_type === 'service' && formData.staff_id" class="w-full">
            <form-group :validation="vFrom$" name="id">
              <template #default="{ attrs }">
                <BaseComboBox
                  v-bind="attrs"
                  id="service_select"
                  :model-value="formData.id"
                  place-holder="serviceName"
                  required
                  arial-label="Search"
                  :disabled="!servicesOptions.length"
                  :options="servicesOptions"
                  :label="$t('form.service')"
                  @update:model-value="selectService($event)"
                >
                  {{ $t("booking.service") }}
                </BaseComboBox>
              </template>
            </form-group>
          </div>

          <p v-if="formData.item_type === 'service' && formData.staff_id && !servicesOptions.length" class="text-sm text-red-600">
            {{ $t("booking.no_service") }}
          </p>

          <!-- Product Selection -->
          <div v-if="formData.item_type === 'product'" class="w-full">
            <form-group :validation="vFrom$" name="id">
              <template #default="{ attrs }">
                <BaseComboBox
                  v-bind="attrs"
                  id="product_select"
                  :model-value="formData.id"
                  place-holder="productName"
                  required
                  arial-label="Search"
                  :disabled="!productsOptions.length"
                  :options="productsOptions"
                  :label="$t('form.product')"
                  @update:model-value="selectProduct($event)"
                >
                  {{ $t("product") }}
                </BaseComboBox>
              </template>
            </form-group>
          </div>

          <!-- Quantity -->
          <div class="w-full">
            <form-group :validation="vFrom$" name="quantity">
              <template #default="{ attrs }">
                <NumberInput
                  v-bind="attrs"
                  id="quantity"
                  v-model="formData.quantity"
                  :label="$t('quantity')"
                  :min="1"
                  required
                  @change="changeQuantity"
                />
              </template>
            </form-group>
          </div>

          <!-- Price and Discount Section -->
          <div class="col-span-1 w-full min-w-0 text-start">
            <form-group
              :validation="vFrom$"
              name="unit_amount"
              error-name="unit_amount"
            >
              <template #default="{ attrs }">
                <NumberInput
                  v-bind="attrs"
                  id="pricePerUnit"
                  v-model="formData.unit_amount"
                  :label="$t('price')"
                  :hint="$t('pricePerUnit')"
                  min="1"
                  :placeholder="$t('pricePerUnit')"
                  @change="calculateTotal"
                />
              </template>
            </form-group>
            <button
              v-if="!formData.showDiscount"
              class="text-sm cursor-pointer text-primary"
              @click="showDiscount"
            >
              {{ $t("add_discount") }}
            </button>
            <button
              v-else
              class="text-sm cursor-pointer text-primary"
              @click="hideDiscount"
            >
              {{ $t("hide_discount") }}
            </button>
          </div>
          <!-- Discount -->
          <div v-if="formData.showDiscount || formData.discount_amount" class="col-span-1 w-full min-w-0 text-start">
            <form-group
              :validation="vFrom$"
              name="discount_amount"
              error-name="discount_amount"
            >
              <template #default="{ attrs }">
                <DiscountInput
                  id="itemDiscount"
                  :model-value="{ amount: formData.discount_amount, type: formData.discount_type || 'amount' }"
                  :label="$t('discount_amount')"
                  :placeholder="$t('discount_amount')"
                  :hint="$t('hints.discount')"
                  :min="1"
                  @update:model-value="val => {
                    formData.discount_amount = val.amount;
                    formData.discount_type = val.type;
                    changePricePerDiscount({ target: { value: val.amount } });
                  }"
                />
              </template>
            </form-group>
          </div>

          <!-- Total Amount -->
          <div class="w-full">
            <form-group :validation="vFrom$" name="total_amount">
              <template #default="{ attrs }">
                <NumberInput
                  v-bind="attrs"
                  id="total_amount"
                  v-model="formData.total_amount"
                  readonly
                  min="0"
                  :label="$t('total')"
                />
              </template>
            </form-group>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-center w-full mt-6">
          <BaseButton
            type="submit"
            custome-bg="bg-secondary"
            :processing="processingBtn"
          >
            {{ formData.item_type === 'service' ? $t("fields.add_service") : $t("fields.add_product") }}
          </BaseButton>
        </div>
      </form>
    </div>
  </Modal>
</template>
