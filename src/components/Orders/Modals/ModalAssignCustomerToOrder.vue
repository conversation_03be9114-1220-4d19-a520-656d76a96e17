<script setup lang="ts">
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import type { OrderItems, Staff, TimeSlot } from '@/types'
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  customerSelected: {
    type: Object as PropType<OrderItems>,
    default: () => ({}),
    required: false,
  },
  orderId: {
    type: String,
    default: '',
    required: true,
  },
})
const emits = defineEmits(['close'])
const { getLocale } = storeToRefs(useLocalesStore())
const { assignCustomerToOrder } = useOrder()
const { locale } = useI18n()
const customer = ref()
const formData = reactive({
  customer_id: '',
})
const processing = ref(false)

const { customerSelected } = toRefs(props)

watch(
  () => customerSelected.value,
  (val) => {
    if (!val)
      return
    customer.value = val
    formData.customer_id = val.uuid
  },
  { immediate: true },
)

function setNewCustomer(val: Record<string, any>) {
  formData.customer_id = val.customer_id
}

async function updateCustomer() {
  processing.value = true
  try {
    await assignCustomerToOrder(props.orderId, formData.customer_id)
    emits('close')
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    title="assign_customer_to_order"
    panel-classes="w-full max-w-2xl transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all  sm:w-2xl md:px-8 lg:px-8 "
    @close="emits('close')"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    <form class="text-start" @submit.prevent="updateCustomer()">
      <div class="grid grid-cols-1 gap-4 gap-y-6">
        <div class="w-full">
          <CustomerAndStaff
            :model-value="formData"
            :customer="customer"
            @update:model-value="setNewCustomer"
          />
        </div>
        <div class="w-full flex justify-center">
          <BaseButton
            class="inline-flex hover:bg-green-700"
            custome-bg="bg-green-600"
            :processing="processing"
          >
            {{ $t("assign_customer") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </Modal>
</template>
