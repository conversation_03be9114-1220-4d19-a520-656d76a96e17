<script lang="ts" setup>
// Types
import type { PropType } from 'vue'
import { computed, nextTick, onBeforeMount, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'
import { useVuelidate } from '@vuelidate/core'
import { onClickOutside, useDebounceFn } from '@vueuse/core'
import { uniqueId } from 'lodash'
import dayjs from 'dayjs'
import { PlusIcon } from '@heroicons/vue/24/outline'
import type { BookingService, OrderItems , DaySlot, TimeSlot } from "@/types";

// Vue & Composables

// Utils
import { required, requiredIf } from '@/utils/i18n-validators'

// Components
import { Disclosure, DisclosureButton, DisclosurePanel, Switch } from '@headlessui/vue'
import { ChevronUpIcon } from '@heroicons/vue/20/solid'
import CalendarsIcon from '@/components/Icons/CalendarsIcon.vue'
import TrashIcon from '@/components/Icons/TrashIcon.vue'
import PlusIconBordered from '@/components/Icons/PlusIconBordered.vue'
import ItemTypeSelection from '@/components/Orders/ItemTypeSelection.vue'
import DiscountInput from '@/components/FormControls/Inputs/DiscountInput.vue'
import TimeSlotsGrid from '@/components/Bookings/TimeSlotsGrid.vue'
import OrderSummary from '@/components/Orders/OrderSummary.vue'
import OrderMetaDataFields from '@/components/Orders/OrderMetaDataFields.vue'
import CustomerAndStaff from '@/components/Bookings/CustomerAndStaff.vue'
import { TagIcon } from '@heroicons/vue/24/outline'

// Stores
import { useLocalesStore } from '@/stores/locales'
import { useOrder } from '@/stores/order'
import { useAuthStore } from '@/stores/auth'
import { useMetaDataStore } from '@/stores/metaData'
import { usePosStore } from '@/stores/pos'
import { useEventStore } from '@/stores/event'

// Component Props & Emits
const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'refresh'): void
}>()

// Store References
const { getLocale } = storeToRefs(useLocalesStore())
const { getOrderDetails } = storeToRefs(useOrder())
const { getUserInfo } = storeToRefs(useAuthStore())
const { locale } = useI18n()
const { t } = useI18n()

// Store Actions
const { createOrder } = useOrder()
const { fetchTeamMetaData } = useMetaDataStore()
const { checkoutCalculate } = usePosStore()
const { fetchTimeSlots } = useEventStore()

// Component State
const formData = reactive({
  team_id: '',
  order_id: '',
  customer_id: '',
  items: [] as BookingService[],
  note: '',
  start: null as string | null,
  transportation_fee_amount: '',
  // Global discount fields
  discountAmount: 0,
  discountType: 'fixed', // "fixed" or "percentage"
  apply_all: false,
})
const discountObject = ref({
  amount: 0,
  type: 'fixed',
})
const processing = ref(false)
const processingBtn = ref(false)
const processingCalculations = ref(false)
const timeSlotProcessing = ref(false)
const startDate = ref<string | null>(null)
const timeSlotsByDay = ref<Record<string, TimeSlot[]>>({})
const metaDataFields = ref([])
const isTypeMenuOpen = ref(false)
const showDiscountSection = ref(false)
const showDiscountPopover = ref(false)

// Replace the static VISIBLE_DAYS constant with a reactive ref
const VISIBLE_DAYS = ref(7)

// Add screen size breakpoints
const SCREEN_BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1991,
}

const updateVisibleDays = () => {
  const width = window.innerWidth
  if (width < SCREEN_BREAKPOINTS.sm)
    VISIBLE_DAYS.value = 1
  else if (width < SCREEN_BREAKPOINTS.md)
    VISIBLE_DAYS.value = 2
  else if (width < SCREEN_BREAKPOINTS.lg)
    VISIBLE_DAYS.value = 3
  else if (width < SCREEN_BREAKPOINTS.xl)
    VISIBLE_DAYS.value = 4
  else
    VISIBLE_DAYS.value = 6
}

// Add resize listener
onMounted(async () => {
  updateVisibleDays()
  window.addEventListener('resize', updateVisibleDays)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateVisibleDays)
})

// Computed Properties
const services = computed(() => formData.items)
const teams = computed(() => getUserInfo.value.teams)

const isSelectedTeamIsHomeService = computed(() => {
  return teams.value.find(team => team.uuid === formData.team_id)?.location === 'home-service'
})
const onlyServices = computed(() => formData.items.filter(item => item.type == 'service'))

const minValueForDiscount = computed(() => {
  return formData.items.filter(item => item.id && item.price && item.quantity).reduce((acc, item) => acc + item.price * item.quantity, 0)
})

// Validation Rules
const rules = {
  team_id: { required },
  start: { required: requiredIf(() => onlyServices.value.length > 0) },
  discountAmount: { required: requiredIf(() => formData.items.filter(item => item.id && item.price && item.quantity).length > 0) },
}

const vFrom$ = useVuelidate(rules, formData)

// Methods
const debouncedGetFreshAppointments = useDebounceFn(
  async date => await fetchSlots({ startDate: date }),
  500,
)

const debouncedCalculateTotals = useDebounceFn(
  async (items) => {
    if (items.length === 0)
      return
    await calculateTotals(items)
  },
  1000,
)

onBeforeMount(async () => {
  formData.items.push({
    staffId: '',
    id: '',
    quantity: 1,
    price: 0,
    unit_amount: 0,
    total_amount: 0,
    key: uniqueId(),
    type: 'service',
  })
  startDate.value = dayjs().format('YYYY/MM/DD')
  formData.team_id = getUserInfo.value.teams[0].uuid
})

const timeSlotsList = ref([])

const fetchSlots = async (options: { nextAvailability?: boolean; startDate?: string } = {}) => {
  try {
    let startDateToFetch: string
    const { nextAvailability = false, startDate } = options
    timeSlotProcessing.value = true
    if (nextAvailability)
      startDateToFetch = dayjs().add(1, 'day').format('YYYY-MM-DD')
    else if (startDate)
      startDateToFetch = startDate
    else
      startDateToFetch = dayjs().format('YYYY-MM-DD')

    const onlyServices = formData.items.filter(item => item.type == 'service')
    const queries = {
      staffIds: onlyServices.map(item => item?.staffId).filter(Boolean),
      itemIds: onlyServices.map(item => item?.id).filter(Boolean),
      durations: onlyServices.map(item => item?.duration).filter(Boolean),
      quantities: onlyServices.map(item => item?.quantity).filter(Boolean),
    }

    // Only fetch if we have valid queries
    if (queries.staffIds.length === 0 || queries.itemIds.length === 0) {
      timeSlotsList.value = []
      return false
    }

    const { data } = await fetchTimeSlots(
      startDateToFetch,
      queries.staffIds,
      queries.itemIds,
      queries.durations,
      queries.quantities,
      VISIBLE_DAYS.value,
    )

    timeSlotsList.value = Object.values(data.data)
    return Object.values(timeSlotsByDay.value).some(slots => slots.length > 0)
  }
  catch (error) {
    console.error('Error fetching time slots:', error)
    timeSlotsByDay.value = {}
    return false
  }
  finally {
    timeSlotProcessing.value = false
  }
}

// Watch for service changes

watch(() => onlyServices.value, async (newItems, oldItems) => {
  // Check if length changed
  if (newItems.length !== oldItems?.length) {
    timeSlotsList.value = []
    return
  }
  const hasValidServices = newItems.every(item => item.id && item.staffId && item.quantity && item.duration)
  if (hasValidServices)
    await debouncedGetFreshAppointments(startDate.value)
  else
    timeSlotsList.value = []
}, { deep: true })

const selectTeam = (team) => {
  formData.team_id = team.uuid
}

const selectStaffAndCustomer = ({ customer_id }) => {
  formData.customer_id = customer_id ?? ''
}

const checkoutData = ref({})

const calculateTotals = async (services) => {
  try {
    processingCalculations.value = true
    const data = {}
    data.items = services.map(item => ({
      applyDiscount: item.applyDiscount || false,
      price: String(item.unit_amount),
      quantity: String(item.quantity),
      staff_id: item.staffId,
      item_id: item.id,
    }))
    data.branch_id = formData.team_id
    data.discount = {
      amount: formData.discountAmount,
      type: formData.discountType,
      apply_all: formData.apply_all,
    }
    const res = await checkoutCalculate(data)
    checkoutData.value.subTotalBeforeTax = res.subTotalBeforeTax
    checkoutData.value.discount = res.discount
    checkoutData.value.totalTaxable = res.totalTaxable
    checkoutData.value.taxes = res.taxes
    checkoutData.value.total = res.total
  }
  finally {
    processingCalculations.value = false
  }
}

watch(
  () => formData.items,
  async (newItems) => {
    // check if all items have price and quantity to recalculate totals
    const shouldRecalculateTotals = newItems.length > 0 && newItems.some(item => item.id && item.price && item.quantity)
    if (shouldRecalculateTotals)
      await debouncedCalculateTotals(newItems.filter(item => item.id && item.price && item.quantity))

    // check if any items not has discount
    const hasItemsWithoutDiscount = newItems.some(item => !item.applyDiscount)
    if (hasItemsWithoutDiscount)
      formData.apply_all = false
  },
  { deep: true },
)

watch(
  () => [formData.discountAmount, formData.discountType, formData.apply_all],
  async (_) => {
    const hasItems = formData.items.length > 0 && formData.items.some(item => item.id && item.price && item.quantity)
    if (hasItems)
      await debouncedCalculateTotals(formData.items.filter(item => item.id && item.price && item.quantity))
  },
  { deep: true },
)

watch(
  () => formData.team_id,
  async (val, oldVal) => {
    if (oldVal && val !== oldVal) {
      formData.items = services.value.map((item) => {
        item.staffId = ''
        item.id = ''
        item.quantity = 1
        item.discount_amount = 0
        item.price = 0
        item.unit_amount = 0
        item.total_amount = 0
        item.key = uniqueId()
        item.duration = '1'
        return item
      })
    }
    const data = await fetchTeamMetaData(val)
    metaDataFields.value = data
  },
)

const updateMetaDataFields = ({
  key,
  value,
}: {
  key: string
  value: unknown
}) => {
  formData[key] = value
}

function removeItem(index: number) {
  formData.items.splice(index, 1)
}

// Save Method
const save = async () => {
  try {
    console.log(vFrom$.value.$errors)
    processingBtn.value = true
    vFrom$.value.$touch()

    // Prepare payload with discount information
    const payload = {
      ...formData,
      services: formData.items.map(item => ({
        ...item,
        applyDiscount: item.applyDiscount || false,
      })),
      items: [],
      start: formData.start ?? dayjs().format('YYYY-MM-DD'),
      // Include global discount fields
      discountAmount: formData.discountAmount,
      discountType: formData.discountType,
      apply_all: formData.apply_all,
    }

    if (vFrom$.value.$invalid)
      return
    await createOrder(payload)
    emit('refresh')
    emit('close')
  }
  catch (error) {
    if (error.error_code === 'STAFF_UNAVAILABLE')
      await debouncedGetFreshAppointments(startDate.value)
  }
  finally {
    processingBtn.value = false
  }
}

const selectType = (type) => {
  formData.items.push({
    type,
    key: uniqueId(),
  })
}

// Add method to apply discount to all items
const applyDiscountToAll = () => {
  formData.apply_all = true
  formData.items.forEach((item, index) => {
    const updatedItem = { ...item, applyDiscount: true }
    // Clear any existing item-specific discount when applying global discount
    if (formData.discountAmount > 0) {
      updatedItem.discount_amount = 0
      updatedItem.showDiscount = false
    }
    formData.items[index] = updatedItem
  })
}

// Add method to clear discount from all items
const clearDiscountFromAll = () => {
  formData.apply_all = false
  formData.items.forEach((item, index) => {
    const updatedItem = { ...item, applyDiscount: false }
    formData.items[index] = updatedItem
  })
}
const applyOrClearDiscountToAll = (val) => {
  if (val)
    applyDiscountToAll()
  else
    clearDiscountFromAll()
}
const updateDiscountObject = async (val) => {
  discountObject.value = val
  formData.discountAmount = val.amount || 0
  formData.discountType = val.type === 'fixed' ? 'fixed' : 'percentage'
  await debouncedCalculateTotals(formData.items.filter(item => item.id && item.price && item.quantity))
}

const popoverRef = ref(null)
// Use onClickOutside for discount popover
onClickOutside(popoverRef, () => {
  showDiscountPopover.value = false
})
</script>

<template>
  <FullScreenModal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="t('modalHeader.create_order')"
    :subtitle="t('modalSubtitle.create_order')"
    :icon="CalendarsIcon"
    panel-classes="w-full  bg-white rounded-xl shadow-[0px_8px_8px_-4px_rgba(10,13,18,0.04)] shadow-[0px_20px_24px_-4px_rgba(10,13,18,0.10)] flex flex-col overflow-visible sm:w-full sm:mx-20 h-full my-20"
    @close="emit('close')"
  >
    <div class="flex flex-col gap-2 p-4">
      <!-- Branch and Customer Section -->

      <div class="flex flex-wrap gap-4 text-start">
        <CustomerAndStaff
          class="flex-1 min-w-[300px]"
          :model-value="formData"
          @update:model-value="selectStaffAndCustomer"
        />
        <div class="flex-1 min-w-[300px]">
          <form-group :validation="vFrom$">
            <template #default="{ attrs }">
              <SelectInput
                id="team_id"
                v-model="formData.team_id"
                v-bind="attrs"
                :label="$t('fields.select_branch')"
                required
              >
                <option
                  v-for="team in teams"
                  :key="team.uuid"
                  :value="team.uuid"
                >
                  {{ team.name }}
                </option>
              </SelectInput>
            </template>
          </form-group>
        </div>
      </div>

      <!-- Divider -->
      <div class="my-6 w-full border-t border-gray-200" />

      <!-- Discount Popover Button -->
      <div class="flex relative justify-end items-center mb-2">
        <LilActionBtn
          :label="formData.discountAmount > 0 ? (formData.discountType === 'fixed' ? (`${formData.discountAmount} ${$t('currency')}`) : (`${formData.discountAmount}%`)) : $t('add_discount')"
          class="relative z-10 mr-1"
          @click="showDiscountPopover = !showDiscountPopover"
        />
        <!-- Popover -->
        <div
          v-if="showDiscountPopover"
          ref="popoverRef"

          data-popover="discount" class="absolute left-0 z-20 p-4 mb-48 w-72 bg-white rounded-lg border border-gray-200 shadow-lg" @click.stop
        >
          <div class="mb-2">
            <DiscountInput
              id="discount_amount_all"
              :model-value="discountObject"
              :disabled="!formData.items.filter(item => item.id && item.price && item.quantity).length"
              :label="$t('discount_amount')"
              :placeholder="discountObject.type === 'fixed' ? $t('enter_discount_amount') : $t('enter_discount_percentage')"
              min="0"
              :max="minValueForDiscount"
              :hint="$t('hints.general_discount_amount')"
              @update:model-value="updateDiscountObject"
            />
          </div>
          <div class="flex gap-2 items-center mb-2">
            <Switch
              id="apply_discount_all"
              v-model="formData.apply_all"
              class="inline-flex relative flex-shrink-0 w-9 h-5 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              :class="formData.apply_all ? 'bg-primary-600' : 'bg-gray-200'"
              @update:model-value="applyOrClearDiscountToAll"
            >
              <span
                aria-hidden="true"
                class="inline-block w-4 h-4 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
                :class="formData.apply_all ? 'translate-x-4 rtl:-translate-x-4' : 'translate-x-0'"
              />
            </Switch>
            <span class="text-xs text-gray-500">{{ $t('apply_discount_to_all_items') }}</span>
          </div>
        </div>
      </div>

      <!-- Services Section -->
      <overlay-loader v-if="processing" :full-screen="false" />
      <div class="flex flex-col gap-4">
        <template v-for="(item, index) in formData.items" :key="item.key">
          <OrderServices v-if="item.type == 'service'" :key="item.key" v-model="formData.items[index]" :team="formData.team_id" :can-remove="formData.items.length > 1" @remove="removeItem(index)" />
          <OrderProducts v-if="item.type == 'product'" :key="item.key" v-model="formData.items[index]" :team="formData.team_id" :can-remove="formData.items.length > 1" @remove="removeItem(index)" />
          <OrderPackage v-if="item.type == 'package'" :key="item.key" v-model="formData.items[index]" :team="formData.team_id" :customer-id="formData.customer_id" :can-remove="formData.items.length > 1" @remove="removeItem(index)" />
        </template>

        <div class="block relative justify-center items-center">
          <button
            type="button"
            class="flex gap-1 items-center text-gray-500 hover:text-blue-400 focus:outline-none disabled:text-gray-300"
            :disabled="processingBtn"
            @click="isTypeMenuOpen = !isTypeMenuOpen"
          >
            <PlusIconBordered class="w-5 h-5 text-blue-400" aria-hidden="true" />
            <span class="text-base">{{ $t("أضف عنصر جديد") }}</span>
          </button>
          <ItemTypeSelection
            v-if="isTypeMenuOpen"
            :is-open="isTypeMenuOpen"
            class="absolute right-0 top-full z-20 mt-2 w-48 bg-white rounded-lg border border-gray-200 shadow-lg"
            @close="isTypeMenuOpen = false"
            @select="selectType"
          />
        </div>
      </div>

      <!-- Divider -->
      <div class="my-6 w-full border-t border-gray-200" />

      <!-- Divider -->

      <!-- Calendar and Summary Section -->
      <div class="grid grid-cols-3 gap-4">
        <!-- Calendar & Time Slots -->
        <div class="w-full  bg-[#F8F8F8] rounded-lg p-6 max-h-[500px] overflow-y-auto  xl:col-span-2 col-span-3">
          <form-group :validation="vFrom$" name="start">
            <template #default="{ attrs }">
              <!-- Add the TimeSlotsGrid component -->
              <TimeSlotsGrid
                :time-slots-list="timeSlotsList"
                :loading="timeSlotProcessing"
                :selected-time="formData.start"
                :visible-days="VISIBLE_DAYS"
                :no-services="onlyServices.length === 0"
                @update-time="($event) => formData.start = $event.fullDate"
                @week-change="fetchSlots({ startDate: $event })"
              />
            </template>
          </form-group>
        </div>

        <!-- Summary Card -->
        <div class="relative col-span-3 w-full h-fit xl:col-span-1">
          <overlay-loader v-if="processingCalculations" :full-screen="false" />
          <OrderSummary :checkout-data="checkoutData" />
        </div>
      </div>
      <div class="my-6 w-full border-t border-gray-200" />
      <!-- Additional Fields Section -->
      <div class="flex flex-col gap-4">
        <OrderMetaDataFields
          v-if="metaDataFields?.length"
          class="text-start"
          :model-value="formData"
          :meta-data-fields="metaDataFields"
          @update:model-value="updateMetaDataFields"
        />

        <!-- Additional Information Disclosure -->
        <div class="px-2  py-4 bg-white rounded-lg outline outline-1 outline-offset-[-1px] outline-gray-200 ">
          <Disclosure v-slot="{ open }" as="div">
            <DisclosureButton
              :class="open ? 'border-b border-gray-200 pb-4' : ''"
              class="flex justify-between items-center w-full cursor-pointer select-none"
            >
              <span class="text-base font-normal text-neutral-800">{{ $t("notes") }}</span>
              <ChevronUpIcon
                :class="open ? 'transform rotate-180' : ''"
                class="w-5 h-5 text-gray-500 transition-transform duration-200"
              />
            </DisclosureButton>
            <DisclosurePanel class="flex flex-col gap-4 pt-4">
              <div v-if="isSelectedTeamIsHomeService" class="sm:col-span-2 text-start">
                <NumberInput
                  id="transportation_fees"
                  v-model="formData.transportation_fee_amount"
                  :label="$t('dashboard.booking.transportation_fee_amount')"
                />
              </div>

              <div class="pt-3 sm:col-span-2 text-start">
                <TextareaInput
                  id="notes"
                  v-model="formData.note"
                  rows="2"
                  :label="$t('booking.notes')"
                />
              </div>
            </DisclosurePanel>
          </Disclosure>
        </div>
      </div>

      <!-- Submit Button -->
      <BaseButton
        class="!px-12 mx-auto mt-6"
        show-icon
        type="button"
        :processing="processingBtn"
        @click="save"
      >
        {{ $t("create_new_order") }}
      </BaseButton>
    </div>
  </FullScreenModal>
</template>

<style lang="scss">
.my-calendar {
  min-width: 280px !important;
  width: 100% !important;
  margin: 0 auto;
  border: none;
  background-color: #f8fafc;
  border-radius: 16px;
  padding: 16px;
  @media (max-width: 768px) {
    min-width: 200px !important;
    padding: 5px;
  }

  & .vc-weeks {
    margin-top: 20px;
  }

  & .vc-arrows-container {
    margin-bottom: 10px;
    .vc-arrow {
      transition: all 0.3s ease;
      background-color: #e2e8f0;
      border-radius: 50%;
      padding: 8px;
      width: 32px !important;
      height: 32px !important;
      color: #334155;
      &:hover {
        background-color: #cbd5e1;
        transform: scale(1.1);
      }
      svg {
        @media (max-width: 768px) {
          width: 24px !important;
          height: 24px !important;
        }

      }
    }
  }

  & .vc-day {
    transition: all 0.3s ease;
    border: none;
    &:not(.is-disabled) {
      background-color: transparent;
      &:hover .vc-day-content {
        transform: scale(1.1);
      }
    }
  }

  & .vc-day-content {
    font-weight: 600;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  & .vc-day.is-today {
    color: black;
    border-radius: 9px !important;
    background-color: #fde68a !important;
  }
}

.my-calendar .is-disabled {
  opacity: 0.2;
  cursor: not-allowed;
  &:hover {
    background-color: #ccc !important;
  }
  & span {
    color: #ccc !important;
    &:hover {
      background-color: #ccc !important;
    }
  }
  &:hover {
    background-color: #ccc !important;
    border-radius: 9px !important;
  }
}
.my-calendar .vc-day:not(.is-disabled) {
  min-height: 40px;
  min-width: 30px !important;
  width: 80% !important;
  border-radius: 9px;
  margin: 0 auto;
  background-color: #e5e5e5;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  border: none;
  @media (max-width: 768px) {
    min-width: 30px !important;
    min-height: 50px !important;
  }

  &:focus {
    background-color: #16689a;
    border-radius: 9px;
  }
  & .vc-highlight {
    background-color: #16689a !important;
    width: 100% !important;
    height: 100% !important;
    border-radius: 9px !important;
    &:hover {
      background-color: #16689a !important;
    }
  }
  & span {
    text-align: center;
    vertical-align: middle;
    color: #000;
    width: 100%;
    display: block;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    @media (max-width: 768px) {
      font-size: 15px;
    }
    &:hover {
      background-color: #16689a;
      border-radius: 9px;
      color: #fff;
    }
    &:focus {
      border-radius: 9px;
      background-color: #16689a;
    }
  }
  &.is-not-in-month {
    visibility: hidden;
  }
}
</style>
