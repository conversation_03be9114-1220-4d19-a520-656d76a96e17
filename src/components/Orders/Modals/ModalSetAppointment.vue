<script setup lang="ts">
import type { PropType } from 'vue'
import useVuelidate from '@vuelidate/core'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { required } from '@/utils/i18n-validators'
import { formatDateAndTime } from '@/composables/dateFormat'
import type { OrderItems, Staff, TimeSlot } from '@/types'
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  selectedItem: {
    type: Object as PropType<OrderItems>,
    default: () => ({}),
    required: false,
  },
  orderId: {
    type: String,
    default: '',
    required: true,
  },
})
const emits = defineEmits(['close', 'refresh'])
const { getLocale } = storeToRefs(useLocalesStore())
const { fetchTimeSlots } = useEventStore()
const { fetchServiceById } = useServicesStore()
const { fetchStaffServices } = useStaffStore()
const { setAppointment } = useOrder()
const { tableData, fetchStaffPage } = useStaff()
const { locale } = useI18n()

const processing = ref(false)
const servicesOptions = ref([])
const editMode = ref(false)
const staffs = ref<Staff[]>([])
const formData = reactive({
  serviceId: '',
  date: new Date().toISOString(),
  staffId: '',
  slot: '',
  duration: '',
  quantity: '',
})
const state = reactive({
  timeSlots: [] as TimeSlot[],
  timeSlotProcessing: false,
  isHoliday: false,
  startDate: '',
  timeSlotsByDay: [] as Record<string, TimeSlot[]>,
})

const rules = {
  staffId: {
    required,
  },
  date: {
    required,
  },
  slot: {
    required,
  },
  serviceId: {
    required,
  },
}
const { selectedItem } = toRefs(props)
const v$ = useVuelidate(rules, formData)

const disableDateSelection = computed(() => {
  return !formData.staffId
})

watch(
  () => selectedItem.value,
  (val) => {
    console.log(val)
    if (val.uuid) {
      editMode.value = true
      formData.staffId = val?.provider?.uuid || ''
      formData.serviceId = val?.uuid || ''
      formData.duration = val?.service_duration || ''
      formData.quantity = val?.quantity || ''
      fetchServiceById(formData.serviceId).then((res) => {
        staffs.value = res.staff
      })
    }
    else {
      fetchStaffPage().then(() => {
        staffs.value = tableData.staffList.map((staff) => {
          return {
            ...staff,
            id: staff.uuid,
            name: staff.name,
          }
        })
      })
    }
  },
  { immediate: true },
)

const VISIBLE_DAYS = ref(2)
const SCREEN_BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1991,
}
const updateVisibleDays = () => {
  const width = window.innerWidth
  if (width < SCREEN_BREAKPOINTS.sm)
    VISIBLE_DAYS.value = 1
  else
    VISIBLE_DAYS.value = 2
}
onMounted(() => {
  updateVisibleDays()
  window.addEventListener('resize', updateVisibleDays)
})
onUnmounted(() => {
  window.removeEventListener('resize', updateVisibleDays)
})

const fetchSlots = async (options = {}, staffId, serviceId, duration, quantity) => {
  try {
    let startDateToFetch: string
    const { nextAvailability = false, startDate } = options
    state.timeSlotProcessing = true
    if (nextAvailability)
      startDateToFetch = dayjs().add(1, 'day').format('YYYY-MM-DD')
    else if (startDate)
      startDateToFetch = startDate
    else
      startDateToFetch = dayjs().format('YYYY-MM-DD')

    console.log(staffId, serviceId, duration, quantity)

    const queries = {
      staffIds: [staffId || formData.staffId],
      itemIds: [serviceId || formData.serviceId],
      durations: [duration || formData.duration],
      quantities: [quantity || formData.quantity],
    }

    const { data } = await fetchTimeSlots(
      startDateToFetch,
      queries.staffIds,
      queries.itemIds,
      queries.durations,
      queries.quantities,
      VISIBLE_DAYS.value,
    )
    state.timeSlotsByDay = Object.values(data.data)
    return Object.values(state.timeSlotsByDay).some(slots => slots.length > 0)
  }
  catch (error) {
    console.error('Error fetching time slots:', error)
    state.timeSlotsByDay = []
    return false
  }
  finally {
    state.timeSlotProcessing = false
  }
}

watch(
  () => [formData.date, formData.staffId, formData.serviceId, formData.duration, formData.quantity],
  (val) => {
    const [date, staffId, serviceId, duration, quantity] = val

    if (date && staffId && serviceId) {
      console.log(dayjs(date).format('YYYY-MM-DD'))
      fetchSlots({ startDate: dayjs(date).format('YYYY-MM-DD') }, staffId, serviceId, duration, quantity)
    }
    else {
      state.timeSlotsByDay = []
    }
  },
  { immediate: true },
)

function setBookingTimeSlot(slot: TimeSlot) {
  state.startDate = slot.fullDate
  formData.slot = slot.fullDate
}

async function setItemToAppointment() {
  v$.value.$touch()
  if (v$.value.$invalid)
    return
  processing.value = true
  try {
    const paylaod = {
      ...formData,
      date: dayjs(formData.slot).format('YYYY/MM/DD').toString(),
      slot: dayjs(formData.slot).format('HH:mm').toString(),
      item_id: selectedItem.value?.item_id || '',
      model_type: selectedItem.value?.model_type || 'App\\Models\\Order',
      model_id: selectedItem.value?.model_id || props.orderId,
    }
    await setAppointment(props.orderId, paylaod)
    emits('refresh')
    emits('close')
  }
  catch (error) {
    if (error.error_code == 'STAFF_UNAVAILABLE')
      fetchSlots({ startDate: dayjs(formData.date).format('YYYY-MM-DD') }, formData.staffId, formData.serviceId, formData.duration, formData.quantity)
  }
  finally {
    processing.value = false
  }
}

function fetchService(staffId: string) {
  if (editMode.value)
    return
  fetchStaffServices(staffId).then(({ services }) => {
    servicesOptions.value = services.map((service) => {
      return {
        ...service,
        value: service.uuid,
        label: service.name,
      }
    })
  })
}
function selectService(uuid: string) {
  const selectedService = servicesOptions.value.find(
    service => service.uuid === uuid,
  )
  if (!selectedService)
    return
  formData.serviceId = selectedService?.uuid || ''
}

const showBookedSlots = ref(false)
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="editMode ? 'set_appointment' : 'new_appointment'"
    :param="selectedItem?.name"
    panel-classes="w-full max-w-2xl transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all  sm:w-2xl md:px-8 lg:px-8 "
    @close="emits('close')"
  >
    <overlay-loader v-if="processing" :full-screen="processing" />
    <form class="text-start" @submit.prevent="setItemToAppointment()">
      <div class="grid grid-cols-1 gap-4 gap-y-6">
        <div class="w-full">
          <form-group :validation="v$" name="staffId">
            <template #default="{ attrs }">
              <SelectInput
                v-bind="attrs"
                id="staff_id"
                v-model="formData.staffId"
                :label="$t('form.staff_users')"
                @change="fetchService($event.target.value)"
              >
                <option
                  v-for="team in staffs"
                  :key="team?.id"
                  :value="team?.id"
                >
                  {{ team.name }}
                </option>
              </SelectInput>
            </template>
          </form-group>
        </div>
        <div v-if="!editMode" class="w-full">
          <form-group :validation="v$" name="serviceId">
            <template #default="{ attrs }">
              <BaseComboBox
                id="service_id"
                :model-value="formData.serviceId"
                place-holder="serviceName"
                required
                arial-label="Search"
                :disabled="!servicesOptions.length"
                :error="v$.$dirty && v$.serviceId.$error"
                :options="servicesOptions"
                class="block w-full text-gray-700 border-gray-300 w-100 focus:border-primary-500 focus:ring-primary-500"
                v-bind="attrs"
                @update:model-value="selectService"
              >
                {{ $t("booking.service") }}
              </BaseComboBox>
            </template>
          </form-group>
        </div>

        <div class="w-full  bg-[#F8F8F8] rounded-lg p-6 ">
          <form-group :validation="v$" name="slot">
            <template #default="{ attrs }">
              <TimeSlotsGrid
                v-bind="attrs"
                :time-slots-list="state.timeSlotsByDay"
                :loading="state.timeSlotProcessing"
                :selected-time="state.startDate"
                :show-booked="showBookedSlots"
                :visible-days="VISIBLE_DAYS"
                @update-time="setBookingTimeSlot"
                @week-change="fetchSlots({ startDate: $event })"
              />
            </template>
          </form-group>
        </div>
        <div class="flex justify-center w-full">
          <BaseButton
            :processing="processing"
            class="w-full"
          >
            {{ $t("set_an_appointment") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </Modal>
</template>
