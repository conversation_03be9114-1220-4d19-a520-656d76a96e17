<script lang="ts" setup>
import type { ComputedRef, PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import type { OrderItems, header } from '@/types'
// const { getPaymentMethodsList } = usePaymentMethodsStore()
import posServices from '@/services/posServices'
const props = defineProps({
  items: {
    type: Array as PropType<OrderItems[]>,
    required: true,
  },
  order_id: {
    type: String,
    required: true,
  },
  isOpen: {
    type: Boolean,
    required: true,
  },
})
const emit = defineEmits(['close'])
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
// const { useAuthStore } = useOrder();
const payment_methods = ref([])
const user = useAuthStore()
const { t } = useI18n()
const { refundOrder } = useOrder()

const processingbtn = ref(false)

const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.name'),
    },
    {
      title: t('quantity'),
    },
    {
      title: t('refund_amount'),
    },

    {
      title: t('total'),
    },
  ]
})
const formData = reactive({
  order_id: props.order_id,
  services: [],
  payment_method: '',
})
const processing = ref(false)
onBeforeMount(() => {
  processing.value = true
  props.items.forEach((item) => {
    formData.services.push({
      id: item.id,
      sale_service_id: item.sale_service,
      quantity: item.quantity,
      unit_amount: item.unit_amount,
    })
  })

  if (formData.services.length) {
    // payment_methods.value = await getPa/ymentMethodsList();
    posServices.getPaymentMethods().then((res) => {
      payment_methods.value = [...res.data.data]
      formData.payment_method = payment_methods.value[0].id
    })
  }
  processing.value = false
})
const total_amount = computed(() => {
  return formData.services.reduce((acc, item) => {
    return acc + item.unit_amount * item.quantity
  }, 0)
})
const RefundOrder = async () => {
  formData.order_id = props.order_id
  const refunded_order = await refundOrder(props.order_id, formData)
  emit('close', refunded_order)
}
function getServicesWithSameId(item) {
  return formData.services.filter(service => service.id === item.id)
}
</script>

<template>
  <div class="relative flex flex-col">
    <Modal
      :dir="getLocale(locale)?.direction" :open="isOpen" title="create_Refund" panel-classes="w-full max-w-5xl transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all  sm:w-2xl md:px-8 lg:px-8 "
      @close="emit('close')"
    >
      <overlay-loader v-if="processing" :full-screen="true" />
      <div class="mx-3 my-3 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="inline-block min-w-full pb-2 align-middle md:px-6 lg:px-8">
          <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <generic-table v-if="formData.services.length > 0" :headers="headers" :data="items" item-key="uuid">
              <template #row="{ item }">
                <grid-td class="flex items-center gap-1 py-2 pl-2 pr-2 text-sm whitespace-nowrap sm:pl-6" :default-style="false">
                  <div class="flex items-center justify-start gap-2">
                    <div class="flex flex-col items-start justify-center gap-2">
                      <span>{{ item?.name }}</span>
                    </div>
                  </div>
                </grid-td>
                <grid-td>
                  <div class="text-gray-900">
                    <span v-if="item?.quantity">{{ item?.quantity }}</span>
                    <span v-else class="text-zinc-400"> - </span>
                  </div>
                </grid-td>
                <grid-td :default-style="false" class="flex items-center gap-1 py-1 pl-0 pr-0 text-sm whitespace-nowrap sm:pl-0">
                  <div class="w-1/2 text-gray-900">
                    <form-group v-if="item?.quantity" name="quantity" error-name="quantity">
                      <template #default="{ attrs }">
                        <NumberInput v-bind="attrs" id="quantity" v-model="getServicesWithSameId(item)[0].quantity" :max="item?.quantity" :min="0" custom-classes="disabled:bg-gray-200" />
                      </template>
                    </form-group>
                    <div v-else class="text-center">
                      0
                    </div>
                  </div>
                </grid-td>
                <grid-td>
                  <div v-if="item?.quantity" class="text-gray-900">
                    {{ getServicesWithSameId(item)[0].quantity * getServicesWithSameId(item)[0].unit_amount }}
                  </div>
                  <div v-else class="text-gray-900">
                    0
                  </div>
                </grid-td>
              </template>
            </generic-table>

            <div v-if="formData.services.length > 0" class="flex items-center justify-end gap-4">
              <grid>
                <tr>
                  <grid-th class="text-center">
                    {{ $t('total_amount') }}
                  </grid-th>
                  <grid-th class="text-center">
                    <price-format
                      :form-data="{
                        price: total_amount,
                        currency: user.tenant.currency || '',
                      }"
                    />
                  </grid-th>
                </tr>
              </grid>
            </div>

            <div v-else>
              <span class="font-medium">{{ $t("no_refundable_items") }}</span>
            </div>
          </div>

          <div v-if="formData.services.length > 0" class="py-2">
            <label for="paymentMethod" class="flex py-2 text-sm font-medium text-gray-700">
              {{ $t('refund') }} &nbsp;  <price-format
                :form-data="{
                  price: total_amount,
                  currency: user.tenant.currency || '',
                }"
              /> &nbsp; {{ $t('by') }}
            </label>
            <div class="flex w-1/3 my-2">
              <SelectInput
                id="paymentMethod"
                v-model="formData.payment_method"
                class="block w-1/2 border-gray-300 rounded-md   focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
              >
                <!-- <option value="" selected>{{ $t("form.all") }}</option> -->
                <option v-for="method in payment_methods" :key="method.slug" :value="method.id">
                  {{ method.name }}
                </option>
              </SelectInput>
            </div>
          </div>
          <div class="flex items-center justify-center w-full rounded-md shadow-sm sm:w-auto gap-y-8 ">
            <BaseButton
              class="flex items-center justify-center col-span-4 px-4 py-2 mt-4 text-sm font-medium text-white transition duration-100 ease-in rounded-md cursor-pointer md:col-span-2 focus:transform active:scale-95"
              custome-bg="bg-red-600" :disabled="formData.services.length > 0 ? false : true" show-icon type="button"
              :processing="processingbtn" @click="RefundOrder()"
            >
              {{ $t("refund_order") }}
            </BaseButton>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>
