<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import ServicesIcon from '@/components/Icons/ServicesIcon.vue'
import ProductsIcon from '@/components/Icons/ProductsIcon.vue'
import PackagesIcon from '@/components/Icons/PackagesIcon.vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['close', 'select'])

const { t } = useI18n()

const itemTypes = [
  { id: 'service', name: 'Service', icon: ServicesIcon },
  { id: 'product', name: 'Product', icon: ProductsIcon },
  { id: 'package', name: 'Package', icon: PackagesIcon },
]

const selectType = (type) => {
  emit('select', type)
  emit('close')
}
</script>

<template>
  <Transition
    enter-active-class="transition duration-100 ease-out"
    enter-from-class="opacity-0 transform scale-95"
    enter-to-class="opacity-100 transform scale-100"
    leave-active-class="transition duration-75 ease-in"
    leave-from-class="opacity-100 transform scale-100"
    leave-to-class="opacity-0 transform scale-95"
  >
    <div v-if="isOpen" class="py-2 mt-1 divide-y divide-gray-100">
      <div class="py-1">
        <div
          v-for="type in itemTypes"
          :key="type.id"
          class="flex px-4 py-2 text-sm text-gray-700 rounded-md cursor-pointer align-right hover:bg-gray-100 hover:text-gray-900"
          @click="selectType(type.id)"
        >
          <component :is="type.icon" class="inline-block align-middle me-2" />
          {{ $t(type.name.toLowerCase()) }}
        </div>
      </div>
    </div>
  </Transition>
</template>

<style scoped>
.absolute {
  transform-origin: top right;
}
</style>
