<script setup lang="ts">
import { storeToRefs } from 'pinia'
import type { PropType } from 'vue'
import { useVuelidate } from '@vuelidate/core'
import { LinkIcon } from '@heroicons/vue/24/outline'
import { required } from '@/utils/i18n-validators'
import TextInput from '@/components/FormControls/Inputs/TextInput.vue'
import NumberInput from '@/components/FormControls/Inputs/NumberInput.vue'
import type { MetaData } from '@/types'
import i18n from '@/i18n'
import DateInput from '@/components/FormControls/Inputs/DateInput.vue'

const props = defineProps({
  modelValue: {
    type: Object as PropType<Record<string, unknown>>,
    default: () => ({}),
  },
  metaDataFields: {
    type: Array as PropType<MetaData[]>,
    default: () => [],
  },
})

const emit = defineEmits(['update:modelValue'])

const { getUserInfo } = storeToRefs(useAuthStore())

const fieldsStore = useMetaDataStore()
const { getMetaData } = storeToRefs(fieldsStore)
const availableMetaDataFields = computed(() => {
  const fields = props.metaDataFields || getMetaData.value || []
  return fields
})
const rules = computed(() => {
  return availableMetaDataFields.value?.reduce<{ [key: string]: unknown }>(
    (rulesValue, field) => {
      if (field.is_required) {
        rulesValue[field.backend_name] = {
          required,
        }
      }
      return rulesValue
    },
    {},
  )
})
const v$ = useVuelidate(rules, props.modelValue)
const updateValue = (data: { key: string; value: unknown }) => {
  emit('update:modelValue', data)
}
const handleHtmlChange = (e: unknown, key: string) => {
  const value = e?.target?.value || e
  updateValue({
    key,
    value,
  })
}
</script>

<template>
  <div v-if="availableMetaDataFields && availableMetaDataFields.length">
    <h2 class="text-base font-bold text-gray-900 mb-4">
      {{ $t("additional_info") }}
    </h2>
    <!-- File fields row at the top -->
    <div v-if="availableMetaDataFields.some(f => f.htmlTag === 'file')" class="grid grid-cols-1 gap-5 mb-4">
      <template v-for="(metaDataField, index) in availableMetaDataFields.filter(f => f.htmlTag === 'file')" :key="`file-${index}`">
        <div class="flex flex-col gap-1">
          <a v-if="typeof modelValue[metaDataField.backend_name] === 'string'" :href="modelValue[metaDataField.backend_name]" target="_blank" class="mb-1">
            <LinkIcon class="w-4 h-4 text-blue-500 inline-block align-middle" />
            <span class="text-xs text-blue-500 ms-1">{{ $t('عرض الملف') }}</span>
          </a>
          <SmallUploadInput
            :id="metaDataField.name"
            :name="metaDataField.name"
            :label="metaDataField.label"
            :required="metaDataField.is_required"
            :default-link="metaDataField?.path"
            @update:model-value="($event) => handleHtmlChange($event, metaDataField.backend_name)"
          />
        </div>
      </template>
    </div>
    <!-- Main 2-col grid for other fields -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-5 text-start">
      <!-- Text/Number fields -->
      <template v-for="(metaDataField, index) in availableMetaDataFields.filter(f => f.htmlTag === 'text' || f.htmlTag === 'number')" :key="`textnum-${index}`">
        <div class="flex flex-col gap-1">
          <Component
            :is="metaDataField.htmlTag === 'text' ? TextInput : NumberInput"
            :id="metaDataField.name"
            :value="props.modelValue[metaDataField.backend_name]"
            :name="metaDataField.name"
            :label="metaDataField.label"
            :required="metaDataField.is_required"
            :class="{ 'error-input': v$[metaDataField.backend_name]?.$errors.length }"
            :placeholder="`${metaDataField.label}...`"
            @update:model-value="($event) => handleHtmlChange($event, metaDataField.backend_name)"
          />
          <p v-for="error of v$[metaDataField.backend_name]?.$errors" :key="error.$uid" class="error-message mt-1">
            <span v-if="error.$validator == 'required'">{{ $t("validations.required", { property: metaDataField.label }) }}</span>
            <span v-else>{{ error.$message }}</span>
          </p>
        </div>
      </template>
      <!-- Select fields -->
      <template v-for="(metaDataField, index) in availableMetaDataFields.filter(f => f.htmlTag === 'select')" :key="`select-${index}`">
        <div class="flex flex-col gap-1">
          <SelectInput
            :id="metaDataField.name"
            v-model="props.modelValue[metaDataField.backend_name]"
            :required="metaDataField.is_required"
            :class="{ 'error-input': v$[metaDataField.backend_name]?.$errors.length }"
            :name="metaDataField.name"
            :label="metaDataField.label"
            @change="($event) => handleHtmlChange($event, metaDataField.backend_name)"
          >
            <option v-for="(value, key) in metaDataField.values" :key="key" :value="value">
              {{ value }}
            </option>
          </SelectInput>
          <p v-for="error of v$[metaDataField.backend_name]?.$errors" :key="error.$uid" class="error-message mt-1">
            <span v-if="error.$validator == 'required'">{{ $t("validations.required", { property: metaDataField.label }) }}</span>
            <span v-else>{{ error.$message }}</span>
          </p>
        </div>
      </template>
      <!-- Date fields -->
      <template v-for="(metaDataField, index) in availableMetaDataFields.filter(f => f.htmlTag === 'date')" :key="`date-${index}`">
        <div class="flex flex-col gap-1">
          <DateInput
            :id="metaDataField.name"
            v-model="props.modelValue[metaDataField.backend_name]"
            :label="metaDataField.label"
            :hint="metaDataField.hint || ''"
            :required="metaDataField.is_required"
            :min="metaDataField.min || ''"
            :max="metaDataField.max || ''"
            :error="v$[metaDataField.backend_name]?.$errors[0]?.$message"
            @update:model-value="val => handleHtmlChange({ target: { value: val } }, metaDataField.backend_name)"
          />
          <p v-for="error of v$[metaDataField.backend_name]?.$errors" :key="error.$uid" class="error-message mt-1">
            <span v-if="error.$validator == 'required'">{{ $t('validations.required', { property: metaDataField.label }) }}</span>
            <span v-else>{{ error.$message }}</span>
          </p>
        </div>
      </template>
      <!-- Textarea fields (2-col grid like others) -->
      <template v-for="(metaDataField, index) in availableMetaDataFields.filter(f => f.htmlTag === 'textarea')" :key="`textarea-${index}`">
        <div class="flex flex-col gap-1">
          <TextareaInput
            :id="metaDataField.name"
            v-model="props.modelValue[metaDataField.backend_name]"
            :name="metaDataField.name"
            :label="metaDataField.label"
            :required="metaDataField.is_required"
            rows="1"
            @change="($event) => handleHtmlChange($event, metaDataField.backend_name)"
          />
          <p v-for="error of v$[metaDataField.backend_name]?.$errors" :key="error.$uid" class="error-message mt-1">
            <span v-if="error.$validator == 'required'">{{ $t("validations.required", { property: metaDataField.label }) }}</span>
            <span v-else>{{ error.$message }}</span>
          </p>
        </div>
      </template>
    </div>
  </div>
</template>
