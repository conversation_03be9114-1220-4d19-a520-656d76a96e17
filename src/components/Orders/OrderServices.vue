<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { ChevronUpIcon, PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'
import type { PropType } from 'vue'
import { uniqueId } from 'lodash'
import { useVuelidate } from '@vuelidate/core'
import { Disclosure, DisclosureButton, DisclosurePanel, Switch } from '@headlessui/vue'
import type { BookingService, Staff } from '@/types'
import {
  maxValue,
  minValue,
  required,
  requiredIf,
} from '@/utils/i18n-validators'
const props = defineProps({
  modelValue: {
    type: Object as PropType<BookingService>,
    default: () => ({
      id: '',
      quantity: 1,
      price: 0,
      unit_amount: 0,
      total_amount: 0,
      staffId: '',
      duration: 1,
      type: 'service',
      key: '',
      applyDiscount: false,
    }),
  },
  team: {
    type: String as PropType<string>,
    default: '',
  },
  canRemove: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue', 'remove'])

const { modelValue } = toRefs(props)

const maxDiscount = computed(() => {
  return modelValue.value.unit_amount * modelValue.value.quantity
})

const rules = {
  id: {
    required,
  },
  staffId: {
    required,
  },
  price: {
    required,
    minValue: minValue(0),
  },
  quantity: {
    required,
    minValue: minValue(1),
  },
  duration: {
    required,
    minValue: minValue(1),
  },

  total_amount: {
    required,
    minValue: minValue(0),
  },
}

const v$ = useVuelidate(rules, modelValue)

const updateValue = (value: BookingService) => {
  emit('update:modelValue', value)
}

// Add method to handle apply discount checkbox
const handleApplyDiscount = (checked: boolean) => {
  console.log(checked)
  updateValue({
    ...modelValue.value,
    applyDiscount: checked,
    // Clear individual discount when applying global discount
  })
}

const items = computed(() => {
  return props.modelValue
})
const removeService = (index: number) => {
  const servicesToUpdate = [...props.modelValue]
  servicesToUpdate.splice(index, 1)
  updateService(servicesToUpdate)
}
const changedService = (value: BookingService, index: number) => {
  const servicesToUpdate = [...props.modelValue]
  servicesToUpdate[index] = value
  updateService(servicesToUpdate)
}
const staffList = ref<Staff[]>([])
const { fetchStaff } = useStaffStore()

const processing = ref(false)
watch(
  () => props.team,
  async (val) => {
    if (val) {
      const pageNo = 1
      try {
        processing.value = true
        const res = await fetchStaff(pageNo, { team: props.team, perPage: 100 })
        staffList.value = res.data
      }
      finally {
        processing.value = false
      }
    }
  },
  { immediate: true },
)

const staffsOptions = computed(() => {
  return staffList.value.map((staff) => {
    return {
      ...staff,
      id: staff.uuid,
      name: staff.name,
    }
  })
})
const selectStaff = (uuid: string) => {
  updateValue({
    ...modelValue.value,
    staffId: uuid,
    id: '',
  })
}
const { fetchStaffServices } = useStaffStore()
const servicesOptions = ref([])
const staffs = ref<Staff[]>([])

const selectService = (uuid: string) => {
  const selectedService = servicesOptions.value.find(
    service => service.uuid === uuid,
  )

  if (selectedService?.uuid == props.modelValue.id)
    return
  updateValue({
    ...props.modelValue,
    id: selectedService?.uuid ?? '',
    quantity: selectedService?.quantity || 1,
    discount_amount: selectedService?.discount_amount || 0,
    price: selectedService?.price || 0,
    unit_amount: selectedService?.price || 0,
    duration: selectedService?.duration || 1,
    total_amount:
      (selectedService?.price || 0) * (selectedService?.quantity || 1) || 0,
  })
}

watch(
  () => props.modelValue?.staffId,
  (val, oldVal) => {
    if (val == oldVal)
      return
    if (val) {
      processing.value = true
      fetchStaffServices(val, '*')
        .then((data) => {
          servicesOptions.value = data.data
            .filter(service => service.canServe)
            .map((service) => {
              return {
                ...service,
                value: service.uuid,
                label: service.name,
              }
            })
          if (props.modelValue.id) {
            const selectedService = servicesOptions.value.find(
              service => service.uuid === props.modelValue.id,
            )
            if (selectedService.uuid)
              changeDuration(selectedService.duration)
          }
        })
        .finally(() => {
          processing.value = false
        })
    }
    else {
      servicesOptions.value = []
    }
  },
  { immediate: true, deep: true },
)

const changeQuantity = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...props.modelValue,
    quantity: +event.target.value,
    total_amount:
      +event.target.value * (props.modelValue?.unit_amount || 0)
        - props.modelValue?.discount_amount || 0,
  })
}
const changePricePerUnit = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...props.modelValue,
    unit_amount: +event.target.value,
    total_amount:
      +event.target.value * (props.modelValue?.quantity || 0)
        - props.modelValue?.discount_amount || 0,
  })
}
const changePricePerDiscount = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  let total = 0
  total = (props.modelValue?.unit_amount || 0) * (props.modelValue?.quantity || 0)
  updateValue({
    ...props.modelValue,
    total_amount: total,
  })
}
const changeDuration = (mins: number) => {
  updateValue({
    ...props.modelValue,
    duration: mins,
  })
}
</script>

<template>
  <div class="space-y-4">
    <div v-for="(service, index) in [modelValue]" :key="service.id || index" class="relative p-4 bg-white rounded-lg border shadow-sm">
      <Disclosure v-slot="{ open }" as="div" default-open>
        <DisclosureButton

          :class="open ? 'border-b border-gray-200 pb-4' : ''"
          class="flex justify-between items-center w-full cursor-pointer select-none"
        >
          <div class="flex gap-2 justify-between items-center w-full">
            <span class="text-base font-normal text-neutral-800">{{ $t("service") }} </span>
            <div class="flex gap-2 justify-end items-center">
              <span class="flex gap-2 items-center">
                <Switch
                  :id="`discount-applying-${modelValue.key}`"
                  v-model="modelValue.applyDiscount"
                  class="inline-flex relative flex-shrink-0 mt-1 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  :class="[
                    modelValue.applyDiscount
                      ? 'bg-primary-600'
                      : 'bg-gray-200',
                  ]"
                >
                  <span
                    aria-hidden="true"
                    class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
                    :class="[
                      modelValue.applyDiscount
                        ? 'translate-x-5 rtl:-translate-x-5'
                        : 'translate-x-0',
                    ]"
                  />
                </Switch>
                <span class="text-sm text-gray-700">  {{ $t('apply_discount') }} </span>

              </span>
              <button
                v-if="canRemove"
                type="button"
                class="flex justify-center items-center w-9 h-9 text-red-500 bg-white rounded-full border border-gray-200 shadow-sm transition hover:bg-red-50 hover:text-red-700"
                aria-label="حذف الخدمة"
                :title="$t('deleteService')"
                @click.stop.prevent="$emit('remove')"
              >
                <TrashIcon class="w-5 h-5" />
              </button>
              <ChevronUpIcon
                :class="open ? 'transform rotate-180' : ''"
                class="w-5 h-5 text-gray-500 transition-transform duration-200"
              />
              <!--  -->
            </div>
          </div>
        </DisclosureButton>
        <!-- Trash icon absolute top-left -->

        <DisclosurePanel>
          <div>
            <!-- Apply Discount Checkbox -->
            <!-- <div class="flex absolute top-2 right-2 z-10 gap-2 items-center px-2 py-1 bg-white rounded border border-gray-200 shadow-sm"> -->
            <!-- <input
          type="checkbox"
          :id="`apply-discount-${modelValue.key}`"
          v-model="modelValue.applyDiscount"
          @change="handleApplyDiscount($event.target.checked)"
          class="w-4 h-4 text-blue-600 bg-gray-100 rounded border-gray-300 focus:ring-blue-500"
        />

      </div> -->

            <div class="grid overflow-visible gap-2.5 items-start grid-cols-1 sm:[grid-template-columns:repeat(auto-fit,minmax(180px,1fr))]">
              <!-- Staff select -->
              <div class="col-span-1 w-full min-w-0 text-start">
                <form-group :validation="v$" name="staffId">
                  <template #default="{ attrs }">
                    <SelectInput
                      v-bind="attrs"
                      id="staff_id"
                      :model-value="modelValue.staffId"
                      :label="$t('form.staff')"
                      required
                      @update:model-value="selectStaff($event)"
                    >
                      <option
                        v-for="staff in staffsOptions"
                        :key="staff?.id"
                        :value="staff?.id"
                      >
                        {{ staff.name }}
                      </option>
                    </SelectInput>
                  </template>
                </form-group>
              </div>
              <!-- Service select -->
              <div class="col-span-1 w-full min-w-0 text-start">
                <form-group :validation="v$" name="id">
                  <template #default="{ attrs }">
                    <BaseComboBox
                      v-bind="attrs"
                      :model-value="modelValue.id"
                      :id="`service-${name}`"
                      place-holder="serviceName"
                      required
                      arial-label="Search"
                      :disabled="!servicesOptions.length"
                      :error="v$.$dirty && v$.id.$error"
                      :options="servicesOptions"
                      :label="$t('form.service')"
                      class="w-full min-w-[120px]"
                      @update:model-value="selectService($event)"
                    >
                      {{ $t("booking.service") }}
                    </BaseComboBox>
                  </template>
                </form-group>
                <p v-if="!servicesOptions.length" class="error-message">
                  {{ $t("booking.no_service") }}
                </p>
              </div>
              <!-- Duration -->
              <div class="col-span-1 w-full min-w-0 text-start">
                <form-group
                  :validation="v$"
                  name="duration"
                  :error-name="`items.${index}.duration`"
                >
                  <template #default="{ attrs }">
                    <div class="flex flex-wrap gap-2 w-full min-w-0">
                      <DurationInput
                        v-bind="attrs"
                        id="duration-input"
                        v-model="modelValue.duration"
                        :label="$t('service_duration')"
                        :required="true"
                        class="w-full min-w-[100px]"
                        @update:model-value="changeDuration"
                      />
                    </div>
                  </template>
                </form-group>
              </div>
              <!-- Quantity -->
              <div class="col-span-1 w-full min-w-0 text-start">
                <form-group
                  :validation="v$"
                  name="quantity"
                  :error-name="`items.${index}.quantity`"
                >
                  <template #default="{ attrs }">
                    <NumberInput
                      v-bind="attrs"
                      :id="`quantity-${name}`"
                      v-model="modelValue.quantity"
                      :label="$t('quantity')"
                      min="1"
                      :placeholder="$t('quantity')"
                      @change="changeQuantity"
                    />
                  </template>
                </form-group>
              </div>
              <!-- Price -->
              <div class="col-span-1 w-full min-w-0 text-start">
                <form-group
                  :validation="v$"
                  name="price"
                  :error-name="`services.${index}.unit_amount`"
                >
                  <template #default="{ attrs }">
                    <NumberInput
                      v-bind="attrs"
                      :id="`pricePerUnit-${name}`"
                      v-model="modelValue.price"
                      :label="$t('price')"
                      :hint="$t('pricePerUnit')"
                      min="1"
                      :placeholder="$t('pricePerUnit')"
                      @change="changePricePerUnit"
                    />
                  </template>
                </form-group>
              </div>

              <!-- Total -->
              <div class="col-span-1 w-full min-w-0 text-start">
                <form-group
                  :validation="v$"
                  name="total_amount"
                  :error-name="`services.${index}.total_amount`"
                >
                  <template #default="{ attrs }">
                    <NumberInput
                      v-bind="attrs"
                      :id="`servicePrice-${name}`"
                      :key="`servicePrice-${name}`"
                      v-model="modelValue.total_amount"
                      readonly
                      :label="$t('total')"
                      @blur="attrs.onblur"
                    />
                  </template>
                </form-group>
              </div>
            </div>
          </div>
        </DisclosurePanel>
      </Disclosure>
    </div>
  </div>
</template>
