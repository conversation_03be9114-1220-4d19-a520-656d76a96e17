<script setup lang="ts">
import { storeToRefs } from 'pinia'
import type { PropType } from 'vue'
import { ref, watch } from 'vue'
import type { Order } from '@/types'
import { useOrder } from '@/stores/order'
import { useLocalesStore } from '@/stores/locales'

const props = defineProps({
  order: {
    type: Object as PropType<Order>,
    required: true,
  },
})

const emit = defineEmits(['refresh'])
const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const orderStore = useOrder()
const { orderStatuses, fetchStatus } = orderStore

const showStatusDropdown = ref(false)
const showChangeStatusModal = ref(false)
const status = ref('')
const removedStatus = [
  'failed',
  'waiting-for-payment',
  'waiting-for-bank-transfer-confirmation',
  'fully-refunded',
  'partial-refund',
  'canceled',
  'pending',
]

function handleClickOutside(event) {
  const dropdown = document.getElementById('order-status-dropdown')
  if (dropdown && !dropdown.contains(event.target))
    showStatusDropdown.value = false
}

watch(showStatusDropdown, (val) => {
  if (val)
    document.addEventListener('mousedown', handleClickOutside)
  else
    document.removeEventListener('mousedown', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside)
})

function openChangeStatusModal(statusObject) {
  showChangeStatusModal.value = true
  showStatusDropdown.value = false
  status.value = statusObject.status
}

function handleStatusChange() {
  emit('refresh')
  showChangeStatusModal.value = false
}
onMounted(async () => {
  console.log(orderStatuses.value)
  if (!orderStatuses.value || Object.keys(orderStatuses.value)?.length === 0) {
    console.log('fetching status')

    await fetchStatus()
  }
})
</script>

<template>
  <div class="flex gap-2 items-center">
    <span class="text-sm font-normal text-neutral-500">{{ $t('ord_details.order_stat') }}</span>
    <div class="relative">
      <button
        class="flex items-center gap-2 px-3 py-1.5 rounded-xl text-sm border-none focus:outline-none min-w-[120px] justify-center focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        :style="{
          'background-color': order.status?.bg_color ?? '#000000',
          'color': order.status?.text_color ?? '#000000',
        }"
        @click="showStatusDropdown = !showStatusDropdown"
      >
        <span>{{ order.status?.label }}</span>
        <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <div v-if="showStatusDropdown" id="order-status-dropdown" class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-xl border border-gray-100 shadow-lg">
        <ul class="py-1">
          <li v-for="status in Object.values(orderStatuses).filter(s => !removedStatus.includes(s.status))" :key="status.status">
            <button
              class="px-4 py-2.5 w-full text-sm text-start hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
              @click="openChangeStatusModal(status)"
            >
              {{ status.label }}
            </button>
          </li>
        </ul>
      </div>
    </div>
    <ModalChangeOrderStatus
      v-if="showChangeStatusModal"
      :show-modal="showChangeStatusModal"
      :order="order"
      :status="status"
      @close="showChangeStatusModal = false"
      @refresh="handleStatusChange"
    />
  </div>
</template>
