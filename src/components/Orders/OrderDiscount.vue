<script setup lang="ts">
import { Switch } from '@headlessui/vue'
import { storeToRefs } from 'pinia'
import type { OrderDiscountItem } from '@/types/orders'
const props = defineProps<{
  items: OrderDiscountItem[]
}>()
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()

const addDiscount = ref(false)

const title = 'modalHeader.add_discount_to_order'

const processing = ref(false)
</script>

<template>
  <div>
    <div>
      <LabelInput for="display_on_booking_page">
        {{ $t(title) }}
      </LabelInput>
      <Switch
        id="display_on_booking_page" v-model="addDiscount"
        :disabled="items.length === 0 || processing"
        class="inline-flex relative flex-shrink-0 mt-1 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        :class="[
          addDiscount
            ? 'bg-primary-600'
            : 'bg-gray-200',
          true ? 'opacity-50 cursor-not-allowed' : '',
        ]"
      >
        <span
          aria-hidden="true"
          class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
          :class="[
            addDiscount
              ? 'translate-x-5 rtl:-translate-x-5'
              : 'translate-x-0',
          ]"
        />
      </Switch>
    </div>
  </div>

  <Modal :dir="getLocale(locale)?.direction" :open="addDiscount" title="add_discount_to_order" @close="addDiscount = false">
    <overlay-loader v-if="processing" :full-screen="false" />
    <modal :open="addDiscount" title="add_discount_to_order" :dir="getLocale(locale)?.direction" @close="addDiscount = false">
      <form class="text-start" @submit.prevent="() => { }">
        <div class="mb-4">
          <LabelInput for="discount_type">
            {{ $t("orders.discount_type") }}
          </LabelInput>
          <select
            id="discount_type" v-model="items[0].discountType"
            class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="percentage">
              {{ $t("orders.percentage") }}
            </option>
            <option value="fixed">
              {{ $t("orders.fixed_amount") }}
            </option>
          </select>
        </div>
        <div class="mb-4">
          <LabelInput for="discount_amount">
            {{ $t("orders.discount_amount") }}
          </LabelInput>
          <input
            id="discount_amount" v-model="items[0].discount" type="number"
            class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Enter discount amount"
          >
        </div>
        <div class="flex justify-end">
          <button type="submit" class="px-4 py-2 text-white bg-primary-600 rounded-md hover:bg-primary-700">
            {{ $t("common.save") }}
          </button>
        </div>
      </form>
    </modal>
  </Modal>
</template>
