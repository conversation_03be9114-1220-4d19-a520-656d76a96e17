<script setup lang="ts">
import type { ComputedRef, PropType } from 'vue'
import { ReceiptRefundIcon } from '@heroicons/vue/24/solid'
import { storeToRefs } from 'pinia'
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'
import { ChevronUpIcon } from '@heroicons/vue/24/outline'
import { formatDateAndTime } from '@/composables/dateFormat'
import type { header } from '@/types'
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue'
defineProps({
  orders: {
    type: Object,
    required: true,
  },
})
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const { t } = useI18n()
const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('order_number'),
    },
    {
      title: t('items'),
    },
    {
      title: t('quantity'),
    },
    {
      title: t('total_order'),
    },
    {
      title: t('order_date'),
    },

  ]
})
const openRefundOrder = (order: Object) => {
  window.location.href = `/orders/?order_id=${order.uuid}`
}
</script>

<template>
  <DisclosureWrapper :title="$t('refunds_orders')">
    <!-- No header actions in this case -->
    <template #default>
      <div v-if="orders && orders.length" class="w-full min-w-[900px] md:min-w-0 overflow-x-auto pt-4">
        <!-- Table header -->
        <div class="flex w-full text-xs border-b border-gray-200 bg-stone-50 min-w-[900px]">
          <div class="px-2 py-3 w-1/6 text-start text-neutral-500">
            {{ $t("order_number") }}
          </div>
          <div class="px-2 py-3 w-1/6 text-start text-neutral-500">
            {{ $t("items") }}
          </div>
          <div class="px-2 py-3 w-1/6 text-start text-neutral-500">
            {{ $t("quantity") }}
          </div>
          <div class="px-2 py-3 w-1/6 text-start text-neutral-500">
            {{ $t("total_order") }}
          </div>
          <div class="px-2 py-3 w-1/6 text-start text-neutral-500">
            {{ $t("order_date") }}
          </div>
        </div>
        <!-- Table rows -->
        <div v-for="item in orders" :key="item.uuid" class="flex flex-nowrap items-center w-full text-xs md:text-sm border-b border-gray-200 min-w-[900px] cursor-pointer hover:bg-[#F6F7FF]  transition-colors" @click="openRefundOrder(item)">
          <div class="px-2 py-3 w-1/6 text-start text-neutral-800">
            #{{ item.OrderNum }}
          </div>
          <div class="px-2 py-3 w-1/6 text-start text-neutral-800">
            {{ item.items }}
          </div>
          <div class="px-2 py-3 w-1/6 text-start text-neutral-800">
            {{ item.quantity }}
          </div>
          <div class="px-2 py-3 w-1/6 text-start text-neutral-800">
            {{ item.total }} {{ userInfo?.tenant?.currency }}
          </div>
          <div class="px-2 py-3 w-1/6 text-start text-neutral-800">
            {{ formatDateAndTime(item.created_at) }}
          </div>
        </div>
      </div>
      <div v-else class="py-6 text-center text-neutral-400">
        {{ $t('empty_refunds_orders') }}
      </div>
    </template>
  </DisclosureWrapper>
</template>
