<script setup lang="ts">
import { computed } from 'vue'
const props = defineProps({
  summary: { type: Object, required: true },
  branch: { type: Object, required: false },
  source: { type: String, required: false },
})
const { tenant } = useAuthStore()
</script>

<template>
  <div class="flex flex-col flex-1 gap-2 items-start p-4 bg-white rounded-lg">
    <span class="w-40 text-base font-bold text-start text-neutral-800">{{
      $t("order_summary")
    }}</span>
    <div class="flex flex-col gap-2 w-full text-start">
      <div class="flex justify-between w-full">
        <span class="text-sm font-medium text-neutral-800">{{ $t("modalPlacholder.branch") }}:</span>
        <span class="text-sm font-normal text-neutral-800">{{
          branch?.name || "-"
        }}</span>
      </div>
      <div class="flex justify-between w-full">
        <span class="text-sm font-medium text-neutral-800">{{ $t("ord_details.source") }}:</span>
        <span class="text-sm font-normal text-neutral-800">{{
          $t(`booking.sources.${source || "-"}`)
        }}</span>
      </div>
      <div class="flex justify-between w-full">
        <span class="text-sm font-medium text-neutral-800">{{ $t("ord_details.fees_1") }}:</span>
        <span class="text-sm font-normal text-neutral-800">{{ summary?.sub_total || 0 }} {{ tenant?.currency || "" }}</span>
      </div>
      <div class="flex justify-between w-full" v-if="summary?.discount_amount">
        <span class="text-sm font-medium text-neutral-800"
          >{{ $t("ord_details.discounts") }}:</span
        >
        <span class="text-sm font-normal text-neutral-800"
          >{{ summary?.discount_value || 0 }}

          <span class="text-sm font-normal text-neutral-800">{{
            summary?.discount_symbol || ""
          }}</span></span>
      </div>
      <div
        v-if="summary?.transportation_fees"
        class="flex justify-between w-full"
      >
        <span class="text-sm font-medium text-neutral-800">{{ $t("form.boundary_fees") }}:</span>
        <span class="text-sm font-normal text-neutral-800">+{{ summary?.transportation_fees || 0 }}
          {{ tenant?.currency || "" }}</span>
      </div>
      <div
        v-if="summary?.discount_amount && summary?.coupon?.code"
        class="flex flex-col gap-2 py-2 w-full border-t border-b border-gray-200"
      >
        <div
          v-if="summary?.discount_amount"
          class="flex justify-between w-full"
        >
          <span class="text-sm font-medium text-neutral-800">{{ $t("ord_details.discounts") }}:</span>
          <span class="text-sm font-normal text-neutral-800">-{{ summary?.discount_amount || 0 }}
            {{ tenant?.currency || "" }}</span>
        </div>
        <div v-if="summary?.coupon?.code" class="flex justify-between w-full">
          <span class="text-sm font-medium text-neutral-800">{{ $t("ord_details.coupons") }}:<span class="text-[10px]">(#{{ summary.coupon.code }})</span>:</span>
          <span class="text-sm font-normal text-neutral-800">-{{ summary.coupon.discount_amount || 0 }}
            {{ tenant?.currency || "" }}</span>
        </div>
      </div>
      <div class="flex justify-between w-full">
        <span class="text-sm font-medium text-neutral-800">{{ $t("pos.totalTaxable") }}:</span>
        <span class="text-sm font-normal text-neutral-800">{{ summary?.total_amount || 0 }} {{ tenant?.currency || "" }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
