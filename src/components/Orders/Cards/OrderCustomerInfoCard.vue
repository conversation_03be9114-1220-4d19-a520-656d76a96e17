<script setup lang="ts">
import { computed, ref } from 'vue'
import { ClipboardIcon, LinkIcon, PencilSquareIcon, PhoneIcon } from '@heroicons/vue/24/outline'
import { LockClosedIcon } from '@heroicons/vue/24/solid'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import WhatsappIcon from '../../Icons/WhatsappIcon.vue'
import EmailIcon from '../../Icons/EmailIcon.vue'

const props = defineProps({
  customer: { type: Object, required: true },
  pin_code: { type: String, required: false },
  editable: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    required: false,
  },
})
const { t } = useI18n()
const router = useRouter()
const copiedPin = ref(false)
const copiedAll = ref(false)
const copiedEmail = ref(false)
const openAssignModal = ref(false)

function copyPinCode(pin) {
  if (!pin)
    return
  navigator.clipboard.writeText(pin.toString())
  copiedPin.value = true
  setTimeout(() => copiedPin.value = false, 1200)
}
function copyAllCustomerInfo() {
  const c = props.customer
  const info = `Name: ${c.first_name || ''} ${c.last_name || ''}\nPhone: ${c.phone || ''}\nEmail: ${c.email || ''}`
  navigator.clipboard.writeText(info)
  copiedAll.value = true
  setTimeout(() => copiedAll.value = false, 1200)
}
function copyEmail() {
  if (!props.customer?.email)
    return
  navigator.clipboard.writeText(props.customer.email)
  copiedEmail.value = true
  setTimeout(() => copiedEmail.value = false, 1200)
}
function goToCustomerProfile() {
  if (props.customer?.uuid)
    router.push({ name: 'customer', params: { id: props.customer.uuid } })
}
const customerActions = computed(() => {
  const c = props.customer || {}
  return [
    {
      icon: WhatsappIcon,
      type: 'link',
      href: c.phone ? `https://wa.me/${c.phone.replace(/[^0-9]/g, '')}` : null,
      title: t('whatsapp'),
    },
    {
      icon: EmailIcon,
      type: 'copyEmail',
      onClick: copyEmail,
      title: t('email'),
      active: copiedEmail.value,
    },
    {
      icon: PhoneIcon,
      type: 'link',
      href: c.phone ? `tel:${c.phone}` : null,
      title: t('phone'),
    },
    {
      icon: ClipboardIcon,
      type: 'copy',
      onClick: copyAllCustomerInfo,
      title: t('ord_details.copy_all_info'),
      active: copiedAll.value,
    },
    {
      icon: LinkIcon,
      type: 'profile',
      onClick: goToCustomerProfile,
      title: t('ord_details.go_to_profile'),
    },
  ]
})
const openEditCustomerModal = () => {
  openAssignModal.value = true
}
</script>

<template>
  <div class="flex flex-col flex-1 gap-2 items-start p-4 bg-white rounded-lg shadow-sm transition-shadow duration-200 hover:shadow-md">
    <div class="flex justify-between items-center w-full">
      <span class="text-base font-bold text-neutral-800">{{ t('customer_data') }}</span>
      <button class="flex gap-1 items-center p-2 h-6 bg-indigo-50 rounded-lg" @click="openEditCustomerModal">
        <EditIcon class="w-4 h-4 text-sky-950" />
        <span class="text-xs font-normal text-sky-950">تعديل</span>
      </button>
    </div>
    <div class="flex flex-col gap-2 w-full">
      <div class="flex justify-between items-center w-full">
        <span class="text-sm font-medium text-neutral-800">{{ t('form.customerName') }}:</span>
        <span class="text-sm font-normal text-neutral-800">{{ customer?.first_name || '' }} {{ customer?.last_name || '' }}</span>
      </div>
      <div v-if="customer?.phone" class="flex justify-between w-full">
        <span class="text-sm font-medium text-neutral-800">{{ t('form.phone') }}:</span>
        <span class="text-sm font-normal text-neutral-800">{{ customer.phone }}</span>
      </div>
      <div class="flex justify-between w-full">
        <span class="text-sm font-medium text-neutral-800">{{ t('orders_completed') }}:</span>
        <span class="text-sm font-normal text-neutral-800">{{ customer?.orders_count || 0 }}</span>
      </div>
      <div class="flex justify-between items-center w-full">
        <span class="text-sm font-medium text-neutral-800">{{ t('order_pin_code') }}:</span>
        <span v-if="pin_code" class="flex gap-1 justify-center items-center px-2 py-1 bg-gray-100 rounded-full transition cursor-pointer hover:bg-gray-200" @click="copyPinCode(pin_code)">
          <span class="px-2 text-sm font-bold py-1text-black">{{ pin_code }}</span>
          <span class="flex relative items-center h-6 justify-centerw-6">
            <Icons name="copy" class="inline-block w-4 h-4" :class="copiedPin ? 'text-green-500' : 'text-gray-400'" aria-hidden="true" />
            <div v-if="copiedPin" class="absolute bottom-full left-1/2 z-10 px-2 py-1 mb-2 text-xs text-blue-900 whitespace-nowrap bg-blue-100 rounded shadow -translate-x-1/2">
              {{ t('copied_pin_code') }}
            </div>
          </span>
        </span>
        <span v-else class="text-sm font-normal text-neutral-800">-</span>
      </div>
    </div>
    <div class="flex gap-3 items-center mt-2">
      <template v-for="(action, idx) in customerActions" :key="idx">
        <a
          v-if="action.type === 'link' && action.href"
          :href="action.href"
          target="_blank"
          class="flex items-center p-2 rounded-3xl bg-stone-50"
          :title="action.title"
        >
          <component :is="action.icon" class="block w-4 h-4 text-black" />
        </a>
        <button
          v-else
          class="flex relative items-center p-2 rounded-3xl bg-stone-50"
          :title="action.title"
          @click="action.onClick && action.onClick()"
        >
          <component
            :is="action.icon" class="block w-4 h-4"
            :class="(action.type === 'copy' && action.active) || (action.type === 'copyEmail' && action.active) ? 'text-green-500' : 'text-black'"
          />
          <div v-if="action.type === 'copyEmail' && copiedEmail" class="absolute bottom-full left-1/2 z-10 px-2 py-1 mb-2 text-xs text-blue-900 whitespace-nowrap bg-blue-100 rounded shadow -translate-x-1/2">
            {{ t('copied_email') }}
            <div class="absolute top-full left-1/2 w-3 h-3 bg-blue-100 rotate-45 -translate-x-1/2" />
          </div>
          <div v-if="action.type === 'copy' && copiedAll" class="absolute bottom-full left-1/2 z-10 px-2 py-1 mb-2 text-xs text-blue-900 whitespace-nowrap bg-blue-100 rounded shadow -translate-x-1/2">
            {{ t('copied_customer_info') }}
            <div class="absolute top-full left-1/2 w-3 h-3 bg-blue-100 rotate-45 -translate-x-1/2" />
          </div>
        </button>
      </template>
    </div>
  </div>
  <modal-assign-customer-to-order
    v-if="openAssignModal"
    :is-open="openAssignModal"
    :customer-selected="customer"
    :order-id="orderId"
    @close="openAssignModal = false"
  />
</template>

<style scoped>
</style>
