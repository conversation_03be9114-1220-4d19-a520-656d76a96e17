<script setup lang="ts">
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { GoogleMap, Marker, MarkerCluster } from 'vue3-google-map'
import { storeToRefs } from 'pinia'
import EditIcon from '../../Icons/EditIcon.vue'

const props = defineProps({
  address: { type: String, required: false },
  longitude: { type: [String, Number], required: false },
  latitude: { type: [String, Number], required: false },
  orderId: { type: [String, Number], required: false },
})
const emit = defineEmits(['edit-address', 'refresh'])
const API_KEY = import.meta.env.VITE_GOOGLE_MAP_KEY
const { locale } = useI18n()
const { updateOrderLocation } = useOrder()

const { t } = useI18n()

const showModal = ref(false)
const processing = ref(false)
const formData = reactive({
  longitude: 0,
  latitude: 0,
})

function openMapEdit() {
  showModal.value = true
  if (props.longitude && props.latitude) {
    formData.longitude = Number(props.longitude)
    formData.latitude = Number(props.latitude)
  }
  else {
    navigator.geolocation.getCurrentPosition((position) => {
      formData.longitude = position.coords.longitude
      formData.latitude = position.coords.latitude
    })
  }
}

const closeModal = () => {
  showModal.value = false
}

const updateLocation = async () => {
  try {
    processing.value = true
    await updateOrderLocation(props.orderId, formData)
    emit('refresh')
    showModal.value = false
  }
  catch (error) {
    console.error(error)
  }
  finally {
    processing.value = false
  }
}

const mark = (event: any) => {
  formData.latitude = event.latLng.lat()
  formData.longitude = event.latLng.lng()
}
</script>

<template>
  <div class="flex-1 flex flex-col items-start gap-2  p-4 bg-white rounded-lg ">
    <Modal :open="showModal" title="update_location" @close="closeModal">
      <OverlayLoader v-if="processing" :full-screen="false" />
      <div id="add-transaction-button" class="flex gap-5 justify-start items-center p-1 pb-1 w-full">
        <GoogleMap
          ref="map"
          :api-key="API_KEY"
          style="width: 100%; height: 500px"
          :center="{ lat: +formData.latitude, lng: +formData.longitude }"
          :draggable="true"
          :zoom="8"
          @click="mark($event)"
        >
          <MarkerCluster>
            <Marker
              :draggable="true"
              :options="{
                position: {
                  lat: +formData.latitude,
                  lng: +formData.longitude,
                },
              }"
            />
          </MarkerCluster>
        </GoogleMap>
      </div>
      <div class="flex justify-center items-center py-2">
        <button
          class="flex justify-center items-center px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-md transition duration-100 ease-in cursor-pointer focus:transform active:scale-95"
          @click="updateLocation"
        >
          {{ $t("form.update") }}
        </button>
      </div>
    </Modal>

    <div class="flex justify-between items-center w-full">
      <span class="text-base font-bold text-neutral-800">العنوان</span>
      <button class="flex gap-1 items-center p-2 h-6 bg-indigo-50 rounded-lg" @click="openMapEdit">
        <EditIcon class="w-4 h-4 text-sky-950" />
        <span class="text-xs font-normal text-sky-950">تعديل</span>
      </button>
    </div>
    <div v-if="longitude && latitude" class="overflow-hidden w-full h-28 rounded-lg">
      <GoogleMap
        :api-key="API_KEY"
        style="width: 100%; height: 100%"
        :center="{
          lat: Number(latitude),
          lng: Number(longitude),
        }"
        :draggable="false"
        :zoom="6"
      >
        <MarkerCluster>
          <Marker
            :draggable="false"
            :options="{
              position: {
                lat: Number(latitude),
                lng: Number(longitude),
              },
            }"
          />
        </MarkerCluster>
      </GoogleMap>
    </div>
    <div v-else class="flex justify-center items-center w-full h-28 bg-gray-100 rounded-lg">
      <span class="text-gray-400">{{ t('no_location') }}</span>
    </div>
    <div class="w-full text-xs font-normal text-neutral-500 text-start">
      <a
        v-if="longitude && latitude"
        target="_blank"
        class="underline cursor-pointer text-primary"
        :href="`https://maps.google.com/?q=${latitude},${longitude}`"
      >
        {{ address || '-' }}
      </a>
      <span v-else>{{ address || $t('no_address') }}</span>
    </div>
  </div>
</template>

<style scoped>
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #7f7f80 #e5e7eb;
}
</style>
