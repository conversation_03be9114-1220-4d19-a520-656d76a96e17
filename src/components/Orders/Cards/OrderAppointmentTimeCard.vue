<script lang="ts" setup>
import type { PropType } from 'vue'
import { ClockIcon } from '@heroicons/vue/24/outline'
import dayjs from 'dayjs'
import type { OrderCustomer } from '@/types'
const props = defineProps({
  staffs: {
    type: Object as PropType<OrderCustomer>,
    required: true,
  },
  dates: {
    type: String,
    required: true,
  },

})

const router = useRouter()

const { dates, staffs } = toRefs(props)
const [start, end] = toRefs(dates.value)
</script>

<template>
  <div class="flex flex-col text-black border border-gray-200 rounded-lg text-md">
    <h3 class="flex items-center justify-between p-2 mb-2 text-gray-400 bg-gray-100">
      {{ $t("appointment_time") }}
    </h3>
    <div class="flex items-start justify-start w-full gap-5 p-6 text-wrap xl:flex-row sm:flex-col">
      <ClockIcon class="inline-block w-16 h-16 text-black cursor-pointer" />
      <div>
        <p v-if="start" class="text-lg font-semibold text-primary-900">
          {{
            `${dayjs(start).format('DD MMM YYYY')} - ${dayjs(start).format('HH:mm A')} : ${dayjs(end).format('HH:mm A')}`

          }}
        </p>
        <p v-else class="text-lg font-semibold text-red-700">
          لا يوجد حجز
        </p>
        <p class="text-lg font-semibold text-gray-500 text-wrap ">
          مزودي الخدمات :
          {{ staffs.join(' , ') }}
        </p>
      </div>
    </div>
  </div>
</template>
