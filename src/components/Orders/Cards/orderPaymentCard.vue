<script setup lang="ts">
import type { PropType } from 'vue'
import { CheckCircleIcon, ComputerDesktopIcon, ExclamationTriangleIcon, GlobeAltIcon, XCircleIcon } from '@heroicons/vue/24/solid'
import type { OrderPayments } from '@/types'
import BaseButton from '@/components/BaseButton.vue'

const props = defineProps({
  payments: {
    type: Object as PropType<OrderPayments>,
    required: true,
  },
  orderId: {
    type: String,
    required: true,
  },
  status: {
    type: String,
    required: false,
  },
  showPaymentLink: {
    type: Boolean,
    default: false,
  },
})
const { previewInvoicePdf, fetchPaymentLink } = usePosStore()
const paymentLinkActivate = ref(false)
const paymentLink = ref<string>('')

const copyToClipboard = (text: string) => {
  if (navigator.clipboard && window.isSecureContext) {
    // For modern browsers that support navigator.clipboard in secure contexts
    navigator.clipboard.writeText(text).then(() => {
      paymentLinkActivate.value = true
      setTimeout(() => {
        paymentLinkActivate.value = false
      }, 1000)
    })
  }
  else {
    // Fallback for Safari and older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    try {
      document.execCommand('copy')
      paymentLinkActivate.value = true
      setTimeout(() => {
        paymentLinkActivate.value = false
      }, 1000)
    }
    catch (err) {
      console.error('Failed to copy: ', err)
    }

    document.body.removeChild(textArea)
  }
}

const generatingPaymentLink = ref<boolean>(false)

const generatePaymentLink = () => {
  generatingPaymentLink.value = true

  fetchPaymentLink(props.orderId).then(({ data }) => {
    if (data)
      paymentLink.value = data
  }).finally(() => {
    generatingPaymentLink.value = false
  })
}
</script>

<template>
  <div class="flex flex-col text-black rounded-lg border border-gray-200 text-md">
    <h3 class="flex justify-between items-center p-2.5 mb-2 text-gray-400 bg-gray-100">
      <span>{{ $t("payment") }}</span>
    </h3>
    <div class="flex gap-5 justify-start items-center p-6 pb-4 w-full">
      <span class="flex-shrink-0 w-16 h-16 rounded-full">
        <XCircleIcon v-if="payments.status == 'unpaid'" class="inline-block text-red-700" />
        <CheckCircleIcon v-if="payments.status == 'paid'" class="inline-block text-green-700" />
        <ExclamationTriangleIcon v-if="payments.status == 'partially-paid'" class="inline-block text-gray-500" />
      </span>
      <h3 class="text-lg font-semibold text-black">
        {{ $t(`pos.status.${payments.status}`) }}
      </h3>
    </div>
    <div
      v-if="payments.status != 'paid'"
      id="copy-payment-link" class="flex z-10 flex-col justify-center items-center p-4 px-6"
    >
      <div
        v-if="showPaymentLink"
        class="flex flex-col w-full"
      >
        <!-- Payment link input and copy button -->
        <div v-if="paymentLink" class="flex relative mb-2 w-full">
          <input
            type="text"
            readonly
            class="flex-1 p-2 pr-10 text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-1 focus:ring-primary"
            :value="paymentLink"
          >
          <button
            class="flex absolute inset-y-0 right-0 items-center px-3 text-gray-500 transition-colors hover:text-primary"
            type="button"
            @click="copyToClipboard(paymentLink)"
          >
            <svg
              class="w-5 h-5"
              xmlns="http://www.w3.org/2000/svg"
              width="24px"
              height="24px"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M16 12.9V17.1C16 20.6 14.6 22 11.1 22H6.9C3.4 22 2 20.6 2 17.1V12.9C2 9.4 3.4 8 6.9 8H11.1C14.6 8 16 9.4 16 12.9Z"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M22 6.9V11.1C22 14.6 20.6 16 17.1 16H16V12.9C16 9.4 14.6 8 11.1 8H8V6.9C8 3.4 9.4 2 12.9 2H17.1C20.6 2 22 3.4 22 6.9Z"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <div
              class="flex absolute -top-10 left-1/2 z-10 flex-col items-center transform -translate-x-1/2"
              :class="{ hidden: !paymentLinkActivate }"
            >
              <span
                class="px-2 py-1 text-xs font-medium leading-none whitespace-nowrap rounded shadow-lg bg-primary-100 text-primary"
              >
                {{ $t("copied_paymeny_url") }}
              </span>
              <div class="-mt-1 w-2 h-2 rotate-45 bg-primary-100" />
            </div>
          </button>
        </div>

        <BaseButton
          v-if="!paymentLink"
          :disabled="generatingPaymentLink"
          class="self-start hover:bg-primary-800"
          custome-bg="bg-primary-700 "
          @click="generatePaymentLink"
        >
          <span v-if="!generatingPaymentLink" class="mx-2"> {{ $t("generate_payment_link") }} </span>
          <span v-else class="mx-2"> {{ $t("generating_payment_link") }} </span>
          <svg
            class="inline-block w-6 h-6"
            xmlns="http://www.w3.org/2000/svg"
            width="24px"
            height="24px"
            viewBox="0 0 24 24"
            fill="none"
            aria-hidden="true"
          >
            <path
              d="M14.1625 18.4876L13.4417 19.2084C11.053 21.5971 7.18019 21.5971 4.79151 19.2084C2.40283 16.8198 2.40283 12.9469 4.79151 10.5583L5.51236 9.8374"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
            />
            <path
              d="M9.8374 14.1625L14.1625 9.8374"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
            />
            <path
              d="M9.8374 5.51236L10.5583 4.79151C12.9469 2.40283 16.8198 2.40283 19.2084 4.79151M18.4876 14.1625L19.2084 13.4417C20.4324 12.2177 21.0292 10.604 20.9988 9"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        </BaseButton>
      </div>
      <div
        v-else
        class="flex relative mb-2 w-full rounded-md ltr:flex-row-reverse rtl:flex-row"
      >
        <span class="block mb-2 text-sm text-red-400">
          {{ $t('payment_link_can_not_be_sent') }}
        </span>
      </div>
    </div>
  </div>
</template>
