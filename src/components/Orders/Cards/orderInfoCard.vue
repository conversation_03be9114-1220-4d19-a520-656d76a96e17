<script setup lang="ts">
import type { PropType } from 'vue'
import dayjs from 'dayjs'
import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/vue/24/outline'
import { HomeIcon, ReceiptRefundIcon } from '@heroicons/vue/20/solid'
import type { Order } from '@/types'
const prop = defineProps({
  order: {
    type: Object as PropType<Order>,
    required: true,
  },
})
const emits = defineEmits(['refresh'])
const showChangeStatusModal = ref(false)
const { t } = useI18n()
const disallowedStatusChange = ['fully-refunded', 'partial-refund']
function openStatusModal() {
  if (disallowedStatusChange.includes(prop.order?.status.status))
    return
  c.value = true
}

function visitOriginalOrder(orderId: string) {
  window.location.href = `/orders/${orderId}`
}
</script>

<template>
  <ModalChangeOrderStatus
    v-if="showChangeStatusModal"
    :order="order"
    :show-modal="showChangeStatusModal"
    @close="showChangeStatusModal = false"
    @refresh="emits('refresh')"
  />
  <div class="mt-6 mb-6">
    <generic-alert
      v-if="order.type == 'refund'"
      :title="
        `${$t('main_order')} : #${order.original_refund_order?.OrderNum}`
      "
      type="info"
      class="w-full cursor-pointer hover:shadow-md hover:bg-gray-200"
      @click="visitOriginalOrder(order.original_refund_order?.uuid)"
    />
    <div
      class="grid grid-cols-1 gap-4 p-4 w-full rounded-lg border border-gray-200 sm:grid-cols-3"
    >
      <div
        class="flex flex-col justify-center items-center text-black text-md sm:items-start"
      >
        <span class="flex mb-2 text-gray-400">
          {{ $t("order_number") }}
          <span v-if="order?.type == 'refund'" class="text-red-500"><ReceiptRefundIcon
            class="flex-shrink-0 w-7 h-7"
            aria-hidden="true"
            color="444"
          /></span>
        </span>
        <span class="font-medium"> # {{ order.OrderNum }} </span>
      </div>

      <div class="flex flex-col justify-center items-center text-black text-md">
        <span class="flex gap-1 items-center mb-2 text-gray-400">
          <CalendarDaysIcon class="inline-block w-5 h-5" aria-hidden="true" />
          {{ $t("order_recieve_time") }}
        </span>
        <span class="font-medium">
          {{ dayjs(order.created_at).format("dddd, DD MMMM YYYY | hh:mm A") }}
        </span>
      </div>
      <div
        class="flex flex-col justify-center items-center text-black text-md sm:items-end"
      >
        <span class="flex gap-1 items-center mb-2 text-gray-400">
          <FlagIcon class="inline-block w-5 h-5" aria-hidden="true" />
          {{ $t("order_status") }}
        </span>
        <button
          class="flex gap-1 items-center px-2 py-1 text-black bg-white rounded-full border border-gray-200"
          :style="{
            'background-color': order.status?.bg_color ?? '#000000',
            'color': order.status?.text_color ?? '#000000',
          }"
          @click="openStatusModal"
        >
          <span
            class="inline-block mr-2 w-3 h-3 rounded-full"
            :style="{
              'background-color': order.status?.text_color ?? '#000000',
            }"
          />
          {{ order.status.label }}
          <ArrowLeftIcon
            class="inline-block hidden w-4 h-4 text-white rtl:inline-block"
            aria-hidden="true"
            :style="{
              color: order.status?.text_color ?? '#000000',
            }"
          />
          <ArrowRightIcon
            class="inline-block w-4 h-4 text-white rtl:hidden"
            aria-hidden="true"
            :style="{
              color: order.status?.text_color ?? '#000000',
            }"
          />
        </button>
      </div>
    </div>
  </div>
</template>
