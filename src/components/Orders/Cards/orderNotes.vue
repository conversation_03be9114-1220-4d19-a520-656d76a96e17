<script setup lang="ts">
import type { PropType } from 'vue'
import useVuelidate from '@vuelidate/core'
import { PencilIcon } from '@heroicons/vue/24/outline'
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'
import { ChevronUpIcon } from '@heroicons/vue/20/solid'
import { minLength, required } from '@/utils/i18n-validators'
import LilActionBtn from '@/components/Buttons/LilActionBtn.vue'
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue'
import EditIcon from '@/components/Icons/EditIcon.vue'

const props = defineProps({
  note: {
    type: String as PropType<string | null>,
    required: true,
  },
  orderId: {
    type: String as PropType<string>,
    required: true,
  },
})
const emits = defineEmits(['update'])
const { updateOrderNote } = useOrder()
const showEditNoteModal = ref(false)

const formData = reactive({
  note: '',
})

const rules = {
  note: {
    required,
    minLength: minLength(3),
  },
}
const v$ = useVuelidate(rules, formData)

const editNote = () => {
  showEditNoteModal.value = true
  formData.note = props.note ?? ''
}
const updateNote = async () => {
  if (v$.value.$invalid)
    return

  await updateOrderNote(props.orderId, formData).finally(() => {
    showEditNoteModal.value = false
    emits('update')
  })
}
</script>

<template>
  <modal :show="showEditNoteModal" @close="showEditNoteModal = false">
    <h2 class="text-lg font-medium text-gray-900">
      {{ $t("form.order_note") }}
    </h2>
    <div class="w-full">
      <form-group :validation="v$" name="content">
        <template #default="{ attrs }">
          <TextareaInput
            v-bind="attrs"
            id="notification-content"
            v-model="formData.note"
            :label="$t('note')"
            :placeholder="$t('note')"
            dir="auto"
            :rows="5"
            name="notification-content"
            custom-classes="block py-1.5 w-full text-gray-900 rounded-md border-0 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </template>
      </form-group>
      <base-button
        class="flex justify-center items-center px-4 py-2 my-5 text-sm font-medium text-white rounded-md transition duration-100 ease-in cursor-pointer focus:transform active:scale-95"
        :default-style="false"
        custome-bg="bg-blue-500"
        @click="updateNote"
      >
        {{ $t("form.update") }}
      </base-button>
    </div>
  </modal>

  <DisclosureWrapper :title="$t('notes')">
    <template #header-actions>
      <LilActionBtn
        :icon="EditIcon"
        :aria-label="$t('edit_note')"
        @click="editNote"
      />
    </template>
    <div class="w-full min-w-[600px] md:min-w-0 overflow-x-auto">
      <div class="inline-block min-w-full align-middle text-start">
        {{ note ?? $t("empty_notes") }}
      </div>
    </div>
  </DisclosureWrapper>
</template>
