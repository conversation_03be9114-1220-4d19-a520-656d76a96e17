<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  gift_card: { type: Object, required: true },
})
const { t } = useI18n()
const copiedEmail = ref(false)
const copiedPhone = ref(false)

function copyEmail() {
  if (!props.gift_card?.email)
    return
  navigator.clipboard.writeText(props.gift_card.email)
  copiedEmail.value = true
  setTimeout(() => copiedEmail.value = false, 1200)
}
function copyPhone() {
  if (!props.gift_card?.phone)
    return
  navigator.clipboard.writeText(props.gift_card.phone)
  copiedPhone.value = true
  setTimeout(() => copiedPhone.value = false, 1200)
}
</script>

<template>
  <div class="flex flex-col flex-1 gap-2 items-start  p-4 bg-white rounded-lg ">
    <div class="flex justify-between items-center w-full">
      <span class="text-base font-bold text-neutral-800">{{ t('fields.gift_card') }}</span>
    </div>
    <div class="flex flex-col gap-2 w-full">
      <div class="flex justify-between items-center w-full">
        <span class="text-sm font-medium text-neutral-800">{{ t('receiver_name') }}:</span>
        <span class="text-sm font-normal text-neutral-800">{{ gift_card?.name || '-' }}</span>
      </div>
      <div class="flex justify-between items-center w-full">
        <span class="text-sm font-medium text-neutral-800 text-right">{{ t('fields.email') }}:</span>
        <span class="flex gap-1 items-center">
          <span class="text-sm font-normal text-neutral-800 ">{{ gift_card?.email || '-' }}</span>
          <button
            v-if="gift_card?.email"
            class="flex relative items-center p-1 rounded-full bg-stone-50"
            :title="t('copy')"
            @click="copyEmail"
          >
            <Icons name="copy" class="inline-block w-4 h-4" :class="copiedEmail ? 'text-green-500' : 'text-black'" aria-hidden="true" />
            <div v-if="copiedEmail" class="absolute bottom-full left-1/2 z-10 px-2 py-1 mb-2 text-xs text-blue-900 whitespace-nowrap bg-blue-100 rounded shadow -translate-x-1/2">
              {{ t('copied_email') }}
              <div class="absolute top-full left-1/2 w-3 h-3 bg-blue-100 rotate-45 -translate-x-1/2" />
            </div>
          </button>
        </span>
      </div>
      <div class="flex justify-between items-center w-full">
        <span class="text-sm font-medium text-neutral-800">{{ t('fields.phone') }}:</span>
        <span class="flex gap-1 items-center">
          <span class="text-sm font-normal text-neutral-800">{{ gift_card?.phone || '-' }}</span>
          <button
            v-if="gift_card?.phone"
            class="flex relative items-center p-1 rounded-full bg-stone-50"
            :title="t('copy')"
            @click="copyPhone"
          >
            <Icons name="copy" class="inline-block w-4 h-4" :class="copiedPhone ? 'text-green-500' : 'text-black'" aria-hidden="true" />
            <div v-if="copiedPhone" class="absolute bottom-full left-1/2 z-10 px-2 py-1 mb-2 text-xs text-blue-900 whitespace-nowrap bg-blue-100 rounded shadow -translate-x-1/2">
              {{ t('copied') }}
              <div class="absolute top-full left-1/2 w-3 h-3 bg-blue-100 rotate-45 -translate-x-1/2" />
            </div>
          </button>
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
</style>
