<script setup lang="ts">
import type { PropType } from 'vue'
import { MapIcon } from '@heroicons/vue/24/solid'
import { GoogleMap, Marker, MarkerCluster } from 'vue3-google-map'
import { PencilIcon, PlusIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
const props = defineProps({
  address: {
    type: String,
    required: true,
  },
  long: {
    type: Number,
    required: false,
  },
  lat: {
    type: Number,
    required: false,
  },
  orderId: {
    type: String,
    required: true,
  },
})
const emits = defineEmits(['update'])
const API_KEY = import.meta.env.VITE_GOOGLE_MAP_KEY
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { updateOrderLocation } = useOrder()
const showModal = ref(false)
const formData = reactive({
  longitude: 0,
  latitude: 0,
})
const editLocation = () => {
  showModal.value = true
  if (props.long && props.lat) {
    formData.longitude = props.long
    formData.latitude = props.lat
  }
  else {
    navigator.geolocation.getCurrentPosition((position) => {
      formData.longitude = position.coords.longitude
      formData.latitude = position.coords.latitude
    })
  }
}
const closeModal = () => {
  showModal.value = false
}
const processing = ref(false)
const updateLocation = async () => {
  processing.value = true
  await updateOrderLocation(props.orderId, formData)
  showModal.value = false
  emits('update')
  processing.value = false
}
const mark = (event: any) => {
  formData.latitude = event.latLng.lat()
  formData.longitude = event.latLng.lng()
}
</script>

<template>
  <modal :dir="getLocale(locale)?.direction" :open="showModal" title="update_location" @close="closeModal">
    <overlay-loader v-if="processing" :full-screen="false" />
    <div class="flex items-center justify-start w-full gap-5 p-1 pb-1">
      <GoogleMap
        ref="map" :api-key="API_KEY"
        style="width: 100%; height: 500px" :center="{ lat: +formData.latitude, lng: +formData.longitude }" :draggable="true" :zoom="8"
        @dragend="true" @click="mark($event)"
      >
        <MarkerCluster>
          <Marker
            :draggable="true" :options="{
              position: {
                lat: +formData.latitude,
                lng: +formData.longitude,
              },
            }"
          />
        </MarkerCluster>
      </GoogleMap>
    </div>
    <div class="flex justify-center items-center py-2">
      <button
        class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white transition duration-100 ease-in rounded-md cursor-pointer focus:transform active:scale-95 bg-blue-500"
        @click="updateLocation"
      >
        {{ $t("form.update") }}
      </button>
    </div>
  </modal>
  <div class="flex flex-col text-black border border-gray-200 rounded-lg text-md">
    <h3 class="flex items-center justify-between p-2.5 mb-2 text-gray-400 bg-gray-100">
      <span> {{ $t("address") }} </span>
      <!-- <MapIcon class="w-4 h-4 text-blue-500" /> -->
      <BaseButton
        v-if="lat && long" id="add-transaction-button"
        class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white transition duration-100 ease-in rounded-md cursor-pointer focus:transform active:scale-95"
        :default-style="false" custome-bg="bg-blue-500" @click="editLocation"
      >
        {{ $t("edit") }}
        <PencilIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
      <BaseButton
        v-else id="add-transaction-button"
        class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white transition duration-100 ease-in rounded-md cursor-pointer focus:transform active:scale-95"
        :default-style="false" custome-bg="bg-blue-500" @click="editLocation"
      >
        {{ $t("add_location") }}
        <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </h3>
    <div v-if="lat && long" class="flex items-center justify-start w-full gap-5 p-1 pb-1">
      <GoogleMap
        :api-key="API_KEY" style="width: 100%; height: 150px" :center="{
          lat: Number(lat),
          lng: Number(long),
        }" :draggable="true" :editable="true" :zoom="6"
      >
        <MarkerCluster>
          <Marker
            :draggable="false" :options="{
              position: {
                lat: Number(lat),
                lng: Number(long),
              },
            }"
          />
        </MarkerCluster>
      </GoogleMap>
    </div>
    <div v-else class="flex items-center justify-center w-full gap-5 p-1 pb-1">
      <span class="text-gray-400">
        {{ $t("no_location") }}
      </span>
    </div>
    <div class="grid grid-cols-1 mx-6 sm:grid-cols-1">
      <div class="flex items-center gap-2 py-3 border-b border-stone-100">
        <div class="flex mt-1 font-semibold">
          <div class="relative flex items-stretch flex-grow focus-within:z-10">
            <a
              target="_blank" class="underline cursor-pointer text-primary"
              :href="`https://maps.google.com/?q=${lat},${long}`"
            >
              {{ address }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
