<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { PhotoIcon } from '@heroicons/vue/24/outline'
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue'
defineProps({
  images: {
    type: Object,
    required: true,
  },
})
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const { t } = useI18n()

const openImage = (url: string) => {
  window.open(url, '_blank')
}
</script>

<template>
  <DisclosureWrapper :title="$t('close_images')">
    <template #default>
      <div class="relative flex flex-col text-black border border-gray-200 rounded-lg text-md">
        <div class="flex items-center justify-between px-2 py-4 text-lg text-black bg-gray-100">
          <span class="flex items-center gap-2 font-medium">
            <PhotoIcon class="w-6 h-6" />
            {{ $t("altNav.order_images") }}
          </span>
        </div>
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
          <div v-if="images" class="inline-block min-w-full align-middle">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4">
              <div v-for="image in images" :key="image.id">
                <img :src="image.url" alt="Order Image" class="w-full h-full object-cover rounded-lg cursor-pointer" @click="openImage(image.url)">
              </div>
            </div>
          </div>
          <div v-else class="py-6 overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
            <div class="inline-block min-w-full align-middle px-4 md:px-6 lg:px-8">
              {{ $t('no_order_images') }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </DisclosureWrapper>
</template>
