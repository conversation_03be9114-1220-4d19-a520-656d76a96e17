<script setup lang="ts">
import type { ComputedRef, PropType } from 'vue'
import {
  CheckCircleIcon,
  ComputerDesktopIcon,
  ExclamationTriangleIcon,
  GlobeAltIcon,
  XCircleIcon,
} from '@heroicons/vue/24/solid'
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/vue'
import { storeToRefs } from 'pinia'
import { formatDateAndTime } from '@/composables/dateFormat'
import type { header } from '@/types'
const props = defineProps({
  invoices: {
    type: Array,
    required: true,
  },
  order_id: {
    type: Number,
    required: true,
  },
})
const { t } = useI18n()
const authStore = useAuthStore()
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const { previewInvoicePdf, sendInvoicePdf, sendWhatsAppInvoice }
  = usePosStore()
const userInfo = computed(() => authStore.userInfo)
const invoiceProcessing = ref(false)
const printOptions = computed(() => {
  return [
    {
      name: t('export_invoice'),
      id: 'export_invoice',
      icon: 'summary',
      event: downloadPdfInvioce,
    },
    {
      name: t('preview_invoice'),
      id: 'preview_invoice',
      icon: 'invoice',
      event: openInvoicePdf,
    },
    {
      name: t('send_invoice_email'),
      id: 'send_invoice_email',
      icon: 'invoice',
      event: sendInvoice,
    },
    {
      name: t('send_invoice_whatsapp'),
      id: 'send_invoice_whatsapp',
      icon: 'invoice',
      event: sendInvoiceWhatsApp,
    },
  ]
})
function downloadPdfInvioce(id: String) {
  invoiceProcessing.value = true
  previewInvoicePdf(id as string).finally(() => {
    invoiceProcessing.value = false
  })
}
function openInvoicePdf(id: String) {
  window.open(`/print-invoice/${props.order_id}`, '_blank')
}

function sendInvoice(id: String) {
  invoiceProcessing.value = true
  sendInvoicePdf(id as string).finally(() => {
    invoiceProcessing.value = false
  })
}

function sendInvoiceWhatsApp(id: String) {
  invoiceProcessing.value = true
  sendWhatsAppInvoice(id as string).finally(() => {
    invoiceProcessing.value = false
  })
}

const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('invoice.invoice_number'),
    },
    {
      title: t('invoice.created_at'),
    },
    {
      title: t('invoice.created_by'),
    },
    {
      title: t('invoice.type'),
    },
    {
      title: t('invoice.amount'),
    },
    {
      title: t('pos.print'),
    },
  ]
})
</script>

<template>
  <div
    class="flex flex-col text-black rounded-lg border border-gray-200 text-md"
  >
    <h3 class="flex justify-between items-center p-2 mb-2 bg-gray-100">
      <span class="flex gap-2 items-center font-medium">
        <icons name="summary" aria-hidden="true" color="#26405B" />
        {{ $t("invoices") }}
      </span>
    </h3>

    <div class="">
      <GenericTable
        :data="invoices"
        :headers="headers"
        tr-class="cursor-pointer hover:bg-green-200"
        item-key="id"
      >
        <template #row="{ item }">
          <grid-td> # {{ item.invoice_number }} </grid-td>
          <grid-td>
            {{ formatDateAndTime(item.created_at) }}
          </grid-td>
          <grid-td>
            {{ item.created_by }}
          </grid-td>
          <grid-td>
            {{
              item.type == "invoice"
                ? t("invoice.invoice")
                : t("invoice.refund")
            }}
          </grid-td>
          <grid-td>
            {{ item.amount }} {{ userInfo?.tenant?.currency }}
          </grid-td>
          <grid-td>
            <div class="w-1/2">
              <Listbox as="div" model-value="" class="relative w-full">
                <div class="">
                  <ListboxButton
                    class="flex justify-between items-center px-3 py-1 bg-sky-500 rounded-lg border border-gray-200 shadow-md cursor-pointer text-start focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <TheLoader v-if="invoiceProcessing" />
                    <span
                      v-else
                      class="flex items-center rtl:left-auto rtl:right-0"
                    >
                      <icons name="print" color="#FFF" />
                    </span>
                  </ListboxButton>

                  <transition
                    enter-active-class="transition duration-100 ease-out"
                    enter-from-class="opacity-0 transform scale-95"
                    enter-to-class="opacity-100 transform scale-100"
                    leave-active-class="transition duration-75 ease-out"
                    leave-from-class="opacity-100 transform scale-100"
                    leave-to-class="opacity-0 transform scale-95"
                  >
                    <ListboxOptions
                      class="overflow-auto absolute z-10 py-1 mt-1 w-56 max-h-56 text-base bg-white rounded-md ring-1 ring-black ring-opacity-5 shadow-lg focus:outline-none sm:text-sm"
                      :style="getLocale(locale)?.direction === 'ltr' ? 'right: 0;' : 'left: 0;'"
                    >
                      <ListboxOption
                        v-for="option in printOptions"
                        :key="option.id"
                        v-slot="{ active, selected }"
                        as="template"
                        :value="option"
                        @click="option.event(item.id as string)"
                      >
                        <li
                          class="relative cursor-pointer  select-none py-2 px-2" :class="[
                            active
                              ? 'bg-gray-600 text-white'
                              : 'text-gray-900',
                          ]"
                        >
                          <div
                            class="flex gap-3 justify-start items-center"
                          >
                            <span
                              class="flex justify-center items-center mx-1 w-44 h-6 text-gray-400 md:w-6 md:h-6"
                            >
                              <icons :name="option.icon" />
                            </span>
                            <span
                              class=" text-xs sm:text-sm" :class="[
                                selected ? 'font-semibold' : 'font-normal',
                              ]"
                            >
                              {{ option.name }}
                            </span>
                          </div>

                          <span
                            v-if="selected"
                            class="relative inset-y-0 right-0 flex items-center pr-4" :class="[
                              active ? 'text-white' : 'text-indigo-600',
                            ]"
                          >
                            <CheckIcon class="w-5 h-5" aria-hidden="true" />
                          </span>
                        </li>
                      </ListboxOption>
                    </ListboxOptions>
                  </transition>
                </div>
              </Listbox>
            </div>
          </grid-td>
        </template>
      </GenericTable>
    </div>
  </div>
</template>
