<script setup lang="ts">
import type { ComputedRef, PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'
import { ChevronUpIcon } from '@heroicons/vue/20/solid'
import { ref } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'
import { formatDateAndTime } from '@/composables/dateFormat'
import type { header } from '@/types'
import SentNotifications from '@/services/SentNotificationSercices'
import useNotifications from '@/composables/useNotifications'
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue'

defineProps({
  notifications: {
    type: Object,
    required: true,
  },
})
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const { t } = useI18n()
const { showNotification } = useNotifications()

const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('notification_body'),
    },
    {
      title: t('channel'),
    },
    {
      title: t('receiver_type'),
    },
    {
      title: t('notification_create_at'),
    },

  ]
})

const showModal = ref(false)
const selectedNotification = ref(null)
const resendingId = ref(null)

function openNotificationModal(item) {
  selectedNotification.value = item
  showModal.value = true
}
function closeNotificationModal() {
  showModal.value = false
  selectedNotification.value = null
}

async function resendNotification(item) {
  resendingId.value = item.id
  try {
    await SentNotifications.retry(item.id)
    showNotification({
      title: t('Success'),
      type: 'success',
      message: t('operations.retried'),
    })
    item.was_sent = true
  }
  catch (e) {
    showNotification({
      title: t('Error'),
      type: 'error',
      message: t('operations.error'),
    })
  }
  finally {
    resendingId.value = null
  }
}
</script>

<template>
  <DisclosureWrapper :title="$t('altNav.notifications')">
    <template #default>
      <div v-if="notifications" class="w-full min-w-[600px] md:min-w-0">
        <div class="flex flex-col">
          <div class="flex sticky top-0 z-10 w-full text-xs border-b border-gray-200 bg-stone-50">
            <div class="px-2 py-3 w-1/2 md:px-4 text-start text-neutral-500">
              {{ $t("notification_body") }}
            </div>
            <div class="px-2 py-3 w-1/6 md:px-4 text-start text-neutral-500">
              {{ $t("channel") }}
            </div>
            <div class="px-2 py-3 w-1/6 md:px-4 text-start text-neutral-500">
              {{ $t("receiver_type") }}
            </div>
            <div class="px-2 py-3 w-1/6 md:px-4 text-start text-neutral-500">
              {{ $t("notification_create_at") }}
            </div>
          </div>
          <div class="overflow-y-auto" style="max-height: 200px;">
            <div v-for="item in notifications" :key="item.id" class="flex flex-nowrap items-center w-full text-sm border-b border-gray-200 hover:bg-[#F6F7FF] cursor-pointer group">
              <div class="px-2 py-3 w-1/2 md:px-4 text-start text-neutral-800 truncate" :title="item.body" @click="openNotificationModal(item)">
                {{ item.body }}
              </div>
              <div class="px-2 py-3 w-1/6 md:px-4 text-start text-neutral-800">
                {{ $t(`form.${item.channel}`) }}
              </div>
              <div class="px-2 py-3 w-1/6 md:px-4 text-start text-neutral-800">
                {{ $t(item.receiver_type) }}
              </div>
              <div class="px-2 py-3 w-1/6 md:px-4 text-start text-neutral-800">
                <template v-if="item.was_sent">
                  {{ formatDateAndTime(item.created_at) }}
                </template>
                <template v-else>
                  <button
                    class="bg-[#EDF0FF] text-primary-900 px-2 py-1 rounded border border-gray-200 text-xs font-medium hover:bg-primary-100 disabled:opacity-50"
                    :disabled="resendingId === item.id"
                    @click.stop="resendNotification(item)"
                  >
                    <span v-if="resendingId === item.id">{{ $t('loading') }}</span>
                    <span v-else>{{ $t('notification_resend') }}</span>
                  </button>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="py-6 text-sm text-center text-gray-500">
        {{ $t('no_notifications') }}
      </div>
    </template>
  </DisclosureWrapper>

  <!-- Notification Modal -->
  <div v-if="showModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
    <div class="bg-white rounded-xl shadow-xl p-6 max-w-2xl w-full relative">
      <button class="absolute left-4 top-4 text-gray-400 hover:text-gray-600" @click="closeNotificationModal">
        <XMarkIcon class="w-6 h-6" />
      </button>
      <div class="text-center text-xl font-bold mb-4">
        {{ $t('altNav.notifications') }}
      </div>
      <div class="text-sm text-gray-800 whitespace-pre-line break-words text-center mb-2">
        {{ selectedNotification?.body }}
      </div>
      <div class="text-xs text-gray-500 text-center mt-2">
        <template v-if="selectedNotification?.was_sent">
          {{ selectedNotification?.created_at ? formatDateAndTime(selectedNotification.created_at) : '' }}
        </template>
        <template v-else>
          <button
            class="bg-[#EDF0FF] text-primary-900 px-2 py-1 rounded border border-gray-200 text-xs font-medium hover:bg-primary-100 disabled:opacity-50"
            :disabled="resendingId === selectedNotification?.id"
            @click="resendNotification(selectedNotification)"
          >
            <span v-if="resendingId === selectedNotification?.id">{{ $t('loading') }}</span>
            <span v-else>{{ $t('notification_resend') }}</span>
          </button>
        </template>
      </div>
    </div>
  </div>
</template>
