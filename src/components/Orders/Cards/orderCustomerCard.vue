<script lang="ts" setup>
import type { PropType } from 'vue'
import { LockClosedIcon, PencilSquareIcon } from '@heroicons/vue/24/outline'
import type { OrderCustomer } from '@/types'

defineProps({
  customer: {
    type: Object as PropType<OrderCustomer>,
    required: true,
  },
  orderId: {
    type: String,
    required: true,
  },
  editable: {
    type: Boolean,
    default: false,
  },
  pin_code: {
    type: String,
    default: '',
  },
})
const router = useRouter()
const openAssignModal = ref(false)
const emailCopyActivate = ref(false)
const emailCopy = (text: string) => {
  navigator.clipboard.writeText(text)
  emailCopyActivate.value = true
  setTimeout(() => {
    emailCopyActivate.value = false
  }, 1000)
}
const copyPinCode = (pinCode) => {
  navigator.clipboard.writeText(pinCode)
}
</script>

<template>
  <modal-assign-customer-to-order
    v-if="openAssignModal"
    :is-open="openAssignModal"
    :customer-selected="customer"
    :order-id="orderId"
    @close="openAssignModal = false"
  />
  <div class="flex flex-col text-black border border-gray-200 rounded-lg text-md">
    <h3 class="flex items-center justify-between p-2 mb-2 text-gray-400 bg-gray-100">
      {{ $t("Customer") }}
      <PencilSquareIcon
        v-if="editable"
        class="inline-block w-5 h-5 text-black cursor-pointer" @click="openAssignModal = true"
      />
    </h3>
    <div class="flex items-start justify-start w-full gap-5 p-6 truncate xl:flex-row sm:flex-col">
      <img
        v-if="customer.photo"
        class="flex-shrink-0 w-16 h-16 bg-gray-300 rounded-full"
        :src="customer.photo"
        alt="customer_image"
      >
      <svg
        v-else
        class="flex-shrink-0 w-16 h-16 bg-gray-300 rounded-full"
        fill="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"
        />
      </svg>
      <div class="truncate">
        <h3 v-if="customer.uuid" class="mb-4 text-lg font-semibold text-blue-500 underline truncate cursor-pointer" @click="$router.push({ name: 'customer', params: { id: customer.uuid } })">
          {{ `${customer?.first_name} ${customer?.last_name || ''}` }}
        </h3>
        <h3 v-else class="mb-4 text-lg font-semibold text-black truncate ">
          {{ $t('pos.guest') }}
        </h3>
        <p class="mb-3 font-medium text-black truncate text-md">
          {{ customer.phone }}
        </p>
        <div class="flex flex-wrap items-center gap-3 mb-3">
          <a
            v-if="customer.phone"
            class="flex items-center justify-center px-2 py-1 bg-gray-100 rounded-full w-9 h-9"
            :href="`https://wa.me/${customer.phone}`"
            target="_blank"
          >
            <Icons name="whatsapp" aria-hidden="true" />
          </a>
          <span
            class="relative flex items-center justify-center px-2 py-1 bg-gray-100 rounded-full cursor-pointer w-9 h-9"
            @click="emailCopy(customer.email)"
          >
            <Icons name="copy" class="inline-block w-6 h-6" aria-hidden="true" />
            <div
              class="absolute flex flex-col items-center bottom-full w-max"
              :class="{ hidden: !emailCopyActivate }"
            >
              <span
                class="relative z-50 p-2 text-xs font-medium leading-none shadow-lg bg-primary-100 text-primary space-no-wrap"
              >
                {{ $t("copied_email") }}
              </span>
              <div class="w-3 h-3 -mt-2 rotate-45 bg-primary-100" />
            </div>
          </span>
          <a
            v-if="customer.email"
            class="flex items-center justify-center px-2 py-1 bg-gray-100 rounded-full cursor-pointer w-9 h-9"
            :href="`mailto:${customer.email}`"
          >
            <Icons name="sms" class="inline-block w-6 h-6" aria-hidden="true" />
          </a>
          <a
            v-if="customer.phone"
            class="flex items-center justify-center px-2 py-1 bg-gray-100 rounded-full w-9 h-9"
            :href="`tel:${customer.phone}`"
          >
            <Icons name="phone" class="inline-block w-6 h-6" aria-hidden="true" />
          </a>
        </div>
        <div class="flex items-center gap-2">
          <span class="flex items-center justify-center w-8 h-8 py-1">
            <Icons name="shopCart" aria-hidden="true" />
          </span>
          <span class="text-sm text-gray-400">{{ customer.orders_count }}
            {{ $t("orders_completed") }}
          </span>
        </div>
        <div v-if="pin_code" class="flex items-center text-sm text-gray-400 gap-1">
          <span class="flex items-center justify-center w-8 h-8 py-1">
            <LockClosedIcon class="inline-block w-5 h-5 text-black cursor-pointer" />
          </span>
          <span>
            {{ `${$t("order_pin_code")} : ${pin_code}` }}
          </span>
          <span
            class="relative flex items-center justify-center px-2 py-1  rounded-full cursor-pointer w-9 h-9"
            @click="copyPinCode(pin_code)"
          >
            <Icons name="copy" class="inline-block w-6 h-6" aria-hidden="true" />
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
