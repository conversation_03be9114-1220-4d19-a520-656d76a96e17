<script lang="ts" setup>
import { ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { uniqueId } from 'lodash'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  modelValue: {
    type: Array,
    required: true,
  },
  team: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue'])

const { t } = useI18n()
const { fetchProducts } = useProductStore()
const { getUserInfo } = storeToRefs(useAuthStore())

const products = ref([])
const loading = ref(false)

const searchProducts = async (search) => {
  loading.value = true
  try {
    const { data } = await fetchProducts({
      page: '*',
    })
    products.value = data.data
  }
  finally {
    loading.value = false
  }
}

const addProduct = () => {
  const items = [...props.modelValue]
  items.push({
    id: '',
    quantity: 1,
    discount_amount: 0,
    price: 0,
    unit_amount: 0,
    total_amount: 0,
    key: uniqueId(),
    service_type: 'product',
  })
  emit('update:modelValue', items)
}

const removeProduct = (index) => {
  const items = [...props.modelValue]
  items.splice(index, 1)
  emit('update:modelValue', items)
}

const updateProductItem = (index, product) => {
  const items = [...props.modelValue]
  items[index].id = product.uuid
  items[index].unit_amount = Number(product.price)
  items[index].total_amount = Number(product.price) * items[index].quantity
  items[index].service_type = 'product'
  emit('update:modelValue', items)
}

const updateQuantity = (index, quantity) => {
  const items = [...props.modelValue]
  items[index].quantity = quantity
  items[index].total_amount = items[index].unit_amount * quantity - items[index].discount_amount
  emit('update:modelValue', items)
}

const updateDiscount = (index, discount) => {
  const items = [...props.modelValue]
  items[index].discount_amount = discount
  items[index].total_amount = items[index].unit_amount * items[index].quantity - discount
  emit('update:modelValue', items)
}
</script>

<template>
  <div class="space-y-4">
    <div class="flex justify-between items-center">
      <h3 class="text-lg font-medium">
        {{ $t('products') }}
      </h3>
    </div>

    <div v-for="(item, index) in modelValue.filter(item => item.service_type === 'product')" :key="item.key" class="p-4 rounded-lg border border-gray-200">
      <div class="grid grid-cols-12 gap-4">
        <!-- Product Selection -->
        <div class="col-span-12 sm:col-span-5">
          <label class="block mb-1 text-sm font-medium text-gray-700">
            {{ $t('product') }}
          </label>
          <AutoCompleteInput
            :items="products"
            :loading="loading"
            :model-value="item.id"
            display-key="name"
            value-key="uuid"
            placeholder="Search products..."
            @search="searchProducts"
            @select="product => updateProductItem(index, product)"
          />
        </div>

        <!-- Quantity -->
        <div class="col-span-6 sm:col-span-2">
          <label class="block mb-1 text-sm font-medium text-gray-700">
            {{ $t('quantity') }}
          </label>
          <NumberInput
            :model-value="item.quantity"
            :min="1"
            class="w-full"
            @update:model-value="val => updateQuantity(index, val)"
          />
        </div>

        <!-- Discount -->
        <div class="col-span-6 sm:col-span-2">
          <label class="block mb-1 text-sm font-medium text-gray-700">
            {{ $t('discount') }}
          </label>
          <NumberInput
            :model-value="item.discount_amount"
            :min="0"
            class="w-full"
            @update:model-value="val => updateDiscount(index, val)"
          />
        </div>

        <!-- Price Display -->
        <div class="col-span-9 sm:col-span-2">
          <label class="block mb-1 text-sm font-medium text-gray-700">
            {{ $t('total') }}
          </label>
          <div class="flex items-center px-3 h-10 text-gray-700 bg-gray-100 rounded-md border border-gray-300">
            <price-format
              :form-data="{
                price: item.total_amount || 0,
                currency: getUserInfo?.tenant?.currency || '',
              }"
            />
          </div>
        </div>

        <!-- Remove Button -->
        <div class="flex col-span-3 items-end sm:col-span-1">
          <button
            type="button"
            class="flex justify-center items-center w-10 h-10 text-red-500 bg-red-100 rounded-md hover:bg-red-200 focus:outline-none"
            @click="removeProduct(index)"
          >
            <span class="sr-only">{{ $t('remove') }}</span>
            <i class="fas fa-trash" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
