<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import type { PropType } from 'vue'
import { PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'
import {
  maxValue,
  minValue,
  required,
  requiredIf,
} from '@/utils/i18n-validators'
import type { BookingService, Service, Staff } from '@/types'

const props = defineProps({
  modelValue: {
    type: Object as PropType<BookingService>,
    default: () => ({
      id: '',
      price: 0,
    }),
  },
  name: {
    type: String,
    required: true,
  },
  staffList: {
    type: Array as PropType<Staff[]>,
    required: true,
  },
  hideRemoveIcon: {
    type: Boolean,
    default: false,
  },
  showAddIcon: {
    type: Boolean,
    default: false,
  },
  index: {
    type: Number,
  },
})
const emit = defineEmits(['update:modelValue', 'set-processing'])
const { fetchStaffServices } = useStaffStore()
const { fetchServiceById } = useServicesStore()
const servicesOptions = ref([])
const staffs = ref<Staff[]>([])
const { modelValue } = toRefs(props)
const maxDiscount = computed(() => {
  return modelValue.value.unit_amount * modelValue.value.quantity
})
const rules = {
  id: {
    required,
  },
  staffId: {
    required,
  },

  price: {
    required,
    minValue: minValue(0),
  },
  quantity: {
    required,
    minValue: minValue(1),
  },
  duration: {
    required,
    minValue: minValue(1),
  },
  discount_amount: {
    required,
    minValue: minValue(0),
    maxValue: maxValue(maxDiscount),
  },
  total_amount: {
    required,
    minValue: minValue(0),
  },
}

const v$ = useVuelidate(rules, modelValue)
const updateValue = (value: BookingService) => {
  emit('update:modelValue', value)
}
const selectService = (uuid: string) => {
  const selectedService = servicesOptions.value.find(
    service => service.uuid === uuid,
  )

  if (selectedService?.uuid == modelValue.value.id)
    return
  updateValue({
    ...modelValue.value,
    id: selectedService?.uuid ?? '',
    quantity: selectedService?.quantity || 1,
    discount_amount: selectedService?.discount_amount || 0,
    price: selectedService?.price || 0,
    unit_amount: selectedService?.price || 0,
    duration: selectedService?.duration || 1,
    total_amount:
      (selectedService?.price || 0) * (selectedService?.quantity || 1) || 0,
  })
}
const changeQuantity = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...modelValue.value,
    quantity: +event.target.value,
    total_amount:
      +event.target.value * (props.modelValue?.unit_amount || 0)
        - modelValue.value?.discount_amount || 0,
  })
}
const changePricePerUnit = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...modelValue.value,
    unit_amount: +event.target.value,
    total_amount:
      +event.target.value * (modelValue.value?.quantity || 0)
        - modelValue.value?.discount_amount || 0,
  })
}
const changePricePerDiscount = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...modelValue.value,
    discount_amount: event.target.value,
    total_amount:
      (modelValue.value?.unit_amount || 0) * (modelValue.value?.quantity || 0)
      - event.target.value,
  })
}
const changeDuration = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...modelValue.value,
    duration: event.target.value,
  })
}
watch(
  () => modelValue.value?.staffId,
  (val, oldVal) => {
    if (val == oldVal)
      return
    if (val) {
      emit('set-processing', true)
      fetchStaffServices(val, '*')
        .then((data) => {
          servicesOptions.value = data.data
            .filter(service => service.canServe)
            .map((service) => {
              return {
                ...service,
                value: service.uuid,
                label: service.name,
              }
            })
          if (props.modelValue.id) {
            const selectedService = servicesOptions.value.find(
              service => service.uuid === props.modelValue.id,
            )
            if (selectedService.uuid)
              changeDuration({ target: { value: selectedService.duration } })
          }
        })
        .finally(() => {
          emit('set-processing', false)
        })
    }
    else {
      servicesOptions.value = []
    }
  },
  { immediate: true, deep: true },
)
const staffsOptions = computed(() => {
  return props.staffList.map((staff) => {
    return {
      ...staff,
      id: staff.uuid,
      name: staff.name,
    }
  })
})
const selectStaff = (uuid: string) => {
  updateValue({
    ...modelValue.value,
    staffId: uuid,
    id: '',
  })
}
const showDiscount = (key: number) => {
  updateValue({
    ...modelValue.value,
    showDiscount: true,
  })
}

const hideDiscount = (key: number) => {
  updateValue({
    ...modelValue.value,
    discount_amount: 0,
    total_amount:
      (modelValue.value?.unit_amount || 0) * (modelValue.value?.quantity || 0)
      - event.target.value,
    showDiscount: false,
  })
}
</script>

<template>
  <div class="grid grid-cols-1 gap-2.5 mt-3 lg:grid-cols-12">
    <div class="lg:col-span-3 text-start">
      <form-group :validation="v$" name="staffId">
        <template #default="{ attrs }">
          <SelectInput
            v-bind="attrs"
            id="staff_id"
            :model-value="modelValue.staffId"
            :label="$t('form.staff')"
            required
            @update:model-value="selectStaff($event)"
          >
            <option
              v-for="staff in staffsOptions"
              :key="staff?.id"
              :value="staff?.id"
            >
              {{ staff.name }}
            </option>
          </SelectInput>
        </template>
      </form-group>
    </div>
    <div class="lg:col-span-3 text-start">
      <form-group :validation="v$" name="id">
        <template #default="{ attrs }">
          <BaseComboBox
            v-bind="attrs"
            :id="`service-${name}`"
            :model-value="modelValue.id"
            place-holder="serviceName"
            required
            arial-label="Search"
            :disabled="!servicesOptions.length"
            :error="v$.$dirty && v$.id.$error"
            :options="servicesOptions"
            :label="$t('form.service')"
            @update:model-value="selectService($event)"
          >
            {{ $t("booking.service") }}
          </BaseComboBox>
        </template>
      </form-group>
      <p v-if="!servicesOptions.length" class="error-message">
        {{ $t("booking.no_service") }}
      </p>
    </div>
    <div class="lg:col-span-2 text-start">
      <form-group
        :validation="v$"
        name="duration"
        :error-name="`services.${index}.duration`"
      >
        <template #default="{ attrs }">
          <NumberInput
            v-bind="attrs"
            :id="`duration-${name}`"
            v-model="modelValue.duration"
            :label="$t('duration')"
            min="1"
            :placeholder="$t('duration')"
            @change="changeDuration"
          />
        </template>
      </form-group>
    </div>
    <div class="lg:col-span-1 text-start">
      <form-group
        :validation="v$"
        name="quantity"
        :error-name="`services.${index}.quantity`"
      >
        <template #default="{ attrs }">
          <NumberInput
            v-bind="attrs"
            :id="`quantity-${name}`"
            v-model="modelValue.quantity"
            :label="$t('quantity')"
            min="1"
            :placeholder="$t('quantity')"
            @change="changeQuantity"
          />
        </template>
      </form-group>
    </div>
    <div class="lg:col-span-1 text-start">
      <form-group
        :validation="v$"
        name="pricePerUnit"
        :error-name="`services.${index}.unit_amount`"
      >
        <template #default="{ attrs }">
          <NumberInput
            v-bind="attrs"
            :id="`pricePerUnit-${name}`"
            v-model="modelValue.unit_amount"
            :label="$t('pricePerUnit')"
            min="1"
            :placeholder="$t('pricePerUnit')"
            @change="changePricePerUnit"
          />
        </template>
      </form-group>
      <button
        v-if="!modelValue.showDiscount"
        class="text-sm cursor-pointer text-primary"
        @click="showDiscount(key)"
      >
        {{ $t("add_discount") }}
      </button>
      <button
        v-else
        class="text-sm cursor-pointer text-primary"
        @click="hideDiscount(key)"
      >
        {{ $t("hide_discount") }}
      </button>
      <div
        v-if="modelValue.showDiscount || modelValue.discount_amount"
        class="lg:col-span-1 text-start"
      >
        <form-group
          :validation="v$"
          name="discount_amount"
          :error-name="`services.${index}.discount_amount`"
        >
          <template #default="{ attrs }">
            <NumberInput
              v-bind="attrs"
              :id="`serviceDiscount-${name}`"
              :label="$t('discount_amount')"
              min="1"
              :model-value="modelValue.discount_amount"
              :placeholder="$t('discount_amount')"
              @change="changePricePerDiscount"
            />
          </template>
        </form-group>
      </div>
    </div>
    <div class="lg:col-span-1 text-start">
      <form-group
        :validation="v$"
        name="total_amount"
        :error-name="`services.${index}.total_amount`"
      >
        <template #default="{ attrs }">
          <NumberInput
            v-bind="attrs"
            :id="`servicePrice-${name}`"
            :key="`servicePrice-${name}`"
            v-model="modelValue.total_amount"
            readonly
            :label="$t('total')"
            @blur="attrs.onblur"
          />
        </template>
      </form-group>
    </div>
    <div class="flex justify-center items-center lg:col-span-1 text-start">
      <div class="flex gap-2 justify-center items-center">
        <button
          v-if="hideRemoveIcon"
          type="button"
          class="inline-flex h-full bg-gray-100 rounded-md border-2 border-transparent cursor-pointer hover:text-gray-500 focus:outline-none hover:scale-105"
          @click="emit('removeService')"
        >
          <TrashIcon class="w-6 h-6 text-red-800" aria-hidden="true" />
        </button>
        <button
          v-if="showAddIcon"
          type="button"
          class="flex text-gray-400 bg-gray-100 rounded-md border-2 border-transparent cursor-pointer hover:scale-105"
          @click="emit('addNewService')"
        >
          <PlusIcon class="w-6 h-6 text-primary-800" aria-hidden="true" />
        </button>
      </div>
    </div>
  </div>
</template>
