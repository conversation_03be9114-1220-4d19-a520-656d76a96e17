<script lang="ts" setup>
import type { ComputedRef, PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { CalendarDaysIcon, ChevronUpIcon, PlusIcon } from '@heroicons/vue/20/solid'
import { PencilIcon } from '@heroicons/vue/24/outline'
import { Disclosure, DisclosureButton, DisclosurePanel, Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import dayjs from 'dayjs'
import TrashIcon from '@/components/Icons/TrashIcon.vue'
import { useEventStore } from '@/stores/event'

import type { OrderItems, header } from '@/types'
const props = defineProps({
  items: {
    type: Array as PropType<OrderItems[]>,
    required: true,
  },
})
const emit = defineEmits(['refresh'])
const { t } = useI18n()
const showBookingDetailsModal = ref(false)
const showSetAppointmentModal = ref(false)
const eventId = ref('')
const selectedItem = ref({})
const showAlert = ref(false)
const { getOrderDetails } = storeToRefs(useOrder())

// Define headers for the generic table
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.name'),
    },
    {
      title: t('fields.staff_id'),
    },

    {
      title: t('quantity'),
    },
    {
      title: t('price'),
    },
    {
      title: t('discount_amount'),
    },
    {
      title: t('total'),
    },
    {
      title: t('price_after_discount'),
    },
    {
      title: t('status'),
    },
    {
      title: '',
    },
  ]
})
const openBookingDetails = (item) => {
  showBookingDetailsModal.value = true
  eventId.value = item.booking.uuid
}
const closeBookingDetails = () => {
  showBookingDetailsModal.value = false
  eventId.value = ''
}
const openSetAppointment = (item) => {
  if (!getOrderDetails.value.customer.uuid) {
    showAlert.value = true
    return
  }
  console.log('setAppointment', item)
  showSetAppointmentModal.value = true
  selectedItem.value = item
}

const closeSetAppointment = () => {
  showSetAppointmentModal.value = false
  selectedItem.value = {}
}

const isOpenEditOrderItemsModal = ref(false)
const closeEditItemsModal = () => {
  isOpenEditOrderItemsModal.value = false
}

const showAddItemModal = ref(false)
const closeAddItemModal = () => {
  showAddItemModal.value = false
}

// Add confirmation modal state and item ID to delete
const showConfModal = ref(false)
const selectedItemId = ref('')

// Function to show confirmation modal
const showConfirmationModal = (itemId) => {
  selectedItemId.value = itemId
  showConfModal.value = true
}

const { deleteOrderItem, removeOrderDiscount } = useOrder()

// Delete API function for confirmation modal
const deleteOrderItemApi = async (itemId) => {
  return deleteOrderItem(getOrderDetails.value.id, itemId)
}

// Success handler after item is removed
const itemRemoved = () => {
  showConfModal.value = false
  emit('refresh')
}

// Original delete handler modified to use confirmation modal
const deleteItem = async (itemId) => {
  showConfirmationModal(itemId)
}

const eventStore = useEventStore()
const { updateBookingStatus, fetchEventById } = eventStore
const processing = ref(false)

const changeAppointmentStatus = async (bookingId, status) => {
  try {
    processing.value = true
    await updateBookingStatus(bookingId, status)
    // Emit an event to refresh the parent component
    emit('refresh')
  }
  catch (error) {
    console.error('Error updating booking status:', error)
  }
  finally {
    processing.value = false
  }
}

const openCancelModal = async (bookingId) => {
  try {
    processing.value = true
    // Fetch the booking/event data (assuming you have fetchEventById in your event store)
    const data = await fetchEventById(bookingId)
    bookingWillBeCanceled.value = data
    cancelModal.value = true
  }
  catch (error) {
    console.error('Failed to open cancel modal:', error)
  }
  finally {
    processing.value = false
  }
}

const cancelModal = ref(false)
const bookingWillBeCanceled = ref({})
const recurringModal = ref(false)
const selectedBookingForRecurring = ref(null)
const editModal = ref(false)
const selectedBookingForEdit = ref(null)
const showDiscountModal = ref(false)
const openEditModal = (bookingId) => {
  selectedBookingForEdit.value = bookingId
  editModal.value = true
}

const closeEditModal = () => {
  editModal.value = false
  selectedBookingForEdit.value = null
}

const openRecurringModal = (bookingId) => {
  selectedBookingForRecurring.value = bookingId
  recurringModal.value = true
}

const closeRecurringModal = () => {
  recurringModal.value = false
  selectedBookingForRecurring.value = null
}

const bookingCancelled = () => {
  // Implement the logic to refresh data after booking cancellation
  console.log('Booking cancelled')
  cancelModal.value = false
  emit('refresh')
}
const hasDiscount = computed(() => {
  return getOrderDetails.value?.summary?.discount_amount > 0
})
const removeDiscount = async () => {
  try {
    processing.value = true
    await removeOrderDiscount(getOrderDetails.value.id)
    emit('refresh')
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <div>
    <modal-order
      v-if="isOpenEditOrderItemsModal"
      :is-open="isOpenEditOrderItemsModal"
      :order-id="getOrderDetails.id"
      :items="
        getOrderDetails.items?.filter((item) => item.service_type == 'service')
      "
      :edit-mode="true"
      @close="closeEditItemsModal"
      @refresh="emit('refresh')"
    />
    <main-booking-modal
      v-if="showBookingDetailsModal"
      :is-open="showBookingDetailsModal"
      :item-id="eventId"
      @closed="closeBookingDetails"
      @start-loading="() => {}"
      @end-loading="() => {}"
      @refresh-events="emit('refresh')"
    />
    <modal-set-appointment
      v-if="showSetAppointmentModal"
      :is-open="showSetAppointmentModal"
      :selected-item="selectedItem"
      :order-id="getOrderDetails.id"
      @close="closeSetAppointment"
      @refresh="emit('refresh')"
    />
    <AlertModal
      v-if="showAlert"
      :is-open="showAlert"
      :message="$t('you_cant_set_appointment_for_order')"
      @close="showAlert = false"
    />
    <add-single-item-modal
      v-if="showAddItemModal"
      :is-open="showAddItemModal"
      :order-id="getOrderDetails.id"
      @close="closeAddItemModal"
      @refresh="emit('refresh')"
    />
    <!-- Confirmation Modal for Delete -->
    <confirmation-modal
      v-if="showConfModal"
      :is-open="showConfModal"
      :api-call="deleteOrderItemApi"
      :record-id="selectedItemId"
      @removed="itemRemoved"
      @closed="showConfModal = false"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.msg") }}
      </p>
    </confirmation-modal>

    <DisclosureWrapper :title="$t('items')">
      <template #header-actions>
        <div v-if="!getOrderDetails.invoices" class="flex justify-start w-full">
          <div class="flex flex-col gap-2 px-1 sm:flex-row">
            <LilActionBtn
              :icon="PlusIcon"
              @click="showAddItemModal = true"
            />
            <LilActionBtn
              v-if="hasDiscount"
              :icon="TrashIcon"
              :label="t('remove_discount')"
              @click="removeDiscount"
            />
            <LilActionBtn
              v-else
              :label="t('add_discount')"
              @click="showDiscountModal = true"
            />
          </div>
        </div>
      </template>

      <generic-table
        :is-loading="false"
        :data="items"
        :headers="headers"
        item-key="uuid"
        tr-class="hover:bg-gray-50"
      >
        <template #row="{ item }">
          <!-- Name Column -->
          <grid-td class="flex gap-2 items-center max-w-[400px]">
            <div class="flex gap-2 justify-start items-center w-full" :title="item?.name">
              <img v-if="item.photo" class="w-10 h-10 rounded-full" :src="item.photo">
              <svg v-else xmlns="http://www.w3.org/2000/svg" class="w-10" viewBox="0 0 24 24">
                <path fill="#e1e1e1" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z" />
              </svg>
              <div class="flex flex-col gap-2 justify-start items-start grow">
                <span :title="item?.name" class="whitespace-normal text-neutral-800">
                  {{ item?.name }}
                </span>
                <div
                  v-if="item.service_type == 'service' && item.booking"
                  class="flex flex-grow gap-2 justify-center items-center"
                >
                  <span>
                    {{ dayjs(item.booking.start).format("dddd") }}
                  </span>
                  <span>
                    {{ dayjs(item.booking.start).format("DD/MM/YYYY") }}
                  </span>
                  <p dir="auto" class="">
                    <span>
                      {{ dayjs(item.booking.start).format("hh:mm A") }} --
                    </span>
                    <span>
                      {{ dayjs(item.booking.end).format("hh:mm A") }}
                    </span>
                  </p>
                  <span>
                    <BookingStatus :book-status="item.booking.status" />
                  </span>
                </div>
              </div>
            </div>
          </grid-td>

          <!-- Staff ID Column -->
          <grid-td class="text-start max-w-[200px]" :title="item.booking ? item.provider?.name : '-'">
            <p v-if="item.booking" class="truncate">
              {{ item.provider?.name }}
            </p>
            <p v-else class="">
              -
            </p>
          </grid-td>

          <!-- Quantity Column -->
          <grid-td class="text-start">
            <p class="">
              {{ item?.quantity || '-' }}
            </p>
          </grid-td>

          <!-- Price Column -->
          <grid-td class="text-start">
            <p class="">
              {{ item?.unit_amount || '-' }}
            </p>
          </grid-td>

          <!-- Discount Amount Column -->
          <grid-td class="text-start">
            <p class="">
              {{ item?.discount_amount || '-' }}
            </p>
          </grid-td>

          <!-- Total Column -->
          <grid-td class="text-start">
            <p class="">
              {{ item?.price_after_discount || '-' }}
            </p>
          </grid-td>

          <!-- Status Column -->
          <grid-td class="text-start">
            <template v-if="item.booking">
              <div class="mx-1">
                <BookingStatusMenu :status="item.booking.status" :booking-uuid="item.booking.uuid" @change-status="changeAppointmentStatus" />
              </div>
            </template>
            <template v-else>
              <div v-if="item.service_type == 'service' && getOrderDetails.type !== 'refund'" class="mx-1">
                <button
                  class="flex items-start gap-2 px-4 py-1.5 rounded-full transition border border-transparent focus:outline-none bg-[#ffeaea] hover:bg-[#ffd2d2]"
                  @click="openSetAppointment(item)"
                >
                  <span class="text-sm text-red-700 whitespace-nowrap" :title="$t('set_an_appointment')">
                    {{ $t('set_an_appointment') }}
                  </span>
                  <CalendarDaysIcon class="w-4 h-4 text-red-600" />
                </button>
              </div>
              <template v-else>
                <span class="">-</span>
              </template>
            </template>
          </grid-td>

          <!-- Actions Column -->
          <grid-td v-if="true" class="text-start">
            <BookingActionsMenu
              :item="item"
              :get-order-details="getOrderDetails"
              :items="items"
              @delete-item="deleteItem"
              @cancel-booking="openCancelModal"
              @open-recurring-modal="openRecurringModal"
              @open-edit-modal="openEditModal"
            />
          </grid-td>
        </template>
      </generic-table>
    </DisclosureWrapper>
  </div>
  <cancel-booking-modal
    v-if="cancelModal"
    :is-open="cancelModal"
    :booking="bookingWillBeCanceled"
    @closed="cancelModal = false"
    @refresh="bookingCancelled"
  />
  <Appointment-recurring-modal
    v-if="recurringModal"
    :is-open="recurringModal"
    :appointment-id="selectedBookingForRecurring"
    @close="closeRecurringModal"
    @refresh="emit('refresh')"
  />
  <BookingModal
    v-if="editModal"
    :is-open="editModal"
    :event-id="selectedBookingForEdit"
    class="z-[100]"
    @closed="closeEditModal"
    @updated="emit('refresh')"
  />
  <OrderDiscountModal
    :is-open="showDiscountModal"
    :order-id="getOrderDetails.id"
    :items="getOrderDetails.items || []"
    @close="showDiscountModal = false"
    @refresh="emit('refresh')"
  />
</template>
