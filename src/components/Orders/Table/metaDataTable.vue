<script lang="ts" setup>
import type { ComputedRef, PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { PencilIcon } from '@heroicons/vue/24/outline'
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'
import { ChevronUpIcon } from '@heroicons/vue/20/solid'
import type { OrderItems, header } from '@/types'
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue'
const props = defineProps({
  items: {
    type: Array as PropType<OrderItems[]>,
    required: true,
  },
  orderId: {
    type: String as PropType<string>,
    required: true,
  },
  teamId: {
    type: String as PropType<string>,
    required: true,
  },
})
const emits = defineEmits(['update'])
const { updateOrderMetaData } = useOrder()
const { getLocale } = storeToRefs(useLocalesStore())
const { teamId, items } = toRefs(props)
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.label'),
    },
    {
      title: t('fields.value'),
    },
  ]
})
const { t, locale } = useI18n()

const showMetaDataModal = ref(false)
const formData = reactive({})
const { fetchTeamMetaData } = useMetaDataStore()
const { getTeamMetaData } = storeToRefs(useMetaDataStore())
const processing = ref(false)

onBeforeMount(async () => {
  await fetchTeamMetaData(props.teamId)
})

const metaDataFieldsWithFullData = computed(() => {
  return getTeamMetaData.value.map((item) => {
    const customField = items.value.find(
      i => i.backend_name === item.backend_name,
    )
    return { ...item, ...customField }
  })
})

const openCustomFieldsModal = () => {
  // render the custom fields in the modal
  getTeamMetaData.value.forEach((item) => {
    const customField = items.value.find(
      i => i.backend_name === item.backend_name,
    )
    if (customField)
      formData[item.backend_name] = customField.value
    else
      formData[item.backend_name] = ''
  })
  showMetaDataModal.value = true
}

const updateMetaDataFields = async () => {
  try {
    processing.value = true
    await updateOrderMetaData(props.orderId, formData)
  }
  catch (error) {
  }
  finally {
    processing.value = false
  }
  showMetaDataModal.value = false
  emits('update')
}

const updateSingleMetaDataField = ({
  key,
  value,
}: {
  key: string
  value: unknown
}) => {
  formData[key] = value
}
</script>

<template>
  <modal
    :show="showMetaDataModal"
    :dir="getLocale(locale)?.direction"
    panel-classes="lg:!w-8/12	sm:!w-9/12	 "
    @close="showMetaDataModal = false"
  >
    <div class="relative w-full">
      <overlay-loader v-if="processing" :full-screen="false" />
      <OrderMetaDataFields
        :model-value="formData"
        :meta-data-fields="metaDataFieldsWithFullData"
        @update:model-value="updateSingleMetaDataField"
      />
      <div class="flex justify-center items-center">
        <base-button
          class="flex justify-center items-center px-4 py-2 my-5 text-sm font-medium text-white rounded-md transition duration-100 ease-in cursor-pointer focus:transform active:scale-95"
          :disabled="processing"
          :loading="processing"
          :default-style="false"
          custome-bg="bg-blue-500"
          @click="updateMetaDataFields"
        >
          {{ $t("form.update") }}
        </base-button>
      </div>
    </div>
  </modal>

  <DisclosureWrapper :title="$t('additional_info')">
    <!-- No header actions in this case -->
    <template #default>
      <div class="w-full min-w-[600px] md:min-w-0 overflow-x-auto pt-4">
        <!-- Edit button -->

        <!-- Table header -->
        <div class="flex w-full text-xs border-b border-gray-200 bg-stone-50">
          <div class="px-2 py-3 w-1/2 md:px-4 text-start text-neutral-500">
            {{ $t("form.label") }}
          </div>
          <div class="px-2 py-3 w-1/2 md:px-4 text-start text-neutral-500">
            {{ $t("fields.value") }}
          </div>
        </div>

        <!-- Table rows -->
        <div v-for="item in items" :key="item.uuid || item.id" class="flex flex-nowrap items-center w-full text-sm border-b border-gray-200">
          <div class="px-2 py-3 w-1/2 md:px-4 text-start text-neutral-800">
            <span v-if="item?.label">
              <label class="block text-sm font-medium text-neutral-500 w-15">
                {{ item.label }}
              </label>
            </span>
            <span v-else class="text-zinc-400"> - </span>
          </div>
          <div class="px-2 py-3 w-1/2 md:px-4 text-start text-neutral-800">
            <div v-if="item.type === 'file'" class="relative items-center focus-within:z-10">
              <a
                class="px-3 py-1 text-sm text-white rounded shadow bg-primary-600 hover:bg-primary-400"
                target="_blank"
                :href="item.path"
              >
                {{ $t("Link") }}
              </a>
            </div>
            <div v-else-if="item.type !== 'location'" class="relative items-center focus-within:z-10">
              {{ item.value }}
            </div>
            <div v-else class="relative items-center focus-within:z-10">
              <a
                target="_blank"
                :href="`https://maps.google.com/?q=${item.value}`"
              >
                {{ $t("the_location") }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DisclosureWrapper>
</template>
