<script setup lang="ts">
import type { ComputedRef, PropType } from 'vue'
import { CheckCircleIcon, ExclamationTriangleIcon, LinkIcon, PlusIcon, XCircleIcon } from '@heroicons/vue/24/solid'
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'
import { ChevronUpIcon } from '@heroicons/vue/20/solid'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import { onClickOutside } from '@vueuse/core'
import { formatDateAndTime } from '@/composables/dateFormat'
import { usePosStore } from '@/stores/pos'
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue'
import CopyLinkIcon from '@/components/Icons/CopyLinkIcon.vue'
import type { OrderPayments, OrderTranscations, header } from '@/types'

const props = defineProps({
  transcations: {
    type: Array as PropType<OrderTranscations[]>,
    required: true,
  },
  paymentStatus: {
    type: String,
    default: 'unpaid',
  },

  orderId: {
    type: String,
    required: true,
  },
  currency: {
    type: String,
    default: '',
  },

})
const emits = defineEmits(['refresh'])
const { fetchSingleOrder, deleteTransaction } = useOrder()
const { getOrderDetails } = storeToRefs(useOrder())
const { t } = useI18n()
const showPaymentModal = ref(false)
const showTransactionModal = ref(false)
const selectedTransaction = ref<OrderTranscations>({} as OrderTranscations)
const copiedId = ref<string | null>(null)
const refreshInvoiceData = () => {
  emits('refresh')
}
const openTransactionDetailsModal = (item: OrderTranscations) => {
  showTransactionModal.value = true
  selectedTransaction.value = item
}
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    copiedId.value = text
    setTimeout(() => {
      copiedId.value = null
    }, 2000)
  }
  catch (err) {
    console.error('فشل نسخ النص:', err)
  }
}
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('transaction_number'),
    },
    {
      title: t('transaction.date'),
    },
    {
      title: t('transaction.payment_method'),
    },
    {
      title: t('transaction.payment_gateway_id'),
    },
    {
      title: t('transaction.amount'),
    },
    {
      title: t('transaction.status'),
    },
  ]
})

// Payment link functionality
const paymentLinkActivate = ref(false)
const paymentLink = ref<string>('')
const generatingPaymentLink = ref<boolean>(false)
const posStore = usePosStore()
const showPaymentLinkPopover = ref(false)
const paymentLinkButtonRef = ref(null)

// Use onClickOutside for payment link popover
onClickOutside(paymentLinkButtonRef, () => {
  showPaymentLinkPopover.value = false
})

const copyLinkToClipboard = (text: string) => {
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(text).then(() => {
      paymentLinkActivate.value = true
      setTimeout(() => {
        paymentLinkActivate.value = false
      }, 1000)
    })
  }
  else {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    try {
      document.execCommand('copy')
      paymentLinkActivate.value = true
      setTimeout(() => {
        paymentLinkActivate.value = false
      }, 1000)
    }
    catch (err) {
      console.error('Failed to copy: ', err)
    }

    document.body.removeChild(textArea)
  }
}

const generatePaymentLink = () => {
  generatingPaymentLink.value = true

  posStore.fetchPaymentLink(props.orderId).then(({ data }) => {
    if (data)
      paymentLink.value = data
  }).finally(() => {
    generatingPaymentLink.value = false
  })
}
const showPaymentLink = computed(() => {
  return props.paymentStatus !== 'paid'
})
const selectedItemId = ref<string>('')
const showConfModal = ref<boolean>(false)
const deleteRecord = (item) => {
  selectedItemId.value = item.id
  showConfModal.value = true
}
</script>

<template>
  <!-- Payment Status Display -->
  <confirmation-modal
    v-if="showConfModal"
    :is-open="showConfModal"
    :api-call="deleteTransaction"
    :record-id="selectedItemId"
    @removed="refreshInvoiceData"
    @closed="showConfModal = false"
  >
    <p class="leading-7 text-start">
      {{ $t("confirmModal.msg") }}
    </p>
  </confirmation-modal>

  <PaymentModal
    :is-open="showPaymentModal"
    :is-from-booking="false"
    :invoice="getOrderDetails"
    :un-paid-amount="getOrderDetails.payments.remaining_unpaid_amount"
    @closed="showPaymentModal = false"
    @updated="showPaymentModal = false"
    @refresh="refreshInvoiceData"
  />
  <ModalTransactionDetails
    :show-modal="showTransactionModal"
    :transaction-details="selectedTransaction"
    @close="showTransactionModal = false"
    @refresh="refreshInvoiceData"
  />
  <DisclosureWrapper :title="$t('transactions_log')">
    <template #header-actions>
      <div class="flex gap-2 items-center w-full justify-between">
        <div class="flex gap-2 items-center">
          <span
            v-if="paymentStatus === 'unpaid'"
            class="flex gap-1 items-center px-2 py-1 text-xs font-semibold text-red-700 bg-red-100 rounded-full"
          >
            <XCircleIcon class="w-4 h-4" />
            {{ $t('pos.status.unpaid') }}
          </span>
          <span
            v-else-if="paymentStatus === 'paid'"
            class="flex gap-1 items-center px-2 py-1 text-xs font-semibold text-green-700 bg-green-100 rounded-full"
          >
            <CheckCircleIcon class="w-4 h-4" />
            {{ $t('pos.status.paid') }}
          </span>
          <span
            v-else-if="paymentStatus === 'partially-paid'"
            class="flex gap-1 items-center px-2 py-1 text-xs font-semibold text-yellow-700 bg-yellow-100 rounded-full"
          >
            <ExclamationTriangleIcon class="w-4 h-4" />
            {{ $t('pos.status.partially-paid') }}
            <span v-if="getOrderDetails?.payments?.remaining_unpaid_amount">
              - {{ $t('remaining_amount') }} {{ getOrderDetails.payments.remaining_unpaid_amount }} {{ currency }}
            </span>
          </span>
        </div>
        <div v-if="paymentStatus !== 'paid'" class="flex gap-2 items-center">
          <LilActionBtn
            :icon="PlusIcon"
            @click="showPaymentModal = true"
          />
          <template v-if="showPaymentLink">
            <div class="relative">
              <LilActionBtn
                ref="paymentLinkButtonRef"
                :icon="LinkIcon"
                class="min-w-[40px] justify-center"
                @click="() => { if (!paymentLink) generatePaymentLink(); showPaymentLinkPopover = !showPaymentLinkPopover; }"
              />
              <div
                v-if="showPaymentLinkPopover"
                class="absolute z-20 left-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg p-4"
              >
                <div class="flex items-center min-h-[32px] gap-1">
                  <a
                    :href="paymentLink"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="flex-1 text-start text-xs break-words text-blue-600 underline hover:text-blue-800 cursor-pointer"
                    style="word-break: break-all;"
                  >
                    {{ paymentLink }}
                  </a>
                  <LilActionBtn
                    :aria-label="$t('copy')"
                    :icon="CopyLinkIcon"
                    @click="copyLinkToClipboard(paymentLink)"
                  />
                </div>
                <span
                  v-show="paymentLinkActivate"
                  class="block px-3 py-1 text-xs text-white whitespace-nowrap rounded-md shadow-lg transition-opacity duration-200 bg-primary-600 text-center"
                  :style="{ opacity: paymentLinkActivate ? 1 : 0 }"
                >
                  {{ $t('copied_paymeny_url') }}
                </span>
              </div>
            </div>
          </template>
          <span v-else class="block text-sm text-red-400">
            {{ $t('payment_link_can_not_be_sent') }}
          </span>
        </div>
      </div>
    </template>
    <template #default>
      <!-- Transactions Table -->
      <div v-if="transcations?.length > 0" class="w-full">
        <generic-table
          :is-loading="false"
          :data="transcations"
          :headers="headers"
          item-key="id"
          tr-class="hover:bg-[#F6F7FF] cursor-pointer"
          @row-click="openTransactionDetailsModal"
        >
          <template #row="{ item }">
            <!-- Transaction Number Column -->
            <grid-td class="text-start">
              {{ item.transaction_no }}
            </grid-td>

            <!-- Date Column -->
            <grid-td class="text-start">
              {{ formatDateAndTime(item.date) }}
            </grid-td>

            <!-- Payment Method Column -->
            <grid-td class="text-start">
              {{ item.payment_method.name }}
            </grid-td>

            <!-- Payment Gateway ID Column -->
            <grid-td class="text-start">
              <div class="flex gap-2 items-center">
                <a
                  v-if="item.payment_gateway_id"
                  :href="item.payment_gateway_link"
                  target="_blank"
                  :title="t('transaction.payment_gateway_id_description')"
                  rel="noopener noreferrer"
                  class="text-blue-600 hover:text-blue-800 hover:underline"
                >
                  {{ item.payment_gateway_id }}
                </a>
                <button
                  v-if="item.payment_gateway_id"
                  class="p-1 text-gray-500 hover:text-gray-700 focus:outline-none"
                  :title="t('copy')"
                  @click.stop="copyToClipboard(item.payment_gateway_id)"
                >
                  <span v-if="copiedId === item.payment_gateway_id" class="text-green-600">
                    <icons name="check" class="w-4 h-4" />
                  </span>
                  <span v-else>
                    <icons name="copy" class="w-4 h-4" />
                  </span>
                </button>
                <span v-if="!item.payment_gateway_id">-</span>
              </div>
            </grid-td>

            <!-- Amount Column -->
            <grid-td class="text-start">
              {{ item.amount }} {{ item.currency }}
            </grid-td>

            <!-- Status Column -->
            <grid-td class="text-start">
              <div class="flex gap-2 items-center">
                <BookingStatus :book-status="item.status" />
                <button
                  class="p-1 rounded transition hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-200"
                  title="Delete"
                  @click.stop="deleteRecord(item)"
                >
                  <TrashIcon class="w-5 h-5 text-red-500 cursor-pointer hover:text-red-700" />
                </button>
              </div>
            </grid-td>
          </template>
        </generic-table>
      </div>
    </template>
  </DisclosureWrapper>
</template>
