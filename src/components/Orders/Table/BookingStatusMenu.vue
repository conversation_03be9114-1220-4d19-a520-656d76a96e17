<script setup lang="ts">
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { computed, nextTick, ref, watch } from 'vue'
const props = defineProps({
  status: String,
  bookingUuid: String,
})
const statusList = [
  {
    value: 'confirmed',
    label: 'confirmed',
    textColor: 'text-green-700',
    borderColor: 'border-green-500',
    dotColor: 'bg-green-500',
    lineColor: 'bg-green-500',
    iconColor: 'text-green-600',
    buttonClass: 'bg-[#e8fbf2] hover:bg-[#d2f7e5]',
  },
  {
    value: 'completed',
    label: 'completed',
    textColor: 'text-blue-700',
    borderColor: 'border-blue-500',
    dotColor: 'bg-blue-500',
    lineColor: 'bg-blue-500',
    iconColor: 'text-blue-600',
    buttonClass: 'bg-[#eaf3ff] hover:bg-[#d2e7ff]',
  },
  {
    value: 'no-show',
    label: 'no-show',
    textColor: 'text-red-700',
    borderColor: 'border-red-500',
    dotColor: 'bg-red-500',
    lineColor: 'bg-red-500',
    iconColor: 'text-red-600',
    buttonClass: 'bg-[#ffeaea] hover:bg-[#ffd2d2]',
  },
]
const statusObj = computed(() => statusList.find(s => s.value === props.status) || statusList[0])
const statusLabel = computed(() => statusObj.value.label)
const statusTextColor = computed(() => statusObj.value.textColor)
const statusIconColor = computed(() => statusObj.value.iconColor)
const statusButtonClass = computed(() => statusObj.value.buttonClass)

const buttonRef = ref(null)
const menuRef = ref(null)
const menuStyles = ref({ display: 'none' })
const isMenuOpen = ref(false)
function setOpen(val) {
  isMenuOpen.value = val
  return ''
}

watch(
  isMenuOpen,
  async (isOpen) => {
    if (isOpen) {
      await nextTick()
      let buttonEl = buttonRef.value
      if (buttonEl && buttonEl.$el)
        buttonEl = buttonEl.$el
      if (buttonEl && typeof buttonEl.getBoundingClientRect === 'function') {
        const rect = buttonEl.getBoundingClientRect()
        menuStyles.value = {
          position: 'fixed',
          top: `${rect.bottom + 4}px`,
          left: `${rect.left}px`,
          zIndex: 9999,
          display: 'block',
          minWidth: `${rect.width}px`,
        }
      }
      else {
        menuStyles.value = { position: 'fixed', top: '100px', left: '100px', zIndex: 9999, display: 'block' }
      }
    }
    else {
      menuStyles.value = { display: 'none' }
    }
  },
  { immediate: true },
)
</script>

<template>
  <Menu v-slot="{ open }" as="div" class="relative inline-block text-start">
    <MenuButton
      ref="buttonRef"
      class="flex items-startz gap-2 px-4 py-1.5 rounded-full transition border border-transparent focus:outline-none"
      :class="statusButtonClass"
    >
      <span :class="`${statusTextColor} text-sm whitespace-nowrap`" :title="$t(statusLabel)">
        {{ $t(statusLabel) }}
      </span>
      <svg
        v-if="statusIconColor"
        class="w-4 h-4"
        :class="statusIconColor"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path
          fill-rule="evenodd"
          d="M5.23 7.21a.75.75 0 011.06.02L10 11.293l3.71-4.06a.75.75 0 111.08 1.04l-4.25 4.65a.75.75 0 01-1.08 0l-4.25-4.65a.75.75 0 01.02-1.06z"
          clip-rule="evenodd"
        />
      </svg>
    </MenuButton>
    <teleport to="body">
      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <MenuItems
          v-if="isMenuOpen"
          ref="menuRef"
          :style="menuStyles"
          class="fixed z-[9999] w-40 mt-2 origin-top-right bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
        >
          <div class="py-2 flex flex-col gap-1">
            <MenuItem v-for="s in statusList" :key="s.value" v-slot="{ active }">
              <button
                class="flex gap-1 w-full px-3 py-1.5 text-sm rounded-md transition"
                :class="{
                  'bg-primary-50': status === s.value,
                  [s.textColor]: true,
                }"
                dir="rtl"
                @click="$emit('change-status', bookingUuid, s.value)"
              >
                <span
                  class="w-5 h-5 flex items-center justify-center rounded-full border"
                  :class="status === s.value ? s.borderColor : 'border-gray-300'"
                >
                  <span
                    v-if="status === s.value"
                    :class="s.dotColor"
                    class="w-2 h-2 rounded-full block"
                  />
                </span>
                <span>
                  <span>{{ $t(s.label) }}</span>
                </span>
              </button>
            </MenuItem>
          </div>
        </MenuItems>
      </transition>
    </teleport>
    <input type="hidden" :value="setOpen(open)">
  </Menu>
</template>

