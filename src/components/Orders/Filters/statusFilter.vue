<script setup lang="ts">
import { CheckCircleIcon } from '@heroicons/vue/20/solid'
import { storeToRefs } from 'pinia'
import type { OrderStatus } from '@/types'
import OrderService from '@/services/OrderService'
const emit = defineEmits(['filterChanged'])
const processing = ref(false)
const { fetchStatus } = useOrder()
const { orderStatuses } = storeToRefs(useOrder())
const orderStore = useOrder()
onMounted(async () => {
  await fetchStatus()
})
const statusSelected = ref<string>('')

const filterByStatus = (status: string) => {
  if (statusSelected.value === status)
    statusSelected.value = ''
  else
    statusSelected.value = status

  emit('filterChanged', { status: statusSelected.value })
}
</script>

<template>
  <div class="mt-4 sm:mt-0 min-h-[150px] relative">
    <OverlayLoader v-if="orderStore.processingStatuses" :full-screen="false" />
    <dl
      v-else
      class="flex flex-col gap-5 mt-8"
    >
      <div class="flex flex-wrap gap-6 mb-8">
        <button
          v-for="status in orderStatuses"
          :key="status.status"
          class="flex gap-3 items-center px-5 py-3 text-sm font-medium rounded-full ring-2 ring-transparent ring-offset-2 shadow-sm transition-colors duration-200"
          :class="{
            'ring-2 ring-offset-2 ring-primary-600': statusSelected === status.status,
            'hover:opacity-80': statusSelected !== status.status,
          }"
          :style="{
            backgroundColor: status.bg_color || bgStatus(status),
            color: status.text_color || 'black',
          }"
          @click="filterByStatus(status.status)"
        >
          <template v-if="statusSelected === status.status">
            <CheckCircleIcon class="w-5 h-5" />
          </template>
          <template v-else>
            <div class="w-5 h-5 rounded-full border-2" :style="{ borderColor: status.text_color || 'black' }" />
          </template>
          <span class="">{{ status.label }}</span>
          <span class="flex justify-center items-center w-7 h-7 text-sm font-bold bg-white rounded-full" :style="{ color: status.bg_color || bgStatus(status) }">
            {{ status.count }}
          </span>
        </button>
      </div>
    </dl>
  </div>
</template>
