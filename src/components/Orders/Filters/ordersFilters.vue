<script setup lang="ts">
import VueTailwindDatepicker from 'vue-tailwind-datepicker'
import { storeToRefs } from 'pinia'
import { locatizedOption } from '@/composables/useDatePicker'

import useStaff from '@/composables/useStaff'
const emits = defineEmits(['filterChanged'])
const { fetchCustomerByNameOrPhone } = useCustomerStore()
const { getLocale } = storeToRefs(useLocalesStore())

const { tableData, fetchStaffPage } = useStaff()

const { locale } = useI18n()
const range = ref<string[]>([])
const startRange = ref<string[]>([])

const teams = ref('')
const source = ref('')
const payment_status = ref('')
const customer = ref('')
const invoiced = ref('')
const orderNum = ref('')
const staffId = ref('')

const customers = ref([])
const formatDate = ref({
  date: 'DD-MM-YYYY',
  month: 'MMM',
})

watch([range, teams, source, payment_status, customer, invoiced, orderNum, staffId, startRange], () => {
  emits('filterChanged', {
    range: {
      from: range.value[0],
      to: range.value[1],

    },
    startRange: {
      start_from: startRange.value[0],
      start_to: startRange.value[1],
    },
    team: teams.value,
    source: source.value,
    payment_status: payment_status.value,
    customer: customer.value,
    invoiced: invoiced.value,
    orderNum: orderNum.value,
    staffId: staffId.value,
  })
})
onMounted(() => {
  fetchStaffPage(1, '', 'all')
})
const searchCustomers = async (name = '') => {
  fetchCustomerByNameOrPhone(1, `search=${name}`)
    .then((res) => {
      customers.value = res.data
    })
    .finally(() => {

    })
}

const customerOptions = computed(() => {
  return customers.value.map(customer => ({
    label: `${customer.first_name} ${customer.last_name} [ ${customer.phone} ]`,
    value: customer.uuid,
  }))
})

const staffOptions = computed(() => {
  return tableData.staffList.map(staff => ({
    label: `${staff.name}`,
    value: staff.uuid,
  }))
})
</script>

<template>
  <div class="grid grid-cols-1 gap-x-4 gap-y-6 items-stretch mt-6 md:grid-cols-4">
    <div>
      <TextInput
        id="orderNum"
        v-model="orderNum"
        :label="$t('order_number')"
        :placeholder="$t('order_number')"
      />
    </div>
    <div class="">
      <BaseComboBox
        :model-value="customer"
        arial-label="Search"
        :options="customerOptions"
        :class="`block w-full text-gray-700 border-gray-300 rounded-none w-100 focus:border-primary-500 focus:ring-primary-500 ${getLocale(locale)?.direction === 'rtl' ? 'text-right' : ''}`"
        server-filter
        place-holder="search_phone_or_name"
        @search="searchCustomers"
        @update:model-value="customer = $event"
      >
        {{ $t("booking.customer") }}
      </BaseComboBox>
    </div>
    <div>
      <LabelInput>{{ $t("booking.created_at") }}</LabelInput>
      <VueTailwindDatepicker
        v-model="range"
        :formatter="formatDate"
        :placeholder="$t('form.date_rage')"
        use-range
        input-classes="block w-full border-base-gray rounded    focus:border-primary-500 focus:ring-primary-500 sm:text-sm py-3  h-[48px]"
        :i18n="getLocale(locale)?.id === 'ar' ? 'ar-sa' : 'en'"
        :dir="getLocale(locale)?.direction === 'rtl' ? 'ltr' : 'rtl'"
        :options="locatizedOption"
      />
    </div>
    <div>
      <LabelInput>{{ $t("Start order") }}</LabelInput>
      <VueTailwindDatepicker
        v-model="startRange"
        :formatter="formatDate"
        :placeholder="$t('form.date_rage')"
        use-range
        input-classes="block w-full border-base-gray rounded    focus:border-primary-500 focus:ring-primary-500 sm:text-sm py-3  h-[48px]  "
        :i18n="getLocale(locale)?.id === 'ar' ? 'ar-sa' : 'en'"
        :dir="getLocale(locale)?.direction === 'rtl' ? 'ltr' : 'rtl'"
        :options="locatizedOption"
      />
    </div>

    <filter-with-team v-model="teams" />
    <div>
      <BaseComboBox
        :model-value="staffId"
        arial-label="Search"
        :options="staffOptions"
        :class="`block w-full text-gray-700 border-gray-300 rounded-none w-100 focus:border-primary-500 focus:ring-primary-500 ${getLocale(locale)?.direction === 'rtl' ? 'text-right' : ''}`"
        place-holder="staff"
        @update:model-value="staffId = $event"
      >
        {{ $t("form.staff") }}
      </BaseComboBox>
    </div>
    <div>
      <SelectInput id="source" v-model="source" :label="$t('source')" :placeholder="$t('form.date_rage')">
        <option value="">
          {{ $t("form.all") }}
        </option>
        <option value="bookingPage">
          {{ $t("bookingpage") }}
        </option>
        <option value="pos">
          {{ $t("POS") }}
        </option>
        <option value="calendar">
          {{ $t("Calendar") }}
        </option>
      </SelectInput>
    </div>

    <div>
      <SelectInput id="payment_status" v-model="payment_status" :label="$t('payment_status')" :placeholder="$t('form.date_rage')">
        <option value="">
          {{ $t("form.all") }}
        </option>
        <option value="unpaid">
          {{ $t("booking.unpaid") }}
        </option>
        <option value="partially-paid">
          {{ $t("booking.partially-paid") }}
        </option>
        <option value="paid">
          {{ $t("booking.paid") }}
        </option>
      </SelectInput>
    </div>
    <div class="h-full">
      <SelectInput id="invoice" v-model="invoiced" :label="$t('invoice_status')" class="h-[48px]">
        <option value="">
          {{ $t("all") }}
        </option>
        <option value="true">
          {{ $t("form.invoiced") }}
        </option>
        <option value="false">
          {{ $t("form.not-invoiced") }}
        </option>
      </SelectInput>
    </div>
  </div>
</template>
