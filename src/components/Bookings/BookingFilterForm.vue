<script setup lang="ts">
import dayjs from 'dayjs'
import VueTailwindDatepicker from 'vue-tailwind-datepicker'

import { storeToRefs } from 'pinia'

import Multiselect from 'vue-multiselect'
import type { PropType } from 'vue'
import { useEventStore } from '@/stores/event'
import 'vue-multiselect/dist/vue-multiselect.css'
import useStaff from '@/composables/useStaff'
import type { Staff, Tag } from '@/types'

import { locatizedOption } from '@/composables/useDatePicker'

const props = defineProps({
  tags: {
    type: Array as PropType<Tag[]>,
    default: () => [],
  },
})
const emit = defineEmits(['changed'])
const { tableData, fetchStaffPage } = useStaff()
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const customerName = ref('')
const serviceName = ref('')
const BookingNum = ref('')
const orderNum = ref('')
const range: any = ref([])
const team = ref('')
const rangeDeb = debouncedRef(range, 500)
const teamDebounced = debouncedRef(team, 500)
const rangeDebounced = debouncedRef(customerName, 500)
const BookingNumDebounced = debouncedRef(BookingNum, 500)
const orderNumDebounced = debouncedRef(orderNum, 500)
const serviceDebounced = debouncedRef(serviceName, 500)
const tagsList = ref([])
const statusList = ref('')
const staffList = ref('')
const tagsDebounced = debouncedRef(tagsList, 500)
const statusDebounced = debouncedRef(statusList, 500)
const staffDebounced = debouncedRef(staffList, 500)
const { fetchLookupData } = useEventStore()
const serviceLookup = computed(() => {
  return lookup.customers
})

interface Payload {
  name: string
  range: string
  BookingNum: string
  service: string
  tagsLists: string[]
  status: string
  staff: string
  team: string
  orderNum: string
}
const calendar = reactive({
  date: null as null | string,
  attributes: [],
})
const showDatePicker = ref(false)
const formatDate = ref({
  date: 'DD-MM-YYYY',
  month: 'MMM',
})
watch(
  [
    rangeDebounced,
    serviceDebounced,
    BookingNumDebounced,
    orderNumDebounced,
    tagsDebounced,
    statusDebounced,
    staffDebounced,
    rangeDeb,
    teamDebounced,
  ],
  (val) => {
    const [name, service, BookingNum, orderNum, tagsLists, status, staff, range, team]
      = val
    const payload: Payload = {
      BookingNum,
      from: range.length ? range[0] : '',
      to: range.length ? range[1] : '',
      name,
      service,
      tagsLists,
      status,
      staff,
      team,
      orderNum,
    }
    emit('changed', payload)
  },
)
const lookups = reactive({
  services: [],
})
function formattedRange(dateRange: string[]) {
  if (dateRange === null)
    return dateRange

  const [start, end] = dateRange

  return `from=${dayjs(start).format('YYYY-MM-DD')}&to=${dayjs(end).format(
    'YYYY-MM-DD',
  )}`
}
onMounted(() => {
  fetchLookupData().then(({ services }) => {
    lookups.services = services
  })
  fetchStaffPage(1, '', 'all')
})
</script>

<template>
  <div class="mt-4">
    <div class="grid grid-cols-1 mt-6 gap-y-6 gap-x-4 md:grid-cols-3">
      <NumberInput
        id="order-number"
        v-model="orderNum"
        :label="$t('order_number')"
        :placeholder="$t('order_number')"
      />

      <SearchInput
        id="customer-name"
        v-model="customerName"
        :label="$t('Customer')"
        :placeholder="$t('formPlaceHolder.client')"
      />

      <DateRangeInput
        id="date-range"
        v-model="range"
        :label="$t('Date')"
        :placeholder="$t('form.date_rage')"
        :formatter="formatDate"
      />
      <div>
        <div>
          <SelectInput
            v-model="serviceName"
            :label="$t('booking.service')"
          >
            <option value="">
              {{ $t("form.select") }}
            </option>
            <option
              v-for="ser of lookups.services"
              :key="ser"
              :value="ser?.uuid"
            >
              {{ ser?.name }}
            </option>
          </SelectInput>
        </div>
      </div>

      <div>
        <SelectInput
          v-model="staffList"
          :label="$t('booking.staff')"
        >
          <option value="">
            {{ $t("form.select") }}
          </option>
          <option
            v-for="staff in tableData.staffList"
            :key="staff.uuid"
            :value="staff.uuid"
          >
            {{ staff.name }}
          </option>
        </SelectInput>
      </div>
      <div>
        <div>
          <SelectInput
            id="staff"
            v-model="statusList"
            :label="$t('booking.status')"
          >
            <option value="">
              {{ $t("form.select") }}
            </option>
            <option value="completed">
              {{ $t("booking.the_status.completed") }}
            </option>
            <option value="canceled">
              {{ $t("booking.the_status.canceled") }}
            </option>
            <option value="confirmed">
              {{ $t("booking.the_status.confirmed") }}
            </option>
            <option value="no-show">
              {{ $t("booking.the_status.no-show") }}
            </option>
          </SelectInput>
        </div>
      </div>

      <TeamSelectInput
        v-model="team"
        :label="$t('fields.team_id')"
        :placeholder="$t('form.select')"
      />
      <MultiSelectInput
        id="tags-select"
        v-model="tagsList"
        :label="$t('form.tags')"
        :options="tags"
        :placeholder="$t('form.select')"
        track-by="id"
        label-key="name"
      />
    </div>
  </div>
</template>
