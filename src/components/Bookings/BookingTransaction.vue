<script  lang="ts" setup>
import useVuelidate from '@vuelidate/core'
import { storeToRefs } from 'pinia'
import i18n from '@/i18n'
import { required } from '@/utils/i18n-validators'
import useTransaction from '@/composables/useTransaction'
import { useTransactionState } from '@/stores/transaction'
const props = defineProps({
  price: {
    type: Number,
    default: 0,
  },
})
const { tableData, fetchTransactionPage } = useTransaction()
const { updateTrans } = useTransactionState()

const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const {
  payBookingAmount,
} = useEventStore()
const showModal = ref(false)
const payFormat = reactive({
  amount: 0,
  date: null,
})
const amountErr = ref<{ [key: string]: string }[]>([])
const payRule = {
  amount: {
    required,
  },
  date: {
    required,
  },
}
const payV = useVuelidate(payRule, payFormat)
const hideModal = () => {
  showModal.value = false
  payV.value.$reset()
  amountErr.value = []
  payFormat.amount = 0
  payFormat.date = null
}

const route = useRoute()
const router = useRouter()
const processing = ref(false)
const reFetchDate = ref(true)
const submitPayRegisteration = () => {
  payV.value.$touch()
  if (payV.value.$invalid || processing.value)
    return
  amountErr.value = []
  processing.value = true
  reFetchDate.value = false
  payBookingAmount(route.params.id as string, payFormat).then((res) => {
    showModal.value = false
    // router.go(0)
  }).catch((err) => {
    amountErr.value = err.errors
  }).finally(() => {
    processing.value = false
    reFetchDate.value = true
  })
}
const selectedTransaction = ref<object>({})
const showModalTransaction = ref(false)

const openModal = (selectedTrans: Object) => {
  selectedTransaction.value = selectedTrans
  showModalTransaction.value = true
}
const updateTransStatus = (status: string) => {
  processing.value = true
  updateTrans(selectedTransaction?.value.id, status).then(() => {
    fetchTransactionPage()
  }).finally(() => {
    processing.value = false
    showModal.value = false
  })
}
const setDate = (date: string) => payFormat.date = date
</script>

<template>
  <div>
    <Modal :open="showModal" title="bookPay" :dir="getLocale(locale)?.direction" @close="hideModal">
      <err-validations :err-handle="amountErr" />
      <form action="flex flex-col flex-1" class="grid grid-cols-2 gap-4" @submit.prevent="submitPayRegisteration">
        <div>
          <div class="w-full">
            <NumberInput
              id="booking-note"
              v-model="payFormat.amount"
              :label="$t('transaction.amount')"
              :placeholder="$t('transaction.amount')"
              class="block py-3 mb-3 w-full leading-tight text-gray-700 rounded border appearance-none ps-4 pe-4 focus:outline-none focus:bg-white"
              step="0.01"
            />
            <p v-for="error of payV.amount.$errors" :key="error.$uid" class="mt-2 mb-4 error-message text-start">
              {{ $t(error.$message) }}
            </p>
          </div>
        </div>
        <div>
          <label for="booking-start" class="mb-2 w-full flex items-center text-start text-[#261E27] text-base ">{{ $t("booking.created_at") }}<span
            class="text-red-600"
          >*</span></label>
          <v-date-picker
            mode="dateTime"
            is24hr
            :model-value="payFormat.date"
            :locale="i18n.global.locale.value"
            :first-day-of-week="1"
            @update:model-value="setDate"
          >
            <template #default="{ inputValue, inputEvents }">
              <input
                :class="{ 'error-input': payV.date.$errors.length }"
                class="block py-3 w-full leading-tight text-gray-700 rounded border appearance-none  ps-4 pe-4 focus:outline-none focus:bg-white focus:border-gray-500" :value="inputValue" placeholder="MM/DD/YYYY" mode="mode"
                v-on="inputEvents"
              >
            </template>
          </v-date-picker>
          <template v-if="!payFormat.date">
            <p v-for="error of payV.date.$errors" :key="error.$uid" class="error-message">
              {{ $t(error.$message) }}
            </p>
          </template>
        </div>
        <div class="flex col-span-2 justify-center">
          <BaseButton
            class="inline-flex mx-auto w-1/2"
            custome-bg="bg-primary"
            :processing="processing"
          >
            <span>{{ $t("form.bookPay") }}</span>
          </BaseButton>
        </div>
      </form>
    </Modal>

    <div class="flex flex-col">
      <show-transaction :re-fetch="reFetchDate" @show-modal="showModal = true" />
    </div>
  </div>
</template>

<style>

</style>
