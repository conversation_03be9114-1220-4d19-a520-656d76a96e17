<script lang="ts" setup>
import type { PropType } from 'vue'
import { customDateFormat } from '@/composables/dateFormat'

const props = defineProps({
  activities: {
    type: Array as PropType<Activities[]>,
  },
})
interface Activities {
  description: string
  created_at: string
}
</script>

<template>
  <div class="mt-6  ">
    <ul role="list" class="space-y-6">
      <li v-for="act in activities" :key="act.created_at" class="relative flex gap-x-4">
        <div class="absolute rtl:right-0 ltr:left-0 top-0 flex w-6 justify-center -bottom-6">
          <div class="w-px bg-gray-200" />
        </div>
        <div class="relative flex h-6 w-6 flex-none items-center justify-center bg-white">
          <div class="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300" />
        </div>
        <p class="flex-auto py-0.5 text-xs leading-5 text-gray-500">
          <span class="font-medium text-gray-900"><b>{{ act.user }}</b> {{ act.description }}</span>
        </p>
        <time datetime="2023-01-23T10:32" class="flex-none py-0.5 text-xs leading-5 text-gray-500">{{ customDateFormat(act.created_at, "MMMM DD, YYYY HH:mm A") }}</time>
      </li>
    </ul>
  </div>
</template>

<style>

</style>
