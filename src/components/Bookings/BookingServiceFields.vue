<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import type { PropType } from 'vue'
import { minValue, required, requiredIf } from '@/utils/i18n-validators'
import type { BookingService, Service } from '@/types'

const props = defineProps({
  modelValue: {
    type: Object as PropType<BookingService>,
    default: () => ({
      id: '',
      duration: 0,
      price: 0,
    }),
  },
  name: {
    type: String,
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  services: {
    type: Array as PropType<Service[]>,
    default: () => [],
  },
  showQuantity: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:modelValue'])
const rules = {
  id: {
    required,
  },
  duration: {
    required,
    minValue: minValue(1),
  },
  price: {
    required,
    minValue: minValue(0),
  },
  quantity: {
    required: requiredIf(() => props.showQuantity),
    minValue: minValue(1),
  },
}
const v$ = useVuelidate<BookingService>(rules, props.modelValue)
const servicesOptions = computed(() => {
  return props.services
    .filter(s => s.canServe)
    .map(service => ({
      label: service.name,
      value: service.uuid,
    }))
})
const updateValue = (value: BookingService) => {
  emit('update:modelValue', value)
}
const selectService = (uuid: string) => {
  const selectedService = props.services.find(
    service => service.uuid === uuid,
  )
  if (!selectedService)
    return
  updateValue({
    ...props.modelValue,
    id: selectedService?.uuid,
    duration: selectedService.duration || 0,
    quantity: selectedService.quantity || 1,
    ...(!props.showQuantity && { price: selectedService.price || 0 }),
    ...(props.showQuantity && { unit_amount: selectedService.price || 0 }),
    ...(props.showQuantity && { price: selectedService.price * (selectedService?.quantity || 1) }),
  })
}
const changeDuration = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...props.modelValue,
    duration: +event.target.value,
  })
}
const changePrice = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...props.modelValue,
    price: +event.target.value,
  })
}

const changeQuantity = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...props.modelValue,
    quantity: +event.target.value,
    price: +event.target.value * (props.modelValue?.unit_amount || 0),
  })
}
const changePricePerUnit = (
  event: Event & {
    target: HTMLInputElement
  },
) => {
  updateValue({
    ...props.modelValue,
    unit_amount: +event.target.value,
    price: +event.target.value * (props.modelValue?.quantity || 0),
  })
}
</script>

<template>
  <div
    class="grid grid-cols-1 mt-3"
    :class="{
      'sm:grid-cols-7': !showQuantity,
      'sm:grid-cols-9': showQuantity,
    }"
  >
    <div class="sm:col-span-3">
      <form-group :validation="v$" name="id">
        <template #default="{ attrs }">
          <BaseComboBox
            v-bind="attrs"
            :model-value="props.modelValue.id"
            place-holder="serviceName"
            required
            arial-label="Search"
            :disabled="disabled || !servicesOptions.length"
            :error="v$.$dirty && v$.id.$error"
            :options="servicesOptions"
            class="block w-full text-gray-700 border-gray-300 rounded-none w-100 focus:border-primary-500 focus:ring-primary-500"
            custom-classes="sm:rounded-e-none"
            @update:model-value="selectService($event)"
          >
            {{ $t("booking.service") }}
          </BaseComboBox>
        </template>
      </form-group>
      <p v-if="!servicesOptions.length" class="error-message">
        {{ $t("booking.no_service") }}
      </p>
    </div>
    <div
      :class="{
        'sm:col-span-2': !showQuantity,
        'sm:col-span-1': showQuantity,
      }"
    >
      <form-group :validation="v$" name="duration">
        <template #default="{ attrs }">
          <NumberInput
            v-bind="attrs"
            :id="`serviceDuration-${props.name}`"
            v-model="props.modelValue.duration"
            min="1"
            :disabled="disabled"
            :placeholder="$t('booking.duration')"
            :label="$t('booking.duration')"
            @change="changeDuration"
          />
        </template>
      </form-group>
    </div>
    <template v-if="showQuantity">
      <div class="sm:col-span-2">
        <form-group :validation="v$" name="pricePerUnit">
          <template #default="{ attrs }">
            <NumberInput
              v-bind="attrs"
              :id="`pricePerUnit-${props.name}`"
              v-model="props.modelValue.unit_amount"
              :label="$t('pricePerUnit')"
              min="1"
              :disabled="disabled"
              :placeholder="$t('pricePerUnit')"
              @change="changePricePerUnit"
            />
          </template>
        </form-group>
      </div>
      <div v-if="showQuantity" class="sm:col-span-1">
        <form-group :validation="v$" name="quantity">
          <template #default="{ attrs }">
            <NumberInput
              v-bind="attrs"
              :id="`quantity-${props.name}`"
              v-model="props.modelValue.quantity"
              :label="$t('quantity')"
              min="1"
              :disabled="disabled"
              :placeholder="$t('quantity')"
              @change="changeQuantity"
            />
          </template>
        </form-group>
      </div>
      <div class="sm:col-span-2">
        <form-group :validation="v$" name="totalPrice">
          <template #default="{ attrs }">
            <NumberInput
              v-bind="attrs"
              :id="`servicePrice-${props.name}`"
              :key="`servicePrice-${props.name}`"
              v-model="props.modelValue.price"
              :label="$t('totalPrice')"
              :placeholder="$t('totalPrice')"
              readonly
              :disabled="disabled"
              @blur="attrs.onblur"
            />
          </template>
        </form-group>
      </div>
    </template>
    <template v-else>
      <div class="sm:col-span-2">
        <form-group :validation="v$" name="price">
          <template #default="{ attrs }">
            <NumberInput
              v-bind="attrs"
              :id="`servicePrice-${props.name}`"
              :key="`servicePrice-${props.name}`"
              v-model="props.modelValue.price"
              :label="$t('booking.price')"
              :placeholder="$t('booking.price')"
              :disabled="disabled"
              @blur="attrs.onblur"
              @change="changePrice"
            />
          </template>
        </form-group>
      </div>
    </template>
  </div>
</template>
