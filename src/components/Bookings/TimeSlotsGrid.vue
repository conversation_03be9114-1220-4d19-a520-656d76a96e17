<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import dayjs from 'dayjs'
import type { DaySlot, TimeSlot } from '@/types'

const props = defineProps({
  timeSlotsList: {
    type: Array as PropType<DaySlot[]>,
    default: () => [],
  },
  selectedTime: {
    type: String,
    default: '',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showBooked: {
    type: Boolean,
    default: true,
  },
  visibleDays: {
    type: Number,
    default: 7,
  },
  noServices: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'updateTime',
  'week-change', // Emits new start date string (YYYY-MM-DD) when week changes
])

const currentStartDate = ref(
  props.timeSlotsList.length
    ? dayjs(props.timeSlotsList[0].date).format('YYYY-MM-DD')
    : dayjs().format('YYYY-MM-DD'),
)

// Watch for external slot list changes and update currentStartDate if needed
watch(
  () => props.timeSlotsList,
  (newList) => {
    if (newList.length)
      currentStartDate.value = dayjs(newList[0].date).format('YYYY-MM-DD')
  },
)

const dateRangeLabel = computed(() => {
  if (!props.timeSlotsList.length)
    return ''
  const first = dayjs(props.timeSlotsList[0].date)
  const last = dayjs(props.timeSlotsList[props.timeSlotsList.length - 1].date)
  const month = last.locale('ar').format('MMMM')
  const year = last.format('YYYY')
  return `${first.format('D')} - ${last.format('D')} ${month} ${year}`
})

function selectTime(slot: TimeSlot) {
  console.log(slot)
  emit('updateTime', slot)
}

function navigate(direction: 'next' | 'prev') {
  console.log(props.visibleDays)
  const newStart
    = direction === 'next'
      ? dayjs(currentStartDate.value).add(props.visibleDays, 'day')
      : dayjs(currentStartDate.value).subtract(props.visibleDays, 'day')
  currentStartDate.value = newStart.format('YYYY-MM-DD')
  emit('week-change', currentStartDate.value)
}

watch(() => props.visibleDays, (newVal, oldVal) => {
  console.log(newVal, oldVal)
  if (newVal !== oldVal)
    emit('week-change', currentStartDate.value)
})
</script>

<template>
  <div class="relative w-full">
    <overlay-loader v-if="loading" :full-screen="false" />

    <!-- Modern Centered Date Range Navigation -->
    <div v-if="timeSlotsList.length" class="flex gap-4 justify-center items-center mb-4">
      <button
        class="flex justify-center items-center w-9 h-9 text-gray-700 bg-white rounded-full border border-gray-300 transition hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-400"
        :disabled="loading"
        aria-label="{{ $t('calendar.prev') }}"
        @click="navigate('prev')"
      >
        <svg width="20" height="20" fill="none" viewBox="0 0 24 24"><path d="M9 5l7 7-7 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" /></svg>
      </button>
      <div class="text-lg font-bold text-gray-800 select-none">
        {{ dateRangeLabel }}
      </div>
      <button
        class="flex justify-center items-center w-9 h-9 text-gray-700 bg-white rounded-full border border-gray-300 transition hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-400"
        :disabled="loading"
        aria-label="{{ $t('calendar.next') }}"
        @click="navigate('next')"
      >
        <svg width="20" height="20" fill="none" viewBox="0 0 24 24"><path d="M15 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" /></svg>
      </button>
    </div>

    <!-- Responsive Days Grid -->
    <div class="overflow-visible relative">
      <div v-if="timeSlotsList.length" class="flex flex-row flex-wrap gap-4 justify-center w-full">
        <div
          v-for="day in timeSlotsList"
          :key="day.date"
          class="flex flex-col bg-white rounded-xl border border-gray-200 min-w-[110px] max-w-[160px] flex-1 sm:min-w-[140px] sm:max-w-[180px]"
        >
          <!-- Day Header -->
          <div class="flex flex-col justify-center items-center p-1.5 text-sm text-center font-bold text-white bg-secondary rounded-t-xl tracking-wide min-h-[56px] h-14">
            <div>{{ dayjs(day.date).format('dddd') }}</div>
            <div class="mt-0.5 text-xs">
              {{ dayjs(day.date).format('D') }} {{ dayjs(day.date).format('MMMM') }}
            </div>
          </div>

          <!-- Time Slots -->
          <div class="flex-1 flex flex-col bg-[#EFF0F9]">
            <div v-if="loading" class="py-4 text-center text-gray-400 animate-pulse">
              {{ $t('loading') }}
            </div>
            <template v-else>
              <div
                v-if="(showBooked ? day.slots?.length : day.slots?.filter(s => !s.booked).length)"
                class="flex flex-col gap-1.5 p-1.5 max-h-[200px] overflow-y-auto custom-scrollbar"
              >
                <button
                  v-for="slot in (showBooked ? day.slots : day.slots.filter(s => !s.booked))"
                  :key="slot.u"
                  type="button"
                  class="w-full px-0 py-2 text-sm rounded-lg border transition-all duration-200" :class="[
                    slot.fullDate === selectedTime
                      ? 'bg-blue-600 text-white border-blue-600 '
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-blue-50 hover:border-blue-400',
                    slot.booked ? 'border-red-400 text-red-500 bg-red-50' : '',
                  ]"
                  :disabled="slot.booked && !showBooked"
                  :title="slot.booked ? $t('booked') : $t('available')"
                  style="font-variant-numeric: tabular-nums;"
                  @click="selectTime(slot)"
                >
                  {{ slot.t }}
                </button>
              </div>
              <div v-else class="flex-1 flex flex-col justify-center items-center text-gray-400 text-xs min-h-[200px] ">
                <svg width="48" height="48" fill="none" viewBox="0 0 24 24" class="mb-2 w-8 h-8 sm:w-12 sm:h-12"><circle cx="12" cy="12" r="10" stroke="#BDBDBD" stroke-width="2" fill="#F3F4F6" /><path d="M12 8v4" stroke="#BDBDBD" stroke-width="2" stroke-linecap="round" /><circle cx="12" cy="16" r="1" fill="#BDBDBD" /></svg>
                <span class="mt-2 text-sm text-center text-gray-500 sm:text-base">مزود الخدمة ليس لديه أوقات متاحة في هذا اليوم</span>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div v-else-if="noServices" class="flex-1 flex flex-col justify-center items-center text-gray-400 text-xs min-h-[80px]">
        <svg width="32" height="32" fill="none" viewBox="0 0 24 24" class="mb-1"><path stroke="currentColor" stroke-width="1.5" d="M7 12h10M12 7v10" opacity=".3" /><circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="1.5" opacity=".2" /></svg>
        <!-- Holiday -->
        {{ $t('add_service_first') }}
      </div>
      <div v-else class="flex-1 flex flex-col justify-center items-center text-gray-400 text-xs min-h-[80px]">
        <svg width="32" height="32" fill="none" viewBox="0 0 24 24" class="mb-1"><path stroke="currentColor" stroke-width="1.5" d="M7 12h10M12 7v10" opacity=".3" /><circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="1.5" opacity=".2" /></svg>
        <!-- Holiday -->
        {{ $t('no_time_slot') }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.grid {
  direction: rtl;
}
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E1 transparent;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #CBD5E1;
    border-radius: 20px;
  }
}
</style>
