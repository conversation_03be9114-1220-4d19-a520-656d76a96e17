<script setup lang="ts">
import { GoogleMap, Marker, MarkerCluster } from 'vue3-google-map'
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
  Menu,
  MenuButton,
  MenuItem, MenuItems, TransitionChild, TransitionRoot, 
} from '@headlessui/vue'
import dayjs from 'dayjs'
import type { ComputedRef, PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { XCircleIcon } from '@heroicons/vue/24/outline'
import {
  CalendarIcon,
  CheckCircleIcon,
  ClockIcon,
  ComputerDesktopIcon,
  ExclamationTriangleIcon,
  GlobeAltIcon,
  LinkIcon, UserCircleIcon,
} from '@heroicons/vue/24/solid'
import { ArrowPathIcon } from '@heroicons/vue/20/solid'
import type { Booking, Order, header } from '@/types'
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: String as PropType<string>,
    required: false,
    default: '',
  },
  orderId: {
    type: String as PropType<string>,
    required: true,
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits([
  'closed',
  'openAppointmentModal',
  'refresh',
  'openTransactionDetailsModal',
  'openSetAppointmentModal',
])
const API_KEY = import.meta.env.VITE_GOOGLE_MAP_KEY
const user = useAuthStore()
const router = useRouter()
const { orderId, itemId, loading } = toRefs(props)

// locales
const { t } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()

const { fetchSingleOrder } = useOrder()
const { orderDetails } = storeToRefs(useOrder())
const processing = ref(false)

const showOverLay = computed(() => processing.value || loading.value)
// Invoice Options
const { previewInvoicePdf, sendInvoicePdf, sendWhatsAppInvoice }
  = usePosStore()
const invoiceProcessing = ref(false)

function openInvoicePdf() {
  window.open(`/preview-invoices/${orderDetails.value.id}`, '_blank')
}
function sendInvoice() {
  invoiceProcessing.value = true
  sendInvoicePdf(orderDetails.value?.invoices?.id as string).finally(() => {
    invoiceProcessing.value = false
  })
}
function sendInvoiceWhatsApp(id: String) {
  invoiceProcessing.value = true
  sendWhatsAppInvoice(orderDetails.value?.invoices?.id as string).finally(
    () => {
      invoiceProcessing.value = false
    },
  )
}
function downloadPdfInvioce() {
  invoiceProcessing.value = true
  previewInvoicePdf(orderDetails.value?.invoices?.id as string).finally(() => {
    invoiceProcessing.value = false
  })
}
const printOptions = computed(() => {
  return [
    {
      name: t('export_invoice'),
      id: 'export_invoice',
      icon: 'summary',
      event: downloadPdfInvioce,
    },
    {
      name: t('preview_invoice'),
      id: 'preview_invoice',
      icon: 'invoice',
      event: openInvoicePdf,
    },
    {
      name: t('send_invoice_email'),
      id: 'send_invoice_email',
      icon: 'invoice',
      event: sendInvoice,
    },
    {
      name: t('send_invoice_whatsapp'),
      id: 'send_invoice_whatsapp',
      icon: 'invoice',
      event: sendInvoiceWhatsApp,
    },
  ]
})

// order details
const fetchOrderDetailsByOrderId = async (Id = '') => {
  try {
    processing.value = true
    await fetchSingleOrder(Id)
  }
  finally {
    processing.value = false
  }
}

watch(
  () => orderId.value,
  async (val) => {
    if (val)
      await fetchOrderDetailsByOrderId(val)
  },
  { immediate: true },
)

// Change Appointment Status
const appointmentStatused = ['confirmed', 'completed', 'no-show']
const { updateBookingStatus } = useEventStore()
const changeAppointmentStatus = async (
  id: string,
  status: 'confirmed' | 'completed' | 'no-show',
) => {
  try {
    processing.value = true
    await updateBookingStatus(id, status)
    await fetchOrderDetailsByOrderId(orderId.value)
    emits('refresh')
  }
  finally {
    processing.value = false
  }
}

// tabs
const hasCustomFields = computed(() =>
  Boolean(orderDetails.value?.meta_data?.length),
)
const hasTranscations = computed(() =>
  Boolean(orderDetails.value?.payments?.transcations?.length),
)
const currentTab = ref('items')
const tabs = ref([
  { label: t('items'), value: 'items', icon: 'CalendarIcon', show: true },
  {
    label: t('customer_data'),
    value: 'customerData',
    icon: 'UserIcon',
    show: true,
  },
  {
    label: t('order_log'),
    value: 'orderLog',
    icon: 'ClipboardDocumentListIcon',
    show: true,
  },

  {
    label: t('additionalInfo'),
    value: 'additional',
    icon: 'PlusCircleIcon',
    show: true,
  },
  {
    label: t('payment_transactions'),
    value: 'paymentTransactions',
    icon: 'BanknotesIcon',
    show: true,
  },
])

const updateTabVisibility = (tabValue, condition) => {
  tabs.value = tabs.value.map(tab =>
    tab.value === tabValue ? { ...tab, show: condition } : tab,
  )
}

watch(
  hasCustomFields,
  newValue => updateTabVisibility('additional', newValue),
  { immediate: true },
)

watch(
  hasTranscations,
  newValue => updateTabVisibility('paymentTransactions', newValue),
  { immediate: true },
)
// customer
const emailCopyActivate = ref(false)
const emailCopy = (text: string) => {
  navigator.clipboard.writeText(text)
  emailCopyActivate.value = true
  setTimeout(() => {
    emailCopyActivate.value = false
  }, 1000)
}

// transaction headers
const transactionHeaders: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('transaction_number'),
    },
    {
      title: t('transaction.date'),
    },
    {
      title: t('transaction.payment_method'),
    },
    {
      title: t('transaction.amount'),
    },
    {
      title: t('transaction.status'),
    },
  ]
})
const openTransactionDetailsModal = (item) => {
  emits('openTransactionDetailsModal', item)
}
// cancel modal (booking)

const cancelModal = ref(false)
const bookingWillBeCanceled = ref({})
const bookingCancelled = async (id: string) => {
  try {
    processing.value = true
    await fetchOrderDetailsByOrderId(orderId.value)
    emits('refresh')
  }
  finally {
    processing.value = false
  }
}
const { fetchEventById } = useEventStore()

const openCancelModal = async (id: string) => {
  processing.value = true
  try {
    const data = await fetchEventById(id)
    bookingWillBeCanceled.value = data
    cancelModal.value = true
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog
      as="div" :dir="getLocale(locale)?.direction" class="relative z-[50]"
    >
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="overflow-y-auto fixed inset-0">
        <div
          class="flex justify-center items-center p-4 min-h-full text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-3xl p-6 text-left align-middle transition-all transform bg-white shadow-xl min-h-[200px] rounded-2xl"
            >
              <!-- Foucsable button to avid dialog crash -->
              <overlay-loader v-if="showOverLay" :full-screen="false" />
              <DialogTitle
                v-if="orderDetails?.id"
                as="h3"
                class="text-2xl font-medium leading-6 text-gray-900"
              >
                <div class="flex justify-between pb-3">
                  <!--  -->
                  <div class="flex gap-3 items-center">
                    <p
                      class="pb-1 border-b cursor-pointer border-1 hover:border-primary-700"
                      @click="
                        router.push({
                          name: 'orders',
                          query: { order_id: orderDetails.id },
                        })
                      "
                    >
                      {{ `${$t("order")} #${orderDetails?.OrderNum ?? "--"} ` }}
                    </p>
                    <span
                      class="flex gap-1 items-center px-2 text-base text-black bg-white rounded-full border border-gray-200"
                      :style="{
                        'background-color':
                          orderDetails?.status?.bg_color ?? '#000000',
                        'color': orderDetails?.status?.text_color ?? '#000000',
                      }"
                    >
                      {{ orderDetails?.status?.label }}
                    </span>
                    <ComputerDesktopIcon
                      v-if="orderDetails.source == 'pos'"
                      class="inline-block w-5 h-5"
                    />
                    <GlobeAltIcon
                      v-else-if="orderDetails.source == 'bookingPage'"
                      class="inline-block w-5 h-5"
                    />
                    <CalendarIcon
                      v-else-if="orderDetails.source == 'calendar'"
                      class="inline-block w-5 h-5"
                    />
                  </div>
                  <button @click="emits('closed')">
                    <XCircleIcon
                      class="w-9 h-9 text-red-400 cursor-pointer close ms-auto"
                      aria-hidden="true"
                    />
                  </button>
                </div>

                <div class="grid grid-cols-1 items-center text-sm">
                  <div
                    class="flex flex-wrap col-span-1 gap-3 items-center text-sm"
                  >
                    <div
                      v-if="orderDetails.invoices"
                      class="flex font-bold text-white ms-auto"
                    >
                      <Listbox as="div" model-value="" class="relative w-full">
                        <ListboxButton
                          class="flex justify-between items-center px-3 py-3 rounded-lg border border-gray-200 shadow-md ease-out cursor-pointer bg-primary-800/90 text-start sm:text-sm hover:outline hover:outline-primary-800/90 focus:outline-primary-800/90"
                        >
                          <span
                            class="flex items-center rtl:left-auto rtl:right-0"
                          >
                            <TheLoader v-if="invoiceProcessing" />
                            خيارات الفاتورة
                          </span>
                        </ListboxButton>

                        <transition
                          enter-active-class="transition duration-100 ease-out"
                          enter-from-class="opacity-0 transform scale-95"
                          enter-to-class="opacity-100 transform scale-100"
                          leave-active-class="transition duration-75 ease-out"
                          leave-from-class="opacity-100 transform scale-100"
                          leave-to-class="opacity-0 transform scale-95"
                        >
                          <ListboxOptions
                            class="overflow-auto absolute z-10 py-1 mt-1 w-60 max-h-80 text-base bg-white rounded-md ring-1 shadow-lg sm:text-sm start-0"
                          >
                            <ListboxOption
                              v-for="option in printOptions"
                              :key="option.id"
                              v-slot="{ active, selected }"
                              as="template"
                              :value="option"
                              @click="option.event()"
                            >
                              <li
                                class="relative cursor-pointer  select-none py-2 px-2" :class="[
                                  active
                                    ? 'bg-primary-600 text-white'
                                    : 'text-gray-900',
                                ]"
                              >
                                <div
                                  class="flex gap-3 justify-start items-center"
                                >
                                  <span
                                    class="flex justify-center items-center mx-1 w-44 h-6 text-gray-400 md:w-6 md:h-6"
                                  >
                                    <icons :name="option.icon" />
                                  </span>
                                  <span
                                    class=" text-xs sm:text-sm" :class="[
                                      selected
                                        ? 'font-semibold'
                                        : 'font-normal',
                                    ]"
                                  >
                                    {{ option.name }}
                                  </span>
                                </div>

                                <span
                                  v-if="selected"
                                  class="relative inset-y-0 right-0 flex items-center pr-4" :class="[
                                    active ? 'text-white' : 'text-indigo-600',
                                  ]"
                                />
                              </li>
                            </ListboxOption>
                          </ListboxOptions>
                        </transition>
                      </Listbox>
                    </div>
                  </div>

                  <div
                    class="grid grid-cols-1 col-span-1 my-4 w-full sm:grid-cols-2"
                  >
                    <div
                      class="flex gap-2 items-center py-3 border-b border-stone-100"
                    >
                      <span
                        class="block text-sm font-bold text-neutral-500 w-15"
                      >{{ $t("booking.created_at") }}
                      </span>
                      <div class="relative items-center focus-within:z-10">
                        {{
                          dayjs(orderDetails?.created_at).format(
                            "DD MMMM YYYY | hh:mm A",
                          )
                        }}
                      </div>
                    </div>
                    <div
                      class="flex gap-2 items-center py-3 border-b border-stone-100"
                    >
                      <span
                        class="block text-sm font-bold text-neutral-500 w-15"
                      >{{ $t("fields.team_id") }}
                      </span>
                      <div class="relative items-center focus-within:z-10">
                        {{ orderDetails?.branch?.name }}
                      </div>
                    </div>

                    <div
                      class="flex gap-2 items-center py-3 border-b border-stone-100"
                    >
                      <span
                        class="block text-sm font-bold text-neutral-500 w-15"
                      >{{ $t("source") }}
                      </span>
                      <div
                        v-if="orderDetails?.source"
                        class="relative items-center focus-within:z-10"
                      >
                        {{ $t(`booking.sources.${orderDetails?.source}`) }}
                      </div>
                      <div
                        v-else
                        class="relative items-center focus-within:z-10"
                      >
                        -
                      </div>
                    </div>
                    <div
                      class="flex gap-2 items-center py-3 border-b border-stone-100"
                    >
                      <span
                        class="block text-sm font-bold text-neutral-500 w-15"
                      >{{ $t("booking.paymentStatus") }}
                      </span>
                      <div
                        v-if="orderDetails?.payments?.status"
                        class="relative items-center focus-within:z-10"
                      >
                        <div
                          class="flex gap-3 justify-start items-center w-full"
                        >
                          <h3 class="text-base font-semibold text-black">
                            {{
                              $t(`pos.status.${orderDetails.payments.status}`)
                            }}
                          </h3>
                          <span class="flex-shrink-0 w-6 h-6 rounded-full">
                            <XCircleIcon
                              v-if="orderDetails.payments.status == 'unpaid'"
                              class="inline-block text-red-700"
                            />
                            <CheckCircleIcon
                              v-if="orderDetails.payments.status == 'paid'"
                              class="inline-block text-green-700"
                            />
                            <ExclamationTriangleIcon
                              v-if="
                                orderDetails.payments.status == 'partially-paid'
                              "
                              class="inline-block text-gray-500"
                            />
                          </span>
                        </div>
                      </div>
                      <div
                        v-else
                        class="relative items-center focus-within:z-10"
                      >
                        -
                      </div>
                    </div>
                    <div
                      class="flex gap-2 items-center py-3 border-b border-stone-100"
                    >
                      <span
                        class="block text-sm font-bold text-neutral-500 w-15"
                      >{{ $t("booking.notes") }}
                      </span>
                      <div class="relative items-center focus-within:z-10">
                        {{ orderDetails?.note ? orderDetails?.note : "-" }}
                      </div>
                    </div>
                  </div>
                </div>
              </DialogTitle>
              <button className="h-0 w-0 overflow-hidden" />
              <div>
                <tab
                  v-if="orderDetails.id"
                  v-model="currentTab"
                  :tabs="tabs.filter((tab) => tab.show)"
                />
                <div
                  v-if="
                    orderDetails?.items?.length > 0 && currentTab == 'items'
                  "
                  class="relative mt-2"
                >
                  <div class="flex z-30 flex-col gap-2 px-2">
                    <div
                      v-for="item in orderDetails.items"
                      :key="item.uuid"
                      class="relative gap-2 px-3 py-3 rounded-lg border border-gray-200 cursor-pointer"
                      :class="{
                        'shadow-lg border-primary-300/90': item.uuid === itemId,
                      }"
                      @click="
                        item.booking
                          ? emits('openAppointmentModal', item.booking.uuid)
                          : ''
                      "
                    >
                      <template v-if="item.booking">
                        <div
                          class="flex flex-col justify-start items-center sm:flex sm:flex-row"
                        >
                          <div
                            v-if="item.booking.is_recurring"
                            class="absolute top-0 px-2 py-1 text-xs font-semibold end-0"
                          >
                            <ArrowPathIcon
                              class="w-5 h-5 font-semibold text-primary"
                            />
                          </div>

                          <div class="flex text-lg font-semibold">
                            <div class="flex flex-col items-center">
                              <p class="text-primary-800">
                                {{ dayjs(item.booking.start).format("ddd") }}
                              </p>
                              <p class="text-4xl text-primary-800">
                                {{ dayjs(item.booking.start).format("D") }}
                              </p>
                            </div>
                            <p
                              class="w-[2px] mx-3 bg-primary-800 h-20 sm:block hidden"
                            />
                          </div>

                          <div
                            class="flex flex-col gap-2 justify-center items-start"
                          >
                            <p
                              v-if="item.booking"
                              class="text-base text-gray-700"
                            >
                              <ClockIcon
                                class="inline-block w-5 h-5 text-gray-500"
                              />
                              {{ dayjs(item.booking.start).format("HH:mm A") }}
                              -
                              {{ dayjs(item.booking.end).format("HH:mm A") }}
                            </p>
                            <p
                              class="flex gap-2 items-center text-base text-gray-700"
                            >
                              <img
                                v-if="item.photo"
                                id="image"
                                class="w-5 h-5 rounded-full"
                                :src="item.photo"
                              >
                              <UserCircleIcon
                                v-else
                                class="inline-block w-5 h-5 text-gray-500"
                              />
                              {{ item?.name }}
                              <span
                                v-if="item.provider"
                                class="text-primary-600"
                              >
                                تقدم بواسطة
                                {{ item?.provider.name }}
                              </span>
                            </p>
                          </div>
                          <div class="flex flex-col gap-2 items-center ms-auto">
                            <div
                              class="flex relative flex-col gap-2 justify-center items-start mx-auto sm:ms-auto sm:mx-0"
                            >
                              <Menu
                                v-slot="{ open }"
                                as="div"
                                class="inline-block relative"
                                :class="
                                  getLocale(locale)?.direction === 'rtl'
                                    ? 'order-1'
                                    : 'order-2'
                                "
                              >
                                <MenuButton
                                  class="inline-flex items-center justify-center px-3 py-3 text-base font-semibold rounded-lg w-[110px]"
                                  :class="{
                                    'text-red-900 hover:bg-red-400 bg-[#F4433680] ':
                                      item.booking.status === 'no-show',
                                    'text-green-900 hover:bg-green-400 bg-[#4CAF5080] ':
                                      item.booking.status === 'confirmed',
                                    'text-blue-800 hover:bg-blue-400 bg-[#2196F380]  ':
                                      item.booking.status === 'completed',
                                  }"
                                  @click.stop
                                >
                                  {{ $t(`${item.booking.status}`) }}
                                </MenuButton>
                                <div>
                                  <MenuItems
                                    class="absolute z-50 right-0 w-[110px] mt-2 origin-top-right bg-white divide-y divide-gray-100 rounded-md shadow-lg ring-1 ring-black/5 focus:outline-none"
                                    :class="[
                                      getLocale(locale)?.direction === 'rtl'
                                        ? '-right-20'
                                        : '-left-40',
                                    ]"
                                  >
                                    <div class="">
                                      <MenuItem
                                        v-for="status in appointmentStatused.filter(
                                          (status) =>
                                            status !== item.booking.status,
                                        )"
                                        :key="status"
                                        class=""
                                      >
                                        <button
                                          type="button"
                                          class="flex items-center px-4 py-3 w-full text-sm text-base font-semibold rounded-md rounded-lg"
                                          :class="{
                                            'hover:text-red-900 hover:bg-red-400  ':
                                              status === 'no-show',
                                            'hover:text-green-900 hover:bg-green-400  ':
                                              status === 'confirmed',
                                            'hover:text-blue-800 hover:bg-blue-400   ':
                                              status === 'completed',
                                          }"
                                          @click.self.stop="
                                            changeAppointmentStatus(
                                              item.booking.uuid,
                                              status,
                                            )
                                          "
                                        >
                                          {{ $t(`${status}`) }}
                                        </button>
                                      </MenuItem>
                                    </div>
                                  </MenuItems>
                                </div>
                              </Menu>
                            </div>
                            <button
                              v-if="item.booking.status == 'confirmed'"
                              class="p-2 font-semibold text-red-600 bg-white rounded-lg border border-red-600 hover:border-red-700 hover:text-red-700"
                              @click.stop="openCancelModal(item.booking.uuid)
                              "
                            >
                              <span>{{ $t("bookingItems.cancel") }}</span>
                            </button>
                          </div>
                        </div>
                      </template>
                      <template v-else>
                        <div class="flex justify-start items-center">
                          <p class="text-base text-gray-700">
                            <img
                              v-if="item.photo"
                              id="image"
                              class="w-5 h-5 rounded-full"
                              :src="item.photo"
                            >
                            <UserCircleIcon
                              v-else
                              class="inline-block w-5 h-5 text-gray-500"
                            />
                            {{ item?.name }}
                            <span v-if="item.provider" class="text-primary-600">
                              تقدم بواسطة
                              {{ item?.provider.name }}
                            </span>
                          </p>
                          <div class="ms-auto">
                            <base-button
                              v-if="item.service_type == 'service'"
                              custome-bg="bg-gray-700"
                              button-type="button"
                              class="w-fit hover:bg-gray-500"
                              @click.stop="
                                emits('openSetAppointmentModal', item)
                              "
                            >
                              {{ $t("set-appointment") }}
                            </base-button>
                            <base-button
                              v-else
                              custome-bg="bg-gray-700"
                              button-type="button"
                              class="w-fit hover:bg-gray-500"
                              @click.stop="
                                router.push({
                                  name: 'package',
                                  params: { id: item.uuid },
                                })
                              "
                            >
                              تفاصيل الباقة
                            </base-button>
                          </div>
                        </div>
                      </template>
                    </div>
                    <div
                      class="flex z-10 gap-2 justify-between items-center px-5 py-3 text-lg font-medium text-black bg-gray-100"
                    >
                      <h3 class="font-medium text-black text-md">
                        {{ $t("subtotal") }}
                      </h3>
                      <h3 class="font-medium text-black text-md">
                        <price-format
                          :form-data="{
                            price: orderDetails.summary.sub_total,
                            currency: user.tenant.currency || '',
                          }"
                        />
                      </h3>
                    </div>
                    <div
                      v-if="orderDetails.summary.discount_amount > 0"
                      class="flex z-10 gap-2 justify-between items-center px-5 py-3 text-lg font-medium text-black bg-gray-100"
                    >
                      <h3 class="font-medium text-black text-md">
                        {{ $t("pos.discount") }}
                      </h3>
                      <h3 class="font-medium text-black text-md">
                        <price-format
                          :form-data="{
                            price: orderDetails.summary.discount_amount,
                            currency: user.tenant.currency || '',
                          }"
                        />
                      </h3>
                    </div>

                    <div
                      v-if="orderDetails.summary?.coupon?.code"
                      class="flex gap-2 justify-between items-center px-5 py-3 text-lg font-medium text-black bg-gray-100"
                    >
                      <h3 class="font-medium text-black text-md">
                        {{ $t("coupon_if_applied") }}
                        <span
                          v-if="
                            orderDetails.summary.coupon_discount_type
                              == 'percentage'
                          "
                          class="text-sm text-underline"
                        >
                          {{ orderDetails.summary.coupon_discount_amount }}%
                        </span>
                        <span
                          class="items-center px-2 py-1 text-green-800 bg-green-200 rounded-md"
                        >{{ orderDetails.summary?.coupon?.code }}</span>
                      </h3>
                      <h3 class="font-medium text-black text-md">
                        <price-format
                          :form-data="{
                            price: orderDetails?.summary.discount_amount,
                            currency: user.tenant.currency || '',
                          }"
                        />
                      </h3>
                    </div>
                    <div
                      class="flex z-10 gap-2 justify-between items-center px-5 py-3 text-lg font-medium text-black bg-gray-100"
                    >
                      <h3 class="font-medium text-black text-md">
                        {{ $t("total_order") }}
                      </h3>
                      <h3 class="font-medium text-black text-md">
                        <price-format
                          :form-data="{
                            price: orderDetails?.summary?.total_amount,
                            currency: user.tenant.currency || '',
                          }"
                        />
                      </h3>
                    </div>
                  </div>
                </div>
                <div v-if="currentTab === 'customerData'">
                  <div
                    v-if="orderDetails.customer?.uuid"
                    class="flex gap-2 items-start mt-3 w-full text-start"
                  >
                    <div
                      v-if="orderDetails.customer?.uuid"
                      class="flex flex-col gap-2 w-full"
                    >
                      <p
                        class="flex flex-col gap-2 justify-between text-xl font-semibold cursor-pointer sm:flex sm:flex-row"
                        @click="
                          router.push({
                            name: 'customer',
                            params: { id: orderDetails.customer.uuid },
                          })
                        "
                      >
                        <span>
                          <span class="block font-bold text-neutral-500">
                            {{ $t("form.customerName") }}
                          </span>
                          <span
                            class="flex gap-1 items-center text-lg hover:text-primary-"
                          >
                            <LinkIcon class="w-5 h-5 text-primary" />
                            {{ orderDetails.customer.first_name }}
                            {{ orderDetails.customer.last_name }}
                          </span>
                        </span>
                      </p>
                      <div class="flex flex-wrap gap-4 items-center mt-2">
                        <a
                          v-if="orderDetails.customer.phone"
                          class="flex justify-center items-center px-2 py-1 w-10 h-10 bg-gray-100 rounded-full"
                          :href="`https://wa.me/${orderDetails.customer.phone}`"
                          target="_blank"
                        >
                          <Icons name="whatsapp" />
                        </a>
                        <span
                          class="flex relative justify-center items-center px-2 py-1 w-10 h-10 bg-gray-100 rounded-full cursor-pointer"
                          @click="emailCopy(orderDetails.customer.email)"
                        >
                          <Icons
                            name="copy"
                            class="inline-block w-10 h-10"
                          />
                          <div
                            class="flex absolute bottom-full flex-col items-center w-max"
                            :class="{ hidden: !emailCopyActivate }"
                          >
                            <span
                              class="relative z-50 p-2 text-xs font-medium leading-none shadow-lg bg-primary-100 text-primary space-no-wrap"
                            >
                              {{ $t("copied_email") }}
                            </span>
                            <div
                              class="-mt-2 w-3 h-3 rotate-45 bg-primary-100"
                            />
                          </div>
                        </span>
                        <a
                          v-if="orderDetails.customer.email"
                          class="flex justify-center items-center px-2 py-1 w-9 h-9 bg-gray-100 rounded-full cursor-pointer"
                          :href="`mailto:${orderDetails.customer.email}`"
                        >
                          <Icons
                            name="sms"
                            class="inline-block w-8 h-8"
                          />
                        </a>
                        <a
                          v-if="orderDetails.customer.phone"
                          class="flex justify-center items-center px-2 py-1 w-9 h-9 bg-gray-100 rounded-full"
                          :href="`tel:${orderDetails.customer.phone}`"
                        >
                          <Icons
                            name="phone"
                            class="inline-block w-6 h-6"
                          />
                        </a>
                      </div>
                      <div class="h-[1px] bg-stone-100 w-full my-2" />
                      <div class="flex flex-col">
                        <span class="block text-xl font-bold text-neutral-500">
                          {{ $t("form.address") }}
                        </span>
                        <p
                          v-if="orderDetails.customer.address"
                          class="mb-2 text-lg"
                        >
                          {{ orderDetails.address }}
                        </p>
                        <span v-else> -- </span>
                        <div
                          v-if="orderDetails.latitude && orderDetails.longitude"
                        >
                          <GoogleMap
                            :api-key="API_KEY"
                            style="width: 100%; height: 200px"
                            :center="{
                              lat: Number(orderDetails.latitude),
                              lng: Number(orderDetails.longitude),
                            }"
                            :draggable="true"
                            :editable="true"
                            :zoom="15"
                          >
                            <MarkerCluster>
                              <Marker
                                :draggable="false"
                                :options="{
                                  position: {
                                    lat: Number(orderDetails.latitude),
                                    lng: Number(orderDetails.longitude),
                                  },
                                }"
                              />
                            </MarkerCluster>
                          </GoogleMap>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p v-else class="text-xl test-start">
                    {{ $t("customer_deleted") }}
                  </p>
                </div>
                <template v-if="currentTab === 'orderLog'">
                  <orderLogs
                    :order-log="orderDetails.activity_log"
                    :with-out-header="true"
                    class="mt-2"
                  />
                </template>
                <template
                  v-if="
                    currentTab === 'additional'
                      && !!orderDetails?.meta_data.length
                  "
                >
                  <div
                    v-for="singleField in orderDetails?.meta_data"
                    :key="singleField.label"
                    class="flex gap-2 items-center py-3 border-b border-stone-100"
                  >
                    <label
                      for="staff"
                      class="block text-sm font-medium text-neutral-500 w-15"
                    >{{ singleField.label }}
                    </label>
                    <div
                      v-if="singleField.type === 'file'"
                      class="relative items-center focus-within:z-10"
                    >
                      <a
                        class="px-3 py-1 text-sm text-white rounded shadow bg-primary-600 hover:bg-primary-400"
                        target="_blank"
                        :href="singleField.path"
                      >
                        {{ $t("Link") }}
                      </a>
                    </div>
                    <div
                      v-else-if="singleField.type !== 'location'"
                      class="relative items-center focus-within:z-10"
                    >
                      {{ singleField.value }}
                    </div>
                    <div v-else class="relative items-center focus-within:z-10">
                      <a
                        target="_blank"
                        :href="`https://maps.google.com/?q=${singleField.value}`"
                      >{{ $t("the_location") }}</a>
                    </div>
                  </div>
                </template>
                <template
                  v-if="
                    currentTab === 'paymentTransactions'
                      && !!orderDetails?.payments?.transcations?.length
                  "
                >
                  <div
                    class="overflow-auto mt-2 ring-1 ring-black ring-opacity-5 shadow md:rounded-lg"
                  >
                    <div class="inline-block min-w-full align-middle">
                      <GenericTable
                        :data="orderDetails.payments.transcations"
                        :headers="transactionHeaders"
                        item-key="id"
                        :on-row-click="openTransactionDetailsModal"
                        tr-class="cursor-pointer"
                      >
                        <template #row="{ item }">
                          <grid-td>
                            {{ item.transaction_no }}
                          </grid-td>
                          <grid-td>
                            {{ formatDateAndTime(item.date) }}
                          </grid-td>
                          <grid-td>
                            {{ item.payment_method.name }}
                          </grid-td>
                          <grid-td>
                            {{ item.amount }} {{ item.currency }}
                          </grid-td>
                          <grid-td>
                            <BookingStatus :book-status="item.status" />
                          </grid-td>
                        </template>
                      </GenericTable>
                    </div>
                  </div>
                </template>
              </div>
              <cancel-booking-modal
                v-if="cancelModal"
                :is-open="cancelModal"
                :booking="bookingWillBeCanceled"
                @closed="cancelModal = false"
                @refresh="bookingCancelled"
              />
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
