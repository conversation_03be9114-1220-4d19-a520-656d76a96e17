<script setup lang="ts">
import { CheckCircleIcon, ExclamationCircleIcon, XCircleIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
  paymentStatus: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <div class="relative flex items-stretch flex-grow focus-within:z-10">
    <div class="text-gray-900">
      <span v-if="paymentStatus === 'unpaid'" class="text-red-600 flex items-center gap-1">
        {{ $t('booking.unpaid') }} <XCircleIcon class="w-4 h-4 text-red-600" />
      </span>
      <span v-if="paymentStatus === 'paid' " class="text-green-600 flex items-center gap-1">
        {{ $t('booking.paid') }} <CheckCircleIcon class="w-4 h-4 text-green-600" /> </span>

      <span v-if="paymentStatus === 'partially-paid'" class="text-yellow-600 flex items-center gap-1">
        {{ $t('booking.partially-paid') }} <ExclamationCircleIcon class="w-5 h-5 text-yellow-600" />
      </span>
    </div>
  </div>
</template>

<style>

</style>
