<script setup lang="ts">
import type { PropType } from 'vue'
import { ArrowPathIcon } from '@heroicons/vue/20/solid'
import type { Booking } from '@/types'
import { formatTime } from '@/composables/dateFormat'
const props = defineProps({
  bookingList: {
    type: Array as PropType<Booking[]>,
    default: () => [],
  },
  bookCustomer: {
    type: String,
    default: '',
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  // TODO : fix default later
  onRowClick: {
    type: Function,
    default: null,
  },
})

const emit = defineEmits(['openBooking'])
const { t } = useI18n()
const slot = useSlots()
const hasActions = computed(() => Boolean(slot?.actions))
const openModal = (item: any) => {
  emit('openBooking', item.order_id)
}
const headers = computed(() => {
  return [

    {
      title: t('order_number'),
    },
    ...(props.bookCustomer !== 'bookCustomer'
      ? [{ title: t('dashboard.booking.customer') }]
      : []),
    {
      title: t('dashboard.booking.staff'),
    },
    {
      title: t('dashboard.booking.service'),
    },
    {
      title: t('dashboard.booking.date'),
    },
    {
      title: t('dashboard.booking.time'),
    },
    {
      title: t('price'),
    },
    {
      title: t('booking.status'),
    },
  ]
})
</script>

<template>
  <generic-table
    :headers="headers"
    :data="bookingList"
    tr-class="cursor-pointer"
    :on-row-click="onRowClick ?? openModal"
    :is-loading="isLoading"
  >
    <template #row="{ item }">
      <grid-td>
        {{ item?.order_number ?? '-' }}
      </grid-td>
      <grid-td
        v-if="bookCustomer !== 'bookCustomer'"
        :default-style="false"
        class="py-2 text-sm text-black whitespace-nowrap"
      >
        <div class="flex items-center">
          <div class="">
            {{ item?.customer?.first_name }}
            {{ item.customer?.last_name }}
          </div>
        </div>
      </grid-td>

      <grid-td
        :default-style="false"
        class="flex gap-1 items-center px-0 py-2 text-sm whitespace-nowrap"
      >
        <div>
          <svg
            v-if="item?.staff?.imageLink === null"
            xmlns="http://www.w3.org/2000/svg"
            class="w-10"
            viewBox="0 0 24 24"
          >
            <path
              fill="#e1e1e1"
              d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z"
            />
          </svg>
          <img
            v-else
            id="image"
            class="w-10 h-10 rounded-full"
            :src="item?.staff?.imageLink"
            :link="null"
          >
        </div>
        <span>
          {{ item.staff?.name }}
        </span>
      </grid-td>

      <grid-td>
        <div class="flex gap-1 items-center">
          <span
            :style="
              `background-color:${item.service.color}`
            " class="block w-3 h-3 rounded"
          />
          <p>
            {{ item.service.name }}
          </p>
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          {{ item.date ?? item.start.split("T")[0] }}
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          {{ formatTime(item.start) }} - {{ formatTime(item.end) }}
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900 text">
          <price-format
            :form-data="{
              price: item?.price,
              currency: item?.currency || '',
            }"
          />
        </div>
      </grid-td>
      <grid-td>
        <booking-status :book-status="item?.status" />
      </grid-td>
    </template>
    <template v-if="hasActions" #actions="{ item }">
      <slot name="actions" :item="item" />
    </template>
  </generic-table>
</template>
