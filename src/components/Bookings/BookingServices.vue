<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'
import type { PropType } from 'vue'
import type { BookingService, Service } from '@/types'

const props = defineProps({
  modelValue: {
    type: Array as PropType<BookingService[]>,
    default: () => [
      {
        id: '',
        duration: 0,
        price: 0,
      },
    ],
  },
  staffId: {
    type: String,
    required: true,
  },
  multipleServices: {
    type: Boolean,
    default: true,
  },
  showQuantity: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:modelValue'])
const { fetchStaffServices } = useStaffStore()
const serviceObject: BookingService = {
  id: '',
  duration: 0,
  price: 0,
  ...(props.showQuantity && { quantity: 1, unit_amount: 0 }),
}
const servicesData = reactive<{ services: Service[] }>({
  services: [],
})
const updateValue = (services: BookingService[]) => {
  emit('update:modelValue', services)
}

const addNewService = () => {
  updateValue([...props.modelValue, { ...serviceObject }])
}

const services = computed(() => {
  return props.modelValue.map(service => ({
    ...service,
    key: new Date().getTime(), // Workaround to add unique keys to v-for
  }))
})
const removeService = (index: number) => {
  const servicesToUpdate = [...props.modelValue]
  servicesToUpdate.splice(index, 1)
  updateValue(servicesToUpdate)
}
const updateService = (value: BookingService, index: number) => {
  const servicesToUpdate = [...props.modelValue]
  servicesToUpdate[index] = value
  updateValue(servicesToUpdate)
}

onMounted(() => {
  if (!props.modelValue.length)
    addNewService()
})
const processing = ref(false)

watch(
  () => props.staffId,
  async (value: string) => {
    if (!value) {
      servicesData.services = []
      updateValue([{ ...serviceObject }])
      return
    }
    processing.value = true
    fetchStaffServices(value, '*')
      .then((res) => {
        servicesData.services = res.data
      })
      .finally(() => {
        processing.value = false
      })
  }, { immediate: true },
)
</script>

<template>
  <!-- <overlay-loader v-if="processing" /> -->
  <div class="pt-3 border-t border-gray-200 sm:col-span-2">
    <h2 class="text-xl font-medium text-gray-900">
      {{ $t("booking.services") }}
    </h2>
    <overlay-loader v-if="processing" />
    <div
      v-for="(service, index) in services"
      :key="`${service.key}-services-${index}`"
      class="gap-2" :class="[{
        'flex flex-col': props.showQuantity,
        'sm:flex sm:flex-row  flex-col': !props.showQuantity,
      }]"
    >
      <BookingServiceFields
        :show-quantity="showQuantity"
        :model-value="{ ...props.modelValue[index] }"
        :services="servicesData.services"
        :name="`${index}`"
        :disabled="!props.staffId"
        @update:model-value="($event) => updateService($event, index)"
      />
      <div
        v-if="props.multipleServices"
        class="flex items-end mb-1.5 justify-center sm:justify-center"
      >
        <div class="flex items-center gap-4">
          <button
            v-if="props.modelValue.length != 1"
            :disabled="!props.staffId"
            type="button"
            class="inline-flex text-gray-400 bg-white rounded-md hover:text-gray-500 focus:outline-none h-full border-2 border-transparent hover:border-red-400 cursor-pointer"
            @click="removeService(index)"
          >
            <TrashIcon class="w-6 h-8 text-red-400" aria-hidden="true" />
          </button>
          <button
            v-if="props.modelValue.length - 1 === index"
            :disabled="!props.staffId"
            type="button"
            class="flex text-gray-400 bg-white rounded-md hover:text-gray-500 border-2 border-transparent hover:border-primary-500 cursor-pointer"
            @click="addNewService"
          >
            <PlusIcon class="w-6 h-8 text-gray-400" aria-hidden="true" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
