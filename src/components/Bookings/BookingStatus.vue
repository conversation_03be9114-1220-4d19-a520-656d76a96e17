<script lang="ts" setup>
const props = defineProps({
  bookStatus: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <div class="flex space-x-4">
    <div v-if="bookStatus === 'canceled'" class="bg-red-200 text-red-800 px-2 py-1 rounded-md flex items-center">
      {{ $t(`booking.the_status.${bookStatus}`) }}
    </div>
    <div v-else-if="bookStatus === 'confirmed'" class="bg-green-200 text-green-800 px-2 py-1 rounded-md flex items-center">
      {{ $t(`booking.the_status.${bookStatus}`) }}
    </div>
    <div v-else-if="bookStatus === 'completed'" class="bg-blue-200 text-blue-800 px-2 py-1 rounded-md flex items-center">
      {{ $t(`booking.the_status.${bookStatus}`) }}
    </div>
    <div v-else-if="bookStatus === 'unpaid'" class="bg-yellow-200 text-yellow-800 px-2 py-1 rounded-md flex items-center">
      {{ $t(`booking.the_status.${bookStatus}`) }}
    </div>
    <div v-else-if="bookStatus === 'pending'" class="bg-orange-200 text-orange-800 px-2 py-1 rounded-md flex items-center">
      {{ $t(`booking.the_status.${bookStatus}`) }}
    </div>
    <div v-else-if="bookStatus === 'rejected'" class="bg-purple-200 text-purple-800 px-2 py-1 rounded-md flex items-center">
      {{ $t(`booking.the_status.${bookStatus}`) }}
    </div>
    <div v-else-if="bookStatus === 'unapproved'" class="bg-gray-200 text-gray-800 px-2 py-1 rounded-md flex items-center">
      {{ $t(`booking.the_status.${bookStatus}`) }}
    </div>
    <div v-else-if="bookStatus === 'no-show'" class="bg-yellow-300 text-yellow-900 px-2 py-1 rounded-md flex items-center">
      {{ $t(`${bookStatus}`) }}
    </div>
  </div>
</template>

<style>

</style>
