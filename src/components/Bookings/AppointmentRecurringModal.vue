<script setup lang="ts">
import useVuelidate from '@vuelidate/core'
import { storeToRefs } from 'pinia'
import type { RecurringAppointment } from '@/types/lookup'
import { required } from '@/utils/i18n-validators'
import { customDateFormat } from '@/composables/dateFormat'
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  appointmentId: {
    type: String,
    default: '',
    required: true,
  },
})
const emits = defineEmits(['close', 'refresh'])
const { createRecurredAppointment } = useEventStore()
const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const processing = ref(false)
const appointmentUuid = ref<string>('')

const formData = reactive<RecurringAppointment>({
  end_date: customDateFormat(new Date().toISOString(), 'YYYY/MM/DD'),
  type: 'daily',
})

const recurringTypes = toRaw([
  {
    label: t('daily'),
    value: 'daily',
  },
  {
    label: t('weekly'),
    value: 'weekly',
  },
  {
    label: t('monthly'),
    value: 'monthly',
  },
  {
    label: t('3months'),
    value: '3months',
  },
])

const rules = {
  end_date: {
    required,
  },
  type: {
    required,
  },
}

const v$ = useVuelidate(rules, formData)

const { appointmentId } = toRefs(props)
watch(
  () => appointmentId.value,
  (val) => {
    if (val)
      appointmentUuid.value = val
  },
  { immediate: true },
)

async function setRecurringAppointment() {
  v$.value.$touch()
  if (v$.value.$invalid)
    return

  processing.value = true
  try {
    await createRecurredAppointment(appointmentUuid.value, formData)
    emits('close')
    emits('refresh')
  }
  finally {
    processing.value = false
  }
}

function setRecurringEndDate(date: string) {
  formData.end_date = customDateFormat(date, 'YYYY/MM/DD')
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    title=""
    panel-classes="w-full max-w-md transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all"
    @close="emits('close')"
  >
    <overlay-loader v-if="processing" :full-screen="processing" />
    <form class="text-center" @submit.prevent="setRecurringAppointment">
      <div class="text-2xl font-bold text-gray-800 mb-4">
        إضافة موعد متكرر
      </div>
      <div class="grid grid-cols-2 md:grid-cols-2 gap-3 mb-4">
        <div class="flex flex-col items-start">
          <label for="recurring_type" class="mb-1 text-sm font-medium text-gray-700">نوع التكرار <span class="text-red-500">*</span></label>
          <SelectInput
            id="recurring_type"
            v-model="formData.type"
            class="w-full py-2 text-gray-700 border-gray-300 rounded-md"
          >
            <option v-for="type in recurringTypes" :key="type?.value" :value="type?.value">
              {{ type.label }}
            </option>
          </SelectInput>
        </div>
        <div class="flex flex-col items-start">
          <label for="recurring_end_date" class="mb-1 text-sm font-medium text-gray-700">تاريخ الانتهاء <span class="text-red-500">*</span></label>
          <DateInput
            id="recurring_end_date"
            v-model="formData.end_date"
            :locale="locale"
            class="block w-full py-2 border-gray-300 rounded-md focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            placeholder="اختر التاريخ"
          />
        </div>
      </div>
      <BaseButton
        class="w-full h-12 text-lg font-bold bg-primary-900 hover:bg-primary-800 text-white rounded-lg mt-2"
        type="submit"
        :processing="processing"
      >
        تكرار الموعد
      </BaseButton>
    </form>
  </Modal>
</template>
