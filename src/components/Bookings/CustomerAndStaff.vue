<script lang="ts" setup>
import { PlusIcon } from '@heroicons/vue/24/outline'
import { useVuelidate } from '@vuelidate/core'
import type { PropType } from 'vue'
import PlusIconBordered from '../Icons/PlusIconBordered.vue'
import { required } from '@/utils/i18n-validators'
import type { Customer, Staff } from '@/types'
import AddCustomerModel from '@/components/Customers/AddCustomerModel.vue'

interface CustomerAndStaff {
  customer_id: string
}
const props = defineProps({
  modelValue: {
    type: Object as PropType<CustomerAndStaff>,
    default: () => ({
      customer_id: null,
    }),
  },
  customer: {
    type: Object as PropType<Customer | undefined>,
    required: false,
    default: () => null,
  },
  disabled: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  showModal: {
    type: Boolean,
    default: false,
  },
  tags: {
    type: Array as PropType<Tag[]>,
    default: () => [],
  },
  title: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['update:modelValue'])

const componentState = reactive<{
  customerLoading: boolean
  staffLoading: boolean
  showCreateCustomerModal: boolean
  customers: Customer[]
  staff: Staff[]
}>({
  customerLoading: false,
  staffLoading: false,
  showCreateCustomerModal: false,
  customers: [],
  staff: [],
})
const rules = {
  customer_id: {
    required,
  },
}
const { fetchCustomerByNameOrPhone } = useCustomerStore()

const processing = ref(false)
const v$ = useVuelidate<CustomerAndStaff>(rules, props.modelValue)
const searchCustomers = async (name = '') => {
  componentState.customerLoading = true
  processing.value = true
  fetchCustomerByNameOrPhone(1, `search=${name}`)
    .then((res) => {
      let customers = res.data
      if (props.modelValue.customer_id) {
        const selectedCustomer = componentState.customers.find(
          customer => customer?.uuid === props.modelValue.customer_id,
        ) as Customer
        customers = [selectedCustomer, ...customers]
      }
      componentState.customers = customers
    })
    .finally(() => {
      processing.value = false
    })
}

const updateValue = (value: CustomerAndStaff) => {
  emit('update:modelValue', value)
}

const customerSelected = (uuid: string) => {
  updateValue({
    customer_id: uuid,
  })
}
const customerCreated = (customer: Customer) => {
  componentState.customers.push(customer)
  customerSelected(customer.uuid)
  componentState.showCreateCustomerModal = false
}

const customerOptions = computed(() => {
  return componentState.customers
    .filter(customer => customer?.uuid)
    .map(customer => ({
      label: `${customer?.first_name || ''} ${customer?.last_name || ''} ${
        customer.phone ? `[ ${customer.phone} ]` : ''
      }`.trim(),
      value: customer.uuid,
    }))
})

watch(
  props,
  (value) => {
    if (value.customer?.uuid)
      componentState.customers.push(value.customer)
    if (value.staff?.uuid)
      componentState.staff.push(value.staff)
  },
  { immediate: true },
)
</script>

<template>
  <div>
    <form-group :validation="v$" name="customer_id">
      <template #default="{ attrs }">
        <div class="flex flex-col gap-2 items-start">
          <div
            class="block w-full text-gray-700 rounded-none border-gray-300 focus:border-primary-500 focus:ring-primary-500"
            :disabled="disabled"
          >
            <BaseComboBox
              required
              :model-value="props.modelValue.customer_id"
              arial-label="Search"
              :options="customerOptions"
              class="block w-full text-gray-700 rounded-none border-gray-300 w-100 focus:border-primary-500 focus:ring-primary-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
              server-filter
              place-holder="search_phone_or_name"
              :error="v$.$dirty && v$.customer_id.$error"
              v-bind="attrs"
              :loading="processing"
              :disabled="disabled"
              @search="searchCustomers"
              @update:model-value="customerSelected"
            >
              {{ $t("booking.customer") }}
            </BaseComboBox>
          </div>
          <button
            type="button"
            class="flex items-center gap-1 text-gray-500 hover:text-blue-400 focus:outline-none disabled:text-gray-300"
            :disabled="disabled"
            @click="componentState.showCreateCustomerModal = true"
          >
            <PlusIconBordered class="w-5 h-5 text-blue-400" aria-hidden="true" />
            <span class="text-base">{{ $t("أضف عميل جديد") }}</span>
          </button>
        </div>
      </template>
    </form-group>
    <AddCustomerModel
      v-if="componentState.showCreateCustomerModal"
      :show-modal="componentState.showCreateCustomerModal"
      :title="$t('customer')"
      @created="customerCreated"
      @closed="componentState.showCreateCustomerModal = false"
    />
  </div>
</template>
