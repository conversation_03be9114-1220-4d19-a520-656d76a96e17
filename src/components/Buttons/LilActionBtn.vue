<script setup lang="ts">
import { defineEmits, defineProps } from 'vue'

const props = defineProps({
  icon: {
    type: Object,
    required: true,
  },
  label: {
    type: String,
    default: '',
  },
  class: {
    type: String,
    default: '',
  },
  transparent: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['click']);
</script>

<template>
  <button
    :class="[
      'flex items-center',
      label ? 'gap-1' : '',
      transparent ? '' : 'p-2 h-6 bg-indigo-50 rounded-lg',
      props.class
    ]"
    @click="$emit('click')"
    type="button"
  >
    <span v-if="label" class="text-xs font-normal text-sky-950">{{ label }}</span>
    <component :is="icon" class="w-4 h-4 text-sky-950" />
  </button>
</template>
