<script lang="ts" setup>
import type { PropType } from "vue";
import { computed } from "vue";
import { storeToRefs } from "pinia";
import TheLoader from "../TheLoader.vue";
// Simple spinner for loading state (can be replaced with your TheLoader)
const Spinner = defineComponent({
  template: `<svg class="animate-spin" width="20" height="20" viewBox="0 0 50 50"><circle class="opacity-25" cx="25" cy="25" r="20" fill="none" stroke="#fff" stroke-width="5"/><path class="opacity-75" fill="#fff" d="M25 5a20 20 0 0 1 20 20h-5a15 15 0 1 0-15 15v5A20 20 0 0 1 25 5z"/></svg>`
});

const props = defineProps({
  // Legacy props
  processing: { type: Boolean, default: false },
  showIcon: { type: Boolean, default: false },
  buttonType: { type: String as PropType<'button' | 'submit'>, default: 'submit' },
  customeBg: { type: String, default: '' },
  oneSideRounded: { type: Boolean, default: false },
  defaultStyle: { type: Boolean, default: true },
  
  // New design system props
  label: { type: String, default: '' },
  variant: { type: String as PropType<'filled' | 'stroke'>, default: 'filled' },
  icon: { type: Object as PropType<typeof defineComponent | null>, default: null },
  iconPosition: { type: String as PropType<'left' | 'right'>, default: 'left' },
  size: { type: String as PropType<'sm' | 'md' | 'lg'>, default: 'md' },
  block: { type: Boolean, default: false }
});

const { locale } = useI18n();
const { getLocale } = storeToRefs(useLocalesStore());

// Base styles
const baseClasses = computed(() => [
  "flex items-center justify-center gap-2 transition font-medium focus:outline-none",
  props.defaultStyle ? 'rounded-md' : '',
  props.oneSideRounded && getLocale(locale)?.direction === 'rtl' ? 'rounded-l-md' : 'rounded-r-md',
  props.block ? 'w-full' : 'w-fit'
].filter(Boolean).join(' '));

// Variant styles
const variantClasses = computed(() => {
  if (!props.defaultStyle) return props.customeBg || 'bg-secondary';
  
  return props.variant === 'filled' 
    ? 'bg-[#0F2C3F] text-white hover:bg-[#35506a] active:bg-[#0F2C3F] disabled:bg-gray-200 disabled:text-gray-400'
    : 'bg-white border border-[#0F2C3F] text-[#0F2C3F] hover:bg-[#f5f8fa] hover:text-[#35506a] active:bg-[#e6eaf0] disabled:bg-white disabled:text-gray-400 disabled:border-gray-200';
});

// Size styles
const sizeClasses = computed(() => {
  if (!props.defaultStyle) return 'py-2 text-h3 sm:px-4 px-2';
  
  const sizes = {
    sm: "min-w-[100px] min-h-[36px] px-3 py-2 text-sm",
    md: "min-w-[140px] min-h-[44px] px-5 py-2.5 text-base",
    lg: "min-w-[180px] min-h-[52px] px-8 py-3 text-lg"
  };
  return sizes[props.size];
});

// Computed classes
const classes = computed(() => [
  baseClasses.value,
  variantClasses.value,
  sizeClasses.value
].join(' '));

const isDisabled = computed(() => props.processing);
</script>

<template>
  <button
    :type="buttonType"
    :class="classes"
    :disabled="isDisabled"
    v-bind="$attrs"
  >
    <!-- Loading state -->
    <template v-if="processing">
      <TheLoader />
      <span v-if="label">{{ label }}</span>
      <slot v-else />
    </template>
    
    <!-- Normal state -->
    <template v-else>
      <!-- Left icon -->
      <template v-if="icon && iconPosition === 'left'">
        <component :is="icon" class="w-5 h-5" />
      </template>
      
      <!-- Content -->
      <span v-if="label">{{ label }}</span>
      <slot v-else />
      
      <!-- Right icon -->
      <template v-if="icon && iconPosition === 'right'">
        <component :is="icon" class="w-5 h-5" />
      </template>
    </template>
  </button>
</template>

<style scoped>
button {
  transition: background 0.2s, color 0.2s, border 0.2s;
}
</style>