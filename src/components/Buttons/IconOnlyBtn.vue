<script setup lang="ts">
import type { Component, PropType } from 'vue'

defineProps({
  icon: {
    type: [Object, String] as PropType<Component | string>,
    required: true,
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value: string) => ['default', 'primary', 'danger'].includes(value),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  alt: {
    type: String,
    default: '',
  },
  iconColor: {
    type: String,
    default: '',
  },
  borderColor: {
    type: String,
    default: 'border-gray-200',
  },
})
</script>

<template>
  <button
    class="p-2 rounded-lg flex items-center justify-center transition-colors duration-200 hover:bg-gray-50 focus:outline-none focus:ring-2 sm:text-sm border" :class="[
      {
        'text-gray-600 hover:text-gray-900 focus:ring-primary-500': variant === 'default',
        'text-primary-600 hover:text-primary-800 focus:ring-primary-500': variant === 'primary',
        'text-rose-500 hover:text-rose-700 focus:ring-rose-500': variant === 'danger',
      },
      { 'disabled:cursor-not-allowed disabled:opacity-50 hover:bg-transparent': disabled },
      borderColor,
    ]"
    :disabled="disabled"
  >
    <component
      :is="icon"
      class="w-5 h-5"
      :class="iconColor"
      :alt="alt"
    />
  </button>
</template>
