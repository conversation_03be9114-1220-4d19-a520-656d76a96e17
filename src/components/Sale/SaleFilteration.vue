<script setup lang="ts">
import dayjs from 'dayjs'
import VueTailwindDatepicker from 'vue-tailwind-datepicker'
import { storeToRefs } from 'pinia'
import type { PropType } from 'vue'
import type { Tag } from '@/types'
import { locatizedOption } from '@/composables/useDatePicker'

const props = defineProps({
  tags: {
    type: Array as PropType<Tag[]>,
    default: () => [],
  },
})
const emit = defineEmits(['changed'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const salesId = ref('')
const range: any = ref([])
const rangeDeb = debouncedRef(range, 500)
const statusList = ref('')
const statusDebounced = debouncedRef(statusList, 500)
const formatDate = ref({
  date: 'DD MMM YYYY',
})
const id = debouncedRef(salesId, 500)
interface Payload {
  id: string
  range: string
  status: string
}

function formattedRange(dateRange: string[]) {
  if (dateRange === null)
    return dateRange

  const [start, end] = dateRange

  return `from=${dayjs(start).format('YYYY-MM-DD')}&to=${dayjs(end).format('YYYY-MM-DD')}`
}

watch([id, range, statusList, rangeDeb, statusDebounced], (val) => {
  const [id, range, status] = val
  const payload: Payload = {
    id,
    range: Object.keys(JSON.parse(JSON.stringify(range))).length ? formattedRange(range as string[]) : '',
    status,
  }
  emit('changed', payload)
})
</script>

<template>
  <div class="mt-4">
    <div class="grid grid-cols-1 mt-6 gap-y-6 gap-x-4 md:grid-cols-3">
      <div>
        <div class="mt-1">
          <NumberInput id="sales-id" v-model="salesId" :placeholder="$t('form.id')" :label="$t('form.id') " class="block w-full border-gray-300 rounded-md   focus:border-primary-500 focus:ring-primary-500 sm:text-sm py-3" />
        </div>
      </div>
      <div>
        <label class="  mb-2 w-full flex items-center text-start text-[#261E27] text-base    ">{{ $t('Date') }}</label>
        <VueTailwindDatepicker
          v-model="range"
          :formatter="formatDate" use-range class="block  border-gray-300 rounded-md sm:text-sm py-1"
          :placeholder="$t('form.date_rage')"
          :dir="getLocale(locale)?.direction === 'rtl' ? 'ltr' : 'rtl'"
          :options="locatizedOption"
          :i18n="getLocale(locale)?.id === 'ar' ? 'ar-sa' : 'en'"
        />
      </div>

      <div>
        <div class="mt-1">
          <SelectInput
            id="staff"
            v-model="statusList"
            :label="$t('booking.status')"
            :placeholder="$t('booking.status')"
          >
            <option value="">
              {{ $t("form.select") }}
            </option>
            <option value="draft">
              {{ $t("draft") }}
            </option>
            <option value="approved">
              {{ $t("approved") }}
            </option>
            <option value="paid">
              {{ $t("paid") }}
            </option>
            <option value="partial-paid">
              {{ $t("partial-paid") }}
            </option>
            <option value="unpaid">
              {{ $t("unpaid") }}
            </option>
          </SelectInput>
        </div>
      </div>
    </div>
  </div>
</template>
