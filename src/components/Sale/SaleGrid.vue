<script setup lang="ts">
import type { PropType } from 'vue'
import usePos from '@/composables/usePos'
import type { Sale } from '@/types'
import { formatDateAndTime } from '@/composables/dateFormat'
const props = defineProps({
  list: {
    type: Array as PropType<Sale[]>,
    default: () => [],
  },
  loadingSales: {
    type: Boolean,
    default: false,
  },
})
const router = useRouter()
const { t } = useI18n()
const { printInvoice } = usePos()

const headers = computed(() => {
  return [
    {
      title: t('form.invoice#'),
    },
    {
      title: t('form.client'),
    },
    {
      title: t('form.sale_date'),
    },
    {
      title: t('form.no_of_items'),
    },
    {
      title: t('form.location'),
    },
    {
      title: t('booking.payment_status'),
    },
    {
      title: t('form.status'),
    },
    {
      title: t('form.price'),
    },
  ]
})
const readableDate = (date) => {
  return formatDateAndTime(date)
}

const print = async (invoice) => {
  printInvoice(invoice.id)
}

const openInvoiceDetail = ({ id }) => {
  router.push({ name: 'invoice', params: { id } })
}
</script>

<template>
  <generic-table
    :data="list" :is-loading="loadingSales" :headers="headers" tr-class="cursor-pointer"
    :on-row-click="openInvoiceDetail" item-key="id"
  >
    <template #row="{ item }">
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.InvoiceNum">
            {{ item?.InvoiceNum }}
          </span>
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.customer">

            {{ item.customer?.first_name ? `${item?.customer?.first_name} ${item?.customer?.last_name || ''}`
              : $t('pos.guest') }}

          </span>
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.created_at">
            {{ readableDate(item?.created_at) }}
          </span>
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.items">
            {{ item?.items }} {{ $t('item', { count: item?.items }) }}
          </span>
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.branch">
            {{ item?.branch?.name }}
          </span>
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span
            v-if="item.payment_status == 'paid'"
            class="inline-flex items-center rounded px-2.5 py-0.5 text-sm font-medium bg-green-100 text-green-800"
          >{{
            $t(`booking.${item.payment_status}`) }}</span>
          <span
            v-if="item.payment_status == 'unpaid'"
            class="inline-flex items-center rounded px-2.5 py-0.5 text-sm font-medium bg-red-100 text-red-800"
          >{{
            $t(`booking.${item.payment_status}`) }}</span>
          <span
            v-if="item.payment_status == 'partially-paid'"
            class="inline-flex items-center rounded px-2.5 py-0.5 text-sm font-medium bg-gray-100 text-gray-800"
          >{{
            $t(`booking.${item.payment_status}`) }}</span>
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <sale-status v-if="item?.status" :status="item.status" />
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.total">
            <price-format
              :form-data="{
                price: item?.total,
                currency: item?.currency || '',
              }"
            />
          </span>
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
    </template>
  </generic-table>
</template>
