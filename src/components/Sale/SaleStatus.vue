<script setup>
const props = defineProps({
  status: {
    type: String,
    default: '',
  },
})
const statusClass = computed(() => {
  switch (props.status) {
    case 'paid':
      return 'bg-green-100 text-green-800'
    case 'approved':
      return 'bg-yellow-100 text-yellow-800'
    case 'partial-paid':
      return 'bg-blue-100 text-blue-800'
    case 'draft':
      return 'bg-gray-100 text-gray-800'
  }
})
</script>

<template>
  <span class="inline-flex items-center rounded px-2.5 py-0.5 text-sm font-medium" :class="statusClass">{{ $t(`pos.status.${status}`) }}</span>
</template>
