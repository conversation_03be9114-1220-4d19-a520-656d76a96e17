<script setup lang="ts">
import type { PropType } from 'vue'
import type { Products } from '@/types/products'
import type { header } from '@/types/table'
const props = defineProps({
  headers: {
    type: Array as PropType<header[]>,
    default: () => [],
  },
  data: {
    type: Array as PropType<Object[]>,
    default: () => [],
  },
  trClass: {
    type: String,
    default: '',
  },
  onRowClick: {
    type: Function,
    default: () => ({}),
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  itemKey: {
    type: String,
    default: '',
  },
  customClassesPerTr: {
    type: Function,
    default: () => ({}),
  },
  sticky: {
    type: Boolean,
    default: false,
  },
})
const slot = useSlots()
const hasActions = computed(() => Boolean(slot?.actions))
</script>

<template>
  <!--  min-h-[150px] -> To be loader visible at first time if not data -->
  <table
    class="divide-y divide-[#E8E8E8] w-full min-w-[1200px] md:min-w-0"
    :class="{ 'min-h-[150px]': isLoading }"
  >
    <thead
      class="bg-[#F8F8F8] w-full text-xs border-b border-[#E8E8E8]  min-w-[900px]"
      :class="{ 'sticky top-0 z-10': sticky }"
    >
      <tr class="">
        <grid-th
          v-for="(header, index) in headers"
          :key="index"
          class="px-2 py-3 whitespace-normal text-start !text-[#7C7C7C]" :class="[header.className]"
        >
          {{ header.title }}
        </grid-th>
        <grid-th v-if="hasActions">
          #
        </grid-th>
      </tr>
    </thead>
    <tbody class="bg-white divide-y divide-[#E8E8E8] relative">
      <OverlayLoader v-if="isLoading" :full-screen="false" />
      <tr
        v-for="(item, index) in data"
        :key="itemKey ? item[itemKey] : index"
        :class="[trClass, customClassesPerTr(item, index)]"
        @click="onRowClick(item)"
      >
        <slot name="row" :item="item" :index="index" />
        <!-- If has action col -->
        <grid-td v-if="hasActions">
          <slot name="actions" :item="item" />
        </grid-td>
      </tr>
      <tr v-if="!data.length && !isLoading">
        <slot name="no-data">
          <grid-td colspan="6">
            <div class="text-gray-900">
              {{ $t("pagination.empty") }}
            </div>
          </grid-td>
        </slot>
      </tr>
    </tbody>
  </table>
</template>
