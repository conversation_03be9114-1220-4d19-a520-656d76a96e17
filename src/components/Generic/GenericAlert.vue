<script setup lang="ts">
import type { ComputedRef, FunctionalComponent, PropType } from 'vue'

const props = defineProps({
  type: {
    type: String as PropType<'success' | 'error' | 'warning' | 'info'>,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
  message: {
    type: String,
    default: '',
  },
  customClasses: {
    type: String,
    default: '',
  },
})
const { typeAndColorMapping } = useNotifications()
const alertType: ComputedRef<{ [key: string]: string | FunctionalComponent }>
  = computed(() => {
    return typeAndColorMapping[props.type]
  })
</script>

<template>
  <div
    class="flex items-center px-3 py-3 my-3 rounded-lg w-fit"
    :class="[alertType.style, customClasses]"
  >
    <Component :is="alertType.icon" class="w-8 h-8 me-1" aria-hidden="true" />
    <div>
      <h2 v-if="title" class="font-semibold">
        {{ title }}
      </h2>
      <p class="text-md">
        {{ message }}
      </p>
    </div>
  </div>
</template>
