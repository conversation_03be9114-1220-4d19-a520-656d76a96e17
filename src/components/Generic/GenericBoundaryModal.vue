<script setup lang="ts">
import type { PropType } from 'vue'
import { GoogleMap, Polygon } from 'vue3-google-map'
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { required } from '@/utils/i18n-validators'
const props = defineProps({
  showModal: Boolean,
  boundaryId: {
    type: String,
    required: false,
  },
  itemId: {
    type: String,
    required: false,
  },
  model: {
    type: String as PropType<TModel>,
    required: true,
  },
})
const emits = defineEmits(['update:showModal', 'created', 'close'])
const API_KEY = import.meta.env.VITE_GOOGLE_MAP_KEY
type TModel = 'staff' | 'team'
const { locale } = useI18n()

const proccessing = ref(false)

const { boundaryId } = toRefs(props)
const editMode = computed(() => !!boundaryId?.value)

const map = ref(null)
const { getLocale } = storeToRefs(useLocalesStore())
const { createBoundary, updateBoundary, fetchSingleBoundary } = useBoundaries()

const { coords, calculateCenter } = useMap()
const center = ref()

const formData = ref({
  name: '',
  fees: 0,
  boundary: [],
  team_id: null,
})
const rules = {
  name: {
    required,
  },
  team_id: {
    required,
  },
}
const v$ = useVuelidate(rules, formData)
const { userInfo } = storeToRefs(useAuthStore())

const bermudaTriangle = reactive({
  strokeColor: '#FF0000',
  strokeOpacity: 0.8,
  strokeWeight: 2,
  fillColor: '#FF0000',
  fillOpacity: 0.35,
  editable: true,
  draggable: true,
  geodesic: true,
  paths: [],
})

const googleMapPolygon = ref(null)

// watch when boundaryId is changed to fetch the boundary and set the polygon path
watch(
  () => boundaryId?.value,
  async (newVal) => {
    if (newVal && editMode.value) {
      const response = await fetchSingleBoundary(boundaryId?.value as string)
      const paths = response.boundary.coordinates.flat().map(([lng, lat]) => {
        return { lat, lng }
      })
      formData.value.name = response.name
      formData.value.fees = response.fees
      formData.value.boundary = paths
      formData.value.team_id = response.team.id
      googleMapPolygon.value.polygon.setPath(formData.value.boundary)
      center.value = calculateCenter(paths)
    }
  },
  { immediate: true },
)

// watch when polygon is created and coords are available to set the center of the polygon
watch(
  () => [googleMapPolygon.value?.polygon, coords.value],
  ([polygon, coords]) => {
    if (polygon && coords) {
      center.value = { lat: coords.latitude, lng: coords.longitude }
      initPolygonCoordinates(center.value)
    }
  },
  { immediate: true },
)

function initPolygonCoordinates(initValue) {
  const radius = 0.01 // Adjust this to scale the triangle size
  const angleOffset = Math.PI / 3 // Offset to rotate the triangle
  const initShape = [
    {
      lat: initValue.lat + radius * Math.sin(angleOffset),
      lng: initValue.lng + radius * Math.cos(angleOffset),
    },
    {
      lat: initValue.lat + radius * Math.sin(angleOffset + (2 * Math.PI) / 3),
      lng: initValue.lng + radius * Math.cos(angleOffset + (2 * Math.PI) / 3),
    },
    {
      lat: initValue.lat + radius * Math.sin(angleOffset + (4 * Math.PI) / 3),
      lng: initValue.lng + radius * Math.cos(angleOffset + (4 * Math.PI) / 3),
    },
  ]
  googleMapPolygon.value.polygon.setPath(initShape)
}

const updateFormByPolygon = () => {
  const paths = googleMapPolygon.value.polygon
    .getPath()
    .getArray()
    .map((el) => {
      return { lat: el.lat(), lng: el.lng() }
    })
  formData.value.boundary = paths
}

const createRecord = async () => {
  try {
    proccessing.value = true
    updateFormByPolygon()
    await createBoundary(formData.value)
    emits('created')
    resetForm()
  }
  finally {
    proccessing.value = false
  }
}
const updateRecord = async () => {
  try {
    proccessing.value = true
    updateFormByPolygon()
    await updateBoundary(props.boundaryId, formData.value)
    closeModal()
    resetForm()
    emits('updated')
  }
  finally {
    proccessing.value = false
  }
}

const submitForm = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return
  if (editMode.value)
    await updateRecord()
  else await createRecord()
}
function resetForm() {
  v$.value.$reset()
  formData.value = {
    name: '',
    boundary: [],
    team_id: null,
  }
}
const closeModal = () => {
  resetForm()
  emits('update:showModal', false)
  emits('close')
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="editMode ? 'edit_zone' : 'new_zone'"
    @close="closeModal"
  >
    <form class="relative" @submit.prevent="submitForm">
      <overlay-loader v-if="proccessing" :full-screen="false" />
      <div class="w-full">
        <div
          class="grid grid-cols-1 gap-5 mb-4 w-full lg:grid-cols-3 text-start"
        >
          <div class="col-span-1">
            <form-group :validation="v$" name="name">
              <template #default="{ attrs }">
                <TextInput
                  id="name"
                  v-bind="attrs"
                  v-model="formData.name"
                  :label="$t('form.name')"
                  :placeholder="$t('form.name')"
                />
              </template>
            </form-group>
          </div>
          <div class="col-span-1">
            <form-group :validation="v$" name="team_id">
              <template #default="{ attrs }">
                <SelectInput
                  id="branch"
                  v-bind="attrs"
                  v-model="formData.team_id"
                  :label="$t('form.branch')"
                >
                  <option value="">
                    {{ $t("form.select") }}
                  </option>
                  <option
                    v-for="team in userInfo.teams"
                    :key="team.uuid"
                    :value="team.uuid"
                  >
                    {{ team.name }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
          <div class="col-span-1">
            <form-group :validation="v$" name="fees">
              <template #default="{ attrs }">
                <NumberInput
                  id="fees"
                  v-bind="attrs"
                  v-model="formData.fees"
                  :label="$t('form.boundary_fees')"
                  :placeholder="$t('form.boundary_fees')"
                />
              </template>
            </form-group>
          </div>
        </div>
      </div>
      <GoogleMap
        ref="map"
        :api-key="API_KEY"
        class="w-full h-96"
        :center="center"
        :zoom="13"
      >
        <Polygon ref="googleMapPolygon" :options="bermudaTriangle" />
      </GoogleMap>
      <div>
        <base-button class="mx-auto mt-4" type="submit">
          {{ editMode ? $t("form.update") : $t("form.create") }}
        </base-button>
      </div>
    </form>
  </Modal>
</template>
