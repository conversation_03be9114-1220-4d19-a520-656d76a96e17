<template>
  <div
    class="'relative overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    <template v-if="getWorkingHours.length">
      <div class="p-4 sm:p-8">
        <div class="flex gap-4 items-start">
          <div class="flex flex-wrap gap-4 items-stretch w-full">
            <base-button
              v-for="btn in getWorkingHours"
              :key="btn.uuid"
              type="button"
              class="border-1 border sm:text-lg text-base border-gray-300 flex flex-col !py-0.5 "
              custome-bg="bg-gray-100 "
              :class="{
                ' !text-white bg-primary-400 border-primary-400':
                  btn.uuid === activeWorkingHour.uuid,
                '!text-gray-700': btn.uuid !== activeWorkingHour.uuid,
              }"
              @click="changeActiveWorkingHour(btn.uuid)"
            >
              {{ btn.name }}
              <span v-if="btn.start && btn.end" class="text-[10px]">
                {{ $t('from') }} : {{ btn.start }} - {{ $t('to') }} : {{ btn.end }}
              </span>
            </base-button>
            <base-button
              v-if="!withoutServer"
              type="button"
              class="border-1  border sm:text-lg text-base border-gray-300  hover:!bg-green-900"
              custome-bg="bg-green-700"
              @click="openCreateWorkingHourModal"
            >
              {{ $t("form.create") }}
              <PlusIcon class="w-5 h-5" />
            </base-button>
          </div>
          <div v-if="showControlBtns" class="relative w-fit ms-auto">
            <button class="" @click="showOperationMenu = !showOperationMenu">
              <Cog8ToothIcon class="w-6 h-6 text-gray-900" />
            </button>
            <div
              v-if="showOperationMenu"
              class="absolute left-1/2 z-10 py-2 mt-2.5 bg-white rounded-md ring-1 shadow-lg transform origin-top-right -translate-x-1/2 w-fit ring-gray-900/5"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="user-menu-button"
              tabindex="-1"
            >
              <button
                v-for="(operation, index) in operationsMenu"
                :key="index"
                class="block px-3 py-1 w-full text-sm leading-6 text-gray-900 hover:bg-gray-100"
                role="menuitem"
                tabindex="-1"
                type="button"
                @click="operation.action"
              >
                {{ $t(operation.text) }}
              </button>
            </div>
          </div>
        </div>
      </div>
      <working-hour-intervals
        v-if="getWorkingHours.length"
        :working-hour-intervals="activeWorkingHour.intervals"
        :workingHourId="activeWorkingHour.uuid"
        :withoutServer="withoutServer"

      />
    </template>
    <slot v-else name="empty-state" :open-create-modal="openCreateWorkingHourModal" >
      <empty-state
        text="staffHourse"
        btn-text="addStaffHours"
        @show-hours="createFirstWorkingHour"
      />
    </slot>
    <slot name="create-or-edit-modal" :select-item="selectedWorkingHour">
      <working-hour-control-modal
      v-if="showWorkingHourModal"
      :is-first-working-hour="isFirstWorkingHour"
      :is-open="showWorkingHourModal"
      :operations-config="{
        create: handleCreateWorkingHour,
        update: handleUpdateWorkingHour,
      }"
      :model="model"
      :workingHour="selectedWorkingHour || undefined"
      @closed="showWorkingHourModal = false"
      :id="itemId"
      @addedNewWorkingHour="handleAddedNewWorkingHour"
      />
    </slot>
  </div>
</template>

<script lang="ts" setup>
type TModel = "staff" | "team";
import type { WorkingHour } from "@/types";
import { storeToRefs } from "pinia";
import { Cog8ToothIcon , PlusIcon } from "@heroicons/vue/24/solid";
import type { PropType, Ref } from "vue";
import { useI18n } from "vue-i18n";
const processing = ref(false);
const { t } = useI18n();

const emit = defineEmits<{
  (e: 'update:workingHours', workingHours: WorkingHour[]): void
 
}>();

const props = defineProps({
  itemId: {
    type: String,
    required: true,
  },
  model: {
    type: String as PropType<TModel>,
    required: true,
  },
  withoutServer: {
    type: Boolean,
    default: false,
  },
});

const {
  fetchWorkingHours,
  fetchActiveWorkingHour,
  deleteWorkingHour,
  createWorkingHour,
  updateWorkingHour,
  setManualWorkingHours
} = useWorkingHour();

onMounted(async () => {
  if (!props.withoutServer) {
    processing.value = true;
    await Promise.all([
      fetchWorkingHours(props.model, props.itemId),
      fetchActiveWorkingHour(props.model, props.itemId),
    ]);
    processing.value = false;
  }else{
    // defailt working hours
    setManualWorkingHours([{
      intervals: [
    {
        "day": "sun",
        "intervals": [
            {
                "uuid": "303b76cc-e3ce-483b-80e0-fbcddfb78014",
                "day": "sun",
                "from": "09:00",
                "to": "17:00",
                "from_time": {
                    "hours": "09",
                    "minutes": "00"
                },
                "to_time": {
                    "hours": "17",
                    "minutes": "00"
                },
                "active": true
            },
        ]
    },
    {
        "day": "mon",
        "intervals": [
            {
                "uuid": "4c37e996-ae6b-481b-9dcd-e91afacd2848",
                "day": "thu",
                "from": "09:00",
                "to": "17:00",
                "from_time": {
                    "hours": "09",
                    "minutes": "00"
                },
                "to_time": {
                    "hours": "17",
                    "minutes": "00"
                },
                "active": true
            }
        ]
    },
    {
        "day": "tue",
        "intervals": [
            {
                "uuid": "798c5999-c67a-47bf-bf48-d277ea5887a7",
                "day": "tue",
                "from": "09:00",
                "to": "17:00",
                "from_time": {
                    "hours": "09",
                    "minutes": "00"
                },
                "to_time": {
                    "hours": "17",
                    "minutes": "00"
                },
                "active": true
            },
            
        ]
    },
    {
        "day": "wed",
        "intervals": [
            {
                "uuid": "4c37e996-ae6b-481b-9dcd-e91afacd2848",
                "day": "thu",
                "from": "09:00",
                "to": "17:00",
                "from_time": {
                    "hours": "09",
                    "minutes": "00"
                },
                "to_time": {
                    "hours": "17",
                    "minutes": "00"
                },
                "active": true
            }
        ]
    },
      {
        "day": "thu",
        "intervals": [
            {
                "uuid": "4c37e996-ae6b-481b-9dcd-e91afacd2848",
                "day": "thu",
                "from": "09:00",
                "to": "17:00",
                "from_time": {
                    "hours": "09",
                    "minutes": "00"
                },
                "to_time": {
                    "hours": "17",
                    "minutes": "00"
                },
                "active": true
            }
        ]
    },
    {
        "day": "fri",
        "intervals": []
    },
    {
        "day": "sat",
        "intervals": [
            {
                "uuid": "18072298-a043-4a25-b8cc-6150f1f98c4d",
                "day": "sat",
                "from": "09:00",
                "to": "17:00",
                "from_time": {
                    "hours": "09",
                    "minutes": "00"
                },
                "to_time": {
                    "hours": "17",
                    "minutes": "00"
                },
                "active": true
            },
         
        ]
    }
],
name: t("complete_profile.working_hours_title"),
    }])
  }
});

// Working Hour Modal
const selectedWorkingHour: Ref<WorkingHour | null> = ref(null);
const showWorkingHourModal = ref(false);
const openCreateWorkingHourModal = () => {
  showWorkingHourModal.value = true;
  selectedWorkingHour.value = null;
};
const openEditWorkingHourModal = () => {
  showWorkingHourModal.value = true;
  selectedWorkingHour.value = activeWorkingHour.value;
};

// Working Hours
const { getWorkingHours } = storeToRefs(useWorkingHour());
const activeWorkingHour = computed(() => {
  return (
    getWorkingHours.value.find((btn) => btn.active) ||
    getWorkingHours.value?.[0] ||
    {}
  );
});

const changeActiveWorkingHour = (uuid: string) => {
  getWorkingHours.value.forEach((btn) => {
    btn.active = btn.uuid === uuid;
  });
  emit('update:workingHours', getWorkingHours.value);
};

const isFirstWorkingHour = computed(() => {
  return  (selectedWorkingHour.value?.uuid && selectedWorkingHour.value?.start === null && selectedWorkingHour.value.end === null);
});

// Operations Menu
const showOperationMenu = ref(false);
const operationsMenu = markRaw([
  {
    text: "form.edit",
    action: () => {
      openEditWorkingHourModal();
      showOperationMenu.value = false;
    },
  },
  {
    text: "form.delete",
    action: async () => {
        await deleteWorkingHour(
          props.model,
          props.itemId,
          activeWorkingHour.value.uuid
        );
        showOperationMenu.value = false; 
      },
    }
]);

const showControlBtns = computed(() => {
  return getWorkingHours.value.length > 0 && (props.model == 'team' && activeWorkingHour.value.start == null && activeWorkingHour.value.end == null ? false : true);
});

const handleCreateWorkingHour = async (model: string, id: string, formDate: any) => {
    await createWorkingHour(model, id, formDate);
};

const handleUpdateWorkingHour = async (model: string, id: string, workingHourId: string, formDate: any) => {
    await updateWorkingHour(model, id, workingHourId, formDate);
};

const handleAddedNewWorkingHour = () => {
  if (!props.withoutServer) {
    fetchWorkingHours(props.model, props.itemId);
  }
};

const createFirstWorkingHour = async () => {
  const defaultWorkingHourPayload = {
    name: "",
    start: "",
    end: "",
  };
  
 
    await createWorkingHour(props.model, props.itemId, defaultWorkingHourPayload);
    fetchWorkingHours(props.model, props.itemId);
};

watch(() => getWorkingHours.value, (newVal) => {
  emit('update:workingHours', newVal);
} , {deep: true});


</script>
