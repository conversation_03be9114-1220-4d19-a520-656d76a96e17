<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import type { PropType } from 'vue'
import { useLabels } from '@/stores/labels'
import type { Labels } from '@/types/labels'
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  label: {
    type: Object as PropType<Labels | null>,
    default: () => ({}),
  },
})

const emit = defineEmits(['close'])

const { locale } = useI18n()
const { label } = toRefs(props)
const { getLocale } = storeToRefs(useLocalesStore())
const { createLabels, updateLabels, deleteLabels } = useLabels()
const formData = reactive({
  name: '',
  color: '',
})
const rules = {
  name: {
    required,
  },
  color: {
    required,
  },
}
const processing = ref(false)
const errHandle = reactive<{ [key: string]: string[] }>({
  name: [],
  color: [],
})
watch(label, (val: Labels) => {
  formData.name = val?.name || ''
  formData.color = val?.color || '#df1111'
})
const $v = useVuelidate(rules, formData)
const submitForm = () => {
  $v.value.$touch()
  if ($v.value.$invalid)
    return

  processing.value = true
  if (!label.value?.name) {
    createLabels(formData).then(() => {
      emit('close')
    })
      .catch((err) => {
        if (err.errors)
          for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        processing.value = false
      })
  }
  else {
    updateLabels(formData, label.value?.uuid).then(() => {
      emit('close')
    })
      .catch((err) => {
        if (err.errors)
          for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        processing.value = false
      })
  }
}
const deleteTaxe = () => {
  processing.value = true
  deleteLabels(label.value?.uuid).finally(() => {
    processing.value = false
    emit('close')
  })
}
</script>

<template>
  <modal :dir="getLocale(locale)?.direction" :open="showModal" title="labels" @close="$emit('close')">
    <overlay-loader v-if="processing" />
    <form
      :class="[
        getLocale(locale)?.direction === 'rtl' ? 'text-start' : 'text-left',
      ]" @submit.prevent="submitForm"
    >
      <div class="grid grid-cols-6 gap-6">
        <div class="col-span-3">
          <TextInput
            id="tag-name"
            v-model="formData.name"
            :label="$t('form.name')" :placeholder="$t('formPlaceHolder.name')"
            class="block w-full py-3 mb-3 leading-tight text-gray-700 rounded appearance-none ps-4 pe-4 focus:outline-none focus:bg-white"
          />
        </div>
        <div class="col-span-3">
          <div class="relative">
            <TextInput
              v-model="formData.color"
              :label="$t('color')"
              placeholder="#000000" class="
              block
              w-full
              rounded-md
              py-3
              px-4
              border-blue-gray-300
              text-blue-gray-900
              focus:border-blue-500 focus:ring-blue-500
              sm:text-sm
            "
            />
            <input
              v-model="formData.color" type="color" class="
              absolute
              transform
              -translate-y-1/2
              bg-transparent
              border-none
              rounded-full
              appearance-none
              cursor-pointer
              top-1/2
              w-7
              h-7
              end-2
              "
            >
          </div>
        </div>
      </div>

      <div class="mt-8">
        <div class="flex items-center justify-end">
          <div
            v-if="label?.name"
            class="flex gap-2"
          >
            <BaseButton
              type="button"
              class="w-1/2 mx-auto py-3  hover:bg-red-700"
              custome-bg="bg-red-600"
              show-icon
              :processing="processing"
              @click="deleteTaxe"
            >
              {{ $t("form.delete") }}
            </BaseButton>
            <BaseButton
              type="submit"
              class="w-1/2 mx-auto py-3  hover:bg-primary-800"
              custome-bg="bg-primary-700"
              show-icon
              :processing="processing"
            >
              {{ $t("form.update") }}
            </BaseButton>
          </div>
          <BaseButton
            v-else
            type="submit"
            class=" py-3  hover:bg-green-700"
            custome-bg="bg-green-600"
            show-icon
            :processing="processing"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </modal>
</template>
