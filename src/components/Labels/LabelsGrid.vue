<script lang="ts" setup>
import type { ComputedRef, PropType } from 'vue'
import { storeToRefs } from 'pinia'
import type { Labels } from '../../types/labels'
import { useLabels } from '@/stores/labels'
import type { header } from '@/types'
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['openModal'])

const { labels } = storeToRefs(useLabels())

const openModal = (label: Labels) => {
  emit('openModal', label)
}
const { t } = useI18n()
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.name'),
    },
  ]
})
</script>

<template>
  <generic-table
    :is-loading="isLoading"
    :data="labels"
    :headers="headers"
    tr-class="cursor-pointer"
    :on-row-click="openModal"
  >
    <template #row="{ item }">
      <grid-td>
        <span class="flex items-center"> <b class="w-4 h-4 block-inline mr-3 ml-3" :style="{ backgroundColor: item.color }" /> {{ item.name }}</span>
      </grid-td>
    </template>
  </generic-table>
</template>
