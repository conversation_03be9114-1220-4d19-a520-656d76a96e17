<script setup lang="ts">
import { storeToRefs } from 'pinia'

import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/vue/24/solid'
const emit = defineEmits(['click'])

const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
</script>

<template>
  <button
    class="flex items-center gap-2  py-3 mt-5 font-medium leading-4 text-white bg-primary-600 border border-transparent rounded-md shadow-sm ps-3 pe-3 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 "
    :class="[getLocale(locale)?.direction === 'ltr' ? 'flex-row' : 'flex-row-reverse']"
    @click="emit('click')"
  >
    <ArrowLeftIcon v-if="getLocale(locale)?.direction === 'ltr'" class=" h-4 w-4 hover:transform hover:scale-110" aria-hidden="true" />
    {{ $t("steps.back") }}
    <ArrowRightIcon v-if="getLocale(locale)?.direction === 'rtl'" class="  h-4 w-4 hover:transform hover:scale-110" aria-hidden="true" />
  </button>
</template>
