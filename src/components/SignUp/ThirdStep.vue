<script setup lang="ts">
import { PlusIcon } from '@heroicons/vue/20/solid'
import { XCircleIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
const { updateOnBoarding } = useOnBoardingStore()
const { processing } = storeToRefs(useOnBoardingStore())
const form = reactive({
  staffs: [
    {
      name: '',
    },
  ],
})

const activateNextStep = () => {
  updateOnBoarding(form)
}

const removeField = (index: number) => {
  const newData = form.staffs.filter((e, ind) => {
    return ind !== index
  })
  form.staffs = newData
}
</script>

<template>
  <form @submit.prevent="activateNextStep">
    <div class="w-full mt-8 ms-auto me-auto md:w-10/12">
      <div
        v-for="(staff, index) in form.staffs"
        :key="index"
        class="grid grid-cols-0 py-8 gap-y-4 sm:gap-x-8 relative"
        :class="{ 'border-b border-gray-200': index !== form.staffs.length - 1 }"
      >
        <div class="relative w-full">
          <div class="mt-1">
            <form-group :error-name="`staffs.${index}.name`">
              <template #default="{ attrs }">
                <TextInput
                  v-bind="attrs"
                  id="name"
                  v-model="staff.name"
                  :label="$t('form.service')"
                  :placeholder="$t('steps.provider_name')"
                />
              </template>
            </form-group>
          </div>
        </div>
        <XCircleIcon
          v-if="index !== 0"
          class="w-6 h-6 text-red-400 ms-auto absolute top-2 right-0 cursor-pointe absolute top-2 left-0 cursor-pointer"
          aria-hidden="true"
          @click="removeField(index)"
        />
      </div>
      <div class="relative mt-2">
        <div class="relative flex justify-center">
          <button
            type="button"
            class="inline-flex items-center rounded-full border border-gray-300 bg-white ps-4 pe-4 py-1.5 text-sm font-medium leading-5 text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 group hover:scale-105 transition ease-in-out duration-150"
            @click="form.staffs.push({ name: '' })"
          >
            <PlusIcon
              class="-ms-1.5 me-1 h-5 w-5 text-gray-400 group-hover:scale-110 transition ease-in-out duration-150"
              aria-hidden="true"
            />
            <span>{{ $t("steps.add") }}</span>
          </button>
        </div>
      </div>
    </div>
    <div class="w-full">
      <NextButton type="submit" class="w-fit ms-auto" :processing="processing" />
    </div>
  </form>
</template>
