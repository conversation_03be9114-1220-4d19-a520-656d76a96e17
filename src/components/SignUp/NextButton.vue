<script setup lang="ts">
import { storeToRefs } from 'pinia'

import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/vue/24/solid'
defineProps({
  processing: {
    type: Boolean,
  },
})
const emit = defineEmits(['click'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
</script>

<template>
  <button
    class="flex items-center py-3 mt-5 font-medium leading-4 text-white bg-primary-600 border border-transparent rounded-md gap-2 shadow-sm ps-3 pe-3 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:bg-primary-300 disabled:cursor-not-allowed group bg-gray-600"
    :class="[getLocale(locale)?.direction === 'ltr' ? 'flex-row-reverse' : 'flex-row']"
    :disabled="processing"
    @click="emit('click')"
  >
    <TheLoader v-if="processing" />
    <ArrowRightIcon
      v-if="getLocale(locale)?.direction === 'ltr'"
      class="ms-2 h-4 w-4 group-hover:transform group-hover:scale-110"
      aria-hidden="true"
    />
    <slot>{{ $t("pagination.next") }}</slot>
    <ArrowLeftIcon
      v-if="getLocale(locale)?.direction === 'rtl'"
      class="h-4 w-4 group-hover:transform group-hover:scale-110"
      aria-hidden="true"
    />
  </button>
</template>
