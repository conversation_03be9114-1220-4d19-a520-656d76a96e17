<script setup lang="ts">
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { minLength, required } from '@/utils/i18n-validators'
import { useOnBoardingStore } from '@/stores/onBoarding'
const { updateOnBoarding, employeesOptions, changeStepManually }
  = useOnBoardingStore()
const { getUserInfo } = storeToRefs(useAuthStore())
const { getIndustries, processing } = storeToRefs(useOnBoardingStore())
const rules = {
  company_name: {
    required,
    minLength: minLength(3),
  },
  industry: {
    required,
  },
  number_of_employees: {
    required,
  },
}
const state = reactive({
  company_name: '',
  industry: '',
  number_of_employees: '',
})

const v$ = useVuelidate(rules, state)

const activateNextStep = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return
  await updateOnBoarding(state)
  // simuliate step
  await updateOnBoarding({
    staffs: [
      {
        name: getUserInfo.value?.name,
      },
    ],
  })
}
const renderImageDependSlug = (slug) => {
  const imageUrl = new URL(
    `../../assets/industries-icons/${slug}.png`,
    import.meta.url,
  ).href
  return imageUrl
}
</script>

<template>
  <div>
    <div class="mt-10 flex flex-col justify-center items-center">
      <h1 class="text-xl font-bold ">
        {{ $t(`onbord.which_type_of_service`) }}
      </h1>
      <p class="text-center">
        {{ $t(`onbord.choose_type_of_industry`) }}
      </p>
    </div>
    <form class="mt-8" @submit.prevent="activateNextStep()">
      <div class="w-full ms-auto me-auto grid gap-4 sm:grid-cols-2">
        <div>
          <form-group v-slot="{ attrs }" :validation="v$" name="company_name">
            <div class="mt-1">
              <TextInput
                v-bind="attrs"
                id="company_name"
                v-model="state.company_name"
                required
                :label="$t('form.company_name')"
                name="company_name"
                autocomplete="company_name"
                :placeholder="$t('formPlaceHolder.company_name')"
              />
            </div>
          </form-group>
        </div>
        <div class="">
          <LabelInput for="industry">
            {{
              $t("steps.number_of_employees")
            }}
            <span class="text-red-500">*</span>
          </LabelInput>
          <form-group :validation="v$" name="number_of_employees">
            <template #default="{ attrs }">
              <SelectInput
                id="industry"
                v-model="state.number_of_employees"
                v-bind="attrs"
              >
                <option
                  v-for="item of employeesOptions"
                  :key="item"
                  :value="item"
                  class="text-sm"
                >
                  {{ $t(`${item}_employee`) }}
                </option>
              </SelectInput>
            </template>
          </form-group>
        </div>
        <div
          v-if="getIndustries.length > 0"
          class="grid grid-cols-2 col-span-2 gap-2 w-full"
        >
          <div
            v-for="industry in getIndustries"
            :key="industry?.uuid"
            class="px-2 py-4 rounded-md hover:shadow-md cursor-pointer text-center text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 flex items-center justify-start gap-4 md:col-span-1 col-span-2"
            dir="auto"
            role="button"
            area-label="industry"
            :class="{
              '!bg-primary-500 text-white': state.industry === industry?.uuid,
            }"
            @click="state.industry = industry?.uuid"
          >
            <img
              :src="renderImageDependSlug(industry?.slug)"
              class="object-cover"
              :alt="industry?.name"
            >
            <p>
              {{ industry?.name }}
            </p>
          </div>
        </div>
      </div>
      <div class="w-full">
        <NextButton
          type="submit"
          class="w-fit ms-auto w-fit !px-8"
          :processing="processing"
          :disabled="v$.$invalid"
        />
      </div>
    </form>
  </div>
</template>
