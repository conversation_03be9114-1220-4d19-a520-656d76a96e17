<script setup lang="ts">
import { PlusIcon } from '@heroicons/vue/20/solid'
import { XCircleIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
const { updateOnBoarding } = useOnBoardingStore()
const authStore = useAuthStore()
const { processing } = storeToRefs(useOnBoardingStore())
const form = reactive({
  services: [
    {
      name: '',
      duration: 15,
      price: 0,
    },
  ],
})
const router = useRouter()
const activateNextStep = () => {
  updateOnBoarding(form).then(async () => {
    await authStore.getProfile()
    router.push({ path: '/verify-your-email' })
  })
}

const removeField = (index: number) => {
  const newData = form.services.filter((e, ind) => {
    return ind !== index
  })
  form.services = newData
}
</script>

<template>
  <div>
    <div class="mt-10 flex flex-col justify-center items-center">
      <h1 class="text-xl font-bold ">
        {{ $t(`onbord.Add_services`) }}
      </h1>
      <p class="text-center">
        {{ $t(`onbord.Add_services_desc`) }}
      </p>
    </div>
    <form @submit.prevent="activateNextStep">
      <div class="w-full mt-8 ms-auto me-auto">
        <div
          v-for="(service, index) in form.services"
          :key="index"
          class="grid grid-cols-1 py-4 gap-y-2 sm:grid-cols-2 lg:grid-cols-3 sm:gap-x-8 relative"
          :class="{
            'border-b border-gray-200': index !== form.services.length - 1,
          }"
        >
          <div class="mt-2">
            <div class="mt-1">
              <form-group :error-name="`services.${index}.name`">
                <template #default="{ attrs }">
                  <TextInput
                    v-bind="attrs"
                    :id="`name-${index}`"
                    v-model="service.name"
                    :label="$t('form.service')"
                    :placeholder="$t('steps.service_name')"
                  />
                </template>
              </form-group>
            </div>
          </div>

          <div class="mt-2">
            <div class="mt-1">
              <form-group :error-name="`services.${index}.price`">
                <template #default="{ attrs }">
                  <NumberInput
                    v-bind="attrs"
                    :id="`price-${index}`"
                    v-model="service.price"
                    :label="$t('steps.price')"
                    min="0"
                    :placeholder="$t('steps.title')"
                  />
                </template>
              </form-group>
            </div>
          </div>
          <div class="mt-2">
            <div class="mt-1">
              <form-group :error-name="`services.${index}.duration`">
                <template #default="{ attrs }">
                  <NumberInput
                    v-bind="attrs"
                    :id="`time-${index}`"
                    v-model="service.duration"
                    :label="$t('steps.duration')"
                    min="1"
                    :placeholder="$t('steps.durationplaceHolder')"
                  />
                </template>
              </form-group>
            </div>
          </div>
          <XCircleIcon
            v-if="index !== 0"
            class="w-5 h-5 text-red-400 ms-auto absolute top-2 right-0 cursor-pointer left-4"
            aria-hidden="true"
            @click="removeField(index)"
          />
        </div>
        <!-- <div class="relative">
        <div class="absolute inset-0 flex items-center" aria-hidden="true">
          <div class="w-full border-t border-gray-300" />
        </div>
        <div class="relative flex justify-center">
          <button
            type="button"
            class="inline-flex items-center rounded-full border border-gray-300 bg-white ps-4 pe-4 py-1.5 text-sm font-medium leading-5 text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            @click="
              form.services.push({
                name: '',
                duration: 15,
                price: 0,
              })
            "
          >
            <PlusIcon class="-ms-1.5 me-1 h-5 w-5 text-gray-400" aria-hidden="true" />
            <span>{{ $t("steps.add") }}</span>
          </button>
        </div>
      </div> -->
        <div class="w-full">
          <NextButton type="submit" class="w-fit ms-auto !px-8" :processing="processing">
            {{ $t('steps.done') }}
          </NextButton>
        </div>
      </div>
    </form>
  </div>
</template>

<style lang="css">
.multiselect__tags {
  @apply py-3 px-2 focus:border-primary-500 inline-block w-full border border-gray-300   rounded-md !important;
}

.multiselect__placeholder {
  @apply text-gray-400 mb-0 pt-0 text-sm leading-3 !important;
}

.multiselect__select {
  @apply p-0 !important;
}
</style>
