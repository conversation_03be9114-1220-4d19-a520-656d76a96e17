<script setup lang="ts">
import { storeToRefs } from 'pinia'
import {
  CheckIcon,
  UserCircleIcon,
  WrenchScrewdriverIcon,
} from '@heroicons/vue/24/outline'
// const FirstStep = import("./FirstStep.vue");
// const ThirdStep = import("./ThirdStep.vue");
// const FourthStep =  import("./FourthStep.vue")
import SignUpForm from './SignUpForm.vue'
import FirstStep from './FirstStep.vue'
import FourthStep from './FourthStep.vue'
const { getOnboarding, changeStepManually } = useOnBoardingStore()
const { getCurrentStep, processing } = storeToRefs(useOnBoardingStore())

const countOfSteps = 4
const steps = markRaw([
  {
    key: 0,
    title: 'Membership_Registration',
    icon: UserCircleIcon,
    component: SignUpForm,
    completed: false,
  },
  {
    key: 1,
    title: 'account_info',
    component: FirstStep,
    completed: false,
    icon: UserCircleIcon,
  },
  // 2 key  simulate step
  {
    key: 3,
    title: 'services',
    component: FourthStep,
    completed: false,
    icon: WrenchScrewdriverIcon,
  },
])
onMounted(() => {
  const userAuth = localStorage.getItem('userAuth')
  const userAuthenticated = userAuth ? JSON.parse(userAuth) === true : false
  if (userAuthenticated)
    getOnboarding()
  else changeStepManually(0)
})
</script>

<template>
  <div class="">
    <div class="">
      <div
        class="relative items-center justify-between w-full mt-3 hidden xl:flex"
      >
        <div
          class="absolute left-0 top-2/4 h-0.5 w-full -translate-y-2/4 bg-primary transition-all duration-500 sm:px-10 px-16"
        />
        <div
          v-for="(step, index) in steps"
          class="relative z-10 grid w-10 h-10 font-bold text-white transition-all duration-300 bg-primary-800 rounded-md
           place-items-center"
          :class="{
            '!bg-green-500': getCurrentStep > step.key,
          }"
        >
          <CheckIcon
            v-if="getCurrentStep > step.key"
            class="w-5 h-5 text-primary-700 font-bold"
            aria-hidden="true"
          />
          <Component
            :is="step.icon"
            v-else
            class="w-5 h-5"
            aria-hidden="true"
          />
          <div class="absolute -bottom-[2rem] w-max text-center">
            <h6
              class="block font-sans lg:text-base text-xs antialiased font-semibold leading-relaxed tracking-normal text-gray-700"
            >
              {{ $t(`onbord.${step.title}`) }}
            </h6>
          </div>
        </div>
      </div>
      <div class="px-4 py-4 sm:px-6 lg:px-8 xl:hidden block">
        <nav aria-label="Progress">
          <ol
            role="list"
            class="divide-y divide-gray-300 rounded-md border border-gray-300 md:flex md:divide-y-0"
          >
            <li
              v-for="(step, stepIdx) in steps"
              :key="step.title"
              class="relative md:flex md:flex-1"
            >
              <button class="group flex items-center">
                <span
                  class="flex items-center px-6 py-4 text-sm font-medium gap-2"
                >
                  <span
                    class="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-primary-800 group-hover:bg-primary-900"
                    :class="{
                      '!bg-green-500': getCurrentStep > step.key,
                    }"
                  >
                    <CheckIcon
                      v-if="getCurrentStep > step.key"
                      class="w-5 h-5 text-primary-700 font-bold"
                      aria-hidden="true"
                    />
                    <Component
                      :is="step.icon"
                      v-else
                      class="text-white w-5 h-5"
                      aria-hidden="true"
                    />
                  </span>
                  <span class="ml-4 text-sm font-medium text-gray-900">
                    {{ $t(`onbord.${step.title}`) }}
                  </span>
                </span>
              </button>
            </li>
          </ol>
        </nav>
      </div>
      <div class="relative">
        <OverlayLoader v-if="processing" :full-screen="false" />

        <div v-if="getCurrentStep < countOfSteps">
          <Component :is="steps[getCurrentStep ].component" class="text-start" />
        </div>
      </div>
    </div>
  </div>
</template>
