<script setup lang="ts">
import { Text, computed, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import useVuelidate from '@vuelidate/core'
import { email, minLength, required } from '@/utils/i18n-validators'
import { useAuthStore } from '@/stores/auth'
import i18n from '@/i18n'
import { AUTH_TOKEN } from '@/constants'

const route = useRoute()
const router = useRouter()
const { performRegistration } = useAuthStore()

const state = reactive({
  email: route.query.member || '',
  password: '',
  name: '',
  confirmPassword: '',
  phone: '',
  phone_country: '',
})

const sameAsPass = {
  ar: 'كلمه المرور يجب ان تكون متطابقان',
  en: 'The value must be equal to the Password',
}

const rules = computed(() => ({
  email: {
    required,
    email,
  },
  password: {
    required,
    minLength: minLength(8),
  },
  name: {
    required,
  },
  confirmPassword: {
    required,
  },
  phone: {
    required,
  },
}))

const v$ = useVuelidate(rules, state)
const processing = ref(false)

const errHandle = reactive({
  email: [] as string[],
  password: [] as string[],
  name: [] as string[],
  phone: [] as string[],
})

const errHnadle = reactive({
  passwordNoMatch: {
    msg: 'errorMsg.passwordMatch',
    show: false,
  },
})

const performAction = () => {
  v$.value.$touch()

  if (v$.value.$invalid || processing.value || errHnadle.passwordNoMatch.show)
    return

  processing.value = true

  const payload = {
    name: state.name,
    email: state.email,
    password: state.password,
    phone: state.phone,
    phone_country: state.phone_country,
  }

  performRegistration(payload)
    .then((authToken) => {
      if (authToken) {
        localStorage.setItem(AUTH_TOKEN, authToken)
        localStorage.setItem('userAuth', JSON.stringify(true))
        router.push({ name: 'onboarding' })
      }
    })
    .catch((error: any) => {
      for (const i in error.errors) errHandle[i] = error.errors[i]
    })
    .finally(() => {
      processing.value = false
    })
}

watch(
  () => state.confirmPassword,
  (value) => {
    if (state.password !== value)
      errHnadle.passwordNoMatch.show = true
    else errHnadle.passwordNoMatch.show = false
  },
)

watch(() => state.password,
  (value) => {
    if (state.confirmPassword !== value && state.confirmPassword.length > 0)
      errHnadle.passwordNoMatch.show = true
    else errHnadle.passwordNoMatch.show = false
  },
)

const setPhoneNumber = (
  phoneNumber: string,
  phoneObject: { countryCode: string },
) => {
  state.phone = phoneNumber
  state.phone_country = phoneObject.countryCode
}
</script>

<template>
  <div
    class="
      flex flex-col
      justify-center
      flex-1
      py-12
      ps-4
      pe-4
      sm:ps-6 sm:pe-6
      lg:flex-none lg:ps-20 lg:pe-20
      xl:ps-24 xl:pe-24
    "
  >
    <div class="w-full max-w-sm ms-auto me-auto lg:w-96">
      <div>
        <img
          class="w-auto h-12"
          src="../../assets/logo.svg"
          alt="Your Company"
        >
        <h2 class="mt-6 text-2xl font-bold tracking-tight text-gray-900">
          {{ $t("form.signupFor") }}
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          {{ $t("homepage.or") }}
          {{ " " }}
          <span
            class="
              font-medium
              text-primary-600
              cursor-pointer
              hover:text-primary-500
            "
            @click="
              $router.push({
                name: 'auth',
                query: {
                  section: 'sign-in',
                },
              })
            "
          >{{ $t("form.signinHere") }}</span>
        </p>
      </div>

      <div class="mt-8">
        <div class="mt-6">
          <form class="space-y-6" @submit.prevent="performAction()">
            <div>
              <TextInput
                id="name"
                v-model="state.name"
                :class="{ 'error-input': v$.name.$errors.length }"
                name="name"
                autocomplete="name"
                :placeholder="$t('formPlaceHolder.name')"
                :label="$t('form.name')"
                custom-classes="block w-full py-2 placeholder-gray-400 border border-gray-300 rounded-md   appearance-none ps-3 pe-3 focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
              />
              <div v-if="errHandle.name.length > 0">
                <ErrMsg v-for="err in errHandle.name" :key="err" :msg="err" />
              </div>
            </div>
            <div class="space-y-1">
              <div class="mt-1">
                <PhoneInput
                  :model-value="state.phone"
                  label="form.phone"
                  class="
                  block
                  w-full
                  py-2
                  placeholder-gray-400
                  appearance-none
                  pe-3
                  focus:border-primary-500
                  focus:outline-none
                  focus:ring-primary-500
                  sm:text-sm"
                  mode="international"

                  @update:model-value="setPhoneNumber"
                />
              </div>
              <p
                v-for="error of v$.phone.$errors"
                :key="error.$uid"
                class="error-message"
              >
                {{ error.$message }}
              </p>
              <div v-if="errHandle.phone.length > 0">
                <ErrMsg v-for="err in errHandle.name" :key="err" :msg="err" />
              </div>
            </div>
            <div>
              <div class="mt-1">
                <TextInput
                  id="email"
                  v-model="state.email"
                  :label="$t('form.email')"
                  :class="{ 'error-input': v$.email.$errors.length }"
                  name="email"
                  type="email"
                  autocomplete="email"
                  :placeholder="$t('formPlaceHolder.email')"
                  :readonly="!!$route.query.member"
                />
                <p
                  v-for="error of v$.email.$errors"
                  :key="error.$uid"
                  class="error-message"
                >
                  {{ error.$message }}
                </p>
                <div v-if="errHandle.email.length > 0">
                  <ErrMsg
                    v-for="err in errHandle.email"
                    :key="err"
                    :msg="err"
                  />
                </div>
              </div>
            </div>
            <div class="space-y-1">
              <div class="mt-1">
                <PasswordInput
                  id="password"
                  v-model="state.password"
                  :label="$t('password.enterPassword')"
                  name="password"
                  type="password"
                  autocomplete="current-password"
                  :placeholder="$t('formPlaceHolder.registerPass')"
                />
              </div>
              <p
                v-for="error of v$.password.$errors"
                :key="error.$uid"
                class="error-message"
              >
                {{ error.$message }}
              </p>
              <div v-if="errHandle.password.length > 0">
                <ErrMsg
                  v-for="err in errHandle.password"
                  :key="err"
                  :msg="err"
                />
              </div>
            </div>

            <div class="space-y-1">
              <div class="mt-1">
                <PasswordInput
                  id="confirm-password"
                  v-model="state.confirmPassword"
                  :label="$t('password.confirm')"
                  name="confirm-password"
                  type="password"
                  :placeholder="$t('formPlaceHolder.registerConfirmPass')"
                  autocomplete="current-password"
                />
              </div>
              <p v-if="errHnadle.passwordNoMatch.show" class="text-red-600">
                {{ $t(`${errHnadle.passwordNoMatch.msg}`) }}
              </p>
              <p
                v-for="error of v$.confirmPassword.$errors"
                :key="error.$uid"
                class="error-message"
              >
                {{ error.$message }}
              </p>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  class="
                    w-4
                    h-4
                    text-primary-600
                    border-gray-300
                    rounded
                    focus:ring-primary-500
                  "
                >
                <label
                  for="remember-me"
                  class="block text-sm text-gray-900 ms-2"
                >{{ $t("form.remember") }}</label>
              </div>
            </div>

            <div>
              <BaseButton :processing="processing" show-icon>
                {{ $t("form.register") }}
              </BaseButton>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>
