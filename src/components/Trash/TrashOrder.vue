<script setup lang="ts">
import type { ComputedRef } from 'vue'
import {
  ChatBubbleBottomCenterTextIcon,
  EnvelopeIcon,
} from '@heroicons/vue/24/outline'
import {
  ArrowUturnLeftIcon,
  CalendarIcon,
  CheckCircleIcon,
  ComputerDesktopIcon,
  ExclamationTriangleIcon,
  GlobeAltIcon,
  XCircleIcon,
} from '@heroicons/vue/24/solid'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import { TrashIcon } from '@heroicons/vue/20/solid'
import { useTrash } from '@/stores/trash'
import type { header } from '@/types'
const { fetchTrashedOrders, restoreOrder, forceDeleteOrder } = useTrash()
const { getTrashedOrders } = storeToRefs(useTrash())
const { t, locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())

const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('order_number'),
    },
    {
      title: t('transaction.customer'),
    },

    {
      title: t('order_status'),
    },
    {
      title: t('form.branch'),
    },
    {
      title: t('booking.created_at'),
    },
    {
      title: t('source'),
    },
  ]
})
const processing = ref(false)
onMounted(async () => {
  processing.value = true
  await fetchTrashedOrders()
  processing.value = false
})
const showConfModal = ref(false)
const recordIWillDelete = ref('')
async function restore(recordId: string) {
  await restoreOrder(recordId)
}
function deletePermanently(recordId: string) {
  showConfModal.value = true
  recordIWillDelete.value = recordId
  // await deleteOrder(recordId);
}
// const showNotificationDetails = ref(false);
// const notificationSelected = ref<SendNotification>();
// const openDetails = (item: any) => {
//   showNotificationDetails.value = true;
//   notificationSelected.value = item;
// };
</script>

<template>
  <!-- <sent-notification-modal
    :isOpen="showNotificationDetails"
    :notification="notificationSelected"
    @closed="showNotificationDetails = false"
  ></sent-notification-modal> -->
  <confirmation-modal
    v-if="showConfModal"
    :dir="getLocale(locale)?.direction"
    :api-call="forceDeleteOrder"
    :record-id="recordIWillDelete"
    :is-open="showConfModal"
    @closed="showConfModal = false"
  >
    <p class="leading-7 text-start">
      {{ $t("confirmModal.msg") }}
    </p>
  </confirmation-modal>
  <div class="flex flex-col mt-8">
    <div class="">
      <generic-table
        :headers="headers"
        :data="getTrashedOrders.ordersList"
        tr-class="cursor-pointer"
        :is-loading="processing"
      >
        <template #row="{ item }">
          <grid-td> # {{ item.OrderNum }} </grid-td>

          <grid-td
            :default-style="false"
            class="flex gap-1 items-center px-0 py-2 text-sm whitespace-nowrap"
          >
            <div>
              <svg
                v-if="!item.customer?.photo"
                xmlns="http://www.w3.org/2000/svg"
                class="w-12 h-12 rounded-md"
                viewBox="0 0 24 24"
              >
                <path
                  fill="#e1e1e1"
                  d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z"
                />
              </svg>
              <img
                v-else
                id="image"
                class="w-10 h-10 rounded-full"
                :src="item.customer?.photo"
                :link="null"
              >
            </div>
            <span>
              {{
                item.customer.first_name
                  ? `${item.customer?.first_name} ${
                    item.customer?.last_name || ""
                  }`
                  : $t("pos.guest")
              }}
            </span>
          </grid-td>

          <grid-td>
            <sapn
              class="p-1 px-2 text-sm rounded-full"
              :style="{
                'background-color': item.status.bg_color
                  ? item.status.bg_color
                  : '#000000',
                'color': item.status.text_color
                  ? item.status.text_color
                  : '#ffffff',
              }"
            >
              {{ item.status.label }}
            </sapn>
          </grid-td>
          <grid-td>
            <span class="text-sm font-semibold leading-5 text-primary">
              {{ item.branch?.name }}
            </span>
          </grid-td>
          <grid-td>
            <span class="font-medium text-gray-900">
              {{ dayjs(item.created_at).locale(locale).from(new Date()) }}
            </span>
          </grid-td>
          <grid-td>
            <span>
              <ComputerDesktopIcon
                v-if="item.source == 'pos'"
                class="inline-block w-5 h-5"
              />
              <GlobeAltIcon
                v-else-if="item.source == 'bookingPage'"
                class="inline-block w-5 h-5"
              />
              <CalendarIcon
                v-else-if="item.source == 'calendar'"
                class="inline-block w-5 h-5"
              />
            </span>
          </grid-td>
        </template>
        <template #actions="{ item }">
          <div class="flex gap-2 items-center">
            <base-button
              class="!text-white"
              custome-bg="bg-red-600"
              button-type="button"
              @click="deletePermanently(item.id)"
            >
              <TrashIcon class="inline-block w-5 h-5 text-white" />
              {{ t("delete_permanently") }}
            </base-button>
            <base-button
              class="!text-white"
              custome-bg="bg-green-600"
              button-type="button"
              @click="restore(item.id)"
            >
              <ArrowUturnLeftIcon class="inline-block w-5 h-5 text-white" />
              {{ t("restore") }}
            </base-button>
          </div>
        </template>
      </generic-table>
      <Pagination
        v-if="getTrashedOrders.ordersList.length"
        :pagination-meta="getTrashedOrders.paginationMeta"
        :pagination-links="getTrashedOrders.paginationLinks"
        @change="fetchTrashedOrders"
      />
    </div>
  </div>
</template>
