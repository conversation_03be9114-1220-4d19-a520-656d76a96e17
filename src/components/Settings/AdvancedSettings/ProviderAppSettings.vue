<script setup lang="ts">
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid'
import {
  Listbox,
  ListboxButton,
  ListboxLabel,
  ListboxOption,
  ListboxOptions,
  Switch,
} from '@headlessui/vue'

import { storeToRefs } from 'pinia'
import i18n from '@/i18n'

const { fetchAdvancedSettings, updateGeneralCustomeSettings }
  = useAccountSettingStore()
const groups = ref(['provider-app'])
const inputs = ref([])
const processing = ref(false)
onMounted(async () => {
  try {
    processing.value = true
    const res = await fetchAdvancedSettings(groups.value)
    inputs.value = res
  }
  catch (error) {
  }
  finally {
    processing.value = false
  }
})
const customError = reactive<{ [key: string]: string[] }>([])
const submit = (formData) => {
  const payload = {
    setting: {
      'provider-app': {
        ...formData,
      },
    },
  }
  processing.value = true

  updateGeneralCustomeSettings(payload, 'provider-app')
    .catch((err) => {
      customError.value.push(err.errors)
    })
    .finally(() => {
      processing.value = false
    })
}
</script>

<template>
  <div class="py-4 mx-auto lg:py-2">
    <div class="flex flex-col">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("advanced-provider-app") }}
      </h1>
      <customForm
        :inputs="inputs['provider-app']"
        :err="customError"
        :is-loading="processing"
        error-object-name="setting.provider_app"
        @submit="submit"
      />
    </div>
  </div>
</template>
