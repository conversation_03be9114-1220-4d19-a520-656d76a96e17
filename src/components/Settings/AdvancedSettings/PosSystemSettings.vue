<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid'
import {
  Listbox,
  ListboxButton,
  ListboxLabel,
  ListboxOption,
  ListboxOptions,
  Switch,
} from '@headlessui/vue'

import { storeToRefs } from 'pinia'
import i18n from '@/i18n'
import { useAccountSettingStore } from '@/stores/settings'

const { fetchAdvancedSettings, updateGeneralCustomeSettings }
  = useAccountSettingStore()
const groups = ref(['pos_system'])
const inputs = ref([])
const processing = ref(false)
const customError = reactive<{ [key: string]: string[] }>([])
onMounted(async () => {
  try {
    processing.value = true
    const res = await fetchAdvancedSettings(groups.value)
    inputs.value = res
  }
  catch (error) {
  }
  finally {
    processing.value = false
  }
})
const submit = (formData) => {
  const payload = {
    setting: {
      pos_system: {
        ...formData,
      },
    },
  }
  processing.value = true
  updateGeneralCustomeSettings(payload, 'pos_system')
    .catch((err) => {
      customError.value.push(err.errors)
    })
    .finally(() => {
      processing.value = false
    })
}
</script>

<template>
  <div class="py-4 mx-auto lg:py-2">
    <div class="flex flex-col">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("advanced-pos-system") }}
      </h1>
      <customForm
        :inputs="inputs.pos_system"
        :err="customError"
        :is-loading="processing"
        error-object-name="setting.pos_system"
        @submit="submit"
      />
    </div>
  </div>
</template>
