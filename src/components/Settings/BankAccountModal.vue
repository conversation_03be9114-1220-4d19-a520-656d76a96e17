<script setup lang="ts">
import { storeToRefs } from 'pinia'
import type { PropType } from 'vue'
import {
  ClockIcon,
  PlusIcon,
  TrashIcon,
  XCircleIcon,
} from '@heroicons/vue/24/outline'
import useVuelidate from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import { makeArrayEmpty } from '../../utils/makeArrOfErrEmpty'
import { useBank } from '@/stores/bank'
import type { bankAccount } from '@/types/bankAccount'
interface bankAccountUpdate {
  bank_id: string
  bank_account: string
  iban: string
  beneficiary_name: string
  bankName?: string
}
const props = defineProps({
  bank: {
    type: Object as PropType<bankAccountUpdate | null>,
  },
  showModal: {
    type: Boolean,
  },
  modalType: {
    type: String,
  },
})
const emit = defineEmits(['close'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { getBankList, addBankAccountFun, updateAccounts, deleteAccount } = useBank()
const { banks } = storeToRefs(useBank())
const { bank } = toRefs(props)
const processing = ref(false)
const formData = reactive({
  name: '',
  id: '',
  bank: {
    id: '',
    name: '',
  },
  iban: '',
  account_no: '',
})
const rules = {
  name: {
    required,
  },
  iban: {
    required,
  },
  account_no: {
    required,
  },
  bank: {
    id: {
      required,
    },
  },

}
const errHandle = reactive<{ [key: string]: string[] }>({
})

const v$ = useVuelidate(rules, formData)
const resetInputs = () => {
  formData.name = ''
  formData.bank.id = ''
  formData.bank.name = ''
  formData.iban = ''
  formData.account_no = ''
  v$.value.$reset()
}
watch(bank, (val: bankAccount) => {
  formData.account_no = val?.account_no || ''
  formData.bank.id = val?.bank?.id || ''
  formData.bank.name = val?.bank?.name || ''
  formData.id = val?.id || ''
  formData.name = val?.name || ''
  formData.iban = val?.iban || ''
})

const newFormData = reactive({
  bank_id: '',
  bank_account_no: '',
  iban: '',
  beneficiary_name: '',
})
const addBankAccount = () => {
  v$.value.$touch()
  if (v$.value.$invalid || processing.value)
    return
  makeArrayEmpty(errHandle)

  newFormData.bank_id = formData.bank.id
  newFormData.bank_account_no = formData.account_no
  newFormData.beneficiary_name = formData.name
  newFormData.iban = formData.iban
  processing.value = true
  addBankAccountFun(newFormData).then((res) => {
    resetInputs()
  }).catch((err) => {
    if (err.errors) {
      for (const prop in err.errors)
        errHandle[prop] = err.errors[prop]
    }
  }).finally(() => {
    processing.value = false
  })
}
const resetForm = () => {
  v$.value.$reset()
  resetInputs()
  makeArrayEmpty(errHandle)
  emit('close')
}
const updateAccountFun = () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return
  newFormData.bank_id = formData.bank.id
  newFormData.bank_account_no = formData.account_no
  newFormData.beneficiary_name = formData.name
  newFormData.iban = formData.iban
  newFormData.id = formData.id
  processing.value = true
  updateAccounts(newFormData).then(() => {
    resetInputs()
    emit('close')
  }).catch((err) => {
    if (err.errors) {
      for (const prop in err.errors)
        errHandle[prop] = err.errors[prop]
    }
  }).finally(() => {
    processing.value = false
  })
}
const deleteAccountFun = () => {
  processing.value = true
  deleteAccount(formData.id).then(() => {
    emit('close')
  }).finally(() => {
    processing.value = false
  })
}
const isAdd = computed(() => {
  if (props.bank?.iban?.length === 0 || !props.bank)
    return false

  else
    return true
})
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    title="service"
    @close="resetForm"
  >
    <form
      :class="[
        getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left']"
    >
      <overlay-loader v-if="processing" />
      <err-validations :err-handle="errHandle" />

      <div class="grid sm:grid-cols-2 grid-cols-1  gap-4 mt-10">
        <div class="flex flex-col">
          <TextInput
            id="username"
            v-model="formData.name"
            :label="$t('settings.payementLabels.bankUser')"
            :placeholder="$t('settings.payementLabels.bankUser')"
          />
          <p v-for="error of v$.name.$errors" :key="error.$uid" class="error-message">
            {{ $t(error.$message) }}
          </p>
        </div>
        <div class="flex flex-col">
          <label
            class="block mb-2 text-sm font-bold tracking-wide text-gray-700  "
            for="bank"
          >
            {{ $t('settings.payementLabels.bankName') }}
          </label>
          <select id="bank" v-model="formData.bank.id" class="block w-full py-3  leading-tight text-gray-700 rounded appearance-none ps-4 pe-4 focus:outline-none focus:bg-white">
            <option value="">
              {{ $t("form.select") }}
            </option>
            <option v-for="bankAcc in banks" :key="bankAcc" :value="bankAcc.id">
              {{ bankAcc.name }}
            </option>
          </select>
          <p v-for="error of v$.bank.$errors" :key="error.$uid" class="error-message">
            {{ $t(error.$message) }}
          </p>
        </div>
        <div class="flex flex-col">
          <TextInput
            id="iban"
            v-model="formData.iban"
            :label="$t('settings.payementLabels.iban')"
            :placeholder="$t('settings.payementLabels.iban')"
          />
          <p v-for="error of v$.iban.$errors" :key="error.$uid" class="error-message">
            {{ $t(error.$message) }}
          </p>
        </div>
        <div class="flex flex-col">
          <TextInput
            id="bankNumber"
            v-model="formData.account_no"
            :label="$t('settings.payementLabels.bankNo')"
            :placeholder="$t('settings.payementLabels.bankNo')"
          />
          <p v-for="error of v$.account_no.$errors" :key="error.$uid" class="error-message">
            {{ $t(error.$message) }}
          </p>
        </div>
      </div>
      <div class="flex justify-between w-full mt-6">
        <div v-if="modalType === 'edit'" class="flex flex-1 gap-2">
          <BaseButton
            class="inline-flex hover:bg-gray-800 mx-auto w-1/2"
            custome-bg="bg-gray-700"
            type="button"
            @click="updateAccountFun()"
          >
            {{ $t("form.update") }}
          </BaseButton>
          <BaseButton
            class="inline-flex hover:bg-red-700 mx-auto w-1/2"
            custome-bg="bg-red-600"
            type="button"
            @click="deleteAccountFun()"
          >
            {{ $t("form.delete") }}
            <TrashIcon class="ms-2 -me-0.5 h-4 w-4" aria-hidden="true" />
          </BaseButton>
        </div>
        <div v-else class="mx-auto w-1/2 mt-2">
          <BaseButton
            class="inline-flex hover:bg-green-700"
            custome-bg="bg-green-600"
            type="button"
            @click="addBankAccount()"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </Modal>
</template>
