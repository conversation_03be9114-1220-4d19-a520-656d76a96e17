<script lang="ts" setup>
import type { PropType } from 'vue'
import type { ActionType, NotficationAction, NotificationTemplates, Template } from '@/types'
import { useNotificationTemplate } from '@/stores/notificationTemplate'
const props = defineProps({
  notificationTemplateList: {
    type: Object as PropType<NotificationTemplates>,
    default: false,
  },
  currentTab: {
    type: Object as PropType<{ value: ActionType }>,
  },
})

const notificationStore = useNotificationTemplate()

const actionSelected = ref({})
const templateSelected = ref({})
const showModal = ref(false)
const editTemplate = (
  action: NotficationAction,
  template: Template,
) => {
  actionSelected.value = action
  templateSelected.value = template
  showModal.value = true
}
const addNewTemplate = (action: NotficationAction) => {
  actionSelected.value = action
  templateSelected.value = {}
  showModal.value = true
}

const toggleTemplateStatus = async (template: Template, event: Event) => {
  event.stopPropagation()
  if (!template.template_id)
    return

  try {
    await notificationStore.toggleTemplateStatus(
      template.template_id,
      !template.status,
    )
  }
  catch (error) {
    console.error('Failed to toggle template status:', error)
  }
}
</script>

<template>
  <notification-modal
    v-if="showModal"
    v-model:show-modal="showModal"
    :template-selected="templateSelected"
    :action-selected="actionSelected"
    :selected-tab="currentTab"
    :active-channels="notificationTemplateList.channel"
  />
  <div class="mx-auto mt-3 max-w-md sm:max-w-3xl">
    <template
      v-for="(action, index) in notificationTemplateList.actions?.[currentTab.value]"
      :key="index"
    >
      <div
        class="block flex relative justify-center items-center mx-auto mb-1 w-12 w-full h-20 text-center rounded-lg border-2 border-primary h-sm-22"
      >
        <h4 class="block font-semibold text-gray-900">
          {{ action.name }}
        </h4>
      </div>
      <button
        v-for="template in action.templates"
        :key="template.template_id"
        type="button"
        class="block relative mx-auto my-1 w-12 w-full h-28 h-36 text-center rounded-lg border-2 border-gray-300 hover:border-gray-400 active:outline-none active:border-primary h-sm-22"
        @click="editTemplate(action, template)"
      >
        <span class="block px-8 text-sm font-semibold text-gray-900 truncate">
          {{ template.title ?? template.content }}
        </span>
        <span class="absolute top-0 left-0 px-2 mt-2 ml-2">
          <span
            v-if="template.channel === 'sms'"
            class="w-7 h-7 text-sm font-semibold text-gray-400"
          >
            {{ $t("form.sms") }}
          </span>

          <span
            v-if="template.channel === 'email'"
            class="w-7 h-7 text-sm font-semibold text-gray-400"
          >
            {{ $t("form.email") }}
          </span>

          <span
            v-if="template.channel === 'whatsapp'"
            class="w-7 h-7 text-sm font-semibold text-gray-400"
          >
            {{ $t("form.whatsapp") }}
          </span>
        </span>

        <!-- Toggle Button -->
        <div class="absolute top-2 right-2" @click.stop="toggleTemplateStatus(template, $event)">
          <label class="inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              :checked="template.status"
              class="sr-only peer"
            >
            <div
              class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"
            />
          </label>
        </div>
      </button>
      <button
        type="button"
        class="block relative mx-auto my-1 w-12 w-full h-36 text-center rounded-lg border-2 border-gray-300 border-dashed hover:border-gray-400 active:outline-none active:border-primary h-sm-22"
        @click="addNewTemplate(action)"
      >
        <span class="block text-sm font-semibold text-gray-900">
          {{ $t("add_new_notification") }}
        </span>
      </button>
    </template>
  </div>
</template>
