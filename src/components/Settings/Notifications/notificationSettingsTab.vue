<script setup lang="ts">
import useSettings from '@/composables/useSettings'

const formData = reactive({
  channels: [],
  customers: [],
  staff: [],
})

const handleFreshData = ({ customers, channels, staff }) => {
  formData.channels = channels
  formData.customers = customers
  formData.staff = staff
}
const { processing } = useSettings('notifications', handleFreshData)
const { generalCustomeSetting, updateGeneralCustomeSettings }
  = useAccountSettingStore()

const inputs = ref([])
onMounted(() => {
  processing.value = true
  generalCustomeSetting('notifications')
    .then((res) => {
      inputs.value = res
    })
    .finally(() => {
      processing.value = false
    })
})
const customError = ref([])
const submit = (formData) => {
  const payload = {
    setting: {
      notifications: {
        ...formData,
      },
    },
  }
  processing.value = true
  updateGeneralCustomeSettings(payload, 'notifications')
    .catch((err) => {
      customError.value = err.errors
    })
    .finally(() => {
      processing.value = false
    })
}
</script>

<template>
  <div class="relative">
    <div class="flex flex-col">
      <overlay-loader v-if="processing" :full-screen="false" />
      <customForm :inputs="inputs" :err="customError" @submit="submit" />
    </div>
  </div>
</template>
