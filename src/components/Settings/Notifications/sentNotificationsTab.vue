<script setup lang="ts">
import {
  ChatBubbleBottomCenterTextIcon,
  EnvelopeIcon,
} from '@heroicons/vue/24/outline'
import type { ComputedRef } from 'vue'
import type {
  PaginationLinks,
  PaginationMeta,
  SendNotification,
  header,
} from '@/types'
import SentNotifications from '@/services/SentNotificationSercices'
import BaseButton from '@/components/BaseButton.vue'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
import OverlayLoader from '@/components/Common/OverlayLoader.vue'
const props = defineProps({
  currentTab: {
    type: String,
  },
})

const { showNotification } = useNotifications()

const { t } = useI18n()

const selectedTab = computed(() => props.currentTab)

const headers: ComputedRef<header[]> = computed(() => {
  const cols = [
    {
      title: t('notification_body'),
    },
    {
      title: t('notification_channel'),
    },
    {
      title: t('notification_create_at'),
    },
  ]

  if (selectedTab.value === 'failedNotifications') {
    cols.push(...[{
      title: t('notification_fail_reason'),
    },
    {
      title: '',
    }])
  }

  return cols
})

const tableData = reactive({
  sentNotificationList: [] as SendNotification[],
  paginationMeta: {
    current_page: 1,
    from: 1,
    last_page: 1,
    path: '',
    per_page: 1,
    to: 1,
    total: 1,
  } as PaginationMeta,
  paginationLinks: {
    first: '',
    last: '',
    next: '',
    prev: '',
  } as PaginationLinks,
  processing: false,
})

watch(selectedTab, () => {
  getNotification()
})

onMounted(async () => {
  await getNotification()
})

const getNotification = async (page = 1) => {
  tableData.processing = true
  const params = { page, filter: props.currentTab }
  SentNotifications.fetchNotifications(params)
    .then((response) => {
      tableData.sentNotificationList = response.data.data
      tableData.paginationMeta = response.data.meta
      tableData.paginationLinks = response.data.links
    })
    .finally(() => {
      tableData.processing = false
    })
}
const showNotificationDetails = ref(false)
const notificationSelected = ref<SendNotification>()
const openDetails = (item: any) => {
  showNotificationDetails.value = true
  notificationSelected.value = item
}

const retring = ref(false)

const retry = async (item) => {
  let message

  retring.value = true

  await SentNotifications.retry(item.id).then(async () => {
    message = {
      title: t('Success'),
      type: 'success',
      message: t('operations.retried'),
    }

    await getNotification()
  }).catch(() => {
    message = {
      title: t('Error'),
      type: 'error',
      message: t('operations.error'),
    }
  }).finally(() => {
    retring.value = false
  })

  showNotification(message)
}
</script>

<template>
  <sent-notification-modal
    :is-open="showNotificationDetails"
    :notification="notificationSelected"
    :show-retry-button="currentTab === 'failedNotifications'"
    :is-retrying="retring"
    @closed="showNotificationDetails = false"
    @retry="retry"
  />
  <div>
    <div class="flex flex-col mt-8">
      <div class="">
        <generic-table
          :headers="headers"
          :data="tableData.sentNotificationList"
          tr-class="cursor-pointer"
          :is-loading="tableData.processing"
          :on-row-click="openDetails"
        >
          <template #row="{ item }">
            <grid-td class="w-1/2 text-pretty">
              {{ item?.body }}
            </grid-td>

            <grid-td>
              <span class="block px-4">
                <EnvelopeIcon
                  v-if="item.channel == 'email'"
                  class="w-5 h-5 text-gray-400"
                />
                <ChatBubbleBottomCenterTextIcon
                  v-else
                  class="w-5 h-5 text-gray-400"
                />
              </span>
            </grid-td>
            <grid-td>
              <div class="text-gray-900">
                {{ item.created_at }}
              </div>
            </grid-td>

            <template v-if="currentTab === 'failedNotifications'">
              <grid-td>
                <div class="flex gap-2 items-center text-pretty">
                  {{ item.reason }}
                </div>
              </grid-td>
              <grid-td>
                <BaseButton @click="retry(item)">
                  {{ $t("notification_resend") }}
                </BaseButton>
              </grid-td>
            </template>
          </template>
        </generic-table>
        <Pagination
          v-if="tableData.sentNotificationList.length"
          :pagination-meta="tableData.paginationMeta"
          :pagination-links="tableData.paginationLinks"
          @change="getNotification"
        />
      </div>
    </div>
  </div>
</template>
