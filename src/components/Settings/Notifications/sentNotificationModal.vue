<script setup lang="ts">
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { XCircleIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import type { PropType } from 'vue'
import type { SendNotification } from '@/types'

defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  notification: {
    type: Object as PropType<SendNotification>,
    default: () => ({}),
  },
  showRetryButton: {
    type: Boolean,
    default: false,
  },
  isRetrying: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['retry', 'closed'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
</script>

<template>
  <div>
    <TransitionRoot appear :show="isOpen" as="template">
      <Dialog as="div" class="relative z-10" @close="emit('closed')">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all text-start"
                :dir="getLocale(locale)?.direction"
              >
                <DialogTitle
                  as="h3"
                  class="text-2xl text-center font-medium leading-6 text-gray-900"
                >
                  <div class="flex justify-between">
                    <p>
                      {{ $t("notification_details") }}
                    </p>
                    <div>
                      <XCircleIcon
                        class="w-9 h-9 close text-red-400 ms-auto cursor-pointer"
                        aria-hidden="true"
                        @click="$emit('closed')"
                      />
                    </div>
                  </div>
                </DialogTitle>
                <div class="mt-2">
                  <div class="w-full flex items-center justify-start gap-3">
                    <div>
                      <div
                        class="flex items-center gap-2 py-3 border-b border-stone-100"
                      >
                        <label
                          class="block text-sm font-bold text-neutral-500 w-15"
                        >{{ $t("notification_title") }}
                        </label>
                        <div class="relative items-center focus-within:z-10">
                          {{ notification?.title }}
                        </div>
                      </div>
                      <div
                        class="flex items-center gap-2 py-3 border-b border-stone-100"
                      >
                        <label
                          class="block text-sm font-bold text-neutral-500 "
                        >{{ $t("notification_body") }}
                        </label>
                        <div
                          class="flex gap-3 relative items-center focus-within:z-10"
                        >
                          {{ notification?.full_body }}
                        </div>
                      </div>
                      <div
                        class="flex items-center gap-2 py-3 border-b border-stone-100"
                      >
                        <label
                          class="block text-sm font-bold text-neutral-500"
                        >{{ $t("app") }}
                        </label>
                        <div class="relative items-center focus-within:z-10">
                          {{ notification?.app }}
                        </div>
                      </div>
                      <div
                        class="flex items-center gap-2 py-3 border-b border-stone-100"
                      >
                        <label
                          class="block text-sm font-bold text-neutral-500"
                        >{{ $t("notification_channel") }}
                        </label>
                        <div class="relative items-center focus-within:z-10">
                          {{ notification?.channel }}
                        </div>
                      </div>
                      <div
                        class="flex items-center gap-2 py-3 border-b border-stone-100"
                      >
                        <label
                          class="block text-sm font-bold text-neutral-500"
                        >{{ $t("notification_create_at") }}
                        </label>
                        <div class="relative items-center focus-within:z-10">
                          {{ notification?.created_at }}
                        </div>
                      </div>
                      <div
                        class="flex items-center gap-2 py-3 border-b border-stone-100"
                      >
                        <label
                          for="staff"
                          class="block text-sm font-bold text-neutral-500 w-15"
                        >{{ $t("receiver_name") }}
                        </label>
                        <div class="relative items-center focus-within:z-10">
                          {{ notification?.receiver.name }}
                        </div>
                      </div>
                      <div
                        class="flex items-center gap-2 py-3 border-b border-stone-100"
                      >
                        <label
                          class="block text-sm font-bold text-neutral-500 w-15"
                        >{{ $t("receiver_email") }}
                        </label>
                        <div class="relative items-center focus-within:z-10">
                          {{ notification?.receiver.email }}
                        </div>
                      </div>
                      <div
                        class="flex items-center gap-2 py-3 border-b border-stone-100"
                      >
                        <label
                          class="block text-sm font-bold text-neutral-500 "
                        >{{ $t("receiver_phone") }}
                        </label>
                        <div class="relative items-center focus-within:z-10">
                          {{ notification?.receiver.phone }}
                        </div>
                      </div>
                      <div v-if="showRetryButton && !notification.was_sent" class="flex items-center gap-2 py-3 border-b border-stone-100">
                        <BaseButton :processing="isRetrying" :disabled="isRetrying" @click="emit('retry', notification)">
                          {{ $t("notification_resend") }}
                        </BaseButton>
                      </div>
                    </div>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>
