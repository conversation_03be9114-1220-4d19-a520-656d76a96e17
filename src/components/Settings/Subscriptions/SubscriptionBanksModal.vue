<script setup lang="ts">
import useVuelidate from '@vuelidate/core'
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import type { Ref } from 'vue'
import { BuildingLibraryIcon, CreditCardIcon } from '@heroicons/vue/20/solid'
import { useLocalesStore } from '@/stores/locales'
import { required, requiredIf } from '@/utils/i18n-validators'
// building-library

// Props
const props = defineProps({
  isOpen: Boolean,
  selectedPlan: {
    type: Object,
    required: true,
  },
  billingCycle: String,
  renew: {
    type: Boolean,
    required: false,
    default: false,
  },
})
const emits = defineEmits(['close'])
const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const { showNotification } = useNotifications()
const { getBankForSubscription, checkoutSubscription, getCheckout, RenewSubscription }
  = useAccountSettingStore()
const user = useAuthStore()
// Form Data and Validation
const formData = ref({
  payment_method: 'credit_card',
  bank_id: '',
  beneficiary_name: '',
  receipt: null,
  coupon: '',
})

const rules = {
  receipt: {
    required: requiredIf(
      () => formData.value.payment_method === 'bank_transfer',
    ),
  },
  beneficiary_name: {
    required: requiredIf(
      () => formData.value.payment_method === 'bank_transfer',
    ),
  },
}
const v$ = useVuelidate(rules, formData)

const processing = ref(false)
const isMethodSelected = ref(false)

// Computed Values
const planName = computed(() => props.selectedPlan.name[locale.value])
const priceId = computed(
  () =>
    props.selectedPlan.prices.find(
      price => price.billing_cycle === props.billingCycle,
    )?.id,
)
const selectedBankTransfer = computed(
  () => formData.value.payment_method === 'bank_transfer',
)

// Fetch Plan Details and Banks
const planPriceDetails = ref({}) as Ref<{ plan: any; price: any }>
const banksForSubscription = ref<any[]>([])

onMounted(async () => {
  await loadPlanDetails()
  banksForSubscription.value = await getBankForSubscription()
  formData.value.bank_id = banksForSubscription.value?.[0]?.id
})

const loadPlanDetails = async () => {
  const data = await getCheckout(
    props.selectedPlan.uuid,
    priceId.value,
    formData.value.coupon,
  )
  planPriceDetails.value = data
  formData.value.coupon = planPriceDetails.value.coupon?.code || ''
}

// Coupon Application
const applyCoupon = async () => {
  processing.value = true
  try {
    await loadPlanDetails()
    if (formData.value.coupon)
      return
    showNotification({
      title: t('Error'),
      type: 'error',
      message: `${t('coupon_not_found')}`,
    })
  }
  catch (error) {
    showNotification({
      title: t('Error'),
      type: 'error',
      message: error.message,
    })
  }
  finally {
    processing.value = false
  }
}

// Subscription Process
const router = useRouter()
const subscribe = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return

  processing.value = true
  try {
    if (props.renew) {
      const { url = '', message, ...info } = await RenewSubscription(
        props.selectedPlan.subscription,
        cleanFormData(),
      )
      if (formData.value.payment_method === 'credit_card')
        window.location.href = url

      else
        router.push({ name: 'PaymentSuccess', query: { ...info, payment_type: 'bank_transfer' } })

      emits('close')
    }
    else {
      const payload = cleanFormData()
      const { url = '', message, ...info } = await checkoutSubscription(
        props.selectedPlan.uuid,
        priceId.value,
        payload,
      )
      if (formData.value.payment_method === 'credit_card')
        window.location.href = url

      else
        router.push({ name: 'PaymentSuccess', query: { ...info, payment_type: 'bank_transfer' } })

      emits('close')
    }
  }
  finally {
    processing.value = false
  }
}

// Clean Form Data
const cleanFormData = () => {
  if (formData.value.payment_method === 'credit_card') {
    delete formData.value.bank_id
    delete formData.value.beneficiary_name
    delete formData.value.receipt
  }
  return formData.value
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="props.renew ? 'renew_to_plan' : 'subscribe_to_plan'"
    :param="planName"
    panel-classes="w-full max-w-2xl transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all  sm:w-2xl md:px-8 lg:px-8 relative"
    @close="emits('close')"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    <form
      class="text-start"
      @submit.prevent="subscribe"
    >
      <div class="grid grid-cols-1 gap-4">
        <div
          class="flex flex-col gap-2 items-start px-5 py-3 text-lg font-medium text-black bg-gray-100"
        >
          <LabelInput class="block" for="coupon">
            {{ $t("coupon_if_applied") }}
          </LabelInput>
          <div class="flex items-center w-full">
            <TextInput id="coupon" v-model="formData.coupon" />
            <base-button
              :disabled="!formData.coupon || processing"
              class="px-2 py-2 text-white bg-primary-600 disabled:bg-gray-500 disabled:opacity-50"
              @click="applyCoupon"
            >
              {{ $t("apply") }}
            </base-button>
          </div>
        </div>
        <div
          v-if="planPriceDetails.discount"
          class="flex gap-2 justify-between items-center px-5 py-3 text-lg font-medium text-black bg-gray-100"
        >
          <h3 class="font-medium text-black text-md">
            {{ $t("pos.discount") }}
            <span class="text-primary-700">
              (
              {{ parseFloat(planPriceDetails.coupon.value) }}
              {{ planPriceDetails.coupon.type == "percentage" ? "%" : "" }}
              )
            </span>
          </h3>
          <h3 class="font-medium text-black text-md">
            <price-format
              :form-data="{
                price: planPriceDetails.discount,
                currency: 'SAR',
              }"
            />
          </h3>
        </div>
        <div
          class="flex gap-2 justify-between items-center px-5 py-3 text-lg font-medium text-black bg-gray-100"
        >
          <h3 class="font-medium text-black text-md">
            {{ $t("total_included_taxes") }}
          </h3>
          <h3 class="font-medium text-black text-md">
            <price-format
              :form-data="{
                price: planPriceDetails.total_amount,
                currency: 'SAR',
              }"
            />
          </h3>
        </div>
        <div
          v-if="selectedBankTransfer"
          class="flex flex-col col-span-1 gap-2 w-full"
        >
          <div
            v-for="bankInfo in banksForSubscription"
            :key="bankInfo.id"
            class="flex relative flex-col gap-3 p-2 pt-10 w-full bg-gray-300 rounded-md"
          >
            <span
              class="flex absolute top-2 justify-between items-center start-2"
            >
              <RedioInput
                v-model="formData.bank_id"
                :value="bankInfo.id"
                class="w-6 h-6 cursor-pointer text-primary-700"
                name="bank"
              />
            </span>
            <div
              class="grid grid-cols-2 gap-2 justify-start items-center w-full sm:grid-cols-6 sm:gap-4"
            >
              <p class="col-span-1 text-sm font-semibold">
                {{ $t("bank_name") }}
              </p>
              <CopyInput
                :value="bankInfo.bank_name"
                class="col-span-5 w-full"
              />
            </div>
            <div
              class="grid grid-cols-2 gap-2 justify-start items-center w-full sm:grid-cols-6 sm:gap-4"
            >
              <p class="col-span-1 text-sm font-semibold">
                {{ $t("account_owner") }}
              </p>
              <CopyInput :value="bankInfo.name" class="col-span-5 w-full" />
            </div>
            <div
              class="grid grid-cols-2 gap-2 justify-start items-center w-full sm:grid-cols-6 sm:gap-4"
            >
              <p class="col-span-1 text-sm font-semibold">
                {{ $t("account_number") }}
              </p>
              <CopyInput
                :value="bankInfo.account_number"
                class="col-span-5 w-full"
              />
            </div>
            <div
              class="grid grid-cols-2 gap-2 justify-start items-center w-full sm:grid-cols-6 sm:gap-4"
            >
              <p class="col-span-1 text-sm font-semibold">
                {{ $t("iban_number") }}
              </p>
              <CopyInput :value="bankInfo.iban" class="col-span-5 w-full" />
            </div>
          </div>
          <div
            class="flex flex-col gap-3 p-2 mt-3 w-full bg-gray-300 rounded-md"
          >
            <form-group :validation="v$" name="beneficiary_name">
              <template #default="{ attrs }">
                <div
                  class="grid grid-cols-2 gap-2 justify-start items-center w-full sm:grid-cols-6 sm:gap-4"
                >
                  <TextInput
                    v-bind="attrs"
                    id="beneficiaryName"
                    v-model="formData.beneficiary_name"
                    :label="$t('beneficiary_name')"
                    class="col-span-5 w-full"
                    :placeholder="$t('beneficiary_name')"
                  />
                </div>
              </template>
            </form-group>
            <form-group :validation="v$" name="receipt">
              <template #default="{ attrs }">
                <div
                  class="grid grid-cols-2 gap-2 justify-start items-center w-full sm:grid-cols-6 sm:gap-4"
                >
                  <label for="amount" class="col-span-1 text-sm font-semibold">
                    {{ $t("receipt_transfer") }}
                  </label>
                  <FileInput
                    id="receipt"
                    v-model="formData.receipt"
                    class="col-span-5 bg-white"
                    :placeholder="$t('receipt')"
                  />
                </div>
              </template>
            </form-group>
          </div>
        </div>
      </div>
      <div
        v-if="isMethodSelected"
        class="flex gap-4 justify-center items-center mt-4 w-full md:col-span-2"
      >
        <BaseButton
          type="submit"
          class="inline-flex w-full hover:bg-primary-500"
          custome-bg="bg-primary-700"
        >
          {{ $t("complete_payment") }}
        </BaseButton>
      </div>
      <div
        v-else
        class="flex flex-col gap-2"
      >
        <div
          class="flex flex-col gap-4 justify-center items-center mt-4 w-full md:flex md:flex-row md:col-span-2"
        >
          <BaseButton
            type="submit"
            class="inline-flex gap-3 w-full hover:bg-primary-500"
            custome-bg="bg-primary-700"
            @click="
              isMethodSelected = true;
              formData.payment_method = 'bank_transfer';
            "
          >
            {{ $t("bank_transfer") }}
            <BuildingLibraryIcon class="w-6 h-6" />
          </BaseButton>
          <BaseButton
            type="submit"
            class="inline-flex gap-3 w-full hover:bg-primary-500"
            custome-bg="bg-primary-700"
            @click="
              isMethodSelected = true;
              subscribe();
            "
          >
            {{ $t("online_payment") }}
            <CreditCardIcon class="w-6 h-6" />
          </BaseButton>
        </div>
      </div>
    </form>
  </Modal>
</template>
