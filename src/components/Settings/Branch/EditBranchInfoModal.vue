<script setup lang="ts">
import type { PropType } from 'vue'
import { ref, reactive, watch, markRaw } from 'vue'
import { useVuelidate } from '@vuelidate/core'
import { required } from '@/utils/i18n-validators'
import { useTeamStore } from '@/stores/teams'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'
import { useLocalesStore } from '@/stores/locales'

const props = defineProps({
  isOpen: { type: Boolean, default: false },
  branchUuid: { type: String, required: true },
  branch: { type: Object as PropType<any>, required: true },
})
const emits = defineEmits(['close', 'refresh'])

const { locale, t } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { update } = useTeamStore()
const processing = ref(false)

const formData = reactive({
  name: '',
  base_link: '',
  enabled: false,
  address: '',
  phone: '',
  phone_country: '',
  location: '',
  slug: '',
})

// Location types for branch type dropdown
const locationTypes = markRaw(['on-site', 'home-service', 'online'])

const rules = {
  name: { required },
  address: { required },
  location: { required },
  slug: { required },
}

const v$ = useVuelidate(rules, formData)

watch(
  () => props.branch,
  (value) => {
    if (!value) return
    formData.name = value.name || ''
    formData.base_link = value.base_link || ''
    formData.enabled = value.enabled ?? false
    formData.address = value.address || ''
    formData.phone = value.phone || ''
    formData.phone_country = value.phone_country || ''
    formData.location = value.location || ''
    formData.slug = value.slug || ''
  },
  { immediate: true }
)

// Phone number handler
const setPhoneNumber = (
  phoneNumber: string,
  phoneObject: { countryCode: string }
) => {
  formData.phone = phoneNumber
  formData.phone_country = phoneObject.countryCode
}

async function updateBranchInfo() {
  processing.value = true
  try {
    await update({
    teamId: props.branchUuid,
    body: {
        name: formData.name,
        base_link: formData.base_link,
        enabled: formData.enabled,
        address: formData.address,
        phone: formData.phone,
        phone_country: formData.phone_country,
        location: formData.location,
        slug: formData.slug,
    }
    })
    emits('refresh')
    emits('close')
  } catch (error) {
    console.error('Failed to update branch:', error)
  } finally {
    processing.value = false
  }
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="t('editBranch')"
    panel-classes="w-full max-w-xl transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all"
    @close="emits('close')"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    <form class="text-start" @submit.prevent="updateBranchInfo">
 <!-- Basic Information Section -->
 <div class="grid grid-cols-1 gap-3 w-full md:grid-cols-2 lg:grid-cols-3">
        
        <form-group :validation="v$" name="name">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="name"
              v-model="formData.name"
              :label="$t('settings.general.name')"
              :placeholder="$t('modalPlacholder.branch')"
            />
          </template>
        </form-group>

        <form-group error-name="phone">
          <template #default="attrs">
            <PhoneInput
              v-bind="attrs"
              :model-value="formData.phone"
              :label="$t('form.phone')"
              @update:model-value="setPhoneNumber"
            />
          </template>
        </form-group>

        <form-group :validation="v$" name="address">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="address"
              v-model="formData.address"
              :label="$t('settings.general.address')"
              :placeholder="$t('modalPlacholder.address')"
            />
          </template>
        </form-group>

        <form-group :validation="v$" name="location">
          <template #default="{ attrs }">
            <SelectInput
              id="location"
              v-bind="attrs"
              v-model="formData.location"
              :label="$t('branch_type')"
              :placeholder="$t('branch_type')"
            >
              <option v-for="type in locationTypes" :key="type" :value="type">
                {{ $t(type) }}
              </option>
            </SelectInput>
          </template>
        </form-group>

        <div class="w-full">
          <form-group name="enabled">
            <template #default>
              <SwitchInput
                id="enabled-switch"
                v-model="formData.enabled"
                :label="$t('status') + ' : ' + (formData.enabled ? $t('active') : $t('inactive'))"
              />
            </template>
          </form-group>
        </div>
    </div>
      <!-- Submit Button -->
      <div class="mt-6">
        <BaseButton
          type="submit"
          class="w-full inline-flex items-center justify-center gap-2 px-6 py-3 text-white bg-[#0F2C3F] border border-[#0F2C3F] rounded-md hover:bg-white hover:text-[#0F2C3F] transition"
          :processing="processing"
        >
          {{ $t("form.save") }}
        </BaseButton>
      </div>
    </form>
  </Modal>
</template>