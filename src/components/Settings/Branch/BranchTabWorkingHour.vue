<script lang="ts" setup>
import type { PropType } from 'vue'
import type { Teams } from '@/types'
const prop = defineProps({
  team: {
    type: Object as PropType<Teams>,
    required: true,
  },
})

const { team } = toRefs(prop)
</script>

<template>
  <div
    class="'relative overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
  >
    <generic-schedule :item-id="team.uuid" model="team" />
  </div>
</template>

<style></style>
