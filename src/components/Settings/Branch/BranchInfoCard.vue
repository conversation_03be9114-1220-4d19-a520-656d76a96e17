<script setup lang="ts">
import type { PropType } from 'vue'
import BranchIcon from '@/components/Icons/BranchIcon.vue'
import type { Branch } from '@/types/branches'
import EditIcon from '@/components/Icons/EditIcon.vue'

const props = defineProps({
  branch: {
    type: Object as PropType<Branch>,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
})

const { t } = useI18n()

const statusText = computed(() =>
  props.branch?.enabled ? t('active') : t('inactive')
)
const safe = (val: any) => val ?? '-'
</script>

<template>
  <div v-if="branch" class="grid gap-6 p-4 w-full bg-gray-50 rounded-xl">
    <div class="bg-white rounded-lg shadow-sm p-4 w-full">
      <div class="flex justify-between items-center mb-2">
        <span class="text-lg font-bold text-neutral-900">{{ title || $t('modalHeader.branch_details') }}</span>
        <slot name="edit">
          <LilActionBtn
            :icon="EditIcon"
            :label="$t('form.edit')"
            @click="$emit('edit')"
          />
        </slot>
      </div>
      <div class="flex flex-row gap-x-8 flex-wrap gap-2">
        <div class="flex flex-col gap-2 flex-1 min-w-[220px]">
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ t('branch.name') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ safe(props.branch.name) }}</span>
          </div>

          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ t('settings.general.address') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ safe(props.branch.address) }}</span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ t('form.phone') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ safe(props.branch.phone) }}</span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ t('branch_type') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ t(safe(props.branch.location)) }}</span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ t('settings.bookingpage.preview') }}:</span>
            <span class="text-blue-500 font-normal text-start">{{ safe(props.branch.base_link) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
