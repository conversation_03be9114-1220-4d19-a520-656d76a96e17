<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { Listbox, ListboxButton, ListboxOptions, ListboxOption } from '@headlessui/vue'
import { ChevronDownIcon } from '@heroicons/vue/24/outline'
import { useTeamStore } from '@/stores/teams'
import type { PropType } from 'vue'
import type { Branch } from '@/types/branches'

const props = defineProps({
  branch: {
    type: Object as PropType<Branch>,
    required: true,
  },
})
const emit = defineEmits(['refresh'])
const { t } = useI18n()
const { update } = useTeamStore()
const processing = ref(false)

const statusOptions = computed(() => [
  { label: t('active'), value: true },
  { label: t('inactive'), value: false },
])

const statusValue = ref(props.branch?.enabled ?? true)

watch(() => props.branch?.enabled, (newStatus) => {
  statusValue.value = newStatus
})

const updateStatus = async (val: boolean) => {
  if (!props.branch?.uuid || processing.value) return
  if (val === props.branch.enabled) return

  processing.value = true
  try {
    await update({
      teamId: props.branch.uuid,
      body: { ...props.branch, enabled: val }
    })
    emit('refresh')
  } finally {
    processing.value = false
  }
}
</script>

<template>
  <div v-if="branch" class="flex items-center gap-2">
    <span class="text-sm font-medium text-neutral-800">{{ t('status') }}:</span>
    <Listbox v-model="statusValue" @update:modelValue="updateStatus" :disabled="processing">
      <div class="relative w-28">
        <ListboxButton
          class="relative w-full cursor-pointer rounded-lg py-1.5 pl-8 pr-3 text-left focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
          :class="statusValue ? 'bg-primary-50 text-primary-600' : 'bg-red-50 text-red-600'"
        >
          <span class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2">
            <ChevronDownIcon class="h-4 w-4 text-gray-400" />
          </span>
          <span class="block truncate">
            {{ statusOptions.find(opt => opt.value === statusValue)?.label }}
          </span>
        </ListboxButton>
        <ListboxOptions class="absolute z-10 mt-1 w-full rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-sm">
          <ListboxOption
            v-for="opt in statusOptions"
            :key="opt.value"
            :value="opt.value"
            v-slot="{ active, selected }"
            as="template"
          >
            <li
              :class="[
                'relative cursor-pointer select-none py-2 pl-3 pr-9',
                active ? 'bg-gray-100' : ''
              ]"
            >
              <span :class="['block truncate', selected ? 'font-semibold' : 'font-normal']">
                {{ opt.label }}
              </span>
            </li>
          </ListboxOption>
        </ListboxOptions>
      </div>
    </Listbox>
    <span v-if="processing" class="text-xs text-gray-400">{{ $t('loading') }}...</span>
  </div>
</template> 