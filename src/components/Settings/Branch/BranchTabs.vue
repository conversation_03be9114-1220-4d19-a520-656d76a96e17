<script setup lang="ts">
import { ref, watch } from 'vue'
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/vue'
import type { PropType } from 'vue'
import type { Teams } from '@/types'

const props = defineProps({
  team: {
    type: Object as PropType<Teams>,
    default: null,
  },
})

const selectedTab = ref()

const tabs = computed(() => {
  return ['workingHours']
  // return ['workingHours', ...(props.team.location === 'home-service' ? ['zones'] : [])]
})

watch(
  () => props.team,
  (staff) => {
    staff && (selectedTab.value = tabs[0])
  },
)
</script>

<template>
  <TabGroup v-if="team">
    <TabList class="flex p-1 space-x-1 bg-slate-700">
      <Tab
        v-for="(tab, idx) in tabs"
        :key="idx"
        v-slot="{ selected }"
        as="template"
      >
        <button
          class="w-full py-2.5 text-sm font-medium leading-5 text-white focus:outline-none"
          :class="[
            selected
              ? 'bg-slate-600 shadow'
              : 'text-white hover:bg-slate-300/[0.12]',
          ]"
        >
          {{ $t(`staff.${tab}`) }}
        </button>
      </Tab>
    </TabList>

    <TabPanels class="relative w-full mt-2">
      <TabPanel
        v-for="(tab, idx) in tabs"
        :key="idx"
        class="bg-white focus:outline-none focus:ring-2"
      >
        <BranchTabWorkingHour v-if="tab === 'workingHours'" :team="team" />
        <!-- <BranchZones v-else-if="tab === 'zones' " :team="team" /> -->
        <div v-else class="p-4">
          {{ $t(`team.${tab}`) }}
        </div>
      </TabPanel>
    </TabPanels>
  </TabGroup>
</template>
