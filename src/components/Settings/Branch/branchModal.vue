<script lang="ts" setup>
// ===============Import Section===============
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { GoogleMap, Marker, MarkerCluster } from 'vue3-google-map'
import { required } from '@/utils/i18n-validators'
import BranchIcon from '@/components/Icons/BranchIcon.vue'
import type { Branch } from '@/types/branches'
import SwitchInput from '@/components/FormControls/Inputs/SwitchInput.vue'
import ArrowLeftIcon from '@heroicons/vue/24/outline/ArrowLeftIcon.js'
import LocationInput from '@/components/FormControls/Inputs/LocationInput.vue'
// ===============Store and Basic Setup===============
const { createTeam } = useTeamStore()

const emits = defineEmits(['close', 'refresh'])
const closeModal = () => {emits('close') }

const link = ref('')
const {locale} = useI18n()
const {getLocale} = storeToRefs(useLocalesStore())
const processing = ref(false)
const processing2 = ref(false)
const buttonAction = ref(true)
const {t} = useI18n()
const API_KEY = import.meta.env.VITE_GOOGLE_MAP_KEY
const { coords, calculateCenter } = useMap()

// ===============Props===============
const props = defineProps({
  showModal: Boolean,
})

// ===============Form Data Setup===============
const formData = reactive<Branch>({
  uuid: '',
  base_link: '',
  slug: '',
  name: '',
  enabled: true,
  address: '',
  phone_country: '',
  phone: '',
  longitude: coords.value.longitude,
  latitude: coords.value.latitude,
  location: '',
  imageLink: '',
  current: false,
  personal_team: false,
})

// ===============Validation Rules===============
const rules = {
  name: {
    required,
  },
  address: {
    required,
  },
  location: {
    required,
  },
}

const v$ = useVuelidate(rules, formData)

// ===============Form Reset Function===============
const resetForm = () => {
  formData.uuid = ''
  formData.base_link = ''
  formData.slug = ''
  formData.name = ''
  formData.phone_country = ''
  formData.phone = ''
  formData.enabled = true
  formData.address = ''
  formData.longitude = coords.value.longitude
  formData.latitude = coords.value.latitude
  formData.location = 'on-site'
  formData.imageLink = ''
  formData.current = false
  formData.personal_team = false
  link.value = ''
  v$.value.$reset()
}

// ===============Location Operations===============
const locationTypes = markRaw(['on-site', 'home-service', 'online'])

watch(
  () => coords.value,
  (coords) => {
    if (coords) {
      formData.latitude = coords.latitude
      formData.longitude = coords.longitude
    }
  },
  { immediate: true },
)

const mark = (event: any) => {
  formData.latitude = event.latLng.lat()
  formData.longitude = event.latLng.lng()
}

// ===============Phone Number Operations===============
const setPhoneNumber = (
  phoneNumber: string,
  phoneObject: { countryCode: string },
) => {
  formData.phone = phoneNumber
  formData.phone_country = phoneObject.countryCode
}

// ===============CRUD Operations===============
const createTeamRecord = async (payload) => {
  return createTeam(payload).then(() => {
    resetForm()
  })
}

const saveTeam = async () => {
  if (processing.value || processing2.value) return
  v$.value.$touch()
  if (v$.value.$invalid) return

  // Set the correct processing state
  if (buttonAction.value) {
    processing2.value = true
  } else {
    processing.value = true
  }

  let payload = {
    ...formData,
    latitude: parseFloat(formData.latitude.toString()).toFixed(8),
    longitude: parseFloat(formData.longitude.toString()).toFixed(8),
  }
  try {
    await createTeamRecord(payload)
    emits('refresh')
    if (buttonAction.value) {
      // "Create" button: close modal
      closeModal()
    } else {
      // "Save and Add Service" button: reset form for another entry
      resetForm()
    }
  } finally {
    processing.value = false
    processing2.value = false
  }
}
</script>

<template>
  <FullScreenModal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="$t('modalHeader.createBranch')"
    :subtitle="$t('modalSubtitle.createBranch')"
    :icon="BranchIcon"
    panel-classes="w-full bg-white rounded-xl shadow-[0px_8px_8px_-4px_rgba(10,13,18,0.04)] shadow-[0px_20px_24px_-4px_rgba(10,13,18,0.10)] flex flex-col overflow-visible sm:w-full sm:mx-20 h-full my-20"
    @close="closeModal"
  >
    <overlay-loader v-if="processing" :full-screen="false" />

    <form class="flex flex-col gap-6 p-4" :dir="getLocale(locale)?.direction" @submit.prevent="saveTeam">

            <!-- Image Upload Section -->
            <div class="w-full">
              <form-group :validation="v$" name="images">
                <template #default>
                  <MultiFileInput
                    id="branch-images"
                    v-model="formData.images"
                    :multiple="false"
                    size-note="5MB"
                    format-note="JPG, PNG"
                    accept="image/*"
                  />
                </template>
              </form-group>
            </div>

      <!-- Basic Information Section -->
      <div class="grid grid-cols-1 gap-3 w-full md:grid-cols-2 lg:grid-cols-3 ">
        
          <form-group :validation="v$" name="name">
            <template #default="{ attrs }">
              <TextInput
                v-bind="attrs"
                id="name"
                v-model="formData.name"
                :label="$t('settings.general.name')"
                :placeholder="$t('modalPlacholder.branch')"
              />
            </template>
          </form-group>

          <form-group error-name="phone">
            <template #default="attrs">
              <PhoneInput
                v-bind="attrs"
                :model-value="formData.phone"
                :label="$t('form.phone')"
                @update:model-value="setPhoneNumber"
              />
            </template>
          </form-group>

          <form-group :validation="v$" name="address">
            <template #default>
              <LocationInput
                v-bind="attrs"
                id="address"
                v-model="formData.address"
                :label="$t('settings.general.address')"
                :placeholder="$t('modalPlacholder.address')"
              />
            </template>
          </form-group>

          <form-group :validation="v$" name="location">
            <template #default="{ attrs }">
              <SelectInput
                id="location"
                v-bind="attrs"
                v-model="formData.location"
                :label="$t('branch_type')"
                :placeholder="$t('branch_type')"
              >
                <option v-for="type in locationTypes" :key="type" :value="type">
                  {{ $t(type) }}
                </option>
              </SelectInput>
            </template>
          </form-group>



          <form-group name="enabled">
            <template #default>
              <SwitchInput
                id="enabled-switch"
                v-model="formData.enabled"
                :label="$t(formData.enabled ? $t('active') : $t('inactive'))"
              />
            </template>
          </form-group>
            
      </div>


      <GenericSchedule
        :model="formData"
        :item-id="formData.uuid"
        @refresh="emits('refresh')"
        withoutServer
      />

      <!-- Action Buttons -->
      <div class="flex flex-col gap-4 justify-center mt-4 w-full sm:flex-row">
        <BaseButton
          type="submit"
          class="w-full sm:flex-1 px-6 py-3 rounded-md bg-white border border-[#0F2C3F] !text-[#0F2C3F] hover:bg-gray-100 hover:!text-white transition"
          show-icon
          :processing="processing2"
          @mousedown.prevent="buttonAction = true"
        >
          {{ $t("form.create") }}
        </BaseButton>

        <BaseButton
          type="submit"
          class="w-full sm:flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-md text-white bg-[#0F2C3F] border border-[#0F2C3F] hover:bg-white hover:text-[#0F2C3F] transition"
          show-icon
          :processing="processing"
          @mousedown.prevent="buttonAction = false"
        >
          {{ $t("save_and_add_service") }}
          <ArrowLeftIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </form>
  </FullScreenModal>
</template>

<style scoped>
form > div:not(:last-child) {
  flex: 1 0 48%;
}

form > div.full {
  flex: 1 0 100%;
}

@media (max-width: 768px) {
  form > div:not(:last-child) {
    flex: 1 0 100% !important;
  }
}
</style>
