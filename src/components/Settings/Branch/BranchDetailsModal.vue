<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { useTeamStore } from '@/stores/teams'
import { storeToRefs } from 'pinia'
import BranchIcon from '@/components/Icons/BranchIcon.vue'
import BranchInfoCard from '@/components/Settings/Branch/BranchInfoCard.vue'
import { useLocalesStore } from '@/stores/locales'
import { useI18n } from 'vue-i18n'
import EditBranchInfoModal from './EditBranchInfoModal.vue'
import BranchStatusAction from './BranchStatusAction.vue'
import TrashIcon from '@/components/Icons/TrashIcon.vue'
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue'
import BranchTabs from './BranchTabs.vue'

const props = defineProps({
  showModal: Boolean,
  branchUuid: {
    type: String,
    required: true,
  },
})
const emit = defineEmits(['close', 'updated', 'refresh'])

const { locale, t } = useI18n()
const { getLocale } = useLocalesStore()

const { fetchTeamById, removeTeam } = useTeamStore()
const { Team, processing } = storeToRefs(useTeamStore())

const branch = ref(null)
const formData = reactive({
  name: '',
  address: '',
  latitude: '',
  longitude: '',
  // ...add other fields as needed
})

const isBranchLoaded = computed(() => {
  return Team.value && Object.keys(Team.value).length > 0 && Team.value.uuid === props.branchUuid
})

const showEditBranchInfoModal = ref(false)
const showConfModal = ref(false)

watch(
  () => props.branchUuid,
  async (uuid) => {
    showConfModal.value = false;
    if (!uuid) return
    const data = await fetchTeamById(uuid, true)
    branch.value = { ...data }
    formData.name = data.name || ''
    formData.address = data.address || ''
    formData.latitude = data.latitude || ''
    formData.longitude = data.longitude || ''
  },
  { immediate: true }
)

const closeModal = () => emit('close')

function openEditBranchInfoModal() {
  showEditBranchInfoModal.value = true
}
function closeEditBranchInfoModal() {
  showEditBranchInfoModal.value = false
}
function refreshAfterEdit() {
  showEditBranchInfoModal.value = false
  // Refetch branch data to update details after edit
  if (props.branchUuid) {
    fetchTeamById(props.branchUuid, true).then(data => {
      branch.value = { ...data }
      formData.name = data.name || ''
      formData.address = data.address || ''
      formData.latitude = data.latitude || ''
      formData.longitude = data.longitude || ''
    })
  }
}

async function handleDelete() {
  if (!branch.value?.uuid) return
  await removeTeam(branch.value.uuid)
  emit('refresh')
  closeModal()
}
</script>

<template>
  <FullScreenModal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="t('modalHeader.branch_details')"
    :icon="branch?.imageLink || BranchIcon"
    panel-classes="w-full bg-white rounded-xl shadow-[0px_8px_8px_-4px_rgba(10,13,18,0.04)] shadow-[0px_20px_24px_-4px_rgba(10,13,18,0.10)] flex flex-col overflow-hidden sm:w-full sm:mx-20 h-full my-20"
    @close="closeModal"
  >
    <div class="flex justify-between items-center w-full ">
      <BranchStatusAction v-if="branch" :branch="branch" @refresh="refreshAfterEdit" />
      <IconOnlyBtn
        :icon="TrashIcon"
        @click="showConfModal = true"
        class="text-rose-500 outline-rose-200"
      />
    </div>
    <confirmation-modal
      v-if="showConfModal"
      :is-open="showConfModal"
      @closed="showConfModal = false"
      @removed="handleDelete"
      :apiCall="() => removeTeam(branch.value.uuid)"
      :recordId="branch.value?.uuid"
    >
      <p class="leading-7 text-start">
        {{ $t('confirmModal.msg') }}
      </p>
    </confirmation-modal>
    <div class="flex flex-col gap-6  pt-6 pb-6">
      <div class="grid gap-6 w-full bg-gray-50 rounded-xl">
        <BranchInfoCard
          :branch="branch"
          :form-data="formData"
          :loading="processing"
          @edit="openEditBranchInfoModal"
        />
      </div>
    </div>

    <DisclosureWrapper v-if="Team.uuid && !processing" :title="t('modalHeader.workingHours')">
      <BranchTabWorkingHour v-if="Team.uuid && !processing" :team="Team" />
    </DisclosureWrapper>
    
    <EditBranchInfoModal
      v-if="showEditBranchInfoModal"
      :is-open="showEditBranchInfoModal"
      :branch-uuid="props.branchUuid"
      :branch="branch"
      @close="closeEditBranchInfoModal"
      @refresh="refreshAfterEdit"
    />
  </FullScreenModal>
</template>