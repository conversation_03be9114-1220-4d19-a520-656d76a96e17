<script setup lang="ts">
import type { PropType } from 'vue'
import {
  Disclosure, DisclosureButton, DisclosurePanel, Listbox, ListboxButton, ListboxOption, ListboxOptions, Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  Switch,
} from '@headlessui/vue'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import { ArrowLeftIcon, CheckIcon, PlusIcon } from '@heroicons/vue/24/outline'
import { ChevronDownIcon, ChevronUpIcon , HomeIcon } from '@heroicons/vue/20/solid';
import useVuelidate from '@vuelidate/core';
import Multiselect from 'vue-multiselect'
import 'vue-multiselect/dist/vue-multiselect.css'
import { storeToRefs } from 'pinia'
import { toRaw } from 'vue'
import { convertToHoursAndMinutes } from '../../utils/time'
import LinkedInIcon from '../Icons/LinkedInIcon.vue'
import EditServiceInfoModal from './EditServiceInfoModal.vue'
import { minLength, required, requiredIf } from '@/utils/i18n-validators';
import type { HoursMinutes, Service, Staff } from '@/types';
import ServicesIcon from '@/components/Icons/ServicesIcon.vue';
import EditDescServiceModal from './EditDescServiceModal.vue'
import ServiceDataCard from './ServiceInfoCard.vue'
import LilActionBtn from '@/components/Buttons/LilActionBtn.vue'
import EditIcon from '@/components/Icons/EditIcon.vue'

import ExportIcon from '@/components/Icons/ExportIcon.vue'
import FacebookIcon from '@/components/Icons/FacebookIcon.vue'
import PreviewIcon from '@/components/Icons/PreviewIcon.vue'
import ShareIcon from '@/components/Icons/ShareIcon.vue'
import WhatsappIcon from '@/components/Icons/WhatsappIcon.vue'
import RepeatIcon from '@/components/Icons/RepeatIcon.vue'
import TrashIcon from '@/components/Icons/TrashIcon.vue'
import XIcon from '../Icons/XIcon.vue'
import CopyLinkIcon from '../Icons/CopyLinkIcon.vue'

const props = defineProps({
  serviceUuid: {
    default: null,
    type: String,
    required: true,
  },
  showModal: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits<{
  (e: 'created'): void
  (e: 'closed'): void
  (e: 'updated'): void
  (e: 'openShowDetailsModal'): void
  (e: 'refresh'): void
}>()
const { locale, t } = useI18n()
const { fetchCategories, createCategory } = useCategoryStore()
const { getLocale } = storeToRefs(useLocalesStore())
const {
  createService,
  updateService,
  removeService,
  fetchServiceById,
  removeImageFromService,
  addImageToService,
  makeMainImage,
} = useServicesStore()
const { userInfo } = useAuthStore()
const { getUserInfo } = storeToRefs(useAuthStore())
const processing = ref(false)
const processing2 = ref(false)
const buttonAction = ref(false)
const editMode = ref(false)
const serviceUuid = ref('')
const openAssignModal = ref(false)
const showEditStaffServiceModal = ref(false)

const showEditServiceDataModal = ref(false)

const router = useRouter()

const getTeam = computed(() => {
  return getUserInfo.value.teams
})
// const { service } = toRefs(props);
const selected = ref<(typeof options)[0] | null>(null)
const service = ref<Service | null>(null)
const default_duration: HoursMinutes = {
  hours: 0,
  minutes: 15,
}
const default_price = 0
const formData = reactive<Omit<Service, 'uuid'>>({
  name_localized: { ar: '', en: '' },
  description_localized: { ar: '', en: '' },
  duration_time: '',
  slug: '',
  is_public: true,
  staff_users: [],
  color: props.random,
  price: default_price,
  sort_order: 0,
  category_id: '',
  image: null,
  display_on_booking_page: true,
  location: 'on-site',
  team_id: '',
  base_link: '',
  images: [],
})

watch(
  service,
  (value: Service) => {
    if (!value)
      return
    const {
      duration,
      name_localized,
      description_localized,
      category_id,
      imageLink,
      slug,
      color,
      price,
      sort_order,
      team_id,
      is_public,
      display_on_booking_page,
      staff,
      base_link,
    } = value
    formData.name_localized = name_localized || { ar: '', en: '' }

    formData.description_localized = {
      ar: description_localized?.ar || '',
      en: description_localized?.en || '',
    }
    formData.category_id = category_id?.uuid || ''
    formData.imageLink = imageLink || ''
    formData.slug = slug || ''
    formData.staff_users = staff || []
    formData.color = color || '#000000'
    formData.display_on_booking_page = display_on_booking_page
    formData.is_public = is_public
    formData.sort_order = sort_order
    formData.base_link = base_link
    formData.duration_time = duration || 0
    formData.price = price || default_price
    formData.image = ''
    formData.team_id = team_id || ''
    if (value.uuid)
      editMode.value = true
  },
  { immediate: true, deep: true },
)

watch(
  () => formData.team_id,
  (val) => {
    formData.category_id = ''
    fetchCategories().then(({ data }) => {
      categories.value = data
    })
  },
)
const closeModal = () => {
  emit('closed')
}

const isPublicService = computed(() => formData.is_public)

const selectedStaff = computed(() => formData.staff_users)

watch(isPublicService, (value) => {
  if (value)
    formData.staff_users = props.teamStaff
  else
    formData.staff_users = []
})

/// /////// DetailsService //////////////////////
const showConfModal = ref(false)

const serviceSlug = ref('')

const openDeleteModal = () => {
  showConfModal.value = true
  console.log('showConfModal', props.serviceUuid)
}

const openDuplicateModal = () => {
  if (!service.value)
    return
  const newService = { ...service.value }
  newService.uuid = ''
  newService.slug = ''
  service.value = newService
  showEditServiceDataModal.value = true
}

const duplicated = computed(() => {
  return Boolean(!service.value?.uuid)
})

const updated = (resUpdated: Service) => {
  service.value = resUpdated
}

watch(
  () => props.serviceUuid,
  async (value) => {
    if (!value)
      return
    // fetch single service by id
    await fetchSingleService(value)
  },
  { immediate: true },
)

async function fetchSingleService(id = props.serviceUuid as string) {
  processing.value = true
  try {
    const response = await fetchServiceById(`${id}?withStaff=true` as string)
    service.value = response.service
    formData.staff_users = response.staff
  }
  catch (error) {
    console.error('Error fetching service:', error)
  }
  finally {
    processing.value = false
  }
}

const options = computed(() => {
  console.log('service?.full_link', service)

  return [
    {
      name: t('settings.bookingpage.facebook'),
      id: 'order_summary',
      icon: FacebookIcon,
      action: () => {
        window.open(
          `https://www.facebook.com/sharer/sharer.php?u=${service?.value?.full_link}`,
          '_blank',
        )
      },
    },
    {
      name: t('settings.bookingpage.linkedin'),
      id: 'facebook',
      icon: LinkedInIcon,
      action: () => {
        window.open(
          `https://www.linkedin.com/sharing/share-offsite/?url=${service?.value?.full_link}`,
          '_blank',
        )
      },
    },
    {
      name: t('settings.bookingpage.x'),
      id: 'x',
      icon: XIcon,
      action: () => {
        window.open(
          `https://x.com/intent/tweet?text=&url=${service?.value?.full_link}`,
          '_blank',
        )
      },
    },
    {
      name: t('settings.bookingpage.whatsapp'),
      id: 'whatsapp',
      icon: WhatsappIcon,
      action: () => {
        window.open(
          `https://api.whatsapp.com/send?text=%0D%0A${service?.value?.full_link}`,
          '_blank',
        )
      },
    },
    {
      name: t('CopyLink'),
      id: 'CopyLink',
      icon: CopyLinkIcon,
      action: () => {
        navigator.clipboard.writeText(service?.value?.full_link)
      },
    },
  ]
})

const openEditDescriptionModal = () => {
  openAssignModal.value = true
}

const openEditServiceDataModal = () => {
  showEditServiceDataModal.value = true
  service.value.uuid = props.serviceUuid
  service.value.slug = serviceSlug.value
}

const closeEditServiceDataModal = async () => {
  showEditServiceDataModal.value = false
  await fetchSingleService(props.serviceUuid)
}

const closeDeleteModal = () => {
  showConfModal.value = false
  closeModal()
  emit('refresh')
}

const showEditServiceInfoModal = ref(false)

const openEditServiceInfoModal = () => {
  showEditServiceInfoModal.value = true
}
</script>

<template>
  <FullScreenModal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="service?.name"
    :icon="service?.imageLink || ServicesIcon"
    panel-classes="w-full  bg-white rounded-xl shadow-[0px_8px_8px_-4px_rgba(10,13,18,0.04)] shadow-[0px_20px_24px_-4px_rgba(10,13,18,0.10)] flex flex-col overflow-hidden sm:w-full sm:mx-20 h-full my-20"
    @close="closeModal"
  >
    <confirmation-modal
      v-if="showConfModal"
      :dir="getLocale(locale)?.direction"
      :is-open="showConfModal"
      redirect-url="/management/services"
      :api-call="removeService"
      :record-id="props.serviceUuid"
      @closed="closeDeleteModal"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.msg") }}
      </p>
    </confirmation-modal>

    <!-- Buttons Row -->
    <div
      class="flex flex-col gap-4 justify-between items-start w-full sm:flex-row sm:items-center"
    >
      <div
        class="flex flex-wrap gap-3 items-center mb-4 rtl:mr-auto ltr:ml-auto"
      >
        <IconOnlyBtn :icon="RepeatIcon" @click="openDuplicateModal" />

        <div class="flex font-bold text-white rounded-lg">
          <Listbox as="div" class="relative w-full">
            <div>
              <ListboxButton>
                <IconOnlyBtn
                  :icon="ShareIcon"
                  class="outline-primary-800 outline-2 outline-offset-[-1px]"
                  @click="() => {}"
                />
              </ListboxButton>
              <transition
                enter-active-class="transition duration-100 ease-out"
                enter-from-class="opacity-0 transform scale-95"
                enter-to-class="opacity-100 transform scale-100"
                leave-active-class="transition duration-75 ease-out"
                leave-from-class="opacity-100 transform scale-100"
                leave-to-class="opacity-0 transform scale-95"
              >
                <ListboxOptions
                  class="overflow-auto absolute z-10 py-1 mt-2 w-60 max-h-80 text-base bg-white rounded-lg ring-1 ring-black ring-opacity-5 shadow-lg focus:outline-none sm:text-sm"
                  :style="
                    getLocale(locale)?.direction === 'ltr'
                      ? 'right: 0;'
                      : 'left: 0;'
                  "
                >
                  <ListboxOption
                    v-for="option in options"
                    :key="option.id"
                    v-slot="{ active }"
                    as="template"
                    :value="option"
                    @click="option.action"
                  >
                    <li
                      class="relative cursor-pointer select-none py-2.5 px-3" :class="[
                        active ? 'bg-gray-100 text-gray-900' : 'text-gray-900',
                      ]"
                    >
                      <div class="flex gap-3 justify-start items-center">
                        <component
                          :is="option.icon"
                          v-if="option.icon && typeof option.icon !== 'string'"
                          class="block w-5 h-5"
                        />
                        <span
                          class="text-sm" :class="[
                            selected ? 'font-semibold' : 'font-normal',
                          ]"
                        >
                          {{ option.name }}
                        </span>
                      </div>
                      <span
                        v-if="selected"
                        class="relative inset-y-0 right-0 flex items-center pr-4" :class="[
                          active ? 'text-gray-900' : 'text-indigo-600',
                        ]"
                      >
                        <CheckIcon class="w-5 h-5" aria-hidden="true" />
                      </span>
                    </li>
                  </ListboxOption>
                </ListboxOptions>
              </transition>
            </div>
          </Listbox>
        </div>

        <IconOnlyBtn
          :icon="TrashIcon"
          class="text-rose-500 outline-rose-200"
          @click="openDeleteModal"
        />
      </div>
    </div>

    <overlay-loader v-if="processing" :full-screen="false" />
    <div v-else class="flex flex-col gap-6 p-4 pb-6">
      <!-- Service Info Card -->
      <div class="grid gap-6 w-full bg-gray-50 rounded-xl">
        <ServiceInfoCard
          :service="service"
          :user-info="userInfo"
          @edit="openEditServiceInfoModal"
        />
      </div>
      <!-- Staff Table -->
      <div class="flex relative flex-col w-full text-black rounded-lg text-md">
        <ServiceStaffTable
          :processing="processing"
          :items="service?.staff"
          :service="service"
          :service-uuid="service?.uuid"
          @refresh="fetchSingleService"
        />
      </div>
      <!-- Description -->
      <div class="flex relative flex-col w-full text-black rounded-lg text-md">
        <ServiceDescTable
          :service="service"
          field="description"
          :title="$t('fields.description')"
          @edit="openEditDescriptionModal"
        />
      </div>
      <!-- Images -->
      <div class="flex relative flex-col w-full text-black rounded-lg text-md">
        <ServiceImageTable
          :processing="processing"
          :images="service?.images"
          :service-uuid="service?.uuid"
          :service="service"
          @refresh="fetchSingleService"
        />
      </div>
      <!-- Modals -->
      <EditServiceInfoModal
        v-if="showEditServiceInfoModal"
        :is-open="showEditServiceInfoModal"
        :service-uuid="service?.uuid"
        :service="service"
        @close="showEditServiceInfoModal = false"
        @refresh="fetchSingleService"
      />
      <EditDescServiceModal
        v-if="openAssignModal"
        :is-open="openAssignModal"
        :service-uuid="service?.uuid"
        :service="service"
        @close="openAssignModal = false"
        @refresh="fetchSingleService"
      />
      <add-services-modal
        v-if="showEditServiceDataModal"
        :service="service"
        :title="
          duplicated
            ? $t('modalHeader.duplicateService')
            : $t('modalHeader.editService')
        "
        :show-modal="showEditServiceDataModal"
        @refresh="fetchSingleService"
        @updated="updated"
        @closed="closeEditServiceDataModal"
      />
    </div>
  </FullScreenModal>
</template>

<style scoped>
form > div:not(:last-child) {
  flex: 1 0 48%;
}

form > div.full {
  flex: 1 0 100%;
}

@media (max-width: 768px) {
  form > div:not(:last-child) {
    flex: 1 0 100% !important;
  }
}
</style>
