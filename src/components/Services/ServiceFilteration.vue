<script setup lang="ts">
import { storeToRefs } from 'pinia'
import Multiselect from 'vue-multiselect'
import type { PropType } from 'vue'
import { useTagStore } from '@/stores/tags'
import 'vue-multiselect/dist/vue-multiselect.css'
import type { Tag } from '@/types'

const props = defineProps({
  tags: {
    type: Array as PropType<Tag[]>,
    default: () => [],
  },
})
const emit = defineEmits(['changed'])
const tagStore = useTagStore()
// const { tags } = storeToRefs(tagStore)
const { fetchCategories } = useCategoryStore()

const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const serviceName = ref('')
const categoryName = ref('')
const team = ref('')

const tagsList = ref([])
const teamDebounced = debouncedRef(team, 500)
const name = debouncedRef(serviceName, 500)
const categoryDebounced = debouncedRef(categoryName, 500)
const tagsSelect = debouncedRef(tagsList, 500)
interface Payload {
  name: string
  category: string
  tags: string[]
  team: string
}

watch([name, categoryDebounced, tagsSelect, teamDebounced], (val) => {
  if (teamDebounced.value !== teamDebounced.oldValue) {
    fetchCategories('service', teamDebounced.value).then(({ data }) => {
      categories.value = data
    })
  }
  // if(categoryDebounced.value === categoryDebounced.oldValue) val[1] =''
  const [service, category, tags, team] = val
  if (!service && category && !tags.length && !team)
    return

  const payload: Payload = {
    name,
    category,
    team,
    tags,
  }
  emit('changed', payload)
})
const resetCatalog = function () {
  categoryName.value = ''
}
const categories = ref([])
onMounted(() => {
})
</script>

<template>
  <div class="mt-4">
    <div class="grid grid-cols-1 mt-6 gap-y-6 gap-x-4 md:grid-cols-4">
      <div>
        <div>
          <SearchInput
            id="customer-name"
            v-model="serviceName"
            :label="$t('booking.service')"
            :placeholder="$t('booking.service')"
            custom-classes="border-gray-300 focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          />
        </div>
      </div>
      <filter-with-team v-model="team" @change="resetCatalog()" />
      <div class="relative flex flex-col items-stretch flex-grow focus-within:z-10">
        <SelectInput
          id="category"
          v-model="categoryName"
          :label="$t('form.category')"
          class="block w-fulltext-gray-700 border-gray-300 rounded-md"
        >
          <option value="">
            {{ $t("form.all") }}
          </option>
          <option v-for="category of categories" :key="category.uuid" :value="category.uuid">
            {{ category.name }}
          </option>
        </SelectInput>
      </div>
      <div class="w-full">
        <MultiSelectInput
          v-model="tagsList"
          :label="$t('form.tags')"
          track-by="id"
          :clear-on-select="false"
          :close-on-select="false"
          :options="tags"
          :placeholder="$t('tags.selectOption')"
          :select-label="$t('tags.selectLabel')"
          :selected-label="$t('tags.selectedLabel')"
          :deselect-label="$t('tags.deselectLabel')"
        />
      </div>
    </div>
  </div>
</template>
