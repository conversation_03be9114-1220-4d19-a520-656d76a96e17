<script setup lang="ts">
import type { PropType } from '@vue/runtime-core'
import { PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { Switch } from '@headlessui/vue'
import Multiselect from 'vue-multiselect'
import { minLength, required } from '@/utils/i18n-validators'
import type { Service, Tag } from '@/types'
import 'vue-multiselect/dist/vue-multiselect.css'
import { get } from 'lodash'
import TextInput from '../FormControls/Inputs/TextInput.vue'
import TextareaInput from '../FormControls/Inputs/TextareaInput.vue'

const props = defineProps({
  service: {
    type: Object as PropType<Service>,
    default: null,
  },
  tags: {
    type: Array as PropType<Tag[]>,
    default: () => [],
  },
})

const emit = defineEmits(['created', 'updated', 'deleted'])
const diplayForm = useServicesStore()
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const formData = ref({
  name: '',
  imageLink: '',
  description: '',
  color: '#000000',
  duration: 0,
  duration_minutes: 0,
  duration_hours: 0,
  price: 0,
  deposit: 0,
  category_id: '',
  image: null,
  display_on_booking_page: false,
  location: 'on-site',
  tags: [],
})

const state = reactive({
  processing: false,
})

const { service } = toRefs(props)

watch(service, (value: Service) => {
  formData.value.name = value?.name || ''
  formData.value.category_id = value?.category_id || ''
  formData.value.imageLink = value?.imageLink || ''
  formData.value.description = value?.description || ''
  formData.value.color = value?.color || '#000000'
  formData.value.duration = value?.duration || 0
  formData.value.price = value?.price || 0
  formData.value.deposit = value?.deposit || 0
  formData.value.display_on_booking_page = value?.display_on_booking_page === 1
  formData.value.location = value?.location || 'on-site'
  formData.value.tags = value?.tags || []
  formData.value.image = ''
})

const { createService, updateService, removeService } = useServicesStore()
const errHandle = reactive({
  name: [],
  category_id: [],
  duration: [],
  image: [],
})
const rules = {
  name: {
    required,
    minLength: minLength(3),
  },
  color: {
    required,
  },
  price: {
    required,
  },
  duration: {
    required,
  },
}
const v$ = useVuelidate(rules, formData.value)
const create = async () => {
  for (const prop in errHandle)
    errHandle[prop] = []
  v$.value.$touch()
  if (state.processing || v$.value.$invalid)
    return
  state.processing = true
  if (service.value?.uuid) {
    if (formData.value.image === '' || formData.value.image === null)
      delete formData.value.image

    formData.value.display_on_booking_page = formData.value.display_on_booking_page ? 1 : 0

    const payload = { ...formData.value, tags: formData.value.tags!.map(tag => tag.id).join(',') }

    return updateService(service.value.uuid, payload)
      .then((res) => {
        emit('updated', res.data)
        diplayForm.showForm = false
      }).catch((e) => {
        if (e.errors) {
          for (const prop in e.errors)
            errHandle[prop] = e.errors[prop]
        }
      })
      .finally(() => {
        state.processing = false
      })
  }
}

const deleteService = () => {
  if (!service.value?.uuid || state.processing)
    return
  state.processing = true
  removeService(service.value.uuid)
    .then((res) => {
      emit('deleted', service.value)
      diplayForm.showForm = true
    })
    .finally(() => {
      state.processing = false
    })
}

const { fetchCategories, createCategory } = useCategoryStore()

const categories = ref([])

const showAddCategoryForm = ref(false)

const categoryData = reactive({
  name: '',
  // type: 'service',
  team_id:getTeam.value[0].uuid,
  processing: false
})
const { getUserInfo } = storeToRefs(useAuthStore());
const getTeam = computed(() => {
  return getUserInfo.value.teams;
});


const createNewCategory = async () => {
  categoryData.processing = true
  createCategory({ name: categoryData.name, type: team_id: categoryData.team_id })
    .then(({ data }) => {
      categories.value = [...categories.value, data]
      formData.value.category_id = data.uuid
    })
    .finally(() => {
      categoryData.name = ''
      categoryData.type = 'service'
      categoryData.team_id = getTeam.value[0].uuid
      categoryData.processing = false
    })
}

onMounted(() => {
  fetchCategories().then(({ data }) => {
    categories.value = data
  })
})
</script>

<template>
  <div>
    <modal :open="showAddCategoryForm" title="category" :dir="getLocale(locale)?.direction" @close="showAddCategoryForm = false">
      <form
        :class="[
          getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
        ]" @submit.prevent="createNewCategory"
      >
        <div class="grid grid-cols-6 gap-6">
          <div class="col-span-3">
            <TextInput
              :label="$t('form.name')"
              :placeholder="$t('form.name')"
              id="category-name"
              v-model="categoryData.name"
              required
            />
          </div>
          <div class="col-span-3">
            <label for="team_id" class="mb-2 w-full flex items-center text-start text-[#261E27] text-base">{{
              $t("modalPlacholder.branch")
            }}</label>
            <div class="">
              <form-group :validation="v1$" name="team_id">
                <template #default="{ attrs }">
                  <select v-bind="attrs" id="team_id" v-model="categoryData.team_id"
                    class="w-full py-3 text-gray-700 border-gray-300 rounded-md">
                    <option hidden selected value="">
                      {{ $t("form.select") }}
                    </option>
                    <option v-for="team in getTeam" :key="team?.uuid" :value="team?.uuid">
                      {{ team.name }}
                    </option>
                  </select>
                </template>
              </form-group>
            </div>
          </div>
        </div>
        <div
          class="mt-5 sm:mt-6"
        >
          <BaseButton
            type="submit"
            show-icon
            class="w-full d-block hover:bg-green-700"
            custome-bg="bg-green-600"
            :processing="categoryData.processing"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </form>
    </modal>

    <form class="w-full mb-4 lg:basis-1/4" @submit.prevent="create">
      <div class="w-full mb-5">
        <TextInput
          :label="$t('form.name')"
          :placeholder="$t('form.name')"
          id="service-name"
          v-model="formData.name"
        />
        <div v-if="errHandle.name.length > 0">
          <ErrMsg v-for="err in errHandle.name" :key="err" :msg="err" />
        </div>
        <p v-for="error of v$.name.$errors" :key="error.$uid" class="error-message">
          {{ $t(error.$message) }}
        </p>
      </div>
      <div class="w-full mb-6">
        <TextareaInput
          :label="$t('description')"
          :placeholder="$t('description')"
          id="service-description"
          v-model="formData.description"
        />
      </div>
      <div class="grid grid-cols-1 gap-5 lg:grid-cols-2">
        <div>
          <label
            class="block mb-2 text-xs font-bold tracking-wide text-gray-700  "
            for="service-color"
          >
            {{ $t("color") }}<span class="text-red-600">*</span>
          </label>
          <div class="relative">
            <input
              v-model="formData.color"
              type="text"
              placeholder="#000000"

              class="block w-full px-4 py-3 rounded-md   border-blue-gray-300 text-blue-gray-900 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            >
            <input
              v-model="formData.color"
              type="color"

              class="absolute transform -translate-y-1/2 bg-transparent border-none rounded-full appearance-none cursor-pointer top-1/2 w-7 h-7 end-2"
            >
            <p v-for="error of v$.color.$errors" :key="error.$uid" class="error-message">
              {{ $t(error.$message) }}
            </p>
          </div>
        </div>

        <div>
          <label
            class="block mb-2 text-xs font-bold tracking-wide text-gray-700  "
            for="service-price"
          >
            {{ $t("price") }}<span class="text-red-600">*</span>
          </label>
          <input
            id="service-price"
            v-model="formData.price"
            class="block w-full py-3 leading-tight text-gray-700 rounded appearance-none ps-4 pe-4 focus:outline-none focus:bg-white"
            type="number"
            step="0.01"
            min="0"
            :placeholder="$t('price')"
          >
          <p v-for="error of v$.price.$errors" :key="error.$uid" class="error-message">
            {{ $t(error.$message) }}
          </p>
        </div>

        <div>
          <label
            class="block mb-2 text-xs font-bold tracking-wide text-gray-700  "
            for="service-deposit"
          >
            {{ $t("deposit") }}
          </label>
          <input
            id="service-deposit"
            v-model="formData.deposit"
            class="block w-full py-3 leading-tight text-gray-700 rounded appearance-none ps-4 pe-4 focus:outline-none focus:bg-white"
            type="number"
            step="0.01"
            min="0"
            :placeholder="$t('deposit')"
          >
          <p v-for="error of v$.deposit.$errors" :key="error.$uid" class="error-message">
            {{ $t(error.$message) }}
          </p>
        </div>
        <div>
          <label
            for="category"
            class="block mb-2 text-xs font-bold tracking-wide text-gray-700  "
          >
            {{ $t("form.category") }}<span class="text-red-600">*</span>
          </label>
          <div class="flex mt-1 rounded-md  ">
            <div class="relative flex items-stretch flex-grow focus-within:z-10">
              <select
                id="category"
                v-model="formData.category_id"
                class="block w-full text-gray-700 rounded-none focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="">
                  {{ $t('form.select') }}
                </option>
                <option
                  v-for="category of categories"
                  :key="category.uuid"
                  :value="category.uuid"
                >
                  {{ category.name }}
                </option>
              </select>
            </div>
            <button
              type="button"
              class="relative inline-flex items-center px-4 py-2 -ml-px space-x-2 text-sm font-medium text-gray-700 border border-gray-400 bg-gray-50 hover:bg-gray-100 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
              :class="[getLocale(locale)?.direction === 'rtl' ? 'rounded-l-md' : 'rounded-r-md']"
              @click="showAddCategoryForm = true"
            >
              <PlusIcon class="w-5 h-5 text-gray-400" aria-hidden="true" />
              <span>{{ $t("New") }}</span>
            </button>
          </div>
          <div v-if="errHandle.category_id.length > 0">
            <ErrMsg v-for="err in errHandle.category_id" :key="err" :msg="err" />
          </div>
        </div>

        <div class="flex flex-col">
          <label
            class="block mb-2 text-xs font-bold tracking-wide text-gray-700   "
          >
            {{ $t("displayOnBookingPage") }}<span class="text-red-600">*</span>
          </label>
          <Switch
            v-model="formData.display_on_booking_page"
            class="relative inline-flex flex-shrink-0 h-6 transition-colors duration-200 ease-in-out border-2 border-transparent rounded-full cursor-pointer w-11 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            :class="[formData.display_on_booking_page ? 'bg-primary-600' : 'bg-gray-200']"
          >
            <span
              aria-hidden="true"
              class="inline-block w-5 h-5 transition duration-200 ease-in-out transform bg-white rounded-full shadow pointer-events-none ring-0"
              :class="[
                formData.display_on_booking_page
                  ? 'translate-x-5 rtl:-translate-x-5'
                  : 'translate-x-0',
              ]"
            />
          </Switch>
        </div>
      </div>

      <div class="w-full mt-2 mb-6">
        <label for="image" class="block mb-2 text-xs font-bold tracking-wide text-gray-700  ">{{ $t('form.tags')
        }}</label>
        <Multiselect
          v-model="formData.tags"
          :multiple="true"
          track-by="id"
          :clear-on-select="false"
          :close-on-select="false"
          label="name"
          :options="tags"
          :placeholder="$t('tags.selectOption')"
          :select-label="$t('tags.selectLabel')"
          :selected-label="$t('tags.selectedLabel')"
          :deselect-label="$t('tags.deselectLabel')"
        />
      </div>

      <div class="w-full my-5">
        <label
          class="block mb-2 text-xs font-bold tracking-wide text-gray-700  "
          for="service-description"
        >
          {{ $t("form.image") }}
        </label>
        <ImageInput v-model="formData.image" :link="service?.imageLink" />

        <span class="text-sm">(jpg, png, jpeg, 150X150, 2MB max)</span>
        <div v-if="errHandle.image.length > 0">
          <ErrMsg v-for="err in errHandle.image" :key="err" :msg="err" />
        </div>
      </div>

      <div class="flex justify-between w-full ms-auto sm:w-1/2">
        <BaseButton
          v-if="service"
          class="inline-flex hover:bg-red-700 disabled:bg-red-300 me-2"
          :processing="state.processing"
          custome-bg="bg-red-600"
          @click="deleteService"
        >
          {{ $t("form.delete") }}
          <TrashIcon class="ms-2 -me-0.5 h-4 w-4" aria-hidden="true" />
        </BaseButton>
        <BaseButton class="inline-flex hover:bg-gray-800" show-icon :processing="state.processing" custome-bg="bg-gray-700">
          {{ $t("form.update") }}
        </BaseButton>
      </div>
    </form>
  </div>
</template>
