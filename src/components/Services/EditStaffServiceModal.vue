<script setup lang="ts">
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { Switch } from '@headlessui/vue'
import type { Service, Staff } from '@/types'
import SwitchInput from '@/components/FormControls/Inputs/SwitchInput.vue'
import MultiSelectInput from '@/components/FormControls/Inputs/MultiSelectInput.vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  service: {
    type: Object as PropType<Service>,
    default: () => ({}),
    required: true,
  },
  teamStaff: {
    type: Array as PropType<Staff[]>,
    default: () => [],
  },
})
const emits = defineEmits(['close', 'updated', 'refresh'])
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const { updateServicesStaff } = useServicesStore()

const processing = ref(false)
const { fetchAllStaff } = useStaffStore()

const formData = reactive<Omit<Service, 'uuid'>>({
  team_id: '',
  is_public: false,
  staff_users: [],
  display_on_booking_page: true,
})

watch(() => props.service, (value: Service) => {
  if (!value)
    return
  console.log('watch service', value)

  formData.team_id = value.team_id || ''
  formData.is_public = value.is_public || false
  formData.staff_users = props.teamStaff
  formData.display_on_booking_page = value.display_on_booking_page || true
}, { immediate: true })

async function updateServiceStaff() {
  processing.value = true
  try {
    const staffData: Record<string, any> = {
      is_public: formData.is_public ? 1 : 0,
      display_on_booking_page: formData.display_on_booking_page ? 1 : 0,
    }

    if (!formData.is_public) {
      staffData.staff_id = Array.isArray(formData.staff_users)
        ? formData.staff_users.map((user: Staff) => user.id).join(',')
        : ''
    }

    await updateServicesStaff(props.service, staffData)
    emits('refresh')
    emits('close')
  }
  finally {
    processing.value = false
  }
}

const staffList = ref<Staff[]>([])
// fetch staffs by team id
const fetchStaffList = async () => {
  try {
    const response = await fetchAllStaff()
    staffList.value = response.data?.filter(item => item.branch_id === formData.team_id) || []
  }
  catch (error) {
    console.error('Failed to load staff:', error)
  }
}

onMounted(async () => {
  await fetchStaffList()
})
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="$t('edit_staff_service')"
    panel-classes="w-full max-w-2xl transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all"
    @close="emits('close')"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    <form class="text-start" @submit.prevent="updateServiceStaff()">
      <div class="space-y-4">
        <!-- Staff Selection -->
        <div v-show="!formData.is_public">
          <form-group name="staff_users">
            <template #default="{ attrs }">
              <MultiSelectInput
                v-bind="attrs"
                id="staff_users"
                v-model="formData.staff_users"
                :options="(props.teamStaff || []).filter(item => item.branch_id === formData.team_id)"
                :label="$t('form.staff')"
                track-by="id"
                :placeholder="$t('form.select')"
              />
            </template>
          </form-group>
        </div>

        <!-- Switches -->
        <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
          <SwitchInput
            id="is_public"
            v-model="formData.is_public"
            :label="$t('form.isPublicService')"
          />
        </div>

        <!-- Submit Button -->
        <div class="mt-6">
          <BaseButton
            type="submit"
            class="w-full inline-flex items-center justify-center"
            :processing="processing"
          >
            {{ $t("form.save") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </Modal>
</template>
