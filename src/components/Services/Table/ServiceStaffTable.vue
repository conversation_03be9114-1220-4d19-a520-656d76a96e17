<script lang="ts" setup>
import type { ComputedRef, PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { PlusIcon } from '@heroicons/vue/20/solid'
import type { Service, Staff, header } from '@/types'
import TrashIcon from '@/components/Icons/TrashIcon.vue'
import LilActionBtn from '@/components/Buttons/LilActionBtn.vue'
import EditStaffServiceModal from '@/components/Services/EditStaffServiceModal.vue'
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue'

const props = defineProps({
  items: {
    type: Array as PropType<Staff[]>,
    required: true,
  },
  service: {
    type: Object as PropType<Service>,
    required: true,
  },
  serviceUuid: {
    type: String,
    default: '',
    required: true,
  },
})

const emit = defineEmits(['refresh'])
const { t } = useI18n()
const { removeServiceStaff } = useServicesStore()

const headers: ComputedRef<header[]> = computed(() => [
  { title: '#' },
  { title: t('form.name') },
  { title: t('form.phone') },
  { title: t('form.email') },
  { title: '' },
])

const showAddItemModal = ref(false)
const showConfModal = ref(false)
const selectedItemId = ref('')

const showConfirmationModal = (itemId: string) => {
  selectedItemId.value = itemId
  showConfModal.value = true
}

const itemRemoved = () => {
  showConfModal.value = false
  emit('refresh')
}

const deleteItem = async (itemId: string) => {
  showConfirmationModal(itemId)
}

const deleteStaffFromServiceApi = async (itemId: string) => {
  await removeServiceStaff(props.serviceUuid, itemId)
}
</script>

<template>
  <div>
    <EditStaffServiceModal
      v-if="showAddItemModal"
      :is-open="showAddItemModal"
      :service="service"
      :service-uuid="service?.uuid"
      :team-staff="items"
      @refresh="emit('refresh')"
      @close="showAddItemModal = false"
    />
    <confirmation-modal
      v-if="showConfModal"
      :is-open="showConfModal"
      :api-call="deleteStaffFromServiceApi"
      :record-id="selectedItemId"
      @removed="itemRemoved"
      @closed="showConfModal = false"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.msg") }}
      </p>
    </confirmation-modal>
    <DisclosureWrapper :title="$t('form.staff_users')">
      <template #header-actions>
        <LilActionBtn
          :icon="PlusIcon"
          @click="showAddItemModal = true"
        />
      </template>
      <generic-table
        :is-loading="false"
        :data="items"
        :headers="headers"
        item-key="uuid"
        tr-class="hover:bg-gray-50"
      >
        <template #row="{ item, index }">
          <grid-td class="w-4 text-gray-500 text-start">
            <p>{{ index + 1 }}</p>
          </grid-td>
          <grid-td class="text-start">
            <p>{{ item?.name || '-' }}</p>
          </grid-td>
          <grid-td class="text-start">
            <p>{{ item?.phone || '-' }}</p>
          </grid-td>
          <grid-td class="text-start">
            <p>{{ item?.email || '-' }}</p>
          </grid-td>
          <grid-td class="text-end">
            <button
              class="flex gap-2 items-center p-2.5 text-rose-500 rounded-lg cursor-pointer sm:text-sm"
              @click.stop="deleteItem(item?.id)"
            >
              <TrashIcon class="block w-5 h-5" alt="Delete" />
            </button>
          </grid-td>
        </template>
      </generic-table>
    </DisclosureWrapper>
  </div>
</template>
