<script lang="ts" setup>
import type { ComputedRef, PropType } from 'vue'
import { computed, ref } from 'vue'
import PlusIcon from '@heroicons/vue/20/solid/PlusIcon'
import type { Service, header } from '@/types'
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue'
import LilActionBtn from '@/components/Buttons/LilActionBtn.vue'
import TrashIcon from '@/components/Icons/TrashIcon.vue'
import MarkIcon from '@/components/Icons/MarkIcon.vue'
import AddImageServiceModal from '@/components/Services/AddImageServiceModal.vue'

const props = defineProps({
  images: {
    type: Array,
    required: true,
  },
  serviceUuid: {
    type: String,
    default: '',
    required: true,
  },
  service: {
    type: Object as PropType<Service>,
    default: () => ({}),
    required: true,
  },
})

const emit = defineEmits(['refresh'])

const { t } = useI18n()

const headers: ComputedRef<header[]> = computed(() => [
  { title: '#' },
  { title: t('form.image') },
  { title: '' },
])

const showAddItemModal = ref(false)
const { removeImageFromService, makeMainImage } = useServicesStore()

const showConfModal = ref(false)
const selectedItemId = ref('')

const showConfirmationModal = (itemId: string) => {
  selectedItemId.value = itemId
  showConfModal.value = true
}

const deleteItem = async (itemId: string) => {
  showConfirmationModal(itemId)
}
const deleteImageFromServiceApi = async (itemId: string) => {
  await removeImageFromService(props.serviceUuid, itemId)
}
const itemRemoved = () => {
  showConfModal.value = false
  emit('refresh')
}
const makeMainImageItem = async (itemId: string) => {
  await makeMainImage(props.serviceUuid, itemId)
  emit('refresh')
}
</script>

<template>
  <div>
    <AddImageServiceModal
      v-if="showAddItemModal"
      :is-open="showAddItemModal"
      :service="service"
      :service-uuid="service?.uuid"
      @refresh="emit('refresh')"
      @close="showAddItemModal = false"
    />
    <confirmation-modal
      v-if="showConfModal"
      :is-open="showConfModal"
      :api-call="deleteImageFromServiceApi"
      :record-id="selectedItemId"
      @removed="itemRemoved"
      @closed="showConfModal = false"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.msg") }}
      </p>
    </confirmation-modal>
    <DisclosureWrapper :title="$t('images')">
      <template #header-actions>
        <LilActionBtn
          :icon="PlusIcon"
          @click="showAddItemModal = true"
        />
      </template>
      <generic-table
        :is-loading="false"
        :data="images"
        :headers="headers"
        item-key="uuid"
        tr-class="hover:bg-gray-50"
      >
        <template #row="{ item, index }">
          <grid-td class="text-start w-4 text-gray-500">
            <p>{{ index + 1 }}</p>
          </grid-td>
          <grid-td class="text-start">
            <img
              id="image"
              class="object-fill mx-2 w-7 h-7 rounded-full"
              :src="item.image"
              :link="null"
            >
          </grid-td>
          <grid-td class="text-end">
            <div class="flex gap-2 justify-end">
              <button
                v-if="!item.is_main_image"
                class="p-2.5 rounded-lg text-rose-500 flex items-center gap-2 cursor-pointer sm:text-sm"
                @click.stop="makeMainImageItem(item.id)"
              >
                <MarkIcon class="block w-5 h-5" alt="Main" />
              </button>
              <button
                class="p-2.5 rounded-lg text-rose-500 flex items-center gap-2 cursor-pointer sm:text-sm"
                @click.stop="deleteItem(item.id)"
              >
                <TrashIcon class="block w-5 h-5" alt="Delete" />
              </button>
            </div>
          </grid-td>
        </template>
      </generic-table>
    </DisclosureWrapper>
  </div>
</template>
