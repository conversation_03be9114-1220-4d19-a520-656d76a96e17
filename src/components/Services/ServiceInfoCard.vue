<script setup lang="ts">
import type { PropType } from 'vue'
import type { Service } from '@/types'
import { convertToHoursAndMinutes } from '@/utils/time'
import EditIcon from '@/components/Icons/EditIcon.vue'

const props = defineProps({
  service: {
    type: Object as PropType<Service>,
    required: true,
  },
  userInfo: {
    type: Object as PropType<any>,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['edit'])
</script>

<template>
  <div class="grid gap-6 p-4 w-full bg-gray-50 rounded-xl">
    <div class="bg-white rounded-lg shadow-sm p-4 w-full   ">
      <div class="flex justify-between items-center mb-2">
        <span class="text-lg font-bold text-neutral-900">{{ title || $t('modalHeader.data_service') }}</span>
        <slot name="edit">
          <LilActionBtn
            :icon="EditIcon"
            :label="$t('form.edit')"
            @click="$emit('edit')"
          />
        </slot>
      </div>
      <div class="flex flex-row gap-x-8 flex-wrap gap-2 ">
        <div class="flex flex-col gap-2 flex-1 min-w-[180px]">
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('price') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ service?.price }} <span class="text-gray-500">{{ $t(`currenices.${userInfo.tenant?.currency}`) }}</span></span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('displayOnBookingPage') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ service?.display_on_booking_page ? $t('yes') : $t('no') }}</span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('form.sort_order') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ service?.sort_order }}</span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('modalPlacholder.branch') }}:</span>
            <span class="text-neutral-800 font-normal text-start">
              <span v-if="service?.team && typeof service.team === 'object'">{{ service?.team?.name }}</span>
              <span v-else>-</span>
            </span>
          </div>
        </div>
        <div class="flex flex-col gap-2 flex-1 min-w-[180px]">
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('form.category') }}:</span>
            <span class="text-neutral-800 font-normal text-start">
              <span v-if="service?.category_id && typeof service.category_id === 'object'">{{ service?.category_id?.name }}</span>
              <span v-else class="text-neutral-400">-</span>
            </span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('duration') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ convertToHoursAndMinutes(service?.duration || 0, true) }}</span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('color') }}:</span>
            <span class="text-neutral-800 font-normal text-start">
              <span class="inline-block w-6 h-4 rounded" :style="{ backgroundColor: service?.color }" />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
