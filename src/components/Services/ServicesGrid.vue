<script setup lang="ts">
import { PhotoIcon } from '@heroicons/vue/24/outline'
import type { ComputedRef, PropType } from 'vue'
import type { Service } from '@/types/service'
import type { header } from '@/types'
const props = defineProps({
  serviceList: {
    type: Array as PropType<Service[]>,
    default: () => [],
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['openShowDetailsModal'])
const { t } = useI18n()
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.name'),
    },
    {
      title: t('form.category'),
    },
    {
      title: t('form.sort_order'),
    },
    {
      title: t('modalPlacholder.branch'),
    },

  ]
})
const router = useRouter()
const redirectToService = (item: Service) => {
  if (router.currentRoute.value.name == 'packages')
    router.push({ name: 'package', params: { id: item.uuid } })
  else
  // router.push({ name: "service", params: { id: item.uuid } });

    emit('openShowDetailsModal', item.uuid)
}
</script>

<template>
  <generic-table
    :data="serviceList"
    :headers="headers"
    :is-loading="isLoading"
    item-key="uuid"
    :on-row-click="redirectToService"
    tr-class="cursor-pointer"
  >
    <template #row="{ item }">
      <td
        class="flex gap-1 items-center py-2 text-sm whitespace-nowrap sm:pl-4 ps-2 group"
      >
        <div>
          <PhotoIcon
            v-if="item?.imageLink === null"
            class="object-fill mx-2 w-7 h-7 text-gray-600"
          />
          <img
            v-else
            id="image"
            class="object-fill mx-2 w-7 h-7 rounded-full"
            :src="item.imageLink"
            :link="null"
          >
        </div>
        <span>

          <span class="flex gap-2 justify-center items-center">
            {{ item?.name }} <span v-if="item.display_on_booking_page" class="flex gap-2 justify-center items-center font-semibold text-green-500" style="font-size: 35px;">&#8226;
              <i class="opacity-0 group-hover:opacity-100 text-primary-500" style="font-size:10px;">{{ $t('displayed_store') }}</i>
            </span>
            <span v-else class="flex gap-2 items-center font-semibold text-gray-500" style="font-size: 35px;">&#8226;
              <i class="opacity-0 group-hover:opacity-100 text-primary-500" style="font-size:10px;">{{ $t('not_displayed_store') }}</i>
            </span>
          </span>

        </span>
      </td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.category_name">
            {{ item?.category_name }}
          </span>
          <span v-else class="text-zinc-400"> # </span>
        </div>
      </grid-td>
      <grid-td>
        <span class="justify-center">
          <span>
            {{ item?.sort_order }}
          </span>
        </span>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.team?.uuid">
            {{ item?.team.name }}
          </span>
          <span v-else class="text-zinc-400"> - </span>
        </div>
      </grid-td>
    </template>
  </generic-table>
</template>
