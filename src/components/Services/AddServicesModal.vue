<script setup lang="ts">
import type { PropType } from 'vue'
import { Switch } from '@headlessui/vue'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import { ArrowLeftIcon, PlusIcon } from '@heroicons/vue/24/outline'
import useVuelidate from '@vuelidate/core'
import Multiselect from 'vue-multiselect'
import 'vue-multiselect/dist/vue-multiselect.css'
import { storeToRefs } from 'pinia'
import { toRaw } from 'vue'
import { convertToHoursAndMinutes } from '../../utils/time'
import { minLength, required, requiredIf } from '@/utils/i18n-validators'
import type { HoursMinutes, Service, Staff } from '@/types'
import ServicesIcon from '@/components/Icons/ServicesIcon.vue'
import MultiSelectInput from '@/components/FormControls/Inputs/MultiSelectInput.vue'
import { convertPyloadToFormdata } from '@/utils'
const props = defineProps({
  service: {
    type: Object as PropType<Service>,
    default: null,
  },
  showModal: {
    type: Boolean,
    default: false,
  },
  random: {
    type: String,
    default: '#000000',
  },
  teamStaff: {
    type: Array as PropType<Staff[]>,
    default: () => [],
  },

  title: {
    type: String,
    default: 'Add & Edit service',
  },
  subtitle: {
    type: String,
  },
  closeModal: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['created', 'closed', 'updated'])
const { locale, t } = useI18n()
const { fetchCategories, createCategory } = useCategoryStore()
const { getLocale } = storeToRefs(useLocalesStore())
const { createService, updateService } = useServicesStore()
const { userInfo } = useAuthStore()
const { getUserInfo } = storeToRefs(useAuthStore())
const tagStore = useTagStore()
const { tags: tagsList } = storeToRefs(tagStore)
const { fetchTags } = useTagStore()
const processing = ref(false)
const processing2 = ref(false)
const buttonAction = ref(false)

const getTeam = computed(() => {
  return getUserInfo.value.teams
})
const { service } = toRefs(props)

const default_duration: HoursMinutes = {
  hours: 0,
  minutes: 15,
};
const default_price = 0;
const formData = reactive<Omit<Service, "uuid"> & { staff_users: (string | number)[] }>({
  name_localized: { ar: "", en: "" },
  description_localized: { ar: "", en: "" },
  duration_time: 15,
  is_public: true,
  staff_users: [],
  color: props.random,
  price: default_price,
  sort_order: 0,
  category_id: '',
  image: null,
  display_on_booking_page: true,
  location: 'on-site',
  tags: [],
  team_id: '',
  base_link: '',
  images: [],
})

watch(
  service,
  (value: Service) => {
    if (!value)
      return
    const {
      duration,
      name_localized,
      description_localized,
      category_id,
      imageLink,
      color,
      price,
      sort_order,
      team_id,
      is_public,
      display_on_booking_page,
      staff,
      base_link,
    } = value
    formData.name_localized = name_localized || { ar: '', en: '' }

    formData.description_localized = {
      ar: description_localized?.ar || '',
      en: description_localized?.en || '',
    }
    formData.category_id = category_id?.uuid || ''
    formData.imageLink = imageLink || ''
    formData.staff_users = staff || []
    formData.color = color || '#000000'
    formData.display_on_booking_page = display_on_booking_page
    formData.is_public = is_public
    formData.sort_order = sort_order
    formData.base_link = base_link
    formData.duration_time = duration || 15
    formData.price = price || default_price
    formData.image = ''
    formData.team_id = team_id || ''
  },
  { immediate: true, deep: true },
)

watch(
  () => formData.team_id,
  (val) => {
    formData.category_id = ''
    fetchCategories().then(({ data }) => {
      categories.value = data
    })
  },
)
const closeModal = () => {
  emit('closed')
}

const rules = {
  name_localized: {
    ar: {
      required: requiredIf(() => !formData.name_localized.en.trim()),
      minLength: minLength(3),
    },
    en: {
      required: requiredIf(() => !formData.name_localized.ar.trim()),
      minLength: minLength(3),
    },
  },
  images: {
    required: requiredIf(() => formData.images.length === 0),
  },
  color: {
    required,
  },
  price: {
    required,
  },
  duration_time: {
    required,
  },
  team_id: {
    required,
  },
  category_id: {
    required,
  },
  staff_users: {
    required: requiredIf((val) => {
      return !formData.is_public
    }),
  },
}

const v$ = useVuelidate(rules, formData)
// category modal logic
const categories = ref([])
const showAddCategoryModal = ref(false)
const categoryData = reactive({
  name_localized: { ar: '', en: '' },
  team_id: getTeam.value[0].uuid,
  processing: false,
})
const cateValidation = reactive({
  name_localized: {
    ar: {
      required: requiredIf(() => !categoryData.name_localized.en.trim()),
    },
    en: {
      required: requiredIf(() => !categoryData.name_localized.ar.trim()),
    },
  },
  team_id: {
    required,
  },
})

const v1$ = useVuelidate(cateValidation, categoryData)
const createNewCategory = async () => {
  v1$.value.$touch()
  if (v1$.value.$invalid)
    return false
  categoryData.processing = true
  createCategory({
    name_localized: JSON.stringify(categoryData.name_localized),
    team_id: categoryData.team_id,
    description_localized: JSON.stringify({ ar: '', en: '' }),
  })
    .then(({ data }) => {
      categories.value = [...categories.value, data]
      formData.category_id = data.uuid
    })
    .finally(() => {
      resetCategoryForm()
      showAddCategoryModal.value = false
    })
}

const resetForm = () => {
  formData.name_localized = { ar: '', en: '' }
  formData.category_id = ''
  formData.imageLink = ''
  formData.description_localized = { ar: '', en: '' }
  formData.color = '#000000'
  formData.duration_time = 15
  formData.price = default_price
  formData.is_public = true
  formData.sort_order = 0

  formData.image = ''
  formData.staff_users = [];
  (formData.team_id = getUserInfo.value.teams[0]?.uuid || ''),
  (formData.location = 'on-site')
  formData.display_on_booking_page = true
  v$.value.$reset()
}
const resetCategoryForm = () => {
  categoryData.name_localized = { ar: '', en: '' }
  categoryData.processing = false
  categoryData.team_id = getTeam.value[0].uuid
  v1$.value.$reset()
}

const createRecord = async (payload: Service) => {
  return createService(payload).then((res) => {
    resetForm()
    formData.images = []
    emit('created', res.data)
  })
}
const isPublicService = computed(() => formData.is_public)
const assignedForAll = computed(
  () => formData.staff_users.length === (props.teamStaff || []).length,
)

watch(assignedForAll, (value) => {
  if (value) {
    formData.is_public = true;
    formData.staff_users = props.teamStaff.map(staff => staff.uuid);
  }
})

watch(isPublicService, (value) => {
  if (value) {
    formData.staff_users = props.teamStaff.map(staff => staff.uuid);
  } else {
    formData.staff_users = [];
  }
});

onMounted(() => {
  formData.team_id = getUserInfo.value?.teams[0]?.uuid || ''
  if (formData.team_id) {
    fetchCategories().then(({ data }) => {
      categories.value = data
      formData.category_id = data?.[0]?.uuid || ''
    })
  }
})

const saveService = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return false

  if (buttonAction.value)
    processing2.value = true
  else
    processing.value = true

  try {
    // تجهيز البيانات الأساسية
    const raw = toRaw(formData)
    raw.display_on_booking_page = raw.display_on_booking_page ? 1 : 0

    // إذا لم تتغير الصورة الرئيسية، لا ترسلها
    if (raw.imageLink === service?.value?.imageLink)
      delete raw.image

    // تجهيز مصفوفة صور جديدة فقط (إن وجدت)
    const images = Array.isArray(raw.images)
      ? raw.images.filter((img: any) => img instanceof File)
      : []

    // تجهيز قائمة الحقول التي سيتم إرسالها
    const payload: Record<string, any> = {
      ...raw,
      name_localized: JSON.stringify(raw.name_localized),
      description_localized: JSON.stringify(raw.description_localized),
    }

    // حذف الحقول غير الضرورية
    delete payload.base_link
    delete payload.imageLink
    delete payload.image // نحذفها الآن ونرسلها يدويًا
    delete payload.images

    // إضافة الصور إذا وُجدت (مثلاً images[0], images[1] ...)
    images.forEach((file, index) => {
      payload[`images[${index}]`] = file
    })
    payload.staff_users = formData.staff_users.join(',')
    // طباعة البيانات المرسلة (اختياري)
    console.log('Payload being sent (FormData)')

    // إرسال البيانات
    if (payload.is_public)
      delete payload.staff_users
    await createRecord(payload)

    if (buttonAction.value === true)
      emit('closed')
  }
  catch (error) {
    console.error('خطأ أثناء حفظ الخدمة:', error)
  }
  finally {
    processing.value = false
    processing2.value = false
  }
}
</script>

<template>
  <FullScreenModal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="title"
    :subtitle="subtitle"
    :icon="ServicesIcon"
    panel-classes="w-full  bg-white rounded-xl shadow-[0px_8px_8px_-4px_rgba(10,13,18,0.04)] shadow-[0px_20px_24px_-4px_rgba(10,13,18,0.10)] flex flex-col overflow-visible sm:w-full sm:mx-20 h-full my-20"
    @close="closeModal"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    <modal
      v-if="showAddCategoryModal"
      :open="showAddCategoryModal"
      title="category"
      :dir="getLocale(locale)?.direction"
      @close="showAddCategoryModal = false"
    >
      <form class="text-start" @submit.prevent="createNewCategory">
        <div class="grid grid-cols-6 gap-6">
          <div class="col-span-3">
            <LangInput
              v-model="categoryData.name_localized"
              :label="$t('form.name')"
              :placeholder="$t('modalPlacholder.serviceName')"
              :v$="v1$"
            />
          </div>
          <div class="col-span-3">
            <div class="">
              <form-group :validation="v1$" name="team_id">
                <template #default="{ attrs }">
                  <SelectInput
                    v-bind="attrs"
                    id="team_id"
                    v-model="categoryData.team_id"
                    :label="$t('modalPlacholder.branch')"
                  >
                    <option hidden selected value="">
                      {{ $t("form.select") }}
                    </option>
                    <option
                      v-for="team in getTeam"
                      :key="team?.uuid"
                      :value="team?.uuid"
                    >
                      {{ team.name }}
                    </option>
                  </SelectInput>
                </template>
              </form-group>
            </div>
          </div>
        </div>

        <div class="mt-5 sm:mt-6">
          <BaseButton
            type="submit"
            show-icon
            class="mx-auto w-1/2 d-block hover:bg-green-700"
            custome-bg="bg-green-600"
            :processing="categoryData.processing"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </form>
    </modal>
    <form
      class="flex flex-col gap-4 mb-4 w-full text-start"
      @submit.prevent="saveService"
    >
      <div class="w-full">
        <form-group :validation="v$" name="images">
          <template #default="{ attrs }">
            <MultiFileInput
              id="service-files"
              v-model="formData.images"
              required
              size-note="10MB"
              format-note="JPG, PNG, PDF"
              multiple
            />
          </template>
        </form-group>
      </div>

      <!-- Main fields grid: 3 cols on large, 2 on medium, 1 on small -->
      <div class="grid grid-cols-1 gap-3 w-full md:grid-cols-2 lg:grid-cols-3">
        <!-- name -->
        <LangInput
          id="service-name"
          v-model="formData.name_localized"
          :label="$t('form.name')"
          :placeholder="$t('modalPlacholder.serviceName')"
          :v$="v$"
          required
        />
        <!-- branch -->
        <form-group :validation="v$" name="team_id">
          <template #default="{ attrs }">
            <SelectInput
              v-bind="attrs"
              id="team_id"
              v-model="formData.team_id"
              :label="$t('modalPlacholder.branch')"
            >
              <option value="">
                {{ $t("form.select") }}
              </option>
              <option
                v-for="team in getTeam"
                :key="team?.uuid"
                :value="team?.uuid"
              >
                {{ team.name }}
              </option>
            </SelectInput>
          </template>
        </form-group>
        <!-- price -->
        <form-group :validation="v$" name="price">
          <template #default="{ attrs }">
            <NumberInput
              v-bind="attrs"
              id="service-price"
              v-model="formData.price"
              :label="$t('price')"
              :placeholder="$t('price')"
              step="0.01"
              required
              :currency="userInfo.tenant?.currency"
            />
          </template>
        </form-group>
        <!-- category -->
        <form-group :validation="v$" name="category_id">
          <template #default="{ attrs }">
            <div class="flex mt-1 rounded-md">
              <div
                class="flex relative flex-grow items-stretch focus-within:z-10"
              >
                <SelectInput
                  v-bind="attrs"
                  id="category"
                  v-model="formData.category_id"
                  required
                  :label="$t('form.category')"
                >
                  <option value="">
                    {{ $t("form.select") }}
                  </option>
                  <option
                    v-for="category of categories.filter(
                      (cat) => cat.team.uuid == formData.team_id,
                    )"
                    :key="category.uuid"
                    :value="category.uuid"
                  >
                    {{
                      category.name_localized?.ar || category.name_localized?.en
                    }}
                  </option>
                </SelectInput>
                <button
                  type="button"
                  class="inline-flex relative items-center px-4 py-2 -ml-px space-x-2 text-sm font-medium text-gray-700 bg-gray-50 border border-gray-300 hover:bg-gray-100 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 rounded-e-md h-[48px] mt-auto"
                  @click="showAddCategoryModal = true"
                >
                  <PlusIcon class="w-5 h-5 text-gray-400" aria-hidden="true" />
                  <span>{{ $t("New") }}</span>
                </button>
              </div>
            </div>
          </template>
        </form-group>
        <!-- color -->
        <form-group :validation="v$" name="color">
          <template #default="{ attrs }">
            <div class="relative">
              <TextInput
                v-bind="attrs"
                id="service-color"
                v-model="formData.color"
                :label="$t('color')"
                :placeholder="$t('color')"
              />
              <input
                v-model="formData.color"
                type="color"
                class="absolute top-1/2 w-7 h-7 bg-transparent rounded-full border-none transform appearance-none cursor-pointer end-1"
              >
            </div>
          </template>
        </form-group>
        <!-- sort_order -->
        <form-group :validation="v$" name="sort_order">
          <template #default="{ attrs }">
            <NumberInput
              v-bind="attrs"
              id="sort-order"
              v-model="formData.sort_order"
              :label="$t('form.sort_order')"
              :placeholder="$t('form.sort_order')"
            />
          </template>
        </form-group>
        <!-- duration -->
        <form-group :validation="v$" name="duration_time">
          <template #default="{ attrs }">
            <DurationInput
              v-bind="attrs"
              id="minutes"
              v-model="formData.duration_time"
              :label="$t('duration_minutes')"
              :placeholder="$t('Minutes')"
              required
            />
          </template>
        </form-group>
        <!-- staff selection, only if not public -->
        <form-group
          v-if="!formData.is_public && formData.team_id"
          :validation="v$"
          name="staff_users"
        >
          <template #default="{ attrs }">
            <MultiSelectInput
              v-bind="attrs"
              id="staff_users"
              v-model="formData.staff_users"
              :options="
                (teamStaff || [])
                  .filter(item => item.branch_id === formData.team_id)
                  .map(staff => ({ id: staff.uuid, name: staff.name }))
              "
              :label="$t('form.staff')"
              :placeholder="$t('form.select')"
            />
          </template>
        </form-group>
      </div>

      <!-- description -->
      <form-group
        :validation="v$"
        name="description_localized"
        error-name="description_localized.ar"
      >
        <template #default="{ attrs }">
          <MiniEditor
            v-model="formData.description_localized"
            :label="$t('description')"
            :placeholder="$t('description')"
            v-bind="attrs"
          />
        </template>
      </form-group>

      <!-- Switches and buttons -->
      <div class="flex flex-col gap-4 mt-4 w-full sm:flex-row">
        <SwitchInput
          id="display_on_booking_page"
          v-model="formData.display_on_booking_page"
          :label="$t('displayOnBookingPage')"
        />
        <SwitchInput
          id="is_public"
          v-model="formData.is_public"
          :label="$t('form.isPublicService')"
        />
      </div>

      <div class="flex flex-col gap-4 justify-center mt-4 w-full sm:flex-row">
        <BaseButton
          type="submit"
          class="w-full sm:flex-1 px-6 py-3 rounded-md bg-white border border-[#0F2C3F] !text-[#0F2C3F] hover:bg-gray-100 hover:!text-white transition"
          show-icon
          :processing="processing2"
          @mousedown.prevent="buttonAction = true"
        >
          {{ $t("form.create") }}
        </BaseButton>

        <BaseButton
          type="submit"
          class="w-full sm:flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-md text-white bg-[#0F2C3F] border border-[#0F2C3F] hover:bg-white hover:text-[#0F2C3F] transition"
          show-icon
          :processing="processing"
          @mousedown.prevent="buttonAction = false"
        >
          {{ $t("save_and_add_new") }}
          <ArrowLeftIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </form>
  </FullScreenModal>
</template>

<style scoped>
form > div:not(:last-child) {
  flex: 1 0 48%;
}

form > div.full {
  flex: 1 0 100%;
}

@media (max-width: 768px) {
  form > div:not(:last-child) {
    flex: 1 0 100% !important;
  }
}
</style>
