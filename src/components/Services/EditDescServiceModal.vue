<script setup lang="ts">
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import type { Service } from '@/types'
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  serviceUuid: {
    type: String,
    default: '',
    required: true,
  },
  service: {
    type: Object as PropType<Service>,
    default: () => ({}),
    required: true,
  },
})
const emits = defineEmits(['close', 'refresh'])
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const { updateService } = useServicesStore()

const processing = ref(false)

const formData = reactive<Omit<Service, 'uuid'>>({})

watch(
  () => props.service,
  (value: Service) => {
    if (!value)
      return
    console.log('watch service', value)
    formData.team_id = value.team_id || ''
    formData.name_localized = value.name_localized || { ar: '', en: '' }
    formData.price = value.price || 0
    formData.duration = value.duration || 0
    formData.description_localized = value.description_localized || {
      ar: '',
      en: '',
    }
    formData.category_id = value.category_id || ''
    formData.color = value.color || '#000000'
    formData.slug = value.slug || ''
    formData.sort_order = value.sort_order || 0
    formData.duration_time = value.duration || 0
  },
  { immediate: true },
)

async function updateDescription() {
  processing.value = true
  try {
    const res = await updateService(props.serviceUuid, {
      ...formData,
      name_localized: JSON.stringify(formData.name_localized),
      description_localized: JSON.stringify(formData.description_localized),
      category_id: formData.category_id.uuid,
    })
    emits('refresh')
    emits('close')
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    title="edit_description"
    panel-classes="w-full max-w-2xl transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all  sm:w-2xl md:px-8 lg:px-8 "
    @close="emits('close')"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    <form class="text-start" @submit.prevent="updateDescription()">
      <div class="grid grid-cols-1 gap-4 gap-y-6">
        <div class="w-full">
          <label
            class="mb-2 w-full flex items-center text-start text-[#261E27] text-base"
            for="service-description"
          >
            {{ $t("description") }}
          </label>

          <MiniEditor
            v-model="formData.description_localized"
            :placeholder="$t('description')"
          />
        </div>
        <div class="flex justify-center w-full">
          <BaseButton
            class="inline-flex items-center justify-center gap-2 px-6 py-3 rounded-md text-white bg-[#0F2C3F] border border-[#0F2C3F] hover:bg-white hover:text-[#0F2C3F] transition"
            :processing="processing"
          >
            {{ $t("form.save") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </Modal>
</template>
