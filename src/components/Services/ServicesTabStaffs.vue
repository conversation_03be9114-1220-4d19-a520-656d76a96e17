<script setup lang="ts">
import type { PropType } from '@vue/runtime-core'
import { Switch } from '@headlessui/vue'
import type { Service, Staff } from '@/types'
import i18n from '@/i18n'
const props = defineProps({
  service: {
    type: Object as PropType<Service>,
    default: () => ({}),
  },
})
const { fetchServicesStaff, toggleServicesStaff } = useServicesStore()
const { showNotification } = useNotifications()
const tableServicData = reactive({
  processing: false,
  staffList: [] as Staff[],
})
const currentToggle = ref()

const fetchStaffs = async (service: Service) => {
  tableServicData.processing = true
  fetchServicesStaff(service).then((data) => {
    tableServicData.staffList = data.staff
    return data.staff
  }).finally(() => {
    tableServicData.processing = false
  })
}
const toggleService = async (service: Service, staff: Staff) => {
  currentToggle.value = staff.uuid
  toggleServicesStaff(service, staff).then(({ staff }) => {
    showNotification({
      title: i18n.global.t('Success'),
      type: 'success',
      message: i18n.global.t('operations.toggled'),
    })
    tableServicData.staffList = staff
  }).catch(() => {
    tableServicData.staffList = [...tableServicData.staffList].map((item) => {
      if (item.uuid === staff.uuid)
        item.selected = !item.selected
      return item
    })
  }).finally(() => {
    currentToggle.value = null
  })
}

watch(
  () => props.service,
  (service, oldService) => {
    if (service)
      props.service && fetchStaffs(service)
  },
  { immediate: true },
)
</script>

<template>
  <div class="grid grid-cols-1 gap-4 relative">
    <OverlayLoader v-if="tableServicData.processing" :full-screen="false" />
    <ul role="list" class="divide-y divide-gray-200">
      <li
        v-for="staff in tableServicData.staffList.filter((staff) => staff.selected)" :key="staff.uuid" class="flex items-center justify-between py-4 ps-2"
      >
        <div class="flex">
          <div>
            <p class="text-sm font-medium text-gray-900">
              {{ staff.name }}
            </p>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>
