<script setup lang="ts">
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { Switch } from '@headlessui/vue'
import type { Service } from '@/types'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  serviceUuid: {
    type: String,
    default: '',
    required: true,
  },
  service: {
    type: Object as PropType<Service>,
    default: () => ({}),
    required: true,
  },

})
const emit = defineEmits(['close', 'updated', 'refresh'])
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const { addImagesToService } = useServicesStore()

const processing = ref(false)

const formData = reactive<{ images: any[] }>({
  images: [],
})

watch(() => props.service, (value: Service) => {
  if (!value)
    return
}, { immediate: true })

async function AddImagesService() {
  processing.value = true
  console.log('props-serviceUuid', props.serviceUuid)
  try {
    const payload = new FormData()
    if (Array.isArray(formData.images)) {
      formData.images.forEach((file, index) => {
        payload.append(`images[${index}]`, file)
      })
    }
    await addImagesToService(props.serviceUuid, payload)
    emit('refresh')
    emit('close')
  }
  finally {
    processing.value = false
  }
}

function handleClose() {
  emit('close')
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    title="create"
    panel-classes="w-full max-w-2xl transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all  sm:w-2xl md:px-8 lg:px-8 "
    @close="handleClose"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    <form class="text-start" @submit.prevent="AddImagesService()">
      <div class="grid grid-cols-1 gap-4 gap-y-6">
        <div class="w-full">
          <div class="col-span-3">
            <MultiFileInput
              id="service-files"
              v-model="formData.images"
              size-note="10MB"
              format-note="JPG, PNG, PDF"
              multiple
            />
          </div>
        </div>
        <div class="w-full flex justify-center">
          <BaseButton
            class="w-full inline-flex items-center justify-center"
            :processing="processing"
            type="submit"
          >
            {{ $t("form.save") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </Modal>
</template>
