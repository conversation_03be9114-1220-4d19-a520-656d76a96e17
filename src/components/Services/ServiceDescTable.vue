<script setup lang="ts">
import type { PropType } from 'vue'
import { computed } from 'vue'
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue'
import LilActionBtn from '@/components/Buttons/LilActionBtn.vue'
import EditIcon from '@/components/Icons/EditIcon.vue'

const props = defineProps({
  service: {
    type: Object as PropType<Record<string, any> | null>,
    required: true,
  },
  field: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['edit'])

const value = computed(() => {
  if (!props.service)
    return ''
  const v = props.service[props.field]
  return typeof v === 'string' ? v : ''
})
</script>

<template>
  <DisclosureWrapper :title="title || $t('fields.description')">
    <template #header-actions>
      <LilActionBtn
        :icon="EditIcon"
        @click="$emit('edit')"
      />
    </template>
    <div class="flex gap-2 items-center w-full overflow-x-auto">
      <div
        v-if="value && value.length > 0"
        class="text-sm font-medium text-neutral-800 break-words whitespace-pre-line w-full max-h-40 overflow-y-auto"
        v-html="value"
      />
      <span v-else class="text-neutral-400">-</span>
    </div>
  </DisclosureWrapper>
</template>
