<script setup lang="ts">
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { Switch } from '@headlessui/vue'
import { useVuelidate } from '@vuelidate/core'
import { convertToHoursAndMinutes } from '@/utils/time'
import SwitchInput from '@/components/FormControls/Inputs/SwitchInput.vue'
import type { Service } from '@/types'
import {
  minLength,
  minValue,
  required,
  requiredIf,
} from '@/utils/i18n-validators'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  serviceUuid: {
    type: String,
    default: '',
    required: true,
  },
  service: {
    type: Object as PropType<Service>,
    default: () => ({}),
    required: true,
  },
})
const emits = defineEmits(['close', 'refresh'])
const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const { updateService } = useServicesStore()
const { fetchCategories } = useCategoryStore()
const { getUserInfo } = storeToRefs(useAuthStore())
const { userInfo } = useAuthStore()
const processing = ref(false)
const categories = ref([])

const formData = reactive<Omit<Service, 'uuid'>>({
  name_localized: { ar: '', en: '' },
  price: 0,
  display_on_booking_page: true,
  category_id: '',
  sort_order: 0,
  duration_time: 0,
  team_id: '',
  color: '#000000',
  slug: '',
})

const rules = {
  name_localized: {
    ar: {
      required: requiredIf(() => !formData.name_localized.en.trim()),
    },
    en: {
      required: requiredIf(() => !formData.name_localized.ar.trim()),
    },
  },
  price: {
    required,
    minValue: minValue(0),
  },
  team_id: {
    required,
  },
  duration_time: {
    required,
  },
  slug: {
    required,
  },
  category_id: {
    required,
  },
}

const v$ = useVuelidate(rules, formData)

// Get teams for branch selection
const getTeam = computed(() => {
  return getUserInfo.value.teams
})

// Load categories when team changes
watch(
  () => formData.team_id,
  async (val) => {
    if (val) {
      try {
        const { data } = await fetchCategories()
        categories.value = data
      }
      catch (error) {
        console.error('Failed to fetch categories:', error)
      }
    }
  },
)

// Initialize form data from service prop
watch(
  () => props.service,
  (value: Service) => {
    if (!value)
      return

    formData.name_localized = value.name_localized || { ar: '', en: '' }
    formData.price = value.price || 0
    formData.display_on_booking_page = value.display_on_booking_page
    formData.category_id = value.category_id?.uuid || ''
    formData.sort_order = value.sort_order || 0
    formData.duration_time = value.duration || 0
    formData.team_id = value.team_id || ''
    formData.color = value.color || '#000000'
    formData.slug = value.slug || ''
    formData.base_link = value.base_link || ''
  },
  { immediate: true },
)

// Convert duration to hours and minutes for display
const duration = computed(() => {
  return convertToHoursAndMinutes(formData.duration_time)
})

async function updateServiceInfo() {
  processing.value = true
  try {
    await updateService(props.serviceUuid, {
      ...formData,
      name_localized: JSON.stringify(formData.name_localized),
      category_id: formData.category_id,
      display_on_booking_page: formData.display_on_booking_page ? 1 : 0,
    })
    emits('refresh')
    emits('close')
  }
  catch (error) {
    console.error('Failed to update service:', error)
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="$t('editService')"
    panel-classes="w-full max-w-xl transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all"
    @close="emits('close')"
  >
    <overlay-loader v-if="processing" :full-screen="false" />

    <form class="text-start" @submit.prevent="updateServiceInfo">
      <div class="space-y-4">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <!-- Service Name -->
          <form-group :validation="v$" name="name_localized">
            <template #default="{ attrs }">
              <LangInput
                v-model="formData.name_localized"
                :label="$t('form.name')"
                :placeholder="$t('modalPlacholder.serviceName')"
                required
              />
            </template>
          </form-group>

          <!-- Price -->
          <form-group :validation="v$" name="price">
            <template #default="{ attrs }">
              <NumberInput
                v-bind="attrs"
                id="service-price"
                v-model="formData.price"
                :label="$t('price')"
                :placeholder="$t('price')"
                step="0.01"
                required
                :currency="userInfo.tenant?.currency"
              />
            </template>
          </form-group>

          <!-- Branch -->
          <form-group :validation="v$" name="team_id">
            <template #default="{ attrs }">
              <SelectInput
                v-bind="attrs"
                id="team_id"
                v-model="formData.team_id"
                :label="$t('modalPlacholder.branch')"
              >
                <option value="">
                  {{ $t("form.select") }}
                </option>
                <option
                  v-for="team in getTeam"
                  :key="team?.uuid"
                  :value="team?.uuid"
                >
                  {{ team.name }}
                </option>
              </SelectInput>
            </template>
          </form-group>

          <!-- Duration -->
          <form-group :validation="v$" name="duration_time">
            <template #default="{ attrs }">
              <DurationInput
                v-bind="attrs"
                id="minutes"
                v-model="formData.duration_time"
                :label="$t('duration_minutes')"
                :placeholder="$t('Minutes')"
                required
              />
            </template>
          </form-group>
          <div class="col-span-2">
            <form-group :validation="v$" name="slug">
              <template #default="{ attrs }">
                <div class="relative">
                  <div
                    class="flex items-center rounded-md ltr:flex-row-reverse rtl:flex-row"
                  >
                    <TextInput
                      v-bind="attrs"
                      id="service-slug"
                      v-model="formData.slug"
                      :label="$t('fields.slug')"
                      :placeholder="$t('fields.slug')"
                      custom-classes="w-full !border-s-0 text-end ltr:text-start disabled:bg-gray-200 rounded-none border-e"
                      @click="
                        $event.target.setSelectionRange(
                          $event.target.value.length,
                          $event.target.value.length,
                        )
                      "
                    />
                    <div
                      class="flex justify-end items-center px-2 bg-gray-200 border border-gray-300 shadow-sm center-items w-1/2 truncate h-[48px] mt-7"
                    >
                      <span class="text-sm text-start" dir="auto">
                        {{ formData.base_link }}
                      </span>
                    </div>
                  </div>

                  <span class="text-sm">{{ $t("slug_services") }}</span>
                  <br>
                  <span v-if="v$.slug.$dirty" class="text-sm">{{
                    $t("slug_rule")
                  }}</span>
                </div>
              </template>
            </form-group>
          </div>
          <!-- Sort Order -->
          <form-group name="sort_order">
            <template #default="{ attrs }">
              <NumberInput
                v-bind="attrs"
                id="sort-order"
                v-model="formData.sort_order"
                :label="$t('form.sort_order')"
                :placeholder="$t('form.sort_order')"
              />
            </template>
          </form-group>

          <!-- Category -->
          <form-group name="category_id">
            <template #default="{ attrs }">
              <SelectInput
                v-bind="attrs"
                id="category"
                v-model="formData.category_id"
                required
                :label="$t('form.category')"
              >
                <option value="">
                  {{ $t("form.select") }}
                </option>
                <option
                  v-for="category of categories.filter(
                    (cat) => cat.team?.uuid == formData.team_id,
                  )"
                  :key="category.uuid"
                  :value="category.uuid"
                >
                  {{
                    category.name_localized?.ar || category.name_localized?.en
                  }}
                </option>
              </SelectInput>
            </template>
          </form-group>

          <!-- Color -->
          <form-group name="color">
            <template #default="{ attrs }">
              <div class="relative">
                <TextInput
                  v-bind="attrs"
                  id="service-color"
                  v-model="formData.color"
                  :label="$t('color')"
                  :placeholder="$t('color')"
                />
                <input
                  v-model="formData.color"
                  type="color"
                  class="absolute top-1/2 w-7 h-7 bg-transparent rounded-full border-none transform appearance-none cursor-pointer end-1"
                >
              </div>
            </template>
          </form-group>
        </div>

        <!-- Display on Store -->
        <div class="pt-2">
          <SwitchInput
            id="display_on_booking_page"
            v-model="formData.display_on_booking_page"
            :label="$t('displayOnBookingPage')"
          />
        </div>
      </div>

      <!-- Submit Button -->
      <div class="mt-6">
        <BaseButton
          type="submit"
          class="w-full inline-flex items-center justify-center gap-2 px-6 py-3 text-white bg-[#0F2C3F] border border-[#0F2C3F] rounded-md hover:bg-white hover:text-[#0F2C3F] transition"
          :processing="processing"
        >
          {{ $t("form.save") }}
        </BaseButton>
      </div>
    </form>
  </Modal>
</template>
