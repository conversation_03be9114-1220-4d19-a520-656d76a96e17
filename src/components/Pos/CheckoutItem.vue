<script lang="ts" setup>
import { TrashIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import { find } from 'lodash'
import type { PropType } from 'vue'
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'
import {
  ChevronUpIcon,
  MinusIcon,
  PlusIcon,
  XMarkIcon,
} from '@heroicons/vue/24/solid'
import { usePosStore } from '@/stores/pos'
import type { Products } from '@/types'
const props = defineProps({
  modelValue: {
    type: Object as PropType<Products>,
    default: () => ({}),
  },
  index: {
    type: Number,
    default: 0,
  },
})
const emit = defineEmits(['update:modelValue', 'change'])
const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)
const { items } = storeToRefs(usePosStore())

const { deleteProduct } = usePosStore()
const isNumber = (evt) => {
  evt = evt || window.event
  const charCode = evt.which ? evt.which : evt.keyCode
  if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46)
    evt.preventDefault()
  else return true
}
const incrementQty = () => {
  props.modelValue.qty++
  emit('change', props.modelValue)
}
const decrementQty = () => {
  if (props.modelValue.qty > 1) {
    props.modelValue.qty--
    emit('change', props.modelValue)
  }
}
const { getErrorByKey } = useServerErrors()
const hasError = computed(() => {
  return (
    getErrorByKey(`services.${props.index}.staff_id`).length
    || getErrorByKey(`services.${props.index}.qty`).length
    || getErrorByKey(`services.${props.index}.price`).length
    || getErrorByKey(`services.${props.index}.discount`).length
  )
})
</script>

<template>
  <div>
    <Disclosure :key="modelValue.uuid" v-slot="{ open }">
      <DisclosureButton
        class="flex items-center justify-between w-full mt-4 text-sm font-medium text-left rounded-lg bg-blue-50 text-dark-900 hover:bg-blue-200 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75"
        :class="
          hasError ? 'border border-red-500' : 'border border-transparent'
        "
      >
        <div class="flex justify-between w-full h-full items-sterch">
          <button
            class="flex items-center h-full px-3 py-4 rounded-lg hover:bg-red-100"
            @click.stop="deleteProduct(modelValue.uuid)"
          >
            <TrashIcon
              class="w-6 h-full text-red-600 cursor-pointer hover:text-red-800"
            />
          </button>
          <span class="flex items-center w-full px-3 py-2 font-medium text-md">{{ modelValue.name }}
            <span class="text-left">
              <XMarkIcon
                class="w-4 h-4 cursor-pointer"
                @click="deleteProduct(modelValue.uuid)"
              />
            </span>
            {{ modelValue.qty }}
          </span>

          <div class="flex items-center justify-end w-full gap-2">
            <span class="">
              <price-format
                :form-data="{
                  price: modelValue.subTotal,
                  currency: userInfo?.tenant?.currency || '',
                }"
              />
            </span>
            <button class="block h-full px-3 py-4" type="button">
              <ChevronUpIcon
                :class="open ? 'rotate-180 transform' : ''"
                class="w-5 h-full cursor-pointer text-dark-500"
              />
            </button>
          </div>
        </div>
      </DisclosureButton>
      <DisclosurePanel
        :unmount="false"
        class="px-2 pt-2 pb-2 mb-4 overflow-y-auto text-sm text-gray-500"
      >
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div v-if="modelValue.type === 'services'">
            <form-group :error-name="`services.${index}.staff_id`">
              <template #default="{ attrs }">
                <SelectInput
                  v-bind="attrs"
                  id="staff"
                  v-model="modelValue.selectedStaff"
                  :label="$t('form.staff')"
                >
                  <option value="">
                    {{ $t("form.select") }}
                  </option>
                  <option
                    v-for="staff in find(items, (o) => o.id === modelValue.id)
                      .staff.filter((stf) => stf.can_serve)"
                    :value="staff.id"
                  >
                    {{ staff.name }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
          <div class="relative flex flex-col w-full rounded-md">
            <LabelInput for="quantity">
              {{ $t("form.qty") }}
            </LabelInput>
            <form-group :error-name="`services.${index}.quantity`">
              <template #default="{ attrs }">
                <div
                  class="flex items-center"
                  v-bind="attrs"
                  error-name="`services.${index}.qty`"
                >
                  <button
                    type="button"
                    class="px-3 py-3 text-white bg-gray-600 hover:bg-green-600 rounded-s-md"
                    @click="incrementQty"
                  >
                    <PlusIcon class="w-5 h-5" />
                  </button>
                  <TextInput
                    id="quantity"
                    v-model="modelValue.qty"
                    name="quantity"
                    min="1"
                    step="1"
                    class="text-center rounded-none"
                    aria-describedby="price-currency"
                    @input="emit('change', modelValue)"
                    @keypress="isNumber($event)"
                  />
                  <button
                    type="button"
                    class="px-3 py-3 text-white bg-gray-600 hover:bg-red-700 rounded-e-md"
                    @click="decrementQty"
                  >
                    <MinusIcon class="w-5 h-5" />
                  </button>
                </div>
              </template>
            </form-group>
          </div>
          <div class="relative">
            <form-group :error-name="`services.${index}.price`">
              <template #default="{ attrs }">
                <TextInput
                  v-bind="attrs"
                  id="price_unit"
                  v-model="modelValue.price"
                  :label="$t('form.price_unit')"
                  name="price_unit"
                  placeholder="Price"
                  aria-describedby="price_unit"
                  @input="emit('change', modelValue)"
                  @keypress="isNumber($event)"
                />
              </template>
            </form-group>
          </div>

          <div class="relative">
            <form-group :error-name="`services.${index}.discount`">
              <template #default="{ attrs }">
                <NumberInput
                  id="discount"
                  v-model="modelValue.discount"
                  :label="$t('form.discount')"
                  step="0.01"
                  min="0"
                  :max="modelValue.price"
                  name="discount"
                  placeholder="Discount"
                  aria-describedby="price-percentage"
                  @input="emit('change', modelValue)"
                  @keypress="isNumber($event)"
                />
              </template>
            </form-group>
          </div>
        </div>
      </DisclosurePanel>
    </Disclosure>
  </div>
</template>
