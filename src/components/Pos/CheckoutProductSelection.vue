<script setup lang="ts">
import { UsersIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import usePos from '@/composables/usePos'
import { usePosStore } from '@/stores/pos'

const props = defineProps({
  teamId: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['teamChanged'])
const { fetchCheckoutPage } = usePos()
const { checkoutData, items, checkoutItems } = storeToRefs(usePosStore())
const { clearPos } = usePosStore()
const showClientModal = ref(false)

const teamChange = async (value) => {
  checkoutItems.value = []
  if (!value)
    return
  await fetchCheckoutPage(checkoutData.value.team_id)
  emit('teamChanged')
}

const clientSelected = (client) => {
  checkoutData.value.customer = client
}

const { userInfo } = storeToRefs(useAuthStore())
onMounted(() => {
  checkoutData.value.team_id
    = props.teamId || userInfo.value?.teams?.[0]?.uuid || ''
  teamChange(checkoutData.value.team_id)
  showClientModal.value = true
})
const closeClientModal = () => {
  showClientModal.value = false
}
</script>

<template>
  <div class="flex flex-col h-full">
    <client-selection-modal
      v-if="showClientModal"
      v-model="checkoutData.customer.uuid"
      :label="checkoutData.customer"
      :is-open="showClientModal"
      @selected="clientSelected"
      @closed="closeClientModal"
    />
    <div class="overflow-y-auto h-full">
      <form-group error-name="team_id">
        <template #default="{ attrs }">
          <filter-with-team
            v-bind="attrs"
            v-model="checkoutData.team_id"
            :default="0"
            @change="teamChange($event.target.value)"
          />
        </template>
      </form-group>
      <div class="flex justify-between items-center mt-3">
        <div>
          <div class="font-semibold text-md">
            {{
              checkoutData.customer.first_name
                ? `${checkoutData.customer?.first_name} ${
                  checkoutData.customer?.last_name || ""
                }`
                : $t("pos.guest")
            }}
          </div>
          <div class="text-xs text-gray-400">
            {{ checkoutData.customer?.phone || "--" }}
          </div>
        </div>
        <div
          class="flex items-center text-sm duration-150 cursor-pointer hover:text-blue-500"
          @click="showClientModal = true"
        >
          {{ $t("pos.select_client") }}
          <UsersIcon class="w-5 h-5 ltr:ml-2 rtl:mr-2" />
        </div>
      </div>
      <div
        class="sm:h-[calc(100vh_-_20rem)] sm:min-h-auto overflow-y-auto h-screen"
        :class="{
          'sm:!h-fit ': !items.length,
        }"
      >
        <!-- <product-list :list="posStore[currentTab]" :tab="currentTab" /> -->
        <div
          v-if="items.length"
          class="p-2 mb-6 w-full bg-white rounded-2xl h-fit sm:mb-4"
        >
          <checkout-item
            v-for="(item, i) in items"
            :key="`checkout-item-${i}`"
            v-model="checkoutItems[i]"
            :index="item.id"
            @change="
              checkoutItems[i].subTotal = Number(
                checkoutItems[i].price * checkoutItems[i].qty
                  - checkoutItems[i].discount,
              ).toFixed(2)
            "
          />
        </div>
        <div v-else class="p-6 w-full text-center">
          {{ $t("pos.no_item") }}
        </div>
      </div>
    </div>
    <div v-if="checkoutItems.length" class="flex justify-end items-start mt-4">
      <div class="w-full md:w-[300px]">
        <div
          class="flex justify-between items-center p-3 border-b-2 border-gray-200"
        >
          <span>{{ $t("pos.discount") }}</span>
          <span>
            <price-format
              :form-data="{
                price: checkoutData.discount || 0,
                currency: userInfo?.tenant?.currency || '',
              }"
            />
          </span>
        </div>

        <div
          class="flex justify-between items-center p-3 border-b-2 border-gray-200"
        >
          <span>{{ $t("pos.totalTaxable") }}</span>
          <span>
            <price-format
              :form-data="{
                price: checkoutData.totalTaxable || 0,
                currency: userInfo?.tenant?.currency || '',
              }"
            />
          </span>
        </div>

        <div
          class="flex justify-between items-center p-3 border-b-2 border-gray-200"
        >
          <span>{{ $t("pos.tax") }}</span>
          <span>
            <price-format
              :form-data="{
                price: checkoutData.taxes || 0,
                currency: userInfo?.tenant?.currency || '',
              }"
            />
          </span>
        </div>
        <div
          class="flex justify-between items-center p-3 border-b-2 border-gray-200"
        >
          <span>{{ $t("pos.total") }}</span>
          <span>
            <price-format
              :form-data="{
                price: checkoutData.total || 0,
                currency: userInfo?.tenant?.currency || '',
              }"
            />
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
