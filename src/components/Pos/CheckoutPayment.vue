<script setup>
import { storeToRefs } from 'pinia'
import { format, parse } from 'date-fns'
import { usePosStore } from '@/stores/pos'

const emit = defineEmits('completed')

const { items, invoice } = storeToRefs(usePosStore())
const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)

const team = ref(null)
const completed = ref(false)

const readableDate = (date) => {
  return format(parse(date, 'yyyy/MM/dd HH:mm', Date.now()), 'EEEE dd MMM yyyy')
}
</script>

<template>
  <div class="h-full">
    <div class="w-full h-full flex justify-center items-center flex-col">
      <div class="w-[70%] text-center">
        <div class="text-md font-semibold">
          {{ $t('pos.appointment_is_completed') }}
        </div>
        <div class="mt-3 text-xs text-gray-400">
          <span>{{ $t('pos.full_payment_received on') }} </span> <span class="text-blue-900 font-semibold">{{ readableDate(invoice.created_at) }}</span> {{ $t('pos.at') }} <span class="text-blue-900 font-semibold">{{ invoice.branch?.namne }}</span> {{ $t('pos.by') }} <span class="text-blue-900 font-semibold">{{ invoice.customer?.first_name }} {{ invoice.customer?.last_name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
