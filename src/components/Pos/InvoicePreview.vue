<script setup>
import { PrinterIcon } from '@heroicons/vue/24/outline'
import { sumBy } from 'lodash'
import { storeToRefs } from 'pinia'
import { usePosStore } from '@/stores/pos'
import usePos from '@/composables/usePos'

const { items, invoice } = storeToRefs(usePosStore())
const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)
const { printInvoice } = usePos()

const subTotal = computed(() => {
  return sumBy(invoice.value.services, item => Number(item.price) * Number(item.quantity)) + sumBy(invoice.products, item => Number(item.price) * Number(item.quantity))
})

const balance = computed(() => {
  return 0 - Number(subTotal.value)
})

const print = async (invoice) => {
  printInvoice(invoice.id)
}
</script>

<template>
  <div>
    <!-- <div class="w-full md:w-[600px] bg-gray-100 p-6 flex flex-col items-center justify-start">
      <span class="text-lg font-semibold text-gray-800">{{ $t('Invoice') }}</span>
      <span class="text-sm font-medium text-gray-400">{{ invoice.branch?.name }}</span>
      <div class="w-full mt-3 grid grid-cols-2 gap-3 text-sm border-b border-gray-300 py-5">
        <div class="font-semibold">Invoice / <span class="text-gray-400 font-medium">فاتورة</span></div>
        <div>#{{ invoice.InvoiceNum }}</div>
        <div class="font-semibold">Date / <span class="text-gray-400 font-medium">فاتورة</span></div>
        <div>{{ readableDate(invoice.created_at) }}</div>
        <div class="font-semibold">Customer / <span class="text-gray-400 font-medium">عميل</span></div>
        <div>{{ invoice.customer?.first_name }} {{ invoice.customer?.last_name }}</div>
        <div class="font-semibold">Tel No. / <span class="text-gray-400 font-medium">رقم التليفون</span></div>
        <div>{{ invoice.customer?.phone || '--' }}</div>
      </div>
      <div class="w-full text-sm border-b border-gray-300 py-5 mb-2">
        <div class="w-full flex justify-start items-center gap-3 font-semibold">
          <div class="w-16">
            <div>Qnt</div>
            <div class="text-gray-300 font-medium -mt-1">كمية</div>
          </div>
          <div class="flex-grow">
            <div>Item name</div>
            <div class="text-gray-300 font-medium -mt-1">اسم العنصر</div>
          </div>
          <div class="w-30 text-right">
            <div>Price</div>
            <div class="text-gray-300 font-medium -mt-1">سعر</div>
          </div>
        </div>
        <div v-for="item in invoice.services" class="w-full flex justify-start items-center gap-3 flex-grow mt-3">
          <div class="w-16">{{ item.quantity }} X</div>
          <div class="flex-grow">
            <div>{{ item.name }}</div>
          </div>
          <div class="w-20 font-semibold text-right">{{ userInfo?.tenant?.currency }} {{ item.price }}</div>
        </div>
      </div>
      <div class="w-full flex justify-between items-center text-sm border-b border-gray-300 py-5">
        <div class="font-semibold">Subtotal / <span class="text-gray-400 font-medium">المجموع الفرعي</span></div>
        <div class="font-semibold">{{ userInfo?.tenant?.currency }} {{ subTotal }}</div>
      </div>
      <div class="w-full flex justify-between items-center text-sm border-b border-gray-300 py-5">
        <div class="font-semibold">Cash <div class="text-gray-400 font-medium">نقدي</div></div>
        <div class="font-semibold">- {{ userInfo?.tenant?.currency }} {{ subTotal }}</div>
      </div>
      <div class="w-full flex justify-between items-center text-sm border-b border-gray-300 py-5">
        <div class="font-semibold">Invoice notes / <span class="text-gray-400 font-medium">ملاحظات الفاتورة</span></div>
        <div class="font-semibold">{{ invoice.note || '--' }}</div>
      </div>
      <div class="w-full flex justify-between items-center text-sm py-5">
        <div class="font-semibold">Balance / <span class="text-gray-400 font-medium">توازن</span></div>
        <div class="font-semibold">{{ userInfo?.tenant?.currency }} {{ balance }}</div>
      </div>
    </div> -->
    <div class="p-6 flex flex-col items-center justify-start">
      <button
        type="button"
        class="inline-flex items-center gap-x-1.5 rounded-md bg-blue-500 px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        @click="print(invoice)"
      >
        <PrinterIcon class="-mr-0.5 h-5 w-5" aria-hidden="true" />
        {{ $t('pos.print_invoice') }}
      </button>
    </div>
  </div>
</template>
