<script setup lang="ts">
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from '@headlessui/vue'

import type { PropType } from 'vue'
import {
  ChevronUpIcon, PlusIcon,
} from '@heroicons/vue/24/outline'
import { usePosStore } from '@/stores/pos'
import type { Products } from '@/types'

const props = defineProps({
  list: {
    type: Array as PropType<Products[]>,
    default: () => [],
  },
  tab: {
    type: String,
    default: 'services',
  },
})
const { addProduct } = usePosStore()
const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)
</script>

<template>
  <div class="mx-auto w-full h-[50vh] max-w-md rounded-2xl bg-white p-2 overflow-y-auto">
    <Disclosure v-for="(cate, key, index) in list" :key="`category-${index}`" v-slot="{ open }">
      <DisclosureButton
        class="mt-4 flex w-full justify-between items-center rounded-lg bg-blue-50 px-6 py-4 text-left text-sm font-medium text-dark-900 hover:bg-blue-200 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75"
      >
        <div>
          <div class="text-md">
            {{ key }}
          </div>
        </div>
        <ChevronUpIcon :class="open ? 'rotate-180 transform' : ''" class="h-5 w-5 text-dark-500" />
      </DisclosureButton>
      <DisclosurePanel class="px-2 pt-2 pb-2 text-sm text-gray-500 mb-16 max-h-[300px] overflow-y-auto">
        <div
          v-for="item in cate" :key="`product-${item.id}`"
          class="p-4 bg-gray-50 rounded-md mt-2 flex justify-between items-center cursor-pointer hover:shadow-md"
          @click="addProduct(item, tab)"
        >
          <div class="flex items-center justify-start">
            <PlusIcon class="w-5 h-5" />
            <div class="me-2">
              <div class="text-sm font-medium">
                {{ item.name }}
              </div>
              <!-- <div v-if="tab === 'services'" class="text-xs text-gray-400">
                {{ item.staff.map(i => i.name).join(', ') }}
              </div> -->
            </div>
          </div>
          <div>
            <price-format
              :form-data="{
                price: item.price || 0,
                currency: userInfo?.tenant?.currency || '',
              }"
            />
          </div>
        </div>
      </DisclosurePanel>
    </Disclosure>
  </div>
</template>
