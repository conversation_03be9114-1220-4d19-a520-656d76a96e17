<script setup>
import { PlusIcon } from '@heroicons/vue/24/outline'
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { find } from 'lodash'
import { storeToRefs } from 'pinia'
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  isOpen: {
    type: Boolean,
    default: false,
  },
  label: {
    type: String,
    default: '',
  },
})
const emit = defineEmits('selected', 'closed')
const { fetchTags } = useTagStore()
const { tags } = storeToRefs(useTagStore())
const { fetchCustomerByNameOrPhone } = useCustomerStore()
const customerLoading = ref(false)
const showCreateCustomerModal = ref(false)
const customers = ref([])
const selectedClient = ref(null)
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()

const searchCustomers = async (name = '') => {
  customerLoading.value = true
  fetchCustomerByNameOrPhone(1, `search=${name}`)
    .then((res) => {
      customers.value = res.data
    })
    .finally(() => {
      customerLoading.value = false
    })
}

const customerOptions = computed(() => {
  return customers.value.map(customer => ({
    label: `${customer.first_name} ${customer?.last_name || ''} - ${customer?.phone || ''}`,
    value: customer.uuid,
  }))
})
const customerSelected = (uuid, close = false) => {
  selectedClient.value = find(customers.value, c => c?.uuid === uuid) || null
  if (!selectedClient.value)
    return
  emit('selected', selectedClient.value)
  if (close)
    emit('closed')
}

const customerCreated = (customer) => {
  showCreateCustomerModal.value = false
  customers.value.push(customer)
  customerSelected(customer.uuid)
  emit('closed')
}

onMounted(async () => {
  if (props.modelValue && props.label) {
    selectedClient.value = {
      uuid: props.modelValue,
      first_name: props.label.first_name,
      last_name: props.label.last_name,
      phone: props.label.phone,
    }
    customers.value.push(selectedClient.value)
  }
  await fetchTags('customers')
})
</script>

<template>
  <div>
    <TransitionRoot appear :show="isOpen" as="template">
      <Dialog as="div" class="relative z-10" @close="$emit('closed')">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="overflow-y-auto fixed inset-0">
          <div
            class="flex justify-center items-center p-4 min-h-full text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="p-6 w-full max-w-3xl text-left align-middle bg-white rounded-2xl shadow-xl transition-all transform"
              >
                <DialogTitle
                  as="h3"
                  class="text-2xl font-medium leading-6 text-center text-gray-900"
                >
                  {{ $t('modalHeader.selectClient') }}
                </DialogTitle>
                <div class="mt-2">
                  <div :class="`flex gap-1 ${getLocale(locale)?.direction === 'rtl' ? 'flex-row-reverse' : 'flex-row'} items-end`">
                    <BaseComboBox
                      required
                      :model-value="props.modelValue "
                      arial-label="Search"
                      :options="customerOptions"
                      :class="`block w-full mr-2 text-gray-700 border-gray-300 rounded-none w-100 focus:border-primary-500 focus:ring-primary-500 ${getLocale(locale)?.direction === 'rtl' ? 'text-right' : ''}`"
                      server-filter
                      place-holder="search_phone_or_name"
                      @search="searchCustomers"
                      @update:model-value="customerSelected($event, false)"
                    >
                      {{ $t("booking.customer") }}
                    </BaseComboBox>
                    <button
                      type="button"
                      class="inline-flex justify-center px-4 py-2 text-sm font-medium text-blue-900 bg-blue-100 rounded-md border border-transparent hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                      @click="showCreateCustomerModal = true"
                    >
                      <PlusIcon class="w-5 h-5 text-blue-900" aria-hidden="true" />
                      <span>{{ $t("New") }}</span>
                    </button>
                  </div>

                  <div class="flex gap-4 justify-end pt-6 max-w-5xl sm:col-span-2">
                    <BaseButton
                      v-if="modelValue"
                      type="button"
                      class="inline-flex justify-center px-4 py-2 text-sm font-medium text-blue-900 bg-blue-400 rounded-md border border-transparent hover:bg-blue-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                      @click="customerSelected(modelValue, true); "
                    >
                      <span>{{ $t("form.select") }}</span>
                    </BaseButton>
                    <BaseButton
                      type="button"
                      class="inline-flex justify-center px-4 py-2 text-sm font-medium text-blue-900 bg-red-400 rounded-md border border-transparent hover:bg-red-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                      @click="emit('closed')"
                    >
                      <span>{{ $t("form.close") }}</span>
                    </BaseButton>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
        <AddCustomerModel
          v-if="showCreateCustomerModal" :show-modal="showCreateCustomerModal" title="customer"
          :tags="tags.data"
          @created="customerCreated"
          @closed="showCreateCustomerModal = false"
        />
      </Dialog>
    </TransitionRoot>
  </div>
</template>
