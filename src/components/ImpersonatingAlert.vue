<script setup lang="ts">
import TheLoader from './TheLoader.vue'
import useLogin from '@/composables/auth'
const { stopImpersonate } = useAuthStore()

interface IMPERSONATION_RES {
  token: string
}

const processing = ref(false)

const endImpersonation = () => {
  processing.value = true

  stopImpersonate().then((res: IMPERSONATION_RES) => {
    useLogin(res.token).then(() => window.location.reload())
  }).finally(() => {
    processing.value = false
  })
}
</script>

<template>
  <div class="bg-red-500">
    <div class="px-3 py-3 mx-auto max-w-7xl sm:px-6 lg:px-16">
      <div class="flex flex-wrap items-center justify-center">
        <div
          class="flex flex-wrap items-center justify-center flex-1 w-0 gap-3 mx-auto"
        >
          <span class="flex p-2 rounded-lg">
            <ShieldExclamationIcon
              class="w-6 h-6 text-black"
              aria-hidden="true"
            />
          </span>
          <span
            class="font-medium text-center text-white flex items-center justify-center"
          >

            <span class="mx-2">{{ $t("impersonation.impersonate_mode") }}</span>

            <TheLoader v-if="processing" />

            <a v-else href="#" class="text-white" @click.prevent="endImpersonation">
              <span class="text-md text-red-900 fw-bold">[{{ $t("impersonation.end_impersonation") }}]</span>
            </a>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
