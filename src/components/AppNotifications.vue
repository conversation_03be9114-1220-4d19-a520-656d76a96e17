<script setup lang="ts">
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'
import { BellIcon } from '@heroicons/vue/24/outline'

const solutions = [
  { name: 'Blog', description: 'Learn about tips, product updates and company culture.', href: '#' },
  {
    name: 'Help Center',
    description: 'Get all of your questions answered in our forums of contact support.',
    href: '#',
  },
  { name: 'Guides', description: 'Learn how to maximize our platform to get the most out of it.', href: '#' },
  { name: 'Events', description: 'Check out webinars with experts and learn about our annual conference.', href: '#' },
  { name: 'Security', description: 'Understand how we take your privacy seriously.', href: '#' },
]
</script>

<template>
  <Popover v-slot="{ open }" class="relative ms-2 me-2">
    <PopoverButton class="inline-flex items-center text-base font-medium bg-white rounded-md group hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" :class="[open ? 'text-gray-900' : 'text-gray-500']">
      <span class="sr-only">{{ $t('notifications.title') }}</span>
      <BellIcon class="h-7 w-7" aria-hidden="true" />
    </PopoverButton>

    <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0" enter-to-class="translate-y-0 opacity-100" leave-active-class="transition duration-150 ease-in" leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
      <PopoverPanel class="absolute z-10 w-screen max-w-xs mt-3 end-0 ps-2 pe-2 sm:px-0">
        <div class="overflow-hidden rounded-lg shadow-lg ring-1 ring-black ring-opacity-5">
          <div class="relative grid gap-6 py-6 bg-white ps-5 pe-5 sm:gap-8 sm:p-8">
            <a v-for="item in solutions" :key="item.name" :href="item.href" class="block p-3 -m-3 transition duration-150 ease-in-out rounded-md hover:bg-gray-50">
              <p class="text-base font-medium text-gray-900">{{ item.name }}</p>
              <p class="mt-1 text-sm text-gray-500">{{ item.description }}</p>
            </a>
          </div>
        </div>
      </PopoverPanel>
    </transition>
  </Popover>
</template>
