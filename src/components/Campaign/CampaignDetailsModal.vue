<script setup lang="ts">
import useVuelidate from '@vuelidate/core'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { BellAlertIcon, BellSnoozeIcon, CheckBadgeIcon, ClockIcon, ServerIcon, XMarkIcon } from '@heroicons/vue/24/solid'
import type { ComputedRef, PropType } from 'vue'
import type { header } from '@/types'
import { formatDateAndTime } from '@/composables/dateFormat'
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  selectedCampaign: {
    type: Object,
    default: null,
    required: true,
  },
})
const emits = defineEmits(['close'])
const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const processing = ref(false)
onMounted(async () => {
  try {
    processing.value = true
    setTimeout(() => {
      processing.value = false
    }, 200)
  }
  catch (error) {
    processing.value = false
  }
})

const headers: ComputedRef<header[]> = computed(() => {
  return [
    { title: t('form.name') },
    { title: t('form.sent') },
    { title: t('form.sent_at') },
  ]
})
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    title="campaign"
    panel-classes="w-full max-w-1xl transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all  sm:w-2xl md:px-8 lg:px-8 "
    @close="emits('close')"
  >
    <overlay-loader v-if="processing" :full-screen="processing" />
    <div
      class="flex relative flex-col text-black rounded-lg border border-gray-200 text-md"
    >
      <!-- <div
        class="flex justify-between items-center px-2 py-4 text-lg text-black bg-gray-100"
      > -->
      <!-- <span class="flex gap-2 items-center font-medium">
          {{ selectedCampaign.content.replace('{customer_name}', ' ')}}
        </span> -->
      <!-- </div> -->
      <div class="overflow-hidden ring-1 ring-black ring-opacity-5 shadow md:rounded-lg">
        <div v-if="selectedCampaign.customers" class="inline-block min-w-full align-middle">
          <GenericTable
            :data="selectedCampaign.customers"
            :headers="headers"
            tr-class="hover:bg-green-200 "
            item-key="sent_at"
          >
            <template #row="{ item }">
              <grid-td> {{ item.name }} </grid-td>
              <grid-td>
                <span v-if="item.sent == 1"><CheckBadgeIcon class="inline-block w-8 h-8 text-green-500" /></span>
                <span v-else><ClockIcon class="inline-block w-8 h-8 text-sky-500" /> </span>
              </grid-td>
              <grid-td>
                <span v-if="item.sent_at">{{ formatDateAndTime(item.sent_at) }}</span>
                <span v-else>{{ $t('form.not_sent') }}</span>
              </grid-td>
            </template>
          </GenericTable>
        </div>
        <div
          v-else
          class="overflow-hidden py-6 ring-1 ring-black ring-opacity-5 shadow md:rounded-lg"
        >
          <div class="inline-block min-w-full align-middle md:px-6 lg:px-8">
            {{ $t('empty_refunds_orders') }}
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>
