<script setup lang="ts">
import useVuelidate from '@vuelidate/core'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import { required } from '@/utils/i18n-validators'
import { customDateFormat, formatDateAndTime } from '@/composables/dateFormat'
import { useCampaigns } from '@/stores/campaigns'
import { useLocalesStore } from '@/stores/locales'
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  selectedCampaign: {
    type: Object,
    default: null,
    required: true,
  },
})

const emits = defineEmits(['close'])

const { getCampaignsList, getCampaignApps, createCampaign, updateCampaign, deleteCampaign }
  = useCampaigns()

const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const processing = ref(false)
const appsList = ref([])
const varsList = ref([])
const formData = reactive({
  name: '',
  content: '',
  scheduled_at: new Date().toISOString(),
  app_id: '',
})
onMounted(async () => {
  const { apps, variables } = await getCampaignApps()
  appsList.value = apps
  varsList.value = variables
})
const editMode = ref(false)

const rules = {
  name: {
    required,
  },
  scheduled_at: {
    required,
  },
  app_id: {
    required,
  },
  content: {
    required,
  },
}

const v$ = useVuelidate(rules, formData)

const { selectedCampaign } = toRefs(props)
watch(
  () => selectedCampaign.value,
  (val) => {
    if (val?.uuid) {
      editMode.value = true
      formData.name = val.name
      formData.content = val.content
      formData.scheduled_at = new Date(val.scheduled_at).toISOString()
      formData.uuid = val.uuid
      formData.app_id = val.app.uuid
    }
  },
  { immediate: true },
)

async function saveCompaign() {
  v$.value.$touch()
  if (v$.value.$invalid)
    return

  try {
    processing.value = true
    formData.scheduled_at = dayjs(new Date(formData.scheduled_at)).format('YYYY/MM/DD HH:mm')
    if (editMode.value)
      await updateRecord(formData)
    else
      await createRecord(formData)
  }
  finally {
    processing.value = false
  }
}
const resetForm = () => {
  formData.name = ''
  formData.content = ''
  formData.scheduled_at = new Date().toISOString()
  formData.app_id = ''
  editMode.value = false
  v$.value.$reset()
}
const createRecord = async (data) => {
  await createCampaign(data)
  resetForm()
}

const updateRecord = async (data) => {
  await updateCampaign(data)
  resetForm()
  emits('close')
}

const deleteRecord = async () => {
  try {
    processing.value = true
    await deleteCampaign(formData.uuid)
    emits('close')
  }
  finally {
    processing.value = false
  }
}

const setCompaignDate = (val) => {
  formData.scheduled_at = val
}

const addDataToEditor = (key) => {
  formData.content += key
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="editMode ? 'edit_campaign' : 'create_campaign'"
    panel-classes="w-full max-w-2xl transform rounded-2xl bg-white p-6 align-middle shadow-xl transition-all  sm:w-2xl md:px-8 lg:px-8 "
    @close="emits('close')"
  >
    <overlay-loader v-if="processing" :full-screen="processing" />
    <form class="text-start" @submit.prevent="saveCompaign">
      <div class="grid grid-cols-2  gap-4 gap-y-2">
        <div class="col-span-2 sm:col-span-1">
          <form-group :validation="v$" name="name">
            <template #default="{ attrs }">
              <TextInput
                v-bind="attrs"
                id="campaign_name"
                v-model="formData.name"
                :label="$t('campaign_name')"
                :placeholder="$t('campaign_name')"
              />
            </template>
          </form-group>
        </div>
        <div class="col-span-2 sm:col-span-1">
          <LabelInput for="scheduled_at">
            {{ $t("scheduled_at_campaign") }}
          </LabelInput>
          <v-date-picker
            v-bind="attrs"
            mode="datetime"
            is24hr
            :model-value="formData.scheduled_at"
            :locale="locale"
            :first-day-of-week="1"
            visibility="focus"
            :min-date="new Date().toISOString()"
            @update:model-value="setCompaignDate"
          >
            <template #default="{ inputValue, inputEvents }">
              <form-group :validation="v$" name="scheduled_at">
                <template #default="{ attrs }">
                  <TextInput
                    v-bind="attrs"
                    :model-value="inputValue"
                    mode="mode"
                    :placeholder="$t('scheduled_at')"
                    v-on="inputEvents"
                  />
                </template>
              </form-group>
            </template>
          </v-date-picker>
        </div>
        <div class="col-span-2 sm:col-span-1">
          <form-group :validation="v$" name="app_id">
            <template #default="{ attrs }">
              <SelectInput
                v-bind="attrs"
                id="name"
                v-model="formData.app_id"
                :label="$t('app_id')"
                :placeholder="$t('app_id')"
                class="w-full text-gray-700 border-gray-300 rounded-md"
              >
                <option
                  v-for="app in appsList"
                  :key="app?.uuid"
                  :value="app?.uuid"
                >
                  {{ app.name }}
                </option>
              </SelectInput>
            </template>
          </form-group>
        </div>
        <div v-if="!editMode" class="col-span-2 w-full">
          <div class="w-full">
            <generic-alert
              custom-classes="w-full"
              type="info"
              :message="$t('will_send_notification_to_all_users')"
            />
          </div>
        </div>
        <div class="col-span-2 w-full">
          <div class="w-full">
            <label
              class="block mb-1 text-xs font-bold tracking-wide text-gray-700"
              for="notification-content"
            >
              {{ $t("campaign_content") }}
            </label>
            <form-group :validation="v$" name="content">
              <template #default="{ attrs }">
                <TextareaInput
                  v-bind="attrs"
                  id="notification-content"
                  v-model="formData.content"
                  dir="auto"
                  :rows="5"
                  name="notification-content"
                  custom-classes="block w-full rounded-md border-0 py-1.5 text-gray-900   ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </template>
            </form-group>
          </div>
          <div v-if="!editMode" class="col-span-2 w-full">
            <div class="w-full">
              <generic-alert
                custom-classes="w-full"
                type="warning"
                :message="$t('warn_campaign_content_var')"
              />
            </div>
          </div>
          <label
            class="block mb-1 mt-4 text-xs font-bold tracking-wide text-gray-700"
            for="notification-content"
          >
            {{ $t("variables") }}
          </label>
          <button
            v-for="(value, key) in varsList"
            type="button"
            class="rounded-full bg-white px-4 py-2.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 mx-1 mb-1"
            :index="key"
            @click="addDataToEditor(key)"
          >
            {{ value }}
          </button>
        </div>
        <div class="w-full flex justify-center items-center md:col-span-2 gap-4">
          <BaseButton
            class="inline-flex hover:bg-green-700"
            custome-bg="bg-green-600"
          >
            {{ editMode ? $t("form.update") : $t("form.create") }}
          </BaseButton>
          <BaseButton
            v-if="editMode"
            type="button"
            class="inline-flex hover:bg-red-700"
            custome-bg="bg-red-600"
            @click="deleteRecord"
          >
            {{ $t("form.delete") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </Modal>
</template>
