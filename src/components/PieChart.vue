<script setup lang="ts">
import Chart from 'chart.js/auto'
import type { PropType, Ref } from 'vue'
import ChartDataLabels from 'chartjs-plugin-datalabels'
import type { ChartData, ChartOptions } from 'chart.js'

const props = defineProps({
  data: {
    type: Object as PropType<ChartData>,
    default: () => ({}),
  },
  id: {
    type: String,
    default: '',
  },
})

const options = computed<ChartOptions>(() => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      datalabels: {
        color: '#fff',
        formatter(_, context) {
          return `${context.dataset.data[context.dataIndex]}%`
        },
        font: {
          weight: 'bold',
          size: 13,
        },
        textAlign: 'center',
        align: 'center',
      },
      legend: {
        position: 'left' as const, // Position the legend to the left of the chart
        align: 'start', // Align the legend to the start (top) of the chart
        labels: {
          usePointStyle: true,
          font: {
            size: 12,
          },
          boxWidth: 10, // Adjust the width of the legend colored boxes
        },
      },
    },
  }
})

const { data, id } = toRefs(props)
const availableData = ref(true)
let chartInstance: Chart | null = null

function hasData(chartData: ChartData) {
  availableData.value = true
  if (chartData?.datasets?.[0]?.data?.every(dataPoint => dataPoint == 0)) {
    availableData.value = false
    return false
  }
  return true
}

function initChart() {
  if (!data.value || !hasData(data.value))
    return

  const canvasElement = document.getElementById(id.value) as HTMLCanvasElement
  if (!canvasElement)
    return

  // Destroy previous chart instance if it exists
  if (chartInstance)
    chartInstance.destroy()

  // Create a new chart
  chartInstance = new Chart(canvasElement, {
    type: 'pie',
    data: data.value,
    options: options.value,
    plugins: [ChartDataLabels],
  })
}

watch(
  () => data.value,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        initChart()
      })
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onBeforeUnmount(() => {
  if (chartInstance)
    chartInstance.destroy()
})
</script>

<template>
  <div
    class="flex justify-center items-center p-3 w-fit"
    style="height: 400px; width: 100%"
  >
    <div id="chartjsLegend" class="chartjsLegend" />
    <canvas v-show="availableData" :id="id" width="400" height="400" />
    <div id="chartMessage" :class="[availableData ? 'hidden' : 'block']">
      No data available
    </div>
  </div>
</template>
