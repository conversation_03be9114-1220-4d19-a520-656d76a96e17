<script setup lang="ts">
import {
  Dialog,
  DialogPanel,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'

const props = defineProps({
  message: {
    type: String,
    default: '',
  },
  isOpen: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: 'info',
  },
  duration: {
    type: Number,
    default: 2000,
  },
})
const emit = defineEmits(['close'])
const completeButtonRef = ref(null)
const progress = ref(0)
onMounted(() => {
  const increment = 100 / (props.duration / 100)
  const progressInterval = setInterval(() => {
    // Increase progress by 1% every 100 milliseconds
    progress.value += increment
    if (progress.value >= 100) {
      clearInterval(progressInterval)
      emit('close')
    }
  }, 100)
})
</script>

<template>
  <TransitionRoot as="template" appear :show="isOpen">
    <Dialog
      as="div"
      :initial-focus="completeButtonRef"
      class="relative z-50 xl:z-20 w-full max-w-4xl h-full md:h-auto"
      @close="emit('close')"
    >
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div
          class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto overflow-x-auto">
        <div
          class="flex min-h-full items-center justify-center px-4 text-center sm:p-0"
        >
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel
              class="relative transform overflow-y-visible rounded-lg bg-white px-8 md:px-20 lg:px-10 shadow-xl transition-all sm:my-8 sm:w-fit w-full sm:px-6 flex flex-col justify-center items-center"
            >
              <generic-alert :message="message" :type="type" />
              <!-- progress bar -->
              <div class="w-full h-1 bg-white rounded-full mt-4 fixed bottom-0">
                <div
                  class="h-full bg-primary-800 rounded-full transition-all"
                  :style="{ width: `${progress}%` }"
                />
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
