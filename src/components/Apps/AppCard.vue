<script setup lang="ts">
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import type { AppType } from '@/types'

const props = defineProps({
  app: {
    type: Object as PropType<AppType>,
    required: true,
  },
})

const emit = defineEmits(['install'])
const installed = computed(() => {
  return Boolean(props.app.installed?.status)
})
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
</script>

<template>
  <div
    class="bg-white rounded-lg p-4 shadow-neutral-300 relative shadow-3xl hover:shadow transition-all duration-300 cursor-pointer"
    :class="[!!app?.installed ? 'border-teal-500' : 'border-gray-200']"
  >
    <div
      v-if="app?.soon"
      class="absolute -top-2 rounded-md px-3 py-1 text-sm text-white bg-gray-700"
      :class="[getLocale(locale)?.direction === 'rtl' ? '-left-2' : '-right-2']"
    >
      {{ $t("soon") }} ⚡️
    </div>
    <!-- Recommended badge -->
    <div
      v-if="app?.recommended"
      class="absolute -top-2 rounded-md px-3 py-1 text-sm text-white bg-green-700
      capitalize
        -left-2 rtl:-right-2 rtl:left-auto"
    >
      {{ $t("recommended") }} 🌟
    </div>
    <div class="p-5 flex flex-row gap-6">
      <img
        class="w-9 h-9"
        :src="
          app?.banner_url
            || 'https://www.freeiconspng.com/thumbs/message-icon-png/message-icon-png-17.png'
        "
        :alt="app.name_localized"
      >
      <div>
        <h5 class="mb-2 text-1xl font-bold tracking-tight text-gray-900">
          {{ app?.name_localized }}
        </h5>
        <p class="text-sm text-neutral-400">
          {{ app?.description_localized }}
        </p>
        <span />
      </div>
    </div>
    <span
      v-if="installed"
      class="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-700 absolute bottom-2 right-3 rtl:left-3 rtl:right-auto "
    >
      {{ $t("installed") }}
    </span>

    <span
      v-if="app.configuration?.configurable"
      class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium text-green-700 absolute bottom-2 right-3"
      :class="[app.configuration.configured ? 'bg-green-100' : 'bg-red-500 text-white']"
    >
      {{ app.configuration.configured ? $t("settings.apps.configured") : $t("settings.apps.needs_config") }}
    </span>
  </div>
</template>
