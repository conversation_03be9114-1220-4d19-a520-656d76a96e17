<script lang="ts" setup>
defineProps({
  label: {
    type: String,
    required: true,
  },
  cardValue: {
    type: String,
    required: true,
  },
  activeTab: {
    type: String,
    required: true,
  },
})
</script>

<template>
  <button
    type="button"
    class=" text-white inline-flex items-center px-4 py-4 border border-transparent text-md font-medium rounded-md
    transition-transform transform
    active:scale-105 active:rounded-lg w-full truncate

    "
    :class="[activeTab === cardValue ? 'bg-primary-500' : 'bg-primary-100']"
  >
    {{ label }}
  </button>
</template>
