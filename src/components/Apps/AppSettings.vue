<script setup lang="ts">
import { storeToRefs } from 'pinia'
import type { AppType } from '@/types'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const props = defineProps({
  ShowApps: {
    type: String,
    default: false,
  },
})
const appsStore = useAppsStore()
const { showNotification } = useNotifications()
const { getApps, getCategory } = storeToRefs(useAppsStore())
const showForm = ref(false)
const selectedApp = ref()
const activeTab = ref('')
onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search)
  const status = urlParams.get('status')

  if (status) {
    if (status === 'success') {
      showNotification({
        title: i18n.global.t('Success'),
        type: 'success',
        message: i18n.global.t('operations.installed'),
      })
    }
    else if (status === 'error') {
      showNotification({
        title: i18n.global.t('error'),
        type: 'error',
        message: i18n.global.t('operations.error'),
      })
    }

    const url = new URL(window.location.href)

    url.searchParams.delete('status')
    history.replaceState(null, '', url)
  }
  if (props.ShowApps)
    appsStore.fetchAppsByCategory(props.ShowApps)

  else
    Promise.all([appsStore.fetchApps(), appsStore.fetchAppCategories()])
})

const install = (app: object) => {
  selectedApp.value = app
  showModal()
}

const showModal = () => {
  showForm.value = true
}
const closeModal = () => {
  showForm.value = false
}
const filterByCategory = (category: any) => {
  activeTab.value = category
  appsStore.fetchAppsByCategory(category)
}
const refreshApps = () => {
  if (activeTab.value) {
    if (props.ShowApps)
      appsStore.fetchAppsByCategory(props.ShowApps)
    else appsStore.fetchAppsByCategory(activeTab.value)
  }
  else {
    if (props.ShowApps)
      appsStore.fetchAppsByCategory(props.ShowApps)
    else appsStore.fetchApps()
  }
}
</script>

<template>
  <div>
    <Modal :open="showForm" @close="closeModal">
      <UpsertAppModal v-if="selectedApp" :app="selectedApp" @cancel="selectedApp = null;showForm = false" @refresh="refreshApps" />
    </Modal>

    <div>
      <div v-if="!props.ShowApps" class="px-4 py-5 rounded-md shadow-lg sm:px-6 shadow-neutral-200">
        <div class="sm:flex sm:items-center">
          <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">
              {{ $t("settings.apps.heading") }}
            </h1>
            <p class="mt-2 text-sm text-gray-700">
              {{ $t("settings.apps.desc") }}
            </p>
          </div>
        </div>
        <!-- tabs for show categories -->

        <div v-if="!props.ShowApps" class="relative grid grid-cols-1 gap-3 mt-4 lg:grid-cols-6 md:grid-cols-3">
          <OverlayLoader v-if="appsStore.categoriesLoading" :full-screen="false" />
          <div v-for="(value, key) in getCategory" :key="value" class="col-span-1">
            <CategoryCard
              :card-value="key"
              :label="value"
              :active-tab="activeTab"
              @click="filterByCategory(key)"
            />
          </div>
        </div>
      </div>

      <div class="relative grid grid-cols-1 gap-8 py-5 lg:grid-cols-3 md:grid-cols-2 auto-rows-fr ">
        <OverlayLoader v-if="appsStore.appsLoading" :full-screen="false" />
        <AppCard
          v-for="item in getApps"
          v-else :key="item.uuid" class="flex flex-grow" :app="item" @click="install(item)"
          @install="() => install(item)"
        />
      </div>
    </div>
  </div>
</template>
