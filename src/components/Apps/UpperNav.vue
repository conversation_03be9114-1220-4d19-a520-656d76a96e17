<script lang="ts" setup>
import {
  ChevronDownIcon,
  GlobeAltIcon,
  UserCircleIcon,
} from '@heroicons/vue/20/solid'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import useVuelidate from '@vuelidate/core'
import ErrMsg from '../Common/ErrMsg.vue'
import { required } from '@/utils/i18n-validators'
import { AUTH_TOKEN } from '@/constants'
import i18n from '@/i18n'
import type { AppNotification } from '@/types'
import useLogin from '@/composables/auth'

const emit = defineEmits(['openSideBar', 'languageChanged'])
const { userInfo, getUnReadNotifications } = storeToRefs(useAuthStore())
const { UpdateProfile, logout, impersonate, stopImpersonate } = useAuthStore()
const { readNotification, processingNotifications, fetchAppNotifications }
  = useNotificationTemplate()
const useNotification = useNotificationTemplate()
const { getAppNotifications } = storeToRefs(useNotificationTemplate())
const router = useRouter()
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { showNotification } = useNotifications()

const showProfileMenu = ref(false)
const showLangMenu = ref(false)
const profileMenu = ref(null)
const langMenu = ref(null)
const processing = ref(false)
const language = reactive(locale)

const { fetchCustomerByNameOrPhone } = useCustomerStore()

const rules = {
  email: {
    required,
  },
  reason: {
    required,
  },
}
const impersonateForm = reactive({
  processing: false,
  email: '',
  reason: '',
})

const $v = useVuelidate(rules, impersonateForm)

const langMenuItems = [
  {
    text: 'En',
    action: () => updateLanguage('en'),
    value: 'en',
  },
  {
    text: 'Ar',
    action: () => updateLanguage('ar'),
    value: 'ar',
  },
]

watch(
  language,
  (value) => {
    if (userInfo.value.lang == value) {
      dayjs.locale(value)
      return
    }
    i18n.global.locale.value = userInfo.value.lang
    dayjs.locale(i18n.global.locale.value)
  },
  { immediate: true },
)

const updateLanguage = (langSelected: string) => {
  showLangMenu.value = false
  const payload = new FormData()
  payload.append('lang', langSelected)
  payload.append('name', userInfo.value.name as string)
  payload.append('phone', userInfo.value.phone)
  payload.append('phone_country', userInfo.value.phone_country)
  UpdateProfile(payload).then(() => {
    language.value = langSelected
    emit('languageChanged')
  })
}

const goToProfile = () => {
  router.push({ name: 'profile' })
  showProfileMenu.value = false
}

const showImpersonatingModal = ref(false)

const logOut = async () => {
  processing.value = true
  logout()
    .then(() => {
      localStorage.removeItem('userAuth')
      localStorage.removeItem(AUTH_TOKEN)
      router.push({ name: 'auth', query: { section: 'sign-in' } })
    })
    .finally(() => {
      processing.value = false
    })
}
onMounted(() => {
  // detect click outside of profile menu
  window.addEventListener('click', (e) => {
    if (
      !profileMenu?.value?.contains(e.target as Node)
      && showProfileMenu.value
    )
      showProfileMenu.value = false

    if (!langMenu?.value?.contains(e.target as Node) && showLangMenu.value)
      showLangMenu.value = false
  })
})

function updateReadNotifications(notification: AppNotification) {
  readNotification(notification.uuid).then(() => {
    window.location.href = notification.url
  })
}
const customers = ref()
const customerOptions = computed(() => {
  return customers.value
    ?.filter(customer => customer?.uuid)
    .map(customer => ({
      label: `${customer.first_name} ${customer.last_name} ${customer.phone ? `[ ${customer.phone} ]` : ''
        }`,
      value: customer.uuid,
    }))
})
const { setFalseLoader, setTrueLoader } = useAppsStore()
const searchCustomers = async (name = '') => {
  setTrueLoader()
  fetchCustomerByNameOrPhone(1, `search=${name}`)
    .then((res) => {
      customers.value = res.data
    })
    .finally(() => {
      setFalseLoader()
    })
}
const redirectToCustomer = (uuid: any) => {
  router.push({ name: 'customer', params: { id: uuid } })
  if (router.currentRoute.value.name === 'customer')
    router.go(0)
}

interface IMPERSONATION_RES {
  token: string
}

const doImpersonating = async () => {
  try {
    $v.value.$touch()
    if ($v.value.$invalid)
      return false
    impersonateForm.processing = true
    const res = await impersonate({ email: impersonateForm.email, reason: impersonateForm.reason }) as IMPERSONATION_RES
    showNotification({
      title: i18n.global.t('Success'),
      type: 'success',
      message: i18n.global.t('impersonation.success'),
    })
    await useLogin(res.token)
    window.location.reload()
  }
  finally {
    impersonateForm.processing = false
  }
}

const endImpersonation = () => {
  processing.value = true

  stopImpersonate().then((res: IMPERSONATION_RES) => {
    useLogin(res.token).then(() => window.location.reload())
  }).finally(() => {
    processing.value = false
  })
}
</script>

<template>
  <overlay-loader v-if="processing" />
  <Modal
    :dir="getLocale(locale)?.direction" :open="showImpersonatingModal" title="impersonate"
    @close="showImpersonatingModal = false"
  >
    <div class="p-4 bg-red-50 rounded-md border border-red-400">
      <p class="text-sm text-red-900">
        {{ $t("impersonation.warning") }}
      </p>
    </div>
    <form class="relative py-5 mb-4 text-start" @submit.prevent="doImpersonating">
      <div class="">
        <form-group name="email" :validation="$v">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="email"
              v-model="impersonateForm.email"
              :label="$t('form.email')"
              :placeholder="`${$t('impersonation.email_address')}`"
              custom-classes="mt-1"
            />
          </template>
        </form-group>
        <form-group name="reason" :validation="$v">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs" id="reason" v-model="impersonateForm.reason"
              :placeholder="`${$t('impersonation.reason')}`" custom-classes="mt-1"
            />
          </template>
        </form-group>
      </div>
      <div class="w-full">
        <div class="flex gap-4 justify-center mt-6 w-full">
          <BaseButton
            class="col-span-4 md:col-span-2" custome-bg="bg-green-600" show-icon type="submit"
            :processing="impersonateForm.processing"
          >
            {{ $t("modalHeader.submit") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </Modal>

  <div
    class="flex sticky top-0 z-10 gap-x-4 items-center px-4 h-16 bg-white border-b border-gray-200 shadow-sm shrink-0 sm:gap-x-6 sm:px-6 lg:px-8"
  >
    <button type="button" class="p-2.5 -m-2.5 text-gray-700 lg:hidden" @click="$emit('openSideBar')">
      <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
      </svg>
    </button>

    <!-- Separator -->
    <div class="w-px h-6 bg-gray-900/10 lg:hidden" aria-hidden="true" />
    <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
      <BaseComboBox
        arial-label="Search" :options="customerOptions" :search="true"
        server-filter class="py-2 "
        place-holder="search_phone_or_name" @search="searchCustomers" @selected="redirectToCustomer"
      >
        <template #inner-icon>
          <svg
            class="absolute w-5 h-5 text-gray-400 pointer-events-none" viewBox="0 0 20 20" fill="currentColor"
            aria-hidden="true"
          >
            <path
              fill-rule="evenodd"
              d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
              clip-rule="evenodd"
            />
          </svg>
        </template>
      </BaseComboBox>
      <div class="flex gap-x-4 items-center lg:gap-x-6">
        <Menu as="div" class="inline-block relative text-left">
          <MenuButton
            class="inline-flex relative justify-center justify-between items-center py-5 -m-2.5 mx-1 w-full text-gray-400 hover:text-gray-500"
          >
            <button type="button" class="inline-flex relative items-center">
              <svg
                class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  stroke-linecap="round" stroke-linejoin="round"
                  d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"
                />
              </svg>
              <!-- red ciricle to render count -->
              <span
                v-if="getUnReadNotifications > 0"
                class="inline-flex absolute top-0 right-0 justify-center items-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-700 rounded-full transform translate-x-1/2 -translate-y-1/2"
              >{{ getUnReadNotifications }}</span>
            </button>
          </MenuButton>
          <transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="opacity-0 transform scale-95" enter-to-class="opacity-100 transform scale-100"
            leave-active-class="transition duration-75 ease-in" leave-from-class="opacity-100 transform scale-100"
            leave-to-class="opacity-0 transform scale-95"
          >
            <MenuItems
              class="overflow-y-auto absolute right-0 mt-2 w-max max-h-64 bg-white rounded-md divide-y divide-gray-100 ring-1 shadow-lg origin-top-right ring-black/5 focus:outline-none rtl:left-0 rtl:right-auto"
            >
              <overlay-loader v-if="useNotification.processingNotifications" :full-screen="false" />
              <div class="relative px-1 py-1">
                <MenuItem
                  v-for="notification in getAppNotifications" v-slot="{ active }" :key="notification.uuid"
                  @click.prevent
                >
                  <button
                    class="group flex w-full items-center rounded-md px-2 py-2 text-sm" :class="[
                      active ? 'bg-primary-800 text-white' : 'text-gray-900',
                    ]" @click="updateReadNotifications(notification)"
                  >
                    <div class="flex flex-1 justify-between items-start" dir="auto">
                      <h3 class="font-medium text-xsm">
                        {{ notification.description }}
                      </h3>
                      <span class="text-xs font-medium text-blue-500 truncate group-hover:text-inherit" dir="auto">
                        {{
                          dayjs(notification.created_at)
                            .locale(locale)
                            .from(new Date())
                        }}
                      </span>
                    </div>
                  </button>
                </MenuItem>
                <MenuItem v-if="getAppNotifications.length == 0" v-slot="{ active }" @click.prevent>
                  <div class="flex flex-1 justify-between items-center p-3">
                    <h3 class="text-sm font-semibold">
                      {{ $t("no_notifications") }}
                    </h3>
                  </div>
                </MenuItem>
              </div>
            </MenuItems>
          </transition>
        </Menu>

        <div class="relative">
          <button
            type="button" class="flex items-center p-1.5 -m-1.5 text-gray-400 hover:text-gray-500"
            @click.stop="showLangMenu = !showLangMenu"
          >
            <GlobeAltIcon class="w-6 h-6" aria-hidden="true" />
          </button>
          <div
            v-if="showLangMenu"
            ref="langMenu"
            class="absolute left-1/2 z-10 py-2 mt-2.5 bg-white rounded-md ring-1 shadow-lg transform origin-top-right -translate-x-1/2 w-fit ring-gray-900/5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1"
          >
            <a
              v-for="(item, index) in langMenuItems" :id="`user-menu-item-${index}`" :key="index"
              href="#" class="block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-100" :class="{
                'bg-primary-700 text-white hover:bg-primary-700':
                  $i18n.locale === item.value,
              }" role="menuitem" tabindex="-1" @click.prevent="item.action"
            >{{
              $t(item.text) }}</a>
          </div>
        </div>

        <!-- Separator -->
        <div class="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10" aria-hidden="true" />

        <!-- Profile dropdown -->
        <div class="relative">
          <button
            id="user-menu-button" type="button" class="flex items-center p-1.5 -m-1.5" aria-expanded="false"
            aria-haspopup="true" @click.stop="showProfileMenu = !showProfileMenu"
          >
            <UserCircleIcon v-if="!userInfo.profile_photo" class="w-10 h-10 text-gray-300" />
            <img v-else :src="userInfo.profile_photo" alt="user profile img" class="w-10 h-10 rounded-full">
            <span class="hidden lg:flex lg:items-center">
              <span class="ml-4 text-sm font-semibold leading-6 text-gray-900" aria-hidden="true">{{ userInfo.name
              }}</span>
              <ChevronDownIcon class="w-5 h-5 text-gray-400 ms-1.5" aria-hidden="true" />
            </span>
          </button>

          <div
            v-if="showProfileMenu"
            ref="profileMenu" class="absolute right-0 z-10 py-2 mt-2.5 w-32 bg-white rounded-md ring-1 shadow-lg origin-top-right ring-gray-900/5 focus:outline-none rtl:left-0 rtl:right-auto" role="menu" aria-orientation="vertical"
            aria-labelledby="user-menu-button" tabindex="-1"
          >
            <a
              v-if="userInfo.can_imp" id="user-menu-item-0"
              href="#" class="block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-100" role="menuitem"
              tabindex="-1" @click="showImpersonatingModal = true"
            >{{ $t("impersonation.label") }}</a>
            <a
              v-else-if="userInfo.is_imp" id="user-menu-item-0"
              href="#" class="block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-100" role="menuitem"
              tabindex="-1" @click="endImpersonation"
            >{{ $t("impersonation.end_impersonation") }}</a>

            <a
              id="user-menu-item-0" href="#" class="block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-100"
              role="menuitem" tabindex="-1" @click="goToProfile"
            >{{ $t("homepage.profile") }}</a>
            <a
              id="user-menu-item-1" href="#"
              class="block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-red-700 hover:text-white" role="menuitem" tabindex="-1" @click="logOut"
            >{{ $t("form.signout") }}</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
