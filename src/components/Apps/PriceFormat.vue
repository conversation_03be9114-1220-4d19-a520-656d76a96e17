<script lang="ts" setup>
import type { PropType } from 'vue'

defineProps({
  formData: {
    type: Object as PropType<FormData>,
    default: () => ({
      price: 0,
      currency: 'SAR',
    }),
  },
})

const { locale } = useI18n()

interface FormData {
  price: number
  currency: string
}
</script>

<template>
  <div v-if="formData.currency?.length > 0">
    {{ Intl.NumberFormat(`${locale}`, { style: 'currency', currency: `${formData.currency}` }).format(Number(formData.price)) }}
  </div>
</template>

<style>

</style>
