<script lang="ts" setup>
import { HomeIcon } from '@heroicons/vue/20/solid'
import type { PropType } from 'vue'

const props = defineProps({
  crumbs: {
    type: Array as PropType<{ name: string; path: string }[]>,
    default: () => [{ name: '', path: '' }],
  },
})
</script>

<template>
  <nav class="flex" aria-label="Breadcrumb">
    <ol role="list" class="flex items-center md:space-x-4">
      <li>
        <div>
          <router-link to="/dashboard" class="text-gray-400 hover:text-gray-500">
            <HomeIcon class="h-5 w-5 flex-shrink-0" aria-hidden="true" />
            <span class="sr-only">{{ $t('dashboard') }}</span>
          </router-link>
        </div>
      </li>
      <li v-for="(page, ind) in crumbs" :key="page.name">
        <div class="flex items-center">
          <svg class="h-5 w-5 flex-shrink-0 text-gray-300" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
          </svg>
          <button type="button" class="md:ms-4 ms-1 text-sm font-medium text-gray-500 hover:text-gray-700" :disabled="ind === crumbs.length - 1" @click="$router.push({ path: page.path })">
            {{ $t(`${page.name}`) }}
          </button>
        </div>
      </li>
    </ol>
  </nav>
</template>

<style>

</style>
