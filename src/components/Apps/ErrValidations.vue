<script lang="ts" setup>
import type { PropType } from 'vue'

const props = defineProps({
  errHandle: {
    type: Array as PropType<([])[]>,
    default: () => ([]),
  },
})
const newErr = ref([])
</script>

<template>
  <ul v-if="Object.keys(errHandle).length > 0" class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded relative mb-2 text-sm w-full" role="alert">
    <li v-for="(err, index) in errHandle" :key="index" class="font-bold mt-2">
      <div v-if="Object.values(err).length > 0">
        <li v-for="(smErr, index) in Object.values(err)" :key="index" class="font-bold mt-2">
          {{ smErr }}
        </li>
      </div>
    </li>
  </ul>
</template>
