<script setup lang="ts">
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { TrashIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  apiCall: {
    type: Function,
    default: () => ({}),
  },
  // if pass redirect url , will close modal atomaticly after delete
  redirectUrl: {
    type: String,
  },
  recordId: {
    type: [String, Number],
  },
})
const emits = defineEmits(['closed', 'removed'])
const router = useRouter()
const processing = ref(false)
const deleteRecord = async () => {
  // TODO : [if cond] will be removed when pass redirectUrl as props on all components used this modal
  if (props.recordId) {
    try {
      processing.value = true
      await props.apiCall(props?.recordId)
      props?.redirectUrl && router.push({ path: props.redirectUrl })
      emits('removed')
      emits('closed')
    }
    finally {
      processing.value = false
    }
  }
  else {
    emits('removed')
  }
}
//

const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
</script>

<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" class="relative z-[100]" :dir="getLocale(locale)?.direction" @close="$emit('closed')">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm" />
      </TransitionChild>

      <div class="overflow-y-auto fixed inset-0">
        <div
          class="flex justify-center items-center p-4 min-h-full text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="overflow-hidden p-8 w-full max-w-md text-left align-middle bg-white rounded-3xl border border-gray-100 shadow-2xl transition-all transform"
            >
              <overlay-loader v-if="processing" :full-screen="false" />
              <!-- Modern Icon -->
              <div class="flex flex-col justify-center items-center mb-4">
                <div class="p-3 mb-2 bg-red-100 rounded-full">
                  <TrashIcon class="w-8 h-8 text-red-500" />
                </div>
                <DialogTitle
                  as="h3"
                  class="mb-1 text-xl font-bold text-center text-gray-900"
                >
                  {{ $t('modalHeader.confirmation') }}
                </DialogTitle>
                <p class="mb-2 text-base text-center text-gray-500">
                  <slot />
                </p>
              </div>

              <div class="flex flex-row gap-3 mt-6">
                <BaseButton
                  type="button"
                  class="py-2.5 w-full text-lg font-semibold text-white bg-red-600 shadow-sm transition hover:bg-red-700"
                  custome-bg="bg-red-600"
                  @click="deleteRecord"
                >
                  <span class="flex gap-2 justify-center items-center">
                    {{ $t('settings.teamusers.table.remove') }}
                  </span>
                </BaseButton>
                <BaseButton
                  type="button"
                  class="py-2.5 w-full text-lg font-semibold text-gray-700 bg-gray-300 transition hover:bg-gray-200"
                  custome-bg="bg-gray-400"
                  @click="$emit('closed')"
                >
                  {{ $t('form.cancel') }}
                </BaseButton>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
