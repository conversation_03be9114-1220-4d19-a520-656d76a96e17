<script lang="ts" setup>
import {
  HomeIcon,
  InformationCircleIcon,
  PencilIcon,
  PhoneIcon,
  PlusIcon,
  TrashIcon,
} from '@heroicons/vue/24/outline'
// import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { reactive } from 'vue'
import { useAppsStore } from '@/stores/apps'
import i18n from '@/i18n'

const props = defineProps({
  app: {
    type: Object,
    // type: Object as PropType<AppType>,
    required: true,
  },
})

const emit = defineEmits(['cancel', 'refresh'])
const { getLocale } = storeToRefs(useLocalesStore())

const { locale } = useI18n()

const store = useAppsStore()

const processing = ref(false)

const formData = reactive({})

const installApp = () => {
  const app = props.app

  store
    .installApp(
      app.uuid,
      {
        props_value: unref(formData),
      },
      app.requireOauth,
    )
    .then((res) => {
      if (!app.requireOauth) {
        emit('cancel')
        emit('refresh')
        return
      }

      window.location.href = res
    })
    .finally(() => {
      processing.value = false
    })
}

const unInstallApp = () => {
  store
    .uninstallApp(props.app.uuid)
    .then(() => {
      emit('cancel')
      emit('refresh')
    })
    .finally(() => {
      processing.value = false
    })
}
const installed = computed(() => {
  return Boolean(props.app.installed?.status)
})
const handleSubmit = (): void => {
  processing.value = true
  if (installed.value)
    unInstallApp()
  else installApp()
}

onBeforeMount(() => {
  const fields: string[] = Object.values(props.app.props || {})
  const oldFields = props.app.installed?.props_value

  fields.forEach((item: string) => {
    formData[item] = oldFields ? oldFields[item] || '' : ''
  })
})

const appConfig = ref([])
const loadingConfig = ref(false)

const configForm = reactive({})

const processingConfig = ref(false)

const submitConfig = (): void => {
  processingConfig.value = true

  store
    .submitConfig(props.app.uuid, configForm)
    .then((res) => {
      emit('cancel')
      emit('refresh')
    })
    .finally(() => {
      processingConfig.value = false
    })
}
const configurable = computed(() => {
  return props.app?.configuration?.configurable
})
onMounted(() => {
  if (
    props.app?.installed
    && props.app?.configuration?.configurable
    && appConfig.value.length < 1
  ) {
    loadingConfig.value = true
    store
      .getAppConfigurations(props.app.uuid)
      .then((res) => {
        const configValues = props.app.configuration?.config_values || null
        appConfig.value = res
        res.forEach((item) => {
          configForm[item.name] = configValues[item.name] || item.default || ''
        })
      })
      .finally(() => {
        loadingConfig.value = false
      })
  }
})
</script>

<template>
  <div :dir="getLocale(locale)?.direction">
    <h1 class="text-2xl">
      {{ app.name_localized }}
    </h1>
    <div class="grid grid-cols-1 sm:grid-cols-4">
      <div class="col-span-4 p-8 mt-8 lg:col-span-1 bg-zinc-100">
        <div class="flex flex-col justify-center items-center p-4 bg-white">
          <img
            class="w-12 h-12"
            :src="
              app.banner_url
                || 'https://www.freeiconspng.com/thumbs/message-icon-png/message-icon-png-17.png'
            "
            :alt="app.name_localized"
          >
          <h3 class="text-1xl">
            {{ app.name_localized }}
          </h3>
        </div>
        <div class="mt-4 text-sm text-gray-800">
          <div v-if="app.website_url" class="flex mt-3">
            <svg
              width="24px"
              height="24px"
              viewBox="0 0 64 64"
              xmlns="http://www.w3.org/2000/svg"
              stroke-width="3"
              stroke="#000000"
              fill="none"
            >
              <g id="SVGRepo_bgCarrier" stroke-width="0" />
              <g
                id="SVGRepo_tracerCarrier"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <g id="SVGRepo_iconCarrier">
                <path
                  d="M39.93,55.72A24.86,24.86,0,1,1,56.86,32.15a37.24,37.24,0,0,1-.73,6"
                />
                <path d="M37.86,51.1A47,47,0,0,1,32,56.7" />
                <path
                  d="M32,7A34.14,34.14,0,0,1,43.57,30a34.07,34.07,0,0,1,.09,4.85"
                />
                <path
                  d="M32,7A34.09,34.09,0,0,0,20.31,32.46c0,16.2,7.28,21,11.66,24.24"
                />
                <line x1="10.37" y1="19.9" x2="53.75" y2="19.9" />
                <line x1="32" y1="6.99" x2="32" y2="56.7" />
                <line x1="11.05" y1="45.48" x2="37.04" y2="45.48" />
                <line x1="7.14" y1="32.46" x2="56.86" y2="31.85" />
                <path
                  d="M53.57,57,58,52.56l-8-8,4.55-2.91a.38.38,0,0,0-.12-.7L39.14,37.37a.39.39,0,0,0-.46.46L42,53.41a.39.39,0,0,0,.71.13L45.57,49Z"
                />
              </g>
            </svg>
            <div class="px-2 py-1 underline">
              <a :href="`${app.website_url}`" target="_blank">{{
                app.website_url
              }}</a>
            </div>
          </div>
          <div v-if="app.email" class="flex mt-3">
            <svg
              width="24px"
              height="24px"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g id="SVGRepo_bgCarrier" stroke-width="0" />
              <g
                id="SVGRepo_tracerCarrier"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <g id="SVGRepo_iconCarrier">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M3.75 5.25L3 6V18L3.75 18.75H20.25L21 18V6L20.25 5.25H3.75ZM4.5 7.6955V17.25H19.5V7.69525L11.9999 14.5136L4.5 7.6955ZM18.3099 6.75H5.68986L11.9999 12.4864L18.3099 6.75Z"
                  fill="#000000"
                />
              </g>
            </svg>
            <div class="px-2 py-1 underline">
              <a :href="`mailto:${app.email}`">{{ app.email }}</a>
            </div>
          </div>
          <div v-if="app.phone_number" class="flex mt-3">
            <PhoneIcon class="mx-1 w-5 h-5" />
            <div class="px-1 underline">
              <a :href="`tel:${app.phone_number}`">{{ app.phone_number }}</a>
            </div>
          </div>
          <div v-if="app.licence" class="flex mt-3">
            <svg
              width="24px"
              height="24px"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
              fill="#000000"
            >
              <g id="SVGRepo_bgCarrier" stroke-width="0" />
              <g
                id="SVGRepo_tracerCarrier"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <g id="SVGRepo_iconCarrier">
                <path
                  d="M23 1v18h-3v-1h2V2H2v16h8v1H1V1zm-7 2H8v1h8zm-2 3V5h-4v1zm-7 5H3v1h4zm0 2H3v1h4zm-4 3h2v-1H3zm14-3a2 2 0 1 1-2-2 2.002 2.002 0 0 1 2 2zm-1 0a1 1 0 1 0-1 1 1.001 1.001 0 0 0 1-1zm.002-4.293a.965.965 0 0 0 1.32.55 1.08 1.08 0 0 1 1.213.207 1.066 1.066 0 0 1 .21 1.21.966.966 0 0 0 .548 1.324 1.064 1.064 0 0 1 0 2.004.965.965 0 0 0-.549 1.323A1.05 1.05 0 0 1 18 16.816v7.046l-3-2.538-3 2.538v-7.046a1.05 1.05 0 0 1-.744-1.49.965.965 0 0 0-.549-1.324 1.064 1.064 0 0 1 0-2.004.966.966 0 0 0 .549-1.324 1.066 1.066 0 0 1 .209-1.21 1.08 1.08 0 0 1 1.212-.206.965.965 0 0 0 1.32-.551 1.064 1.064 0 0 1 2.005 0zm.998 13v-5.04a.93.93 0 0 0-.998.625 1.064 1.064 0 0 1-2.004 0 .93.93 0 0 0-.998-.625v5.039l2-1.692zm-1.94-4.749a1.967 1.967 0 0 1 1.853-1.308 2.12 2.12 0 0 1 .87.197l.058-.091a1.964 1.964 0 0 1 1.116-2.695v-.122a1.966 1.966 0 0 1-1.116-2.695l-.087-.084a1.965 1.965 0 0 1-2.694-1.117h-.12a1.965 1.965 0 0 1-2.694 1.117l-.087.084a1.966 1.966 0 0 1-1.116 2.695v.122a1.964 1.964 0 0 1 1.116 2.695l.058.09a2.12 2.12 0 0 1 .87-.196 1.967 1.967 0 0 1 1.853 1.308L15 17z"
                />
                <path fill="none" d="M0 0h24v24H0z" />
              </g>
            </svg>
            <div class="px-4 underline">
              {{ app.licence }}
            </div>
          </div>
        </div>
      </div>
      <div class="col-span-3 pt-10 pb-4 ps-0 pe-0 sm:pe-4 sm:ps-10">
        <div class="flex flex-col mb-6">
          <p
            class="font-bold rtl:text-right ltr:text-left"
            v-html="app.description_localized"
          />
          <p
            class="my-2 lead text-start"
            v-html="app.long_description[i18n.global.locale.value]"
          />
        </div>

        <form
          :class="
            getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left'
          "
          class="flex flex-col gap-4"
          @submit.prevent="handleSubmit"
        >
          <div v-if="!app.requireOauth" class="">
            <div>
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                {{ $t("settings.apps.install.heading") }}
              </h3>
              <p class="mt-1 text-sm text-gray-500">
                {{ $t("settings.apps.install.paragraph") }}
              </p>
            </div>
            <div v-if="app.soon === null">
              <div v-if="app.props.length !== 0">
                <div
                  v-for="(item, index) of app.props"
                  :key="index"
                  class="flex flex-col gap-4 pt-4"
                >
                  <div class="flex flex-col sm:pt-2">
                    <div class="mt-1 sm:col-span-2 sm:mt-0">
                      <TextInput
                        :id="item"
                        v-model="formData[item]"
                        :placeholder="item"
                        :label="item"
                        :required="item.required"
                        class="block w-full rounded-md border-gray-300   focus:border-primary-500 focus:ring-primary-500 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
                        :disabled="installed"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="app.soon === null" class="pt-5">
            <div class="flex justify-end">
              <a
                v-if="app.help_link"
                target="_blank"
                type="button"
                class="flex gap-2 justify-center items-center px-2 py-2 text-sm font-medium text-white bg-green-600 rounded-md shadow-sm transition ltr:mr-6 rtl:ml-6 w-fit group sm:px-4 disabled:bg-green-300 disabled:cursor-not-allowed"
                :processing="processing"
                :href="app.help_link"
              >
                {{ $t("settings.apps.install.help_link") }}
                <InformationCircleIcon class="w-4 h-4" />
              </a>
              <div class="flex gap-5 justify-center items-center">
                <base-button
                  v-if="!installed"
                  type="submit"
                  class="w-40 text-sm"
                  :processing="processing"
                  show-icon
                >
                  {{ $t("settings.apps.install.installBtn") }}
                  {{ app.name[locale] }}
                </base-button>
                <base-button
                  v-else
                  type="submit"
                  class="w-40 text-sm"
                  :processing="processing"
                  show-icon
                  custome-bg="bg-red-600"
                >
                  {{ $t("settings.apps.install.uninstall") }}
                </base-button>
              </div>
            </div>
          </div>
        </form>

        <form
          v-if="configurable"
          :class="
            getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left'
          "
          class="flex flex-col gap-4"
          @submit.prevent="submitConfig"
        >
          <hr class="my-6">
          <div>
            <h3 class="text-lg font-medium leading-6 text-gray-900">
              {{ $t("settings.apps.configure") }}
            </h3>
          </div>
          <the-loader v-if="loadingConfig" />
          <div v-else>
            <div
              v-for="(input, idx) of appConfig"
              :key="`config-input-${input.name}-${idx}`"
              class="flex flex-col gap-4 pt-4"
            >
              <label-input>{{ input.label_ar }}</label-input>
              <select-input
                v-model="configForm[input.name]"
                :name="input.name"
                required="required"
              >
                <option value="">
                  {{ $t("form.select") }}
                </option>
                <option
                  v-for="opt of input.values_source"
                  :key="`opt-${opt.value}`"
                  :value="opt.value"
                >
                  {{ opt.label }}
                </option>
              </select-input>
            </div>

            <div class="flex gap-5 justify-end items-center mt-3">
              <base-button
                type="submit"
                class="w-40 text-sm"
                :processing="processingConfig"
                show-icon
                custome-bg="bg-red-600"
              >
                {{ $t("settings.apps.save_config") }}
              </base-button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
