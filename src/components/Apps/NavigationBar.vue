<script lang="ts" setup>
import {
  Dialog,
  DialogPanel,
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  ArrowTrendingUpIcon,
  BanknotesIcon,
  Bars3BottomLeftIcon,
  BellIcon,
  BookmarkSquareIcon,
  CalendarIcon,
  ChatBubbleBottomCenterIcon,
  ChatBubbleBottomCenterTextIcon,
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ClockIcon,
  Cog8ToothIcon,
  CreditCardIcon,
  DocumentChartBarIcon,
  FolderIcon,
  FolderOpenIcon,
  InboxIcon,
  KeyIcon,
  PencilSquareIcon,
  ShoppingCartIcon,
  TagIcon,
  UserGroupIcon,
  UsersIcon,
  XMarkIcon,
} from '@heroicons/vue/24/outline'

import { storeToRefs } from 'pinia'
import IconBranch from '../Icons/Branch.vue'
import IconRole from '../Icons/Role.vue'
import customField from '../Icons/CustomField.vue'
import DashboardIcon from '../Icons/DashboardIcon.vue'
import CalendarsIcon from '../Icons/CalendarsIcon.vue'
import AnalyticsIcon from '../Icons/AnalyticsIcon.vue'
import BoundariesIcon from '../Icons/BoundariesIcon.vue'
import CampaignsIcon from '../Icons/CampaignsIcon.vue'
import CustomersIcon from '../Icons/CustomersIcon.vue'
import CustomPagesIcon from '../Icons/CustomPagesIcon.vue'
import GeneralPanelIcon from '../Icons/GeneralPanelIcon.vue'
import NotificationsIcon from '../Icons/NotificationsIcon.vue'
import POSIcon from '../Icons/POSIcon.vue'
import ProvidersIcon from '../Icons/ProvidersIcon.vue'
import PurchasedPackagesIcon from '../Icons/PurchasedPackagesIcon.vue'
import RefundedOrdersIcon from '../Icons/RefundedOrdersIcon.vue'
import ReportsIcon from '../Icons/ReportsIcon.vue'
import BookingsIcon from '../Icons/BookingsIcon.vue'
import OffersIcon from '../Icons/OffersIcon.vue'
import OrdersIcon from '../Icons/OrdersIcon.vue'
import ReviewsIcon from '../Icons/ReviewsIcon.vue'
import ServicesIcon from '../Icons/ServicesIcon.vue'
import StorePanelIcon from '../Icons/StorePanelIcon.vue'
import TransactionsIcon from '../Icons/TransactionsIcon.vue'
import CouponsIcon from '../Icons/CouponsIcon.vue'
import CategoriesIcon from '../Icons/CategoriesIcon.vue'
import ShareIcon from '../Icons/ShareIcon.vue'
import PricingIcon from '../Icons/PricingIcon.vue'

import Manage from '../Icons/Manage.vue'
import SubscribeStatus from '../auth/SubscribeStatus.vue'
import useNotifications from '@/composables/useNotifications'

const props = defineProps({
  showsidebar: {
    type: Boolean,
    default: false,
  },
  isOpenLargeNav: {
    type: Boolean,
    default: false,
  },
  planDetails: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['closeNav', 'languageChanged', 'toggle-large-nav'])
const iconMapper = {
  ShareIcon,
  CategoriesIcon,
  BanknotesIcon,
  Bars3BottomLeftIcon,
  BellIcon,
  CalendarIcon,
  ChatBubbleBottomCenterIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ClockIcon,
  Cog8ToothIcon,
  CreditCardIcon,
  FolderIcon,
  FolderOpenIcon,
  ChatBubbleBottomCenterTextIcon,
  DashboardIcon,
  CalendarsIcon,
  AnalyticsIcon,
  BoundariesIcon,
  CampaignsIcon,
  CustomersIcon,
  CustomPagesIcon,
  GeneralPanelIcon,
  NotificationsIcon,
  POSIcon,
  ProvidersIcon,
  PurchasedPackagesIcon,
  RefundedOrdersIcon,
  ReportsIcon,
  InboxIcon,
  KeyIcon,
  PencilSquareIcon,
  TagIcon,
  BookmarkSquareIcon,
  UserGroupIcon,
  UsersIcon,
  XMarkIcon,
  ArrowTrendingUpIcon,
  IconRole,
  IconBranch,
  customField,
  Manage,
  DocumentChartBarIcon,
  ShoppingCartIcon,
  BookingsIcon,
  OffersIcon,
  OrdersIcon,
  ReviewsIcon,
  ServicesIcon,
  TransactionsIcon,
  StorePanelIcon,
  CouponsIcon,
  PricingIcon,
}
interface NavigationInt {
  name: string
  href: string
  icon: any
  current: boolean
}
const {
  userInfo,
  getUnReadOrders,
  getUnReadTransactions,
  getUnReadComments,
  getUnReadReviews,
  tenantActiveteWhatsapp,
} = storeToRefs(useAuthStore())

const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { getNavigationRoutes } = storeToRefs(useRoutesStore())
const { showNotification } = useNotifications()

const closeNav = () => {
  emit('closeNav')
}
const hasConfirmedOrders = computed(() => {
  return getUnReadOrders.value > 0
})
const processing = ref(false)

const closeLargeNav = () => {
  emit('toggle-large-nav')
  localStorage.setItem('isOpenLargeNav', String(!props.isOpenLargeNav))
}

const copyStoreUrl = () => {
  const storeUrl = userInfo.value?.tenant?.booking_page?.full_domain
  if (storeUrl) {
    navigator.clipboard.writeText(storeUrl)
      .then(() => {
        showNotification({
          type: 'success',
          title: 'تم النسخ',
          message: 'تم نسخ رابط المتجر بنجاح',
        })
      })
      .catch((err) => {
        showNotification({
          type: 'error',
          title: 'خطأ',
          message: 'فشل نسخ الرابط',
        })
      })
  }
}
const router = useRouter()
const goToSubscription = () => {
  if (props.planDetails.planName === 'Trial')
    router.push('/settings/subscriptions-panel/plans')
  else if (props.planDetails.show_renew_message)
    router.push({ name: 'current-plan', query: { renewNow: true } })

  else
    router.push({ name: 'current-plan' })

  closeNav()
}
</script>

<template class="font-tajawal">
  <overlay-loader v-if="processing" />

  <!-------------------------------------------- Sidebar for small screens -------------------------------------------- -->
  <TransitionRoot as="template" :show="showsidebar">
    <Dialog as="div" class="relative z-50 lg:hidden" @close="closeNav">
      <TransitionChild
        as="template"
        enter="transition-opacity ease-linear duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="transition-opacity ease-linear duration-300"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-900/80" />
      </TransitionChild>

      <div
        class="flex fixed inset-0"
        :dir="getLocale(locale)?.direction === 'rtl' ? 'rtl' : 'ltr'"
      >
        <TransitionChild
          as="template"
          enter="transition ease-in-out duration-300 transform"
          enter-to="translate-x-0"
          leave="transition ease-in-out duration-300 transform"
          leave-from="translate-x-0"
          :enter-from="
            getLocale(locale)?.direction === 'rtl'
              ? 'translate-x-full'
              : '-translate-x-full'
          "
          :leave-to="
            getLocale(locale)?.direction === 'rtl'
              ? 'translate-x-full'
              : '-translate-x-full'
          "
        >
          <DialogPanel
            class="relative flex flex-col w-full max-w-[280px] h-full bg-[#EDF0FF]"
          >
            <!-- Logo -->
            <div class="flex flex-row justify-between items-center px-4 py-4">
              <img
                class="w-auto h-8"
                src="../../assets/logo.svg"
                alt="Your Company"
              >
              <button
                type="button"
                class="p-2 rounded-md transition-colors hover:bg-primary-200/10"
                @click="closeNav"
              >
                <XMarkIcon class="w-6 h-6 text-[#082F49]" aria-hidden="true" />
              </button>
            </div>
            <!-- Store URL section -->
            <div v-if="userInfo.tenant?.booking_page?.full_domain" class="px-4 mt-2">
              <div class="inline-flex flex-col gap-6 justify-start items-start self-stretch">
                <div class="inline-flex overflow-hidden gap-1 justify-end items-center self-stretch h-8">
                  <div class="flex-1 h-8 bg-gradient-to-l from-sky-400 to-green-300 rounded outline outline-1 outline-offset-[-1px] outline-sky-400 flex justify-start items-center overflow-hidden">
                    <div class="w-h-8">
                      <img
                        v-if="userInfo.tenant.logo"
                        :src="userInfo.tenant.logo"
                        class="object-contain w-h-square"
                        alt="Store Logo"
                      >
                      <img
                        v-else
                        src="../../assets/no-image.jpg"
                        class="object-contain w-h-square"
                        alt="Default Store Logo"
                      >
                    </div>

                    <a
                      :href="userInfo.tenant.booking_page.full_domain"
                      target="_blank"
                      class="flex flex-1 gap-0.5 justify-end items-center self-stretch px-1 py-0.5 bg-indigo-50"
                    >
                      <div class="ps-1 w-20 text-right text-sm font-['Tajawal'] leading-none tracking-wide">
                        {{ $t('altNav.visit_store') }}
                      </div>
                      <ChevronLeftIcon v-if="getLocale(locale)?.direction === 'rtl'" class="w-2 h-2" />
                      <ChevronRightIcon v-else class="w-2 h-2" />
                    </a>
                  </div>
                  <div
                    class="flex gap-2.5 justify-center items-center self-stretch p-1 w-8 bg-gradient-to-l from-sky-400 to-green-300 rounded cursor-pointer hover:opacity-90"
                    role="button"
                    :title="$t('copy_store_url')"
                    @click="copyStoreUrl"
                  >
                    <component
                      :is="iconMapper.ShareIcon"
                      class="w-4 h-4 text-white"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Navigation -->
            <div class="overflow-y-auto flex-1 px-4 pb-4 mt-4">
              <nav class="flex flex-col gap-y-4">
                <ul role="list" class="-mx-2 space-y-4">
                  <li v-for="route in getNavigationRoutes" :key="route.name">
                    <div>
                      <router-link
                        v-if="!route.meta?.hasChildren"
                        :to="{ path: route.to }"
                        class="flex gap-2 py-2 text-base leading-6 capitalize rounded-md text-secondary hover:bg-primary-200"
                        :class="{
                          'bg-primary-200 text-white':
                            $route.meta?.activeRoute?.includes(route.name),
                        }"
                        @click="emit('closeNav')"
                      >
                        <component
                          :is="iconMapper[route.meta?.icon]"
                          class="w-5 h-5"
                          :class="{
                            'text-white': $route.meta?.activeRoute?.includes(route.name),
                          }"
                        />
                        {{ $t(`altNav.${route.name}`) }}
                        <span
                          v-if="
                            route.name == 'orders'
                              && hasConfirmedOrders
                              && getUnReadOrders > 0
                          "
                          class="px-2 py-1 ml-auto text-[#082F49] bg-red-800 rounded-full rtl:mr-auto rtl:ml-0"
                        >
                          {{ getUnReadOrders }}
                        </span>
                        <span
                          v-if="route.name == 'mersal-whatsapp'"
                          class="flex justify-center items-center ml-auto w-6 h-6 rtl:mr-auto rtl:ml-0"
                        >
                          <div
                            class="whatsapp-status-circle"
                            :class="{
                              connected: tenantActiveteWhatsapp === 'connected',
                              disconnected: tenantActiveteWhatsapp === 'disconnected',
                            }"
                          />
                        </span>
                      </router-link>

                      <Disclosure v-else v-slot="{ open }" as="div">
                        <DisclosureButton
                          class="flex gap-x-3 justify-between items-center px-3 py-2 w-full text-base leading-6 capitalize rounded-md text-secondary hover:bg-primary-200"
                          :class="[
                            getLocale(locale)?.direction === 'rtl'
                              ? 'flex-row'
                              : 'flex-row-reverse',
                          ]"
                        >
                          <ChevronRightIcon
                            v-if="getLocale(locale)?.direction === 'ltr'"
                            class="w-5 h-5 transition-transform duration-200 shrink-0"
                            :class="[open ? 'rotate-90 text-gray-500' : 'text-gray-400']"
                            aria-hidden="true"
                          />
                          <div class="flex flex-1 gap-3 items-center">
                            <component
                              :is="iconMapper[route.meta?.icon]"
                              class="w-5 h-5"
                            />
                            {{ $t(`altNav.${route.name}`) }}
                          </div>
                          <ChevronLeftIcon
                            v-if="getLocale(locale)?.direction === 'rtl'"
                            class="w-5 h-5 transition-transform duration-200 shrink-0"
                            :class="[open ? 'rotate-90 text-gray-500' : 'text-gray-400']"
                            aria-hidden="true"
                          />
                        </DisclosureButton>
                        <DisclosurePanel class="mt-1 space-y-1">
                          <router-link
                            v-for="subRoute in route.children.filter(
                              (sub) => sub.meta?.showInSidebar,
                            )"
                            :key="subRoute.name"
                            :to="{ path: subRoute.to }"
                            class="flex items-center gap-3 px-8 py-2 text-sm text-[#082F49] capitalize rounded-md hover:bg-primary-200 transition-colors"
                            :class="{
                              'bg-primary-200 text-white':
                                $route.meta?.activeRoute?.includes(subRoute.name),
                            }"
                            @click="emit('closeNav')"
                          >
                            <component
                              :is="iconMapper[subRoute.meta.icon]"
                              v-if="subRoute.meta?.icon"
                              class="w-5 h-5"
                              :class="{
                                'text-white': $route.meta?.activeRoute?.includes(subRoute.name),
                              }"
                            />
                            {{ $t(`altNav.${subRoute.name}`) }}
                          </router-link>
                        </DisclosurePanel>
                      </Disclosure>
                    </div>
                  </li>
                </ul>
              </nav>
            </div>

            <!-- Subscription Progress Section -->
            <li class="px-4 py-4 border-t border-gray-200">
              <button
                class="subscription-link flex flex-col gap-2 p-4 bg-[#082F49] rounded-xl w-full"
                @click="goToSubscription"
              >
                <SubscribeStatus :plan-details="planDetails" />
              </button>
            </li>
          </DialogPanel>
        </TransitionChild>
      </div>
    </Dialog>
  </TransitionRoot>

  <!-------------------------------------------- Static sidebar for desktop -------------------------------------------- -->
  <div
    class="hidden top-0 right-0 bottom-0 transition-all duration-200 ease-in lg:fixed lg:inset-y-0 lg:z-20 lg:flex lg:flex-col ltr:left-0"
    :dir="getLocale(locale)?.direction"
    :class="{
      'lg:w-56': isOpenLargeNav,
      'lg:w-14': !isOpenLargeNav,
    }"
  >
    <div
      class="flex overflow-y-auto flex-col gap-y-5 px-4 text-black border-l border-gray-200 grow bg-[#EDF0FF]"
    >
      <div class="flex relative justify-start items-center mt-8 w-full h-10 shrink-0">
        <img
          v-if="isOpenLargeNav"
          class="w-auto h-8 me-auto"
          src="../../assets/logo.svg"
          alt="Your Company"
        >
        <!-- scall 1.1 -->
        <button
          type="button"
          class="absolute top-1/2 p-1 mx-auto rounded-full border-2 transition-transform duration-200 transform -translate-y-1/2 rtl:-translate-x-1/2 rtl:left-2 border-primary-100 hover:bg-primary-200 hover:scale-110 ltr:-right-2"

          @click="closeLargeNav"
        >
          <span class="ltr:hidden">
            <ChevronDoubleRightIcon
              v-if="isOpenLargeNav"
              class="w-6 h-6 text-secondary"
              aria-hidden="true"
            />
            <ChevronDoubleLeftIcon
              v-else
              class="w-6 h-6 text-secondary"
              aria-hidden="true"
            />
          </span>
          <span class="rtl:hidden">
            <ChevronDoubleLeftIcon
              v-if="isOpenLargeNav"
              class="w-6 h-6 text-secondary"
              aria-hidden="true"
            />
            <ChevronDoubleRightIcon
              v-else
              class="w-6 h-6 text-secondary"
              aria-hidden="true"
            />
          </span>
        </button>
      </div>
      <div v-if="isOpenLargeNav" class="flex items-center">
        <div class="inline-flex flex-col gap-6 justify-start items-start self-stretch">
          <div class="inline-flex overflow-hidden gap-1 justify-end items-center self-stretch h-8">
            <div class="flex-1 h-8 bg-gradient-to-l from-sky-400 to-green-300 rounded outline outline-1 outline-offset-[-1px] outline-sky-400 flex justify-start items-center overflow-hidden">
              <div class="w-h-8">
                <img
                  v-if="userInfo.tenant.logo"
                  :src="userInfo.tenant.logo"
                  class="object-contain w-h-square"
                  alt="Store Logo"
                >
                <img
                  v-else
                  src="../../assets/no-image.jpg"
                  class="object-contain w-h-square"
                  alt="Default Store Logo"
                >
              </div>

              <a
                :href="userInfo.tenant.booking_page.full_domain"
                target="_blank"
                class="flex flex-1 gap-0.5 justify-end items-center self-stretch px-1 py-0.5 bg-indigo-50"
              >
                <div class="ps-1 w-20 text-right    text-sm    font-['Tajawal'] leading-none tracking-wide">
                  {{ $t('altNav.visit_store') }}
                </div>
                <ChevronLeftIcon v-if="getLocale(locale)?.direction === 'rtl'" class="w-2 h-2" />
                <ChevronRightIcon v-else class="w-2 h-2" />

              </a>
            </div>
            <div
              class="flex gap-2.5 justify-center items-center self-stretch p-1 w-8 bg-gradient-to-l from-sky-400 to-green-300 rounded cursor-pointer hover:opacity-90"
              role="button"
              :title="$t('copy_store_url')"
              @click="copyStoreUrl"
            >
              <component
                :is="iconMapper.ShareIcon"
                class="w-4 h-4 text-white"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- <a :href="accountSetting.full_domain"
        target="_blank"
        class=" hover:!bg-transparent flex justify-start align-center
        text-secondary font-normal gap-2"
        >
          <LinkIcon class="w-5 h-5 text-secondary"/>
            Booking Page
        </a>  -->
      <nav class="flex flex-col flex-1">
        <ul role="list" class="flex flex-col flex-1 gap-y-7">
          <li>
            <ul role="list" class="-mx-2 space-y-3">
              <li v-for="route in getNavigationRoutes" :key="route.name">
                <div>
                  <template v-if="route.meta?.hasChildren">
                    <!-- Show parent and disclosure only when sidebar is expanded -->
                    <Disclosure v-if="isOpenLargeNav" v-slot="{ open }" as="div">
                      <DisclosureButton
                        class="flex gap-x-3 justify-between items-center px-3 py-2 w-full text-base leading-6 capitalize rounded-md text-secondary rtl:flex-row ltr:flex-row-reverse hover:bg-primary-200"
                      >
                        <ChevronRightIcon
                          v-if="getLocale(locale)?.direction === 'ltr'"
                          class="w-5 h-5 shrink-0"
                          :class="[open ? 'rotate-90 text-gray-500' : 'text-gray-400']"
                          aria-hidden="true"
                        />
                        <span class="flex gap-2 items-center">
                          <component
                            :is="iconMapper[route.meta?.icon]"
                            class="w-5 h-5"
                          />
                          {{ $t(`altNav.${route.name}`) }}
                        </span>
                        <ChevronLeftIcon
                          v-if="getLocale(locale)?.direction === 'rtl'"
                          class="w-5 h-5 shrink-0"
                          :class="[open ? 'rotate-90 text-gray-500' : 'text-gray-400']"
                          aria-hidden="true"
                        />
                      </DisclosureButton>
                      <DisclosurePanel as="ul" class="flex flex-col gap-2 p-1 mt-1">
                        <li
                          v-for="subRoute in route.children.filter(
                            (sub) => sub.meta?.showInSidebar,
                          )"
                          :key="subRoute.name"
                          class="rounded-md transition-all hover:bg-primary-200 link"
                        >
                          <router-link
                            :to="{ path: subRoute.to }"
                            class="flex relative gap-2 items-center px-4 py-2 rounded-md transition-all hover:bg-primary-200"
                            :class="{
                              'router-link-active router-link-exact-active':
                                $route.meta?.activeRoute?.includes(subRoute.name),
                            }"
                          >
                            <component
                              :is="iconMapper[subRoute.meta.icon]"
                              v-if="subRoute.meta?.icon"
                              class="w-5 h-5"
                            />

                            {{ $t(`altNav.${subRoute.name}`) }}
                          </router-link>
                        </li>
                      </DisclosurePanel>
                    </Disclosure>
                    <!-- Show only children when sidebar is collapsed -->
                    <ul v-else class="flex flex-col gap-1 items-center py-2 w-full">
                      <li
                        v-for="subRoute in route.children.filter(
                          (sub) => sub.meta?.showInSidebar,
                        )"
                        :key="subRoute.name"
                        class="w-full"
                      >
                        <router-link
                          :to="subRoute.to"
                          class="flex relative justify-center items-center p-2 w-full rounded-md transition-all hover:bg-primary-200 group"
                          :class="{
                            'bg-primary-200':
                              $route.meta?.activeRoute?.includes(subRoute.name),
                          }"
                        >
                          <component
                            :is="iconMapper[subRoute.meta.icon]"
                            v-if="subRoute.meta?.icon"
                            class="w-5 h-5 text-secondary group-hover:text-white shrink-0"
                          />
                        </router-link>
                      </li>
                    </ul>
                  </template>
                  <template v-else>
                    <!-- Regular route without children -->
                    <router-link
                      :to="{ path: route.to }"
                      class="block py-2 text-base leading-6 capitalize rounded-md transition-colors text-secondary hover:bg-primary-200 hover:text-white"
                      :class="{
                        'router-link-active router-link-exact-active bg-primary-200 text-white':
                          $route.meta?.activeRoute?.includes(route.name),
                      }"
                    >
                      <span
                        class="flex items-center px-2 rounded-md transition-all" :class="[
                          isOpenLargeNav ? 'justify-start gap-2' : 'justify-center',
                        ]"
                      >
                        <component
                          :is="iconMapper[route.meta?.icon]"
                          class="w-5 h-5 transition-colors text-secondary shrink-0 group-hover:text-white"
                          :class="{
                            'text-white': $route.meta?.activeRoute?.includes(route.name),
                          }"
                        />
                        <span
                          v-if="isOpenLargeNav"
                          class="whitespace-nowrap transition-colors"
                          :class="{
                            'text-white': $route.meta?.activeRoute?.includes(route.name),
                          }"
                        >{{ $t(`altNav.${route.name}`) }}</span>

                        <!-- WhatsApp Status Circle for Desktop -->
                        <span
                          v-if="route.name === 'mersal-whatsapp'"
                          class="flex justify-center items-center ml-auto w-6 h-6 rtl:mr-auto rtl:ml-0"
                        >
                          <div
                            class="whatsapp-status-circle"
                            :class="{
                              connected: tenantActiveteWhatsapp === 'connected',
                              disconnected: tenantActiveteWhatsapp === 'disconnected',
                            }"
                          />
                        </span>
                      </span>
                    </router-link>
                  </template>
                </div>
              </li>
            </ul>
          </li>

          <!-- Subscription Progress Section -->
          <li v-if="isOpenLargeNav" class="mt-auto mb-4">
            <button
              class="subscription-link flex flex-col gap-2 p-4 bg-[#082F49] rounded-xl w-full"
              @click="goToSubscription"
            >
              <SubscribeStatus :plan-details="planDetails" />
            </button>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</template>

<style lang="css" scoped>
.sidebar {
  min-height: 100vh;
  overflow-y: scroll;
  overflow-x: hidden;
}

.sidebar::-webkit-scrollbar {
  width: 5px;
}

.sidebar::-webkit-scrollbar-track {
  background: #152433;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #93a4b5a1;
  border-radius: 20px;
  padding: 5px;
}

.branch-item:nth-last-child(1) {
  border-bottom: 0px !important;
}

.nav-active.router-link-active {
  @apply bg-gray-900;
}

.dialog {
  height: 100vh;
}

a:hover,
a.router-link-active,
a.router-link-exact-active {
  @apply bg-primary-200;
}

nav.nav-bar a.router-link-active svg,
nav.nav-bar a.router-link-exact-active svg {
  @apply text-secondary;
}

/* Ensure SVG icons are visible */
svg {
  display: block;
  visibility: visible;
  opacity: 1;
}

/* Fix for SVG icon colors */
.router-link-active svg,
.router-link-exact-active svg,
a:hover svg {
  @apply text-white;
}

/* Standardize icon sizes */
:deep(svg) {
  width: 20px !important;
  height: 20px !important;
  min-width: 20px;
  min-height: 20px;
}

/* Ensure proper icon alignment in collapsed state */
:deep(.w-16) ul {
  @apply px-0;
}

:deep(.w-16) .router-link-active,
:deep(.w-16) .router-link-exact-active {
  @apply bg-primary-200;
}

:deep(.w-16) svg {
  @apply mx-auto;
}

/* Ensure proper spacing between icons */
:deep(.w-16) li + li {
  @apply mt-1;
}

/* Override any default icon sizes */
:deep(.h-5),
:deep(.w-5) {
  width: 20px !important;
  height: 20px !important;
}

/* Ensure consistent sizing for HeroIcons */
:deep(.w-5.h-5) {
  width: 20px !important;
  height: 20px !important;
}

/* Fix text alignment in expanded state */
.router-link-active span,
.router-link-exact-active span {
  @apply items-center;
}

/* Fix hover/focus states for both icons and text */
.router-link-active,
.router-link-exact-active,
a:hover {
  @apply text-white bg-primary-200 whitespace-nowrap;
}

.router-link-active svg,
.router-link-exact-active svg,
a:hover svg {
  @apply text-white;
}

/* Ensure hover effect on parent hover */
a:hover svg,
a:focus svg,
a:hover span,
a:focus span {
  @apply text-white;
}

/* Active state should always show white icon and text */
.router-link-active svg,
.router-link-exact-active svg,
.router-link-active span,
.router-link-exact-active span {
  @apply text-white;
}

/* Adjust padding in collapsed state */
:deep(.w-14) ul {
  @apply px-0;
}

/* Ensure proper icon alignment in smaller width */
:deep(.w-14) svg {
  @apply mx-auto;
}

/* Reset general nav styles for subscription section */
.subscription-link {
  @apply hover:bg-[#082F49] transition-colors;
  background-color: #051C2C;
}

.subscription-link:hover,
.subscription-link.router-link-active,
.subscription-link.router-link-exact-active {
  background-color: #051C2C;
}

.subscription-link:hover .subscription-icon,
.subscription-link.router-link-active .subscription-icon,
.subscription-link.router-link-exact-active .subscription-icon {
  @apply text-white;
}

/* Override any inherited nav styles */
.subscription-link:hover span,
.subscription-link:focus span,
.subscription-link.router-link-active span,
.subscription-link.router-link-exact-active span {
  @apply text-white;
}

/* WhatsApp Status Circle Animations */
.whatsapp-status-circle {
  position: relative;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.whatsapp-status-circle.connected {
  background-color: #10B981;
  box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  animation: whatsapp-pulse-connected 2s infinite;
}

.whatsapp-status-circle.disconnected {
  background-color: #EF4444;
  box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  animation: whatsapp-pulse-disconnected 2s infinite;
}

.whatsapp-status-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  opacity: 0.6;
  animation: whatsapp-ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.whatsapp-status-circle.connected::before {
  background-color: #10B981;
}

.whatsapp-status-circle.disconnected::before {
  background-color: #EF4444;
}

@keyframes whatsapp-pulse-connected {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

@keyframes whatsapp-pulse-disconnected {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

@keyframes whatsapp-ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.progress-circle {
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
  transition: stroke-dasharray 0.3s ease;
}

.radial-progress {
  --size: 5rem;
  --thickness: 5px;
  width: var(--size);
  height: var(--size);
  border-radius: 9999px;
  position: relative;
  display: grid;
  place-items: center;
  background: #051C2C;
}

.radial-progress::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 9999px;
  border: var(--thickness) solid rgba(255, 255, 255, 0.1);
}

.radial-progress::after {
  content: "";
  position: absolute;
  inset: calc(-1 * var(--thickness));
  border-radius: 9999px;
  background: conic-gradient(#38BDF8 var(--progress), transparent 0deg);
  -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - var(--thickness)), #000 calc(100% - var(--thickness)));
  mask: radial-gradient(farthest-side, transparent calc(100% - var(--thickness)), #000 calc(100% - var(--thickness)));
}
</style>
