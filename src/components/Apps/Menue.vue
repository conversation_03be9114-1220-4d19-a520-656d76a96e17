<script lang="ts" setup>
import {
  Dialog,
  DialogPanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { AUTH_TOKEN } from '@/constants'

const router = useRouter()
const { logout } = useAuthStore()
const userData = useAuthStore()
const teamStore = useTeamStore()
const authStore = useAuthStore()
const switchToTeam = (team: { current: boolean; uuid: string }) => {
  if (team.current)
    return

  teamStore.swithTeams(team.uuid).finally(() => {
    router.go(0)
  })
}
onMounted(async () => {
  if (userData.userInfo?.uuid !== null)
    await teamStore.fetchTeams()
})

const userNavigation = [
  { name: 'homepage.profile', href: 'profile' },
  { name: 'homepage.subscription', href: 'plans' },
  { name: 'listing.setting', href: 'settings' },
]

const logmeout = async () => {
  logout().then(() => {
    localStorage.removeItem('userAuth')
    localStorage.removeItem(AUTH_TOKEN)
    router.push({ name: 'auth', query: { section: 'sign-in' } })
  })
}
</script>

<template>
  <div class="flex items-center ms-4 md:ms-6">
    <!-- <LanguageSwitcher class="pt-1" /> -->

    <!-- Profile dropdown -->
    <Menu as="div" class="relative ms-3 text-center ">
      <div>
        <MenuButton
          class="flex items-center max-w-xs text-sm bg-white rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        >
          <span class="sr-only">Open user menu</span>
          <span class="inline-block h-8 w-8 overflow-hidden rounded-full bg-gray-100">
            <svg
              v-if="(userData.getUserInfo.profile_photo === null)"
              class="w-full h-full text-gray-300"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"
              />
            </svg>
            <img
              v-else
              :src="userData.getUserInfo.profile_photo" alt="user profile img"
            >
          </span>
        </MenuButton>
      </div>
      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <MenuItems
          class="absolute text-start z-10 w-48 py-1 mt-2 origin-top-right bg-white rounded-md shadow-lg end-0 ring-1 ring-black ring-opacity-5 focus:outline-none border-b px-2"
        >
          <MenuItem
            v-for="item in userNavigation"
            :key="item.name"
            v-slot="{ active }"
          >
            <router-link
              v-if="item.href !== 'sub'"
              :to="{ name: item.href }"
              class="block py-2 text-sm text-gray-700 ps-4 pe-4"
              :class="[active ? 'bg-gray-100' : '']"
            >
              {{ $t(item.name) }}
            </router-link>
            <a
              v-else
              :href="item.link"
              class="block py-2 text-sm text-gray-700 ps-4 pe-4"
              :class="[active ? 'bg-gray-100' : '']"
            >
              {{ $t(item.name) }}
            </a>
          </MenuItem>
          <!-- <div class="border-t border-b">
            <MenuItem
              v-for="item in teamStore.getTeams"
              :key="item.uuid"
            >
              <a
                class="branch-item block py-2 text-sm text-gray-700 ps-4 pe-4 cursor-pointer  px-2"
                @click.prevent="switchToTeam(item)"
              >
                <p class="flex flex-row-reverse justify-end">
                  <svg
                    v-show="item.current"
                    class="mr-2 h-5 w-5 text-green-400"
                    fill="none"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  ><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                  {{ item.name }}
                </p>
              </a>
            </MenuItem>
          </div> -->
          <MenuItem>
            <a
              href="#"
              class="block py-2 text-sm text-red-700 ps-4 pe-4"
              @click.prevent="logmeout"
            >
              {{ $t("form.signout") }}
            </a>
          </MenuItem>
        </MenuItems>
      </transition>
    </Menu>
  </div>
</template>

