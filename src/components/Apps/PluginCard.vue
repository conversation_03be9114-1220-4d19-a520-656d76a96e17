<script setup lang="ts">
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { Switch } from '@headlessui/vue'
import type { AppType } from '@/types'
import Noimg from '@/assets/no-image.jpg'
const props = defineProps({
  app: {
    type: Object as PropType<AppType>,
    required: true,
  },
})

const emit = defineEmits(['install'])
const installed = computed(() => {
  return Boolean(props.app.installed)
})
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
</script>

<template>
  <div class="grid grid-cols-1">
    <div
      class="relative p-4 transition-all duration-300 bg-white rounded-lg cursor-pointer shadow-neutral-300 shadow-3xl hover:shadow"
      :class="[!!app?.installed ? 'border-teal-500' : 'border-gray-200']"
    >
      <div
        v-if="app?.soon" class="absolute px-3 py-1 text-sm text-white bg-gray-700 rounded-md -top-2"
        :class="[getLocale(locale)?.direction === 'rtl' ? '-left-2' : '-right-2']"
      >
        {{ $t("soon") }} ⚡️
      </div>
      <div class="flex justify-between ">
        <div class="w-3/4">
          <h5 class="mb-2 font-bold tracking-tight text-gray-900 text-1xl">
            {{ app?.name_localized }}
          </h5>
          <p class="text-sm text-neutral-400">
            {{ app?.description_localized }}
          </p>
        </div>
        <div class="flex justify-between">
          <Switch
            id="installed" v-model="installed"
            class="relative inline-flex flex-shrink-0 h-6 mt-1 transition-colors duration-200 ease-in-out border-2 border-transparent rounded-full cursor-pointer w-11 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            :class="[
              installed ? 'bg-primary-600' : 'bg-gray-200',
            ]" @click="$emit('toggleActive')"
          >
            <span
              aria-hidden="true"
              class="inline-block w-5 h-5 transition duration-200 ease-in-out transform bg-white rounded-full shadow pointer-events-none ring-0"
              :class="[
                installed
                  ? 'translate-x-0'
                  : 'translate-x-5 rtl:-translate-x-5',
              ]"
            />
          </Switch>
        </div>
      </div>
    </div>
  </div>
</template>
