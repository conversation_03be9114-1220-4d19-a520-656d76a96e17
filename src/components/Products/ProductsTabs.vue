<script setup lang="ts">
import { ref, watch } from 'vue'
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/vue'
import type { PropType } from '@vue/runtime-core'
import type { Service } from '@/types'

const props = defineProps({
  service: {
    type: Object as PropType<Service>,
    default: null,
  },
})

const selectedTab = ref()

const tabs = ['staffs']

watch(() => props.service, (service) => {
  service && (selectedTab.value = tabs[0])
})
</script>

<template>
  <div>
    <div />
    <h2 class="text-2xl mb-4 font-semibold">
      {{ $t(`services.staffs`) }}
    </h2>
    <ServicesTabStaffs v-if="service" :service="service" />
  </div>
</template>

