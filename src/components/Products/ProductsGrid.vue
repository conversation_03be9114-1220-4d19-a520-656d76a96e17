<script setup lang="ts">
import { PhotoIcon } from '@heroicons/vue/24/outline'
import type { PropType } from 'vue'
import type { Products } from '@/types/products'
const props = defineProps({
  productsList: {
    type: Array as PropType<Products[]>,
    default: () => [],
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
})
const router = useRouter()
const redirectToProduct = (product: Products) => {
  router.push({ name: 'product', params: { id: product.uuid } })
}
const slot = useSlots()
const { t } = useI18n()
const hasActions = computed(() => Boolean(slot?.actions))
const headers = [
  {
    title: t('form.name'),
  },

  {
    title: t('form.category'),
  },
  {
    title: t('form.sort_order'),
  },
  {
    title: t('modalPlacholder.branch'),
  },

]
</script>

<template>
  <generic-table
    :data="productsList" :is-loading="isLoading" :headers="headers" :on-row-click="redirectToProduct"
    tr-class="cursor-pointer"
  >
    <template #row="{ item }">
      <grid-td class="flex items-center gap-1 py-2 text-sm whitespace-nowrap sm:pl-4 ps-2 group" :default-style="false">
        <div>
          <PhotoIcon v-if="item?.imageLink === null" class="object-fill rounded-full w-7 h-7" />
          <img v-else id="image" class="object-fill rounded-full w-7 h-7" :src="item.imageLink" :link="null">
        </div>
        <span>
          <span class="flex items-center justify-center gap-2">
            {{ item?.name }} <span v-if="item.display_on_booking_page" class="flex items-center justify-center gap-2 font-semibold text-green-500 " style="font-size: 35px;">&#8226;
              <i class="opacity-0 group-hover:opacity-100 text-primary-500" style="font-size:10px;">{{ $t('displayed_store') }}</i>
            </span>
            <span v-else class="flex items-center gap-2 font-semibold text-gray-500 " style="font-size: 35px;">&#8226;
              <i class="opacity-0 group-hover:opacity-100 text-primary-500" style="font-size:10px;">{{ $t('not_displayed_store') }}</i>
            </span>
          </span>
        </span>
      </grid-td>

      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.category_id">
            {{ item?.category_name }}
          </span>
          <span v-else class="text-zinc-400"> # </span>
        </div>
      </grid-td>
      <grid-td>
        <span class="justify-center">
          <span>
            {{ item?.sort_order }}
          </span>
        </span>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="item?.team?.uuid">
            {{ item?.team?.name }}

          </span>

          <span v-else class="text-zinc-400"> - </span>
        </div>
      </grid-td>
    </template>
    <template v-if="hasActions" #actions="{ item }">
      <slot name="actions" :item="item" />
    </template>
  </generic-table>
</template>
