<script setup lang="ts">
import type { PropType } from '@vue/runtime-core'
import useVuelidate from '@vuelidate/core'
import { storeToRefs } from 'pinia'
import { Switch } from '@headlessui/vue'
import { minLength, minValue, required, requiredIf } from '@/utils/i18n-validators'
import type { Products } from '@/types/products'
import { useProductStore } from '@/stores/products'

// import textLang from '@/composables/textLang'
const props = defineProps({
  products: {
    type: Object as PropType<Products | null>,
    default: null,
    require: false,
  },
  showModal: {
    type: Boolean,
    default: false,
  },
  duplicated: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: 'product',
  },
})
const emit = defineEmits(['created', 'closed', 'updated'])
const editMode = ref(false)
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { products }: any = toRefs(props)
const { createProduct, updateProduct } = useProductStore()
const { userInfo } = useAuthStore()
const { getUserInfo } = storeToRefs(useAuthStore())
const getTeam = computed(() => {
  return getUserInfo.value.teams
})
const formData = reactive<Omit<Products, 'uuid'>>({
  name_localized: { ar: '', en: '' },
  price: 0,
  description_localized: { ar: '', en: '' },
  display_on_booking_page: true,
  sort_order: 0,
  image: null,
  category_id: '',
  team_id: getTeam.value[0].uuid,
})
const processing = ref<Boolean>(false)

watch(
  products,
  (value: Products) => {
    if (!value)
      return
    formData.name_localized = value?.name_localized || { ar: '', en: '' }
    formData.description_localized = value?.description_localized || {
      ar: '',
      en: '',
    }
    formData.display_on_booking_page = value?.display_on_booking_page
    formData.sort_order = value?.sort_order || 0
    formData.price = value?.price || 0
    formData.image = value?.image || null
    formData.category_id = (value?.category_id?.uuid as string) || ''
    formData.team_id = value?.team_id || ''
    if (value.uuid)
      editMode.value = true
  },
  { immediate: true, deep: true },
)

const closeModal = () => {
  emit('closed')
}
const rules = {
  name_localized: {
    ar: {
      required: requiredIf(() => !formData.name_localized.en.trim()),
      minLength: minLength(3),
    },
    en: {
      required: requiredIf(() => !formData.name_localized.ar.trim()),
      minLength: minLength(3),
    },
  },
  price: {
    required,
    minValue: minValue(1),
  },

  category_id: {
    required,
  },
  team_id: {
    required,
  },
}

const v$ = useVuelidate(rules, formData)

const saveProduct = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return false
  processing.value = true
  try {
    if (editMode.value)
      await editRecord()
    else
      await createRecord()
  }
  finally {
    processing.value = false
  }
}
const resetForm = () => {
  formData.name_localized = { ar: '', en: '' }
  formData.description_localized = { ar: '', en: '' }
  formData.price = 0
  formData.image = null
  formData.category_id = ''
  formData.display_on_booking_page = true
  formData.sort_order = 0
  formData.team_id = getTeam.value[0].uuid
  editMode.value = false
  v$.value.$reset()
}
const createRecord = async () => {
  try {
    formData.display_on_booking_page = formData.display_on_booking_page ? 1 : 0
    const res = await createProduct({
      ...formData,
      name_localized: JSON.stringify(formData.name_localized),
      description_localized: JSON.stringify(formData.description_localized),
    })
    resetForm()
    emit('created', res.data)
    closeModal()
  }
  catch (error) {
    console.error(error)
  }
}

const editRecord = async () => {
  try {
    formData.display_on_booking_page = formData.display_on_booking_page ? 1 : 0
    const res = await updateProduct(products.value?.uuid, {
      ...formData,
      name_localized: JSON.stringify(formData.name_localized),
      description_localized: JSON.stringify(formData.description_localized),
    })
    resetForm()
    emit('updated', res)
    closeModal()
  }
  catch (error) {
    console.error(error)
  }
}
// TODO : sparate Category modal login -> if will be used in other places
const { fetchCategories, createCategory } = useCategoryStore()
const categories = ref<Object[]>([])
onMounted(() => {
  fetchCategories().then(({ data }) => {
    categories.value = data
  })
})
const showAddCategoryForm = ref(false)
const categoryData = reactive({
  name_localized: { ar: '', en: '' },
  // type: "product",
  team_id: getTeam.value[0].uuid,
  processing: false,
})
const cateValidation = reactive({
  name_localized: {
    ar: {
      required: requiredIf(() => !categoryData.name_localized.en.trim()),
    },
    en: {
      required: requiredIf(() => !categoryData.name_localized.ar.trim()),
    },
  },
  team_id: {
    required,
  },
})
const v1$ = useVuelidate(cateValidation, categoryData)
const createNewCategory = async () => {
  v1$.value.$touch()
  if (v1$.value.$invalid)
    return false
  categoryData.processing = true
  createCategory({
    name_localized: JSON.stringify(categoryData.name_localized),
    team_id: categoryData.team_id,
    description_localized: JSON.stringify({ ar: '', en: '' }),
  })
    .then(({ data }: { data: { uuid: string } }) => {
      categories.value.push(data)
      formData.category_id = data.uuid
    })
    .finally(() => {
      // reseting cat form
      categoryData.team_id = getTeam.value[0].uuid
      categoryData.processing = false
      showAddCategoryForm.value = false
      v1$.value.$reset()
    })
}

watch(
  () => formData.team_id,
  (val) => {
    fetchCategories('product').then(({ data }) => {
      categories.value = data
    })
  },
)
</script>

<template>
  <div>
    <Modal
      :dir="getLocale(locale)?.direction"
      :open="showModal"
      :title="title"
      @close="closeModal"
    >
      <modal
        :open="showAddCategoryForm"
        title="category"
        :dir="getLocale(locale)?.direction"
        class="relative"
        @close="showAddCategoryForm = false"
      >
        <form
          :class="[
            getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
          ]"
          @submit.prevent="createNewCategory"
        >
          <overlay-loader v-if="processing" :full-screen="false" />

          <div class="grid grid-cols-6 gap-6">
            <div class="col-span-3">
              <label
                class="mb-2 w-full flex items-center text-start text-[#261E27] text-base"
                for="category-name"
              >
                {{ $t("form.name") }}<span class="text-red-600">*</span>
              </label>
              <LangInput
                v-model="categoryData.name_localized"
                :placeholder="$t('form.name')"
                :v$="v1$"
              />
            </div>
            <div class="col-span-3">
              <form-group :validation="v1$" name="team_id">
                <template #default="{ attrs }">
                  <SelectInput
                    v-bind="attrs"
                    id="team_id"
                    v-model="categoryData.team_id"
                    :label="$t('modalPlacholder.branch')"
                    :placeholder="$t('modalPlacholder.branch')"
                  >
                    <option hidden selected value="">
                      {{ $t("form.select") }}
                    </option>
                    <option
                      v-for="team in getTeam"
                      :key="team?.uuid"
                      :value="team?.uuid"
                    >
                      {{ team.name }}
                    </option>
                  </SelectInput>
                </template>
              </form-group>
            </div>
          </div>
          <div class="mt-5 sm:mt-6">
            <BaseButton
              type="submit"
              show-icon
              class="mx-auto w-1/2 d-block hover:bg-green-700"
              custome-bg="bg-green-600"
              :processing="categoryData.processing"
            >
              {{ $t("form.create") }}
            </BaseButton>
          </div>
        </form>
      </modal>
      <form
        class="grid grid-cols-1 gap-y-4 gap-x-6 sm:grid-cols-2 text-start"
        @submit.prevent="saveProduct"
      >
        <div class="col-span-1">
          <label
            class="mb-2 w-full flex items-center text-start text-[#261E27] text-base"
            for="product-name"
          >
            {{ $t("form.name") }}<span class="text-red-600">*</span>
          </label>
          <LangInput
            id="product-name"
            v-model="formData.name_localized"
            type="text"
            :placeholder="`${$t('enter')} ${$t('form.product')}`"
            :v$="v$"
          />
        </div>
        <div class="col-span-1">
          <div class="">
            <form-group :validation="v$" name="team_id">
              <template #default="{ attrs }">
                <SelectInput
                  v-bind="attrs"
                  id="category"
                  v-model="formData.team_id"
                  :label="$t('modalPlacholder.branch')"
                >
                  <option hidden selected value="">
                    {{ $t("form.select") }}
                  </option>
                  <option
                    v-for="team in getTeam"
                    :key="team?.uuid"
                    :value="team?.uuid"
                  >
                    {{ team.name }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
        </div>
        <div v-if="formData.team_id" class="col-span-1">
          <form-group :validation="v$" name="category_id">
            <template #default="{ attrs }">
              <div class="flex mt-1 rounded-md">
                <SelectInput
                  v-bind="attrs"
                  id="category"
                  v-model="formData.category_id"
                  :label="$t('form.category')"
                  :placeholder="$t('form.category')"
                >
                  <option value="">
                    {{ $t("form.select") }}
                  </option>
                  <option
                    v-for="category of categories.filter(
                      (cat) => cat.team.uuid == formData.team_id,
                    )"
                    :key="category.uuid"
                    :value="category.uuid"
                  >
                    {{ category.name_localized?.ar || category.name_localized?.en }}
                  </option>
                </SelectInput>
                <button
                  type="button"
                  class="inline-flex justify-center self-end px-4 py-3 text-sm font-medium bg-gray-300 border border-transparent rounded-e-md border-primary-500 text-primary-800 hover:bg-primary-800 hover:text-white focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2 text-start"
                  @click="showAddCategoryForm = true"
                >
                  <span>{{ $t("New") }}</span>
                </button>
              </div>
            </template>
          </form-group>
        </div>

        <div class="col-span-2 full">
          <form-group :validation="v$" name="description_localized" error-name="description">
            <template #default="{ attrs }">
              <LabelInput for="description">
                {{ $t("description") }}
              </LabelInput>
              <LangInput
                id="description"
                v-model="formData.description_localized"
                type="textarea"
                :placeholder="$t('description')"
                v-bind="attrs"
              />
            </template>
          </form-group>
        </div>

        <div class="col-span-1">
          <div class="relative">
            <form-group :validation="v$" name="price">
              <template #default="{ attrs }">
                <NumberInput
                  v-bind="attrs"
                  id="service-price"
                  v-model="formData.price"
                  :label="$t('price')"
                  class="block py-3 w-full leading-tight text-gray-700 rounded appearance-none ps-4 pe-4 focus:outline-none focus:bg-white"
                  :placeholder="$t('price')"
                  step="0.01"
                />
                <span class="absolute top-3 end-4">{{
                  $t(`currenices.${userInfo.tenant?.currency}`)
                }}</span>
              </template>
            </form-group>
          </div>
        </div>

        <div class="col-span-1">
          <div class="relative">
            <form-group :validation="v$" name="sort_order">
              <template #default="{ attrs }">
                <NumberInput
                  v-bind="attrs"
                  id="sort-order"
                  v-model="formData.sort_order"
                  :label="$t('form.sort_order')"
                  class="block py-3 w-full text-gray-700 rounded-md focus:border-primary-500 focus:ring-primary-500 focus:outline-none focus:bg-white"
                  :placeholder="$t('form.sort_order')"
                />
              </template>
            </form-group>
          </div>
        </div>

        <div class="grid grid-cols-1">
          <div class="m-2">
            <LabelInput for="display_on_booking_page">
              {{ $t("displayOnBookingPage") }}
            </LabelInput>
            <Switch
              id="display_on_booking_page"
              v-model="formData.display_on_booking_page"
              class="inline-flex relative flex-shrink-0 mt-1 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              :class="[
                formData.display_on_booking_page
                  ? 'bg-primary-600'
                  : 'bg-gray-200',
              ]"
            >
              <span
                aria-hidden="true"
                class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
                :class="[
                  formData.display_on_booking_page
                    ? 'translate-x-5 rtl:-translate-x-5'
                    : 'translate-x-0',
                ]"
              />
            </Switch>
          </div>
        </div>
        <div class="flex justify-end w-full">
          <BaseButton
            v-if="products"
            type="submit"
            class="!px-3 py-2 hover:bg-primary-800"
            custome-bg="bg-primary-700"
            show-icon
            :processing="processing"
          >
            {{ $t("form.update") }}
          </BaseButton>
          <BaseButton
            v-else
            type="submit"
            class="!px-3 py-2 hover:bg-green-700"
            custome-bg="bg-green-600"
            show-icon
            :processing="processing"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </form>
    </Modal>
  </div>
</template>

<style scoped>
form > div:not(:last-child) {
  flex: 1 0 48%;
}
form > div.full {
  flex: 1 0 100%;
}
@media (max-width: 768px) {
  form > div:not(:last-child) {
    flex: 1 0 100% !important;
  }
}
</style>
