<script setup lang="ts">
import type { PropType } from '@vue/runtime-core'
import { Switch } from '@headlessui/vue'
import type { Service, Staff } from '@/types'
import useService from '@/composables/useServices'

const props = defineProps({
  service: {
    type: Object as PropType<Service>,
    default: null,
  },
})
const { tableServicData, fetchStaffs, toggleService, currentToggle } = useService()

const handleUpdate = (staff: Staff) => toggleService(props.service, staff)

watch(
  () => props.service,
  (service) => {
    props.service && fetchStaffs(service)
  },
)

onMounted(() => {
  fetchStaffs(props.service)
})
</script>

<template>
  <div class="grid grid-cols-1 gap-4">
    <OverlayLoader v-if="tableServicData.processing" />
    <ul role="list" class="divide-y divide-gray-200">
      <li
        v-for="(staff) in tableServicData.staffList" :key="staff.uuid" class="flex items-center justify-between py-4 ps-2"
      >
        <div class="flex">
          <div>
            <p class="text-sm font-medium text-gray-900">
              {{ staff.name }}
            </p>
          </div>
        </div>
        <the-loader v-if="currentToggle === staff.uuid" />
        <Switch v-else v-model="staff.selected" class="relative inline-flex flex-shrink-0 h-6 transition-colors duration-200 ease-in-out border-2 border-transparent rounded-full cursor-pointer w-11 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" :class="[staff.selected ? 'bg-primary-600' : 'bg-gray-200']" @update:model-value="() => toggleService(service, staff)">
          <span class="sr-only">Service Status</span>
          <span aria-hidden="true" class="inline-block w-5 h-5 transition duration-200 ease-in-out transform bg-white rounded-full shadow pointer-events-none ring-0" :class="[staff.selected ? 'translate-x-5 rtl:-translate-x-5' : 'translate-x-0']" />
        </Switch>
      </li>
    </ul>
  </div>
</template>
