<script setup lang="ts">
import { storeToRefs } from 'pinia'
import Multiselect from 'vue-multiselect'
import type { PropType } from 'vue'
import 'vue-multiselect/dist/vue-multiselect.css'
import type { Tag } from '@/types'

const props = defineProps({
  tags: {
    type: Array as PropType<Tag[]>,
    default: () => [],
  },
})
const emit = defineEmits(['changed'])
// const { tags } = storeToRefs(tagStore)
const { fetchCategories } = useCategoryStore()

const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const productName = ref('')
const categoryName = ref('')
const team = ref('')
const teamDebounced = debouncedRef(team, 500)
const name = debouncedRef(productName, 500)
const category = debouncedRef(categoryName, 500)
interface Payload {
  name: string
  category: string
  team: string
}

watch([name, category, teamDebounced], (val) => {
  const [name, category, team] = val
  const payload: Payload = {
    name,
    category,
    team,
  }
  emit('changed', payload)
})
const categories = ref([])
onMounted(() => {
  fetchCategories().then(({ data }) => {
    categories.value = data
  })
})
</script>

<template>
  <div class="mt-4">
    <div class="grid grid-cols-1 mt-6 gap-y-6 gap-x-4 md:grid-cols-3">
      <div class="relative flex flex-col items-stretch flex-grow focus-within:z-10">
        <label
          for="customer-name"
          class="  mb-2 w-full flex items-center text-start text-[#261E27] text-base    "
        >{{ $t("products.title") }}</label>
        <input
          id="customer-name"
          v-model="productName"
          :placeholder="$t('products.title')"
          type="search"
          class="w-full py-2 mt-1 text-gray-700 border-gray-300 rounded-md"
        >
      </div>
      <div
        class="relative flex flex-col items-stretch flex-grow focus-within:z-10"
      >
        <selectInput
          id="category"
          v-model="categoryName"
          :label="$t('form.category')"
          :placeholder="$t('form.category')"
          class="w-full py-2 mt-1 text-gray-700 border-gray-300 rounded-md"
        >
          <option value="">
            {{ $t("form.select") }}
          </option>
          <option
            v-for="cate of categories"
            :key="cate?.uuid"
            :value="cate?.uuid"
          >
            {{ cate?.name }}
          </option>
        </selectInput>
      </div>
      <filter-with-team v-model="team" />
    </div>
  </div>
</template>
