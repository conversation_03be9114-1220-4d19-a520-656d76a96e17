<script lang="ts" setup>
import { storeToRefs } from 'pinia'

const props = defineProps({
  modelValue: String,
})
const emit = defineEmits(['update:modelValue'])
const { userInfo } = storeToRefs(useAuthStore())
const chaneVal = (value: string) => {
  emit('update:modelValue', value)
}
</script>

<template>
  <SelectInput
    id="branch"
    :model-value="modelValue!"
    :label="$t('modalPlacholder.branch')"
    @update:model-value="chaneVal"
  >
    <option value="">
      {{ $t("form.select") }}
    </option>
    <option v-for="team in userInfo.teams" :key="team.uuid" :value="team.uuid">
      {{ team.name }}
    </option>
  </SelectInput>
</template>

<style>

</style>
