<script setup lang="ts">
import { ArrowDownIcon, ArrowUpIcon, ChevronDownIcon } from '@heroicons/vue/20/solid'

import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'

import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { randomBg } from '@/composables/randomBg'
import { useDashboardStore } from '@/stores/dashboard'
const store = useDashboardStore()
const { tenant } = useAuthStore()
const { t } = useI18n()
const {
  getSourceStatistics,
  getTopServicesStatistics,
  getTodayAppointments,
  getLatestOrders,
} = storeToRefs(useDashboardStore())
const processing = ref(true)
const ranges = [
  {
    label: 'this_day',
    value: '1',
  },
  {
    label: 'this_week',
    value: '7',
  },
  {
    label: 'this_month',
    value: '30',
  },
  {
    label: 'this_year',
    value: '365',
  },
]
const rangeSelect = ref(ranges[0])
const rangeFilter = reactive({
  from: dayjs().startOf('day').format('YYYY-MM-DD'),
  to: dayjs().endOf('day').format('YYYY-MM-DD'),
})

function setStatistics(range: { label: string; value: string }) {
  rangeSelect.value = range
}
const sourceStatistics = computed(() => {
  if (
    !getSourceStatistics.value
    || !Object.keys(getSourceStatistics.value).length
  )
    return
  const totalSales
    = getSourceStatistics.value.bookingPage_sales_count
    + getSourceStatistics.value.pos_sales_count
  return {
    labels: [t('bookingPage_sales_count'), t('pos_sales_count')],
    datasets: [
      {
        data: [
          (
            (getSourceStatistics.value.bookingPage_sales_count / totalSales)
            * 100
          ).toFixed(2),
          (
            (getSourceStatistics.value.pos_sales_count / totalSales)
            * 100
          ).toFixed(2),
        ],
        backgroundColor: ['#F87171', '#60A5FA'],
      },
    ],
  }
})
const topServicestatistics = computed(() => {
  const topServicesSalesCount = getTopServicesStatistics.value.reduce(
    (acc, el) => acc + el.sales_count,
    0,
  )
  return {
    labels: getTopServicesStatistics.value.map(el => el.service_name),
    datasets: [
      {
        data: getTopServicesStatistics.value.map(el =>
          ((el.sales_count / topServicesSalesCount) * 100).toFixed(2),
        ),
        backgroundColor: getTopServicesStatistics.value.map(el => el.color),
      },
    ],
  }
})
onMounted(async () => {
  const { from, to } = rangeFilter
  processing.value = true
  try {
    await Promise.all([
      store.fetchLatetsOrders(),
      store.fetchSalesStatistics(from, to),
      store.fetchSourceStatistics(),
      store.fetchTopServices(),
      store.fetchTodayAppointments(),
    ])
  }
  finally {
    processing.value = false
  }
})

watch(
  () => rangeSelect.value.value,
  async (newVal, oldVal) => {
    processing.value = true
    rangeFilter.from = dayjs().subtract(newVal, 'days').format('YYYY-MM-DD')
    rangeFilter.to = dayjs().format('YYYY-MM-DD')
    await store.fetchSalesStatistics(rangeFilter.from, rangeFilter.to)
    processing.value = false
  },
)

function setRangeSalesStatistics(range: { form; to }) {}
function setRangeTopServiceStatistics(range: { form; to }) {}
async function latestOrdersPageChanged(page: string) {
  processing.value = true
  try {
    await store.fetchLatetsOrders(page)
  }
  finally {
    processing.value = false
  }
}
async function appoinmentsPageChanged(page: string) {
  processing.value = true
  try {
    await store.fetchTodayAppointments(page)
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <div class="relative">
    <OverlayLoader v-if="processing" :full-screen="false" />
    <div class="justify-end md:ml-4 md:flex md:items-center">
      <Menu as="div" class="relative">
        <MenuButton
          type="button"
          class="flex gap-x-1.5 items-center px-3 py-2 text-sm font-semibold text-gray-900 bg-white rounded-md ring-1 ring-inset ring-gray-300 shadow-sm hover:bg-gray-50"
        >
          {{ $t(rangeSelect.label) }}
          <ChevronDownIcon
            class="-mr-1 w-5 h-5 text-gray-400"
            aria-hidden="true"
          />
        </MenuButton>
        <transition
          enter-active-class="transition duration-100 ease-out"
          enter-from-class="opacity-0 transform scale-95"
          enter-to-class="opacity-100 transform scale-100"
          leave-active-class="transition duration-75 ease-in"
          leave-from-class="opacity-100 transform scale-100"
          leave-to-class="opacity-0 transform scale-95"
        >
          <MenuItems
            class="overflow-hidden absolute right-0 z-10 mt-3 w-36 bg-white rounded-md ring-1 ring-black ring-opacity-5 shadow-lg origin-top-right focus:outline-none"
          >
            <div class="py-1">
              <MenuItem
                v-for="(range, index) in ranges"
                :key="index"
                v-slot="{ active }"
              >
                <a
                  href="#"
                  class="block px-4 py-2 text-sm"
                  :class="[
                    active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                  ]"
                  @click="setStatistics(range)"
                >{{ $t(range.label) }}
                </a>
              </MenuItem>
            </div>
          </MenuItems>
        </transition>
      </Menu>
    </div>
    <dl class="grid grid-cols-1 gap-5 mt-5 sm:grid-cols-2 lg:grid-cols-4">
      <div
        v-for="item in store.dashboardStats"
        :key="item.id"
        class="overflow-hidden relative pt-5 pb-12 bg-white rounded-lg shadow ps-4 pe-4 sm:ps-6 sm:pe-6 sm:pt-6"
      >
        <div
          class="font-medium cursor-pointer text-primary-600 hover:text-primary-500"
          role="button"
          tabindex="0"
          @click="$router.push({ path: item.route })"
        >
          <dt>
            <div class="absolute p-3 rounded-md bg-primary-800">
              <component
                :is="item.icon"
                class="w-6 h-6 text-white"
                aria-hidden="true"
              />
            </div>
            <p class="text-sm font-medium text-gray-500 truncate ms-16">
              {{ $t(item.name) }}
            </p>
          </dt>
          <dd class="flex items-baseline pb-6 ms-16 sm:pb-7">
            <p
              class="flex items-center text-2xl font-semibold text-gray-900"
              dir="auto"
            >
              <price-format
                v-if="item.name == 'sales'"
                :form-data="{
                  price: item.stat,
                  currency: tenant.currency || '',
                }"
              />
              <span v-else>{{ item.stat }}</span>
            </p>
            <p
              class="flex hidden items-baseline text-sm font-semibold ms-2"
              :class="[
                item.changeType === 'increase'
                  ? 'text-green-600'
                  : 'text-red-600',
              ]"
            >
              <ArrowUpIcon
                v-if="item.changeType === 'increase'"
                class="flex-shrink-0 self-center w-5 h-5 text-green-500"
                aria-hidden="true"
              />
              <ArrowDownIcon
                v-else
                class="flex-shrink-0 self-center w-5 h-5 text-red-500"
                aria-hidden="true"
              />
              <span class="sr-only">
                {{ item.changeType === "increase" ? "Increased" : "Decreased" }}
                by
              </span>
              {{ item.change }}
            </p>
            <div
              class="absolute inset-x-0 bottom-0 py-4 bg-gray-50 ps-4 pe-4 sm:ps-6 sm:pe-6"
            >
              <div class="text-sm">
                {{ $t("notifications.all")
                }}<span class="sr-only">
                  {{ item.name }} {{ $t("notifications.stats") }}</span>
              </div>
            </div>
          </dd>
        </div>
      </div>
    </dl>
    <div class="grid grid-cols-1 gap-5 mt-8 sm:grid-cols-2">
      <div class="bg-white rounded-lg shadow">
        <div
          class="pb-3 border-b border-gray-200 ps-4 pe-4 sm:ps-3 sm:pe-3 sm:pt-3 -1"
        >
          <h2 class="mb-3 text-lg font-medium text-gray-900">
            {{ t("orders_sources") }}
          </h2>
          <!-- <RangesMenu @rangeSelected="setRangeSalesStatistics"></RangesMenu> -->
        </div>
        <pie-chart v-if="sourceStatistics?.labels?.length > 0" id="sourceStatistics" :data="sourceStatistics" />
      </div>
      <div class="bg-white rounded-lg shadow">
        <div
          class="pb-3 border-b border-gray-200 ps-4 pe-4 sm:ps-3 sm:pe-3 sm:pt-3 -1"
        >
          <h2 class="mb-3 text-lg font-medium text-gray-900">
            {{ t("top_services_ordered") }}
          </h2>
          <!-- <RangesMenu @rangeSelected="setRangeTopServiceStatistics"></RangesMenu> -->
        </div>
        <div class="pt-4 mt-4 border-t border-gray-200 sm:mt-0 sm:pt-0">
          <pie-chart v-if="topServicestatistics?.labels?.length > 0" id="topService" :data="topServicestatistics" />
        </div>
      </div>
    </div>
    <div class="grid grid-cols-1 gap-10 mt-8 sm:grid-cols-2">
      <div class="">
        <h2 class="mb-3 text-lg font-medium text-gray-900">
          {{ t("lastest_orders") }}
        </h2>

        <order-grid-dashborad
          :orders-list="getLatestOrders"
          @page-changed="latestOrdersPageChanged"
        />
      </div>
      <div class="">
        <h2 class="mb-3 text-lg font-medium text-gray-900">
          {{ t("today_appointments") }}
        </h2>

        <appoinment-grid-dashboard
          :appointments-list="getTodayAppointments"
          @page-changed="appoinmentsPageChanged"
        />
      </div>
    </div>
  </div>
</template>
