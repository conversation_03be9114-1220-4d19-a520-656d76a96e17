<script setup lang="ts">
import dayjs from 'dayjs'
import type { PropType } from 'vue'

const props = defineProps({
  planDetails: {
    type: Object as PropType<{
      start: string | number
      end: string | number
      planName: string
    }>,
    required: true,
  },
})

// Convert Unix timestamp (seconds) to date if needed
const convertTimestamp = (timestamp: string | number) => {
  if (typeof timestamp === 'number' && timestamp > 1000000000) {
    // If it's a Unix timestamp (large number)
    return dayjs.unix(timestamp)
  }
  // Otherwise treat as regular date string
  return dayjs(timestamp)
}

const calculateDaysLeft = () => {
  const now = dayjs()
  const expirationDate = convertTimestamp(props.planDetails.end)
  return expirationDate.diff(now, 'day')
}

const trialProgress = computed(() => {
  const now = dayjs()
  const startDate = convertTimestamp(props.planDetails.start)
  const endDate = convertTimestamp(props.planDetails.end)

  // Calculate total duration of the plan in days
  const totalDuration = endDate.diff(startDate, 'day')
  if (totalDuration <= 0)
    return 100 // If dates are invalid

  // Calculate elapsed duration in days
  const elapsedDuration = now.diff(startDate, 'day')

  // Calculate progress percentage
  const progress = (elapsedDuration / totalDuration) * 100

  // Ensure progress is between 0 and 100
  return Math.min(Math.max(progress, 0), 100)
})

// Check if subscription is ending soon (within a month)
const isSubscriptionEndingSoon = computed(() => {
  return props.planDetails.show_renew_message && props.planDetails.planName !== 'Trial'
})
</script>

<template>
  <div class="flex flex-col items-center">
    <!-- Circular Progress Graph -->
    <div class="mb-3">
      <div
        class="radial-progress"
        :class="{ expired: trialProgress === 100 }"
        :style="{ '--progress': `${trialProgress}%` }"
        :aria-valuenow="trialProgress"
        role="progressbar"
      >
        <span class="text-lg font-bold text-white">{{ Math.round(trialProgress) }}%</span>
      </div>
    </div>

    <!-- Text and Button -->
    <div v-if="planDetails.planName === 'Trial'" class="">
      <div class="mb-3 text-sm text-center text-white">
        {{ trialProgress == 100 ? $t('trial_ended') : $t('altNav.trial_ending_soon') }}
      </div>
      <button
        class="w-full py-2 px-4 text-sm font-medium text-[#082F49] bg-white rounded-lg hover:opacity-90 transition-opacity"
      >
        {{ $t('altNav.manage_subscription') }}
      </button>
    </div>

    <div v-else-if="isSubscriptionEndingSoon">
      <div class="mb-3 text-sm text-center text-white">
        {{ $t('subscription_ending_soon') }}
      </div>
      <button
        class="w-full py-2 px-4 text-sm font-medium text-[#082F49] bg-white rounded-lg hover:opacity-90 transition-opacity"
        @click="$router.push({ name: 'current-plan', query: { renewNow: true } })"
      >
        {{ $t('renew_subscription') }}
      </button>
    </div>

    <div v-else class="mb-3 text-sm text-center text-white">
      {{ $t('you_subscribed_to_plan', { planName: planDetails.planName }) }}
    </div>
  </div>
</template>

<style scoped>
.radial-progress {
  --size: 5rem;
  --thickness: 5px;
  --progress-color: #38BDF8;
  width: var(--size);
  height: var(--size);
  border-radius: 50%;
  position: relative;
  display: grid;
  place-items: center;
  background: #051C2C;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.radial-progress.expired {
  --progress-color: #EF4444;
}

.radial-progress::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  border: var(--thickness) solid rgba(255, 255, 255, 0.1);
  box-sizing: border-box;
}

.radial-progress::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(var(--progress-color) var(--progress), transparent 0deg);
  mask: radial-gradient(
    farthest-side,
    transparent calc(100% - var(--thickness) - 0.5px),
    #000 calc(100% - var(--thickness))
  );
  -webkit-mask: radial-gradient(
    farthest-side,
    transparent calc(100% - var(--thickness) - 0.5px),
    #000 calc(100% - var(--thickness))
  );
  z-index: 1;
  animation: fadeIn 0.5s ease-out;
}

.radial-progress span {
  position: relative;
  z-index: 2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.5s ease-in;
  font-weight: bold;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
