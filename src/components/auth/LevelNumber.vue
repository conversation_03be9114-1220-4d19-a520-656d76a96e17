<script setup lang="ts">
import Checked from '@/components/auth/Checked.vue'

defineProps({
  level: {
    type: Number,
    required: true,
  },
  completed: {
    type: Boolean,
    default: false,
  },
})
</script>

<template>
  <div
    class="flex flex-col justify-center items-center me-2 w-8 h-8 rounded-lg shadow-md"
    :class="completed ? 'bg-white shadow-md text-black' : 'bg-secondary text-white'"
  >
    <Checked v-if="completed" />
    <span v-else class="font-bold">{{ level }}</span>
  </div>
</template>
