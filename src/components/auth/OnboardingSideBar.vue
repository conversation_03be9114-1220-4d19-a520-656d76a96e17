<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import Checked from '@/components/auth/Checked.vue'
import LevelNumber from '@/components/auth/LevelNumber.vue'

const { t, locale } = useI18n()
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const isSidebarOpen = ref(window.innerWidth >= 768)
const isMobileMenuOpen = ref(false)

// Toggle sidebar for desktop
const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value
}

// Toggle mobile menu
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

onMounted(() => {
  // Set locale to Arabic
  if (locale.value !== 'ar')
    locale.value = 'ar'

  // Handle responsive behavior on mount and resize
  const handleResize = () => {
    if (window.innerWidth < 768) {
      isSidebarOpen.value = false
      isMobileMenuOpen.value = false
    }
    else {
      isSidebarOpen.value = true
    }
  }

  window.addEventListener('resize', handleResize)
  handleResize() // Initial check

  // Cleanup
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
})

// Get tenant type from auth store

// Get completed level from user info with type assertion
const completedLevel = computed(() => {
  return (authStore.userInfo?.tenant as any)?.onboarding?.completed_level || 1
})
const tenantType = computed(() => authStore.onboardingData?.systemType || 'store')
// Define all steps using i18n translations
const steps = {
  register: {
    label: 'onbord.create_account',
    completed: true, // Always completed
    level: 1,
    apiLevel: 1,
    title: computed(() => t('onboarding.steps.create_account')),
  },
  updateInfo: {
    label: 'onbord.select_needs',
    route: '/onboarding/update-info',
    level: 2,
    apiLevel: 3,
    title: computed(() => t('onboarding.steps.updateInfo')),
  },
  updateProfile: {
    label: computed(() =>
      tenantType.value === 'store'
        ? 'onbord.store_details'
        : 'onbord.activity_details',
    ),
    route: '/onboarding/update-profile',
    level: 3,
    apiLevel: 4,
    title: computed(() => tenantType.value === 'store'
      ? t('onboarding.steps.store_details')
      : t('onboarding.steps.activity_details'),
    ),
  },
  completeProfile: {
    label: 'onbord.service_setup',
    route: '/onboarding/complete-profile',
    level: 4,
    apiLevel: 5,
    onlyForStore: true,
    title: computed(() => t('onboarding.steps.service_setup')),
  },
}

// Helper function to check if a step is completed based on API level
const isStepCompleted = (stepKey: string) => {
  const step = steps[stepKey]
  // Only mark as completed if it's a previous step
  return currentStep.value !== stepKey && completedLevel.value >= step.apiLevel
}

// Determine current step based on the route
const currentStep = computed(() => {
  const path = route.path
  if (path.includes('update-info'))
    return 'updateInfo'
  if (path.includes('update-profile'))
    return 'updateProfile'
  if (path.includes('complete-profile'))
    return 'completeProfile'
  return 'updateInfo' // Default to step 2
})

// Define step order
const stepOrder = computed(() => {
  if (tenantType.value === 'store')
    return ['register', 'updateInfo', 'updateProfile', 'completeProfile']
  else
    return ['register', 'updateInfo', 'updateProfile']
})

// Check if a step is accessible (current or previous steps only )
const isStepAccessible = (stepKey: string) => {
  const completedStep = steps[stepKey].apiLevel
  return completedStep <= completedLevel.value
}

// Determine which steps to show based on tenant type
const showCompleteProfile = computed(() => tenantType.value === 'store')

// Navigate to a step if it's accessible
const goToStep = (stepKey: string, stepRoute: string) => {
  if (isStepAccessible(stepKey)) {
    router.push(stepRoute)
    if (window.innerWidth < 768)
      isMobileMenuOpen.value = false
  }
}

// Complete onboarding and go to launch page
const completeOnboarding = () => {
  if (tenantType.value === 'store' && currentStep.value === 'completeProfile')
    router.push('/launch-profile')
  else if (tenantType.value === 'system' && currentStep.value === 'updateProfile')
    router.push('/launch-profile')
}
</script>

<template>
  <!-- Mobile Header with Logo -->
  <div class="flex fixed top-0 right-0 left-0 z-50 justify-between items-center p-3 bg-white shadow-md md:hidden">
    <button
      class="p-2 rounded-lg transition-colors hover:bg-gray-100"
      @click="toggleMobileMenu"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path v-if="!isMobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
    <img src="@/assets/logo.svg" alt="Logo" class="h-8 transition-all duration-300">
    <div class="w-8" /> <!-- Spacer for balance -->
  </div>

  <!-- Sidebar -->
  <div
    class="md:relative bg-[#EDF0FF] text-black transition-all duration-300 ease-in-out z-40" :class="[
      isMobileMenuOpen ? 'fixed top-14 h-[calc(100%-3.5rem)]' : 'hidden md:block',
      isSidebarOpen ? 'md:w-48' : 'md:w-20',
    ]"
  >
    <!-- Logo (Desktop only) -->
    <div class="hidden justify-between items-center p-4 pb-6 md:flex">
      <img v-if="isSidebarOpen" src="@/assets/logo.svg" alt="Logo" class="mx-auto h-8 transition-all duration-300 md:mx-0">
      <img v-else src="@/assets/logo.svg" alt="Logo Icon" class="mx-auto h-8 transition-all duration-300 md:mx-0">
    </div>

    <!-- Menu Items -->
    <nav class="flex-1 px-2 pt-4 overflow-y-auto max-h-[calc(100vh-4rem)]">
      <ul class="space-y-3 text-base text-black">
        <!-- Step 1 (Always completed) -->
        <li class="flex items-center p-2.5 text-gray-500 rounded-lg transition-all duration-200">
          <Checked />
          <span v-if="isSidebarOpen || isMobileMenuOpen" class="transition-opacity duration-200 ms-2.5" :class="{ 'opacity-0': !isSidebarOpen && !isMobileMenuOpen }">
            {{ steps.register.title }}
          </span>
        </li>

        <!-- Step 2 (Update Info) -->
        <li
          class="flex items-center p-2.5 rounded-lg transition-all duration-200 " :class="[
            currentStep === 'updateInfo'
              ? 'bg-gradient-to-l from-sky-400 to-green-300 text-white font-bold'
              : (isStepAccessible('updateInfo')
                ? 'cursor-pointer hover:bg-secondary hover:text-white text-gray-500'
                : 'text-gray-500'),
          ]"
          @click="goToStep('updateInfo', steps.updateInfo.route)"
        >
          <LevelNumber
            :level="steps.updateInfo.level"
            :completed="isStepCompleted('updateInfo')"
          />
          <span v-if="isSidebarOpen || isMobileMenuOpen" class="transition-opacity duration-200 ms-2.5" :class="{ 'opacity-0': !isSidebarOpen && !isMobileMenuOpen }">
            {{ steps.updateInfo.title }}
          </span>
        </li>

        <!-- Step 3 (Update Profile) -->
        <li
          v-if="tenantType"
          class="flex items-center p-2.5 rounded-lg transition-all duration-200 " :class="[
            currentStep === 'updateProfile'
              ? 'bg-gradient-to-l from-sky-400 to-green-300 text-white font-bold shadow-md'
              : (isStepAccessible('updateProfile')
                ? 'cursor-pointer hover:bg-secondary hover:text-white text-gray-500'
                : 'text-gray-500'),
          ]"
          @click="goToStep('updateProfile', steps.updateProfile.route)"
        >
          <LevelNumber
            :level="steps.updateProfile.level"
            :completed="isStepCompleted('updateProfile')"
          />
          <span v-if="isSidebarOpen || isMobileMenuOpen" class="transition-opacity duration-200 ms-2.5" :class="{ 'opacity-0': !isSidebarOpen && !isMobileMenuOpen }">
            {{ steps.updateProfile.title }}
          </span>
        </li>

        <!-- Step 4 for Store (Complete Profile) -->
        <li
          v-if="tenantType === 'store'"
          class="flex items-center p-2.5 rounded-lg transition-all duration-200  " :class="[
            currentStep === 'completeProfile'
              ? 'bg-gradient-to-l from-sky-400 to-green-300 text-white font-bold shadow-md'
              : (isStepAccessible('completeProfile')
                ? 'cursor-pointer hover:bg-secondary hover:text-white text-gray-500'
                : 'text-gray-500'),
          ]"
          @click="goToStep('completeProfile', steps.completeProfile.route)"
        >
          <LevelNumber
            :level="steps.completeProfile.level"
            :completed="isStepCompleted('completeProfile')"
          />
          <span v-if="isSidebarOpen || isMobileMenuOpen" class="transition-opacity duration-200 ms-2.5" :class="{ 'opacity-0': !isSidebarOpen && !isMobileMenuOpen }">
            {{ steps.completeProfile.title }}
          </span>
        </li>
      </ul>
    </nav>

    <!-- Language Switcher -->
    <div class="absolute right-0 left-0 bottom-20 p-4 border-t border-gray-200">
      <button
        class="flex items-center p-2.5 w-full text-gray-600 rounded-lg transition-colors duration-200 hover:bg-gray-50"
        @click="locale = locale === 'ar' ? 'en' : 'ar'"
      >
        <img src="@/assets/icons/language/globe-alt.svg" alt="Language Switcher" class="w-5 h-5">
        <span v-if="isSidebarOpen || isMobileMenuOpen" class="transition-opacity duration-200 ms-2.5" :class="{ 'opacity-0': !isSidebarOpen && !isMobileMenuOpen }">
          {{ locale === 'ar' ? 'English' : 'العربية' }}
        </span>
      </button>
    </div>

    <!-- Logout Button -->
    <div class="absolute right-0 bottom-0 left-0 p-4 border-t border-gray-200">
      <button
        class="flex items-center p-2.5 w-full text-red-600 rounded-lg transition-colors duration-200 hover:bg-red-50"
        @click="authStore.logout().then(() => router.push({ name: 'login' }))"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
        </svg>
        <span v-if="isSidebarOpen || isMobileMenuOpen" class="transition-opacity duration-200 ms-2.5" :class="{ 'opacity-0': !isSidebarOpen && !isMobileMenuOpen }">{{ $t('form.signout') }}</span>
      </button>
    </div>
  </div>

  <!-- Overlay for mobile menu - only covers the sidebar area -->
  <div
    v-if="isMobileMenuOpen"
    class="fixed inset-0 z-30 pt-14 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300 md:hidden"
    @click="toggleMobileMenu"
  />
</template>

<style scoped>
@media screen and (max-height: 600px) {
    nav {
        padding-top: 0.5rem;
    }

    .space-y-3 {
        margin-top: 0.5rem;
    }

    li {
        padding: 0.5rem !important;
    }
}
</style>
