<script lang="ts" setup>
import useVuelidate from '@vuelidate/core'
import type { ComputedRef } from 'vue'
import { storeToRefs } from 'pinia'
import { Switch } from '@headlessui/vue'
import type { em } from '@fullcalendar/core/internal-common'
import { minLength, minValue, required, requiredIf } from '@/utils/i18n-validators'
const props = defineProps({
  categoryData: {
    type: Object,
    default: () => ({}),
  },
  showModal: {
    type: Boolean,
    default: false,
  },
  editMode: {
    type: Boolean,
    default: false,
  },
  v1$: {
    type: Object,
    default: () => ({}),
  },
  processingModal: {
    type: Boolean,
    default: false,
  },
  teams: {
    type: Array,
    default: () => [],
  },
})
const emit = defineEmits(['closed'])
const { locale, t } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { getUserInfo } = storeToRefs(useAuthStore())
const { fetchCategories, createCategory, removeCategory, updateCategory }
  = useCategoryStore()
const link = ref('')
const categoryData = reactive({
  name_localized: {
    ar: '',
    en: '',
  },
  description_localized: {
    ar: '',
    en: '',
  },
  slug: '',
  sort_order: 0,
  base_link: '',
  type: 'service',
  display_on_booking_page: false,
  uuid: '',
  team_id: '',
  image: '',
})

watch(
  () => props.categoryData,
  (val) => {
    categoryData.name_localized = val.name_localized || { ar: '', en: '' }
    categoryData.slug = val.slug
    categoryData.sort_order = val.sort_order
    categoryData.base_link = val.base_link
    categoryData.description_localized = val.description_localized || {
      ar: '',
      en: '',
    }
    categoryData.display_on_booking_page = val.display_on_booking_page
    categoryData.type = val.type
    categoryData.team_id = val.team_id
    link.value = val.imageLink || ''
    categoryData.uuid = val.uuid
    categoryData.image = val.imageLink || ''
  },
  { immediate: true },
)

const getTeam = computed(() => {
  return getUserInfo.value.teams
})

const cateValidation = {
  name_localized: {
    ar: {
      required: requiredIf(() => !categoryData.name_localized.en.trim()),
      minLength: minLength(3),
    },
    en: {
      required: requiredIf(() => !categoryData.name_localized.ar.trim()),
      minLength: minLength(3),
    },
  },
  slug: {
    // required : requiredIf((val)=>{
    //   return editMode.value
    // }),
    // minLength: minLength(3),
  },
  team_id: {
    required,
  },
  sort_order: {
    minValue: minValue(1),
  },
}

const v1$ = useVuelidate(cateValidation, categoryData)

const processingModal = ref(false)

function resetForm() {
  categoryData.name_localized = { ar: '', en: '' }
  categoryData.slug = ''
  categoryData.sort_order = 0
  categoryData.base_link = ''
  categoryData.image = ''
  categoryData.description_localized = { ar: '', en: '' }
  categoryData.display_on_booking_page = false
  categoryData.type = 'service'
  categoryData.uuid = ''
  link.value = '';
  (categoryData.team_id = getUserInfo.value.teams[0]?.uuid || ''),
  // props.editMode.value = false;
  v1$.value.$reset()
}

async function updateRecord() {
  try {
    v1$.value.$touch()
    if (v1$.value.$invalid)
      return false
    processingModal.value = true
    if (link.value === categoryData.image)
      delete categoryData.image

    await updateCategory(categoryData.uuid, {
      ...categoryData,
      name_localized: JSON.stringify(categoryData.name_localized),
      description_localized: JSON.stringify(categoryData.description_localized),
      display_on_booking_page: categoryData.display_on_booking_page ? 1 : 0,
    })
  }
  catch (error) {
    return Promise.reject(error)
  }
}

async function createRecord() {
  try {
    v1$.value.$touch()
    if (v1$.value.$invalid)
      return false

    await createCategory({
      ...categoryData,
      name_localized: JSON.stringify(categoryData.name_localized),
      description_localized: JSON.stringify(categoryData.description_localized),
      display_on_booking_page: categoryData.display_on_booking_page ? 1 : 0,
    })
  }
  catch (error) {
    return Promise.reject(error)
  }
}

async function saveCategory() {
  v1$.value.$touch()
  if (v1$.value.$invalid)
    return false
  processingModal.value = true
  try {
    // if (link.value === categoryData?.value?.imageLink) delete categoryData.image;
    if (props.editMode)
      await updateRecord()
    else
      await createRecord()

    emit('closed')
    resetForm()
  }
  finally {
    processingModal.value = false
  }
}
</script>

<template>
  <modal
    :open="showModal"
    :title="editMode ? $t('editCategory') : $t('category')"
    :dir="getLocale(locale)?.direction"
  >
    <form class="relative text-start" @submit.prevent="saveCategory">
      <OverlayLoader v-if="processingModal" :full-screen="false" />
      <div
        :class="
          editMode === true
            ? 'grid grid-cols-2 gap-5'
            : 'grid grid-cols-2 gap-1'
        "
      >
        <div class="col-span-2">
          <label
            class="mb-2 w-full flex items-center text-start text-[#261E27] text-base"
            for="category-name"
          >
            {{ $t("form.name") }}<span class="text-red-600">*</span>
          </label>

          <LangInput
            v-model="categoryData.name_localized"
            :placeholder="$t('form.name')"
            :v$="v1$"
          />
        </div>

        <div class="col-span-2 mb-2">
          <label
            class="mb-2 w-full flex items-center text-start text-[#261E27] text-base"
            for="category-description"
          >
            {{ $t("description") }}
          </label>

          <LangInput
            v-model="categoryData.description_localized"
            type="textarea"
            :placeholder="$t('description')"
            :v$="v1$"
          />
        </div>

        <div class="col-span-2 sm:col-span-1">
          <label
            for="team_id"
            class="mb-2 w-full flex items-center text-start text-[#261E27] text-base"
          >{{ $t("modalPlacholder.branch") }}</label>
          <div class="">
            <form-group :validation="v1$" name="team_id">
              <template #default="{ attrs }">
                <select
                  v-bind="attrs"
                  id="team_id"
                  v-model="categoryData.team_id"
                  class="py-3 w-full text-gray-700 rounded-md border-gray-300"
                >
                  <option hidden selected value="">
                    {{ $t("form.select") }}
                  </option>
                  <option
                    v-for="team in teams"
                    :key="team?.uuid"
                    :value="team?.uuid"
                    :selected="team?.uuid === categoryData.team_id"
                  >
                    {{ team.name }}
                  </option>
                </select>
              </template>
            </form-group>
          </div>
        </div>

        <div v-if="editMode" class="col-span-2">
          <LabelInput for="slug" class="mb-2">
            {{ $t("slug") }} {{ categoryData.base_link }}
          </LabelInput>

          <form-group :validation="v1$" name="slug">
            <template #default="{ attrs }">
              <div class="relative">
                <div
                  class="flex mt-1 rounded-md shadow-sm ltr:flex-row-reverse rtl:flex-row"
                >
                  <TextInput
                    v-bind="attrs"
                    id="service-slug"
                    v-model="categoryData.slug"
                    :placeholder="$t('fields.slug')"
                    custom-classes="w-full !border-s-0 text-end ltr:text-start disabled:bg-gray-200 rounded-none border-e"
                    @click="
                      $event.target.setSelectionRange(
                        $event.target.value.length,
                        $event.target.value.length,
                      )
                    "
                  />
                  <div
                    class="flex justify-center items-center px-4 w-auto bg-gray-200 border border-gray-300 shadow-sm center-items"
                  >
                    <span class="text-sm text-start" dir="auto">
                      {{ categoryData.base_link }}
                    </span>
                  </div>
                </div>
              </div>
              <span v-if="v1$.slug.$dirty" class="text-sm">{{
                $t("slug_rule")
              }}</span>
            </template>
          </form-group>
        </div>

        <div class="col-span-2 sm:col-span-1">
          <div class="flex flex-col justify-between">
            <label
              for="image"
              class="mb-2 w-full flex items-center text-start text-[#261E27] text-base"
            >{{ $t("form.image") }}</label>
            <ImageInput
              id="image"
              v-model="categoryData.image"
              :link="link"
              :show-clear-btn="true"
              @image-cleared="link = ''"
              @update:link="link = $event"
            />
          </div>
        </div>
        <div class="col-span-2 sm:col-span-1">
          <label class="mb-2 w-full flex items-center text-start text-[#261E27] text-base " for="sort-order">
            {{ $t("form.sort_order") }}
          </label>
          <div class="relative">
            <form-group name="sort_order" :validation="v1$">
              <template #default="{ attrs }">
                <NumberInput
                  v-bind="attrs"
                  id="sort-order"
                  v-model="categoryData.sort_order"
                  class="block py-3 w-full text-gray-700 rounded-md focus:border-primary-500 focus:ring-primary-500 focus:outline-none focus:bg-white"
                  :placeholder="$t('form.sort_order')"
                />
              </template>
            </form-group>
          </div>
        </div>
        <div class="col-span-2 sm:col-span-1">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <LabelInput for="display_on_booking_page">
                {{ $t("displayOnBookingPage") }}
              </LabelInput>
              <Switch
                id="display_on_booking_page"
                v-model="categoryData.display_on_booking_page"
                class="inline-flex relative flex-shrink-0 mt-1 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                :class="[
                  categoryData.display_on_booking_page
                    ? 'bg-primary-600'
                    : 'bg-gray-200',
                ]"
              >
                <span
                  aria-hidden="true"
                  class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
                  :class="[
                    categoryData.display_on_booking_page
                      ? 'translate-x-5 rtl:-translate-x-5'
                      : 'translate-x-0',
                  ]"
                />
              </Switch>
            </div>
          </div>
        </div>
      </div>
      <div class="mt-8">
        <div class="flex justify-end items-center">
          <div v-if="!editMode" class="flex gap-2 justify-between">
            <BaseButton
              type="submit"
              show-icon
              class="ssssssw-full hover:bg-green-700"
              custome-bg="bg-green-600"
              :processing="processingModal"
            >
              {{ $t("form.create") }}
            </BaseButton>
          </div>
          <div v-else class="w-full">
            <BaseButton
              type="submit"
              show-icon
              class="px-4 mx-auto w-full sm:w-1/2 hover:bg-primary-700"
              custome-bg="bg-primary-700"
              :processing="processingModal"
            >
              {{ $t("form.update") }}
            </BaseButton>
          </div>
        </div>
      </div>
    </form>
  </modal>
</template>
