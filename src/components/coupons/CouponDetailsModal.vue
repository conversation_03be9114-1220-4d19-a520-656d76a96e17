<script setup lang="ts">
// Imports
import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useLocalesStore } from '@/stores/locales';
import DiscountIcon from '@/components/Icons/DiscountIcon.vue';
import type { Coupons } from '@/types/coupons';
import { useCoupons } from '@/stores/coupons';
import CouponInfoCard from '@/components/coupons/CouponInfoCard.vue';
import CouponScopeCard from '@/components/coupons/CouponScopeCard.vue';
import ContentCard from '@/components/Common/ContentCard.vue';
import TrashIcon from '@/components/Icons/TrashIcon.vue';
import FullScreenModal from '@/components/Common/FullScreenModal.vue';
import OverlayLoader from '@/components/Common/OverlayLoader.vue';
import EditDescriptionModal from '@/components/coupons/EditDescriptionModal.vue';
import EditInfoModal from '@/components/coupons/EditInfoModal.vue';
import EditCouponScopeModal from '@/components/coupons/EditCouponScopeModal.vue';
import CouponStatusAction from '@/components/coupons/CouponStatusAction.vue';

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  couponId: {
    type: String,
    required: true
  }
});

// Store and Basic Setup
const emit = defineEmits(['close']);
const { getLocale } = storeToRefs(useLocalesStore());
const { locale, t } = useI18n();
const { deleteCoupons, getCoupon, updateCouponScope } = useCoupons();

// Local State
const coupon = ref<Coupons | null>(null);
const processing = ref(false);
const showConfModal = ref(false);
const showEditDescriptionModal = ref(false);
const showEditInfoModal = ref(false);
const showEditScopeModal = ref(false);

// Fetching Logic
const fetchCouponDetails = async () => {
  if (!props.isOpen) return;
  processing.value = true;
  try {
    const res = await getCoupon(props.couponId);
    coupon.value = res.data.data;
  } catch (error) {
    console.error('Failed to fetch coupon details:', error);
    emit('close');
  } finally {
    processing.value = false;
  }
};

// Watch for modal opening to fetch data
watch(() => props.isOpen, fetchCouponDetails, { immediate: true });

const handleDeleteItem = async (payload: { type: 'product' | 'category' | 'branch'; item: any }) => {
  if (!coupon.value?.uuid) return;

  const { type, item } = payload;
  const key = type === 'product' ? 'products' : type === 'category' ? 'categories' : 'branches';

  if (coupon.value[key]) {
    const updatedItems = coupon.value[key]!.filter(i => i.uuid !== item.uuid);
    const updatePayload = { [key]: updatedItems.map(i => i.uuid) };

    processing.value = true;
    try {
      const res = await updateCouponScope(updatePayload, coupon.value.uuid);
      coupon.value = res.data.data; // Update local coupon data with the response
    } catch (error) {
      console.error(`Failed to delete ${type}:`, error);
    } finally {
      processing.value = false;
    }
  }
};

const handleScopeUpdate = (updatedCoupon: Coupons) => {
  coupon.value = updatedCoupon;
};

// Delete Function
const handleSuccess = () => {
  showConfModal.value = false;
  emit('close');
};
</script>

<template>
  <FullScreenModal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="coupon?.code || '...'"
    :icon="DiscountIcon"
    @close="$emit('close')"
    :panel-classes="'w-full bg-white rounded-xl shadow-[0px_8px_8px_-4px_rgba(10,13,18,0.04)] shadow-[0px_20px_24px_-4px_rgba(10,13,18,0.10)] flex flex-col overflow-visible sm:w-full sm:mx-20 h-full my-20'"
  >
    <overlay-loader v-if="processing || !coupon" :full-screen="true" />

    <template v-if="coupon">
      <div class="flex justify-between items-center w-full p-4 border-b">
        <CouponStatusAction :coupon="coupon" @refresh="fetchCouponDetails" />
        <IconOnlyBtn
          :icon="TrashIcon"
          @click="showConfModal = true"
          class="text-rose-500 outline-rose-200"
        />
      </div>

      <div class="flex flex-col gap-6 p-4 pb-6">
        <!-- Confirmation Modal for Delete -->
        <confirmation-modal
          v-if="showConfModal"
          :is-open="showConfModal"
          @closed="showConfModal = false"
          @removed="handleSuccess"
          :apiCall="deleteCoupons"
          :recordId="coupon.uuid!"
        >
          <p class="leading-7 text-start">
            {{ $t("confirmModal.msg") }}
          </p>
        </confirmation-modal>

        <CouponInfoCard
          :coupon="coupon"
          @edit="showEditInfoModal = true"
        />

        <CouponScopeCard
          :coupon="coupon"
          @edit="showEditScopeModal = true"
          @delete-item="handleDeleteItem"
        />

        <ContentCard
          :title="t('coupons.description')"
          :content="coupon.description"
          :empty-text="t('coupons.no_description')"
          @edit="showEditDescriptionModal = true"
        />

        <!-- Edit Modals -->
        <EditInfoModal
          v-if="showEditInfoModal"
          :is-open="showEditInfoModal"
          :coupon="coupon"
          @close="showEditInfoModal = false"
        />

        <EditDescriptionModal
          v-if="showEditDescriptionModal"
          :is-open="showEditDescriptionModal"
          :coupon="coupon"
          @close="showEditDescriptionModal = false"
        />

        <EditCouponScopeModal
          v-if="showEditScopeModal"
          :is-open="showEditScopeModal"
          :coupon="coupon"
          @close="showEditScopeModal = false"
          @update="handleScopeUpdate"
        />
      </div>
    </template>
  </FullScreenModal>
</template>