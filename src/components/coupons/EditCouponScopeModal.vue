<script setup lang="ts">
import { ref, watch, onMounted, reactive } from 'vue';
import useVuelidate from '@vuelidate/core';
import { required } from '@/utils/i18n-validators';
import type { PropType } from 'vue';
import type { Coupons } from '@/types/coupons';
import { useCoupons } from '@/stores/coupons';
import { useProductStore } from '@/stores/products';
import { useCategoryStore } from '@/stores/category';
import { useTeamStore } from '@/stores/teams';
import { useServicesStore } from '@/stores/services';
import type { Category } from '@/types/service';
import type { Branch } from '@/types/branches';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';

import Modal from '@/components/Common/Modal.vue';

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  coupon: {
    type: Object as PropType<Coupons>,
    required: true,
  },
});

// Emits
const emit = defineEmits(['close', 'update']);

// Composables & Store
const { t } = useI18n();
const { updateCouponScope } = useCoupons();
const productStore = useProductStore();
const categoryStore = useCategoryStore();
const teamStore = useTeamStore();
const servicesStore = useServicesStore();
const { getCategories } = storeToRefs(categoryStore);
const { getTeams } = storeToRefs(teamStore);

// Options Data Setup
const productsOptions = ref<{ id: string; name: string }[]>([]);
const categoriesOptions = ref<{ id:string; name: string }[]>([]);
const branchesOptions = ref<{ id: string; name: string }[]>([]);
const loading = ref(false);

// Form Data
const formData = reactive({
  products: [] as string[],
  categories: [] as string[],
  branches: [] as string[],
});

// Validation
const rules = {
  products: { required },
  categories: { required },
  branches: { required },
};
const v$ = useVuelidate(rules, formData);

// Data Fetching on Mount
onMounted(async () => {
  loading.value = true;
  try {
    const productsRes = await productStore.fetchProducts(1, {});
    const products = productsRes?.data || [];
    const servicesRes = await servicesStore.fetchServices(1, {});
    const services = servicesRes?.data || [];
    const packagesRes = await servicesStore.fetchPackages(1, {});
    const packages = packagesRes?.data || [];
    productsOptions.value = [
      ...products.map(p => ({ id: p.uuid, name: `${p.name}` })),
      ...services.map(s => ({ id: s.uuid, name: `${s.name}` })),
      ...packages.map(pkg => ({ id: pkg.uuid, name: `${pkg.name}` })),
    ];

    await categoryStore.fetchCategories();
    const cats: Category[] = getCategories.value || [];
    categoriesOptions.value = cats.map(c => ({
      id: String(c.uuid),
      name: String((c as any).name_localized?.ar || (c as any).name_localized?.en || (c as any).name)
    }));

    await teamStore.fetchTeams();
    const teams: Branch[] = getTeams.value || [];
    branchesOptions.value = teams.map(b => ({
      id: b.uuid,
      name: b.name
    }));
  } catch (err) {
    console.error("Failed to fetch data for modal", err)
  } finally {
    loading.value = false;
  }
});

// Watch for prop change to set initial form values
watch(() => props.coupon, (newCoupon) => {
  if (newCoupon) {
    formData.products = newCoupon.products?.map(p => p.uuid) || [];
    formData.categories = newCoupon.categories?.map(c => c.uuid) || [];
    formData.branches = newCoupon.branches?.map(b => b.uuid) || [];
  }
}, { immediate: true, deep: true });

// Submit Handler
const onSubmit = async () => {
  v$.value.$touch();
  if (v$.value.$invalid) return;

  try {
    const response = await updateCouponScope(formData, props.coupon.uuid!);
    emit('update', response.data.data);
    emit('close');
  } catch (error) {
    console.error('Failed to update coupon scope:', error);
  }
};
</script>

<template>
  <Modal :open="isOpen" :title="t('coupons.edit_coupon_scope')" @close="$emit('close')">
    <form @submit.prevent="onSubmit" class="space-y-6">

      <div class="flex flex-col gap-4 mb-4">
        <div>
          <form-group name="products" :validation="v$">
            <template #default="{ attrs }">
              <MultiSelectInput
                v-bind="attrs"
                v-model="formData.products"
                :label="t('altNav.services')"
                :hint="t('hints.services_selection')"
                :placeholder="t('form.select')"
                :options="productsOptions"
                item-value="id"
                item-text="name"
                required
              />
            </template>
          </form-group>
        </div>
        <div>
          <form-group name="categories" :validation="v$">
            <template #default="{ attrs }">
              <MultiSelectInput
                v-bind="attrs"
                v-model="formData.categories"
                :label="t('altNav.categories')"
                :placeholder="t('form.select')"
                :options="categoriesOptions"
                item-value="id"
                item-text="name"
                required
              />
            </template>
          </form-group>
        </div>
        <div>
          <form-group name="branches" :validation="v$">
            <template #default="{ attrs }">
              <MultiSelectInput
                v-bind="attrs"
                v-model="formData.branches"
                :label="t('altNav.branches')"
                :placeholder="t('form.select')"
                :options="branchesOptions"
                item-value="id"
                item-text="name"
                required
              />
            </template>
          </form-group>
        </div>
      </div>

        <!-- Action Buttons -->
        <div class="flex w-full gap-3 mt-4">
          <BaseButton
            type="submit"
            variant="filled"
            size="md"
            class="w-full"
            :label="t('form.save')"
          />
        </div>
    </form>
  </Modal>
</template> 