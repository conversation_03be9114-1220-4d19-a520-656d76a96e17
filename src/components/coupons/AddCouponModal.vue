<script setup lang="ts">
// Imports Section
import type { PropType } from "vue";
import { storeToRefs } from "pinia";
import useVuelidate from "@vuelidate/core";
import { required } from "@/utils/i18n-validators";
import { formatDate } from "@/composables/dateFormat";
import { Switch } from "@headlessui/vue";
import DiscountIcon from "@/components/Icons/DiscountIcon.vue";
import { useProductStore } from '@/stores/products';
import { useCategoryStore } from '@/stores/category';
import { useTeamStore } from '@/stores/teams';
import { useServicesStore } from '@/stores/services';
import { onMounted, ref, computed } from 'vue';
import type { Products } from '@/types/products';
import type { Category } from '@/types/service';
import type { Branch } from '@/types/branches';
import type { Coupons } from '@/types/coupons';
import { ArrowLeftIcon } from "@heroicons/vue/24/outline";


// Store and Basic Setup
const { createCoupons } = useCoupons();
const emit = defineEmits(["closed"]);
const { locale } = useI18n();
const { getLocale } = storeToRefs(useLocalesStore());
const processing = ref(false);
const { t } = useI18n();


// Options Data Setup
const productStore = useProductStore();
const categoryStore = useCategoryStore();
const teamStore = useTeamStore();
const servicesStore = useServicesStore();
const { getCategories } = storeToRefs(categoryStore);
const { getTeams } = storeToRefs(teamStore);

const productsOptions = ref<{ id: string; name: string }[]>([]);
const categoriesOptions = ref<{ id: string; name: string }[]>([]);
const branchesOptions = ref<{ id: string; name: string }[]>([]);


// Data Fetching on Mount
onMounted(async () => {
  const productsRes = await productStore.fetchProducts(1, {});
  const products = productsRes?.data || [];

  const servicesRes = await servicesStore.fetchServices(1, {});
  const services = servicesRes?.data || [];

  const packagesRes = await servicesStore.fetchPackages(1, {});
  const packages = packagesRes?.data || [];

  productsOptions.value = [
    ...products.map(p => ({ id: p.uuid, name: `${p.name}` })),
    ...services.map(s => ({ id: s.uuid, name: `${s.name}` })),
    ...packages.map(pkg => ({ id: pkg.uuid, name: `${pkg.name}` })),
  ];

  await categoryStore.fetchCategories();
  const cats: Category[] = getCategories.value || [];
  categoriesOptions.value = cats.map(c => ({
    id: String(c.uuid),
    name: String((c as any).name_localized?.ar || (c as any).name_localized?.en || (c as any).name)
  }));

  await teamStore.fetchTeams();
  const teams: Branch[] = getTeams.value || [];
  branchesOptions.value = teams.map(b => ({
    id: b.uuid,
    name: b.name
  }));
});


// Form Data Setup, Sets sensible starting values for the form
const formData = reactive<Coupons>({
  code: "",
  description_localized: {
    ar: "",
    en: ""
  },
  discount_amount: "",
  discount_type: "fixed",
  start_date: new Date(),
  status: 0,
  end_date: new Date(),
  usage_limit: "1",
  usage_limit_per_user: "1",
  products: [],
  categories: [],
  branches: [],
});

const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
});

// Computed property for discount input
const discountInput = computed({
  get: () => ({
    discount_amount: formData.discount_amount,
    discount_type: formData.discount_type
  }),
  set: (value) => {
    formData.discount_amount = value.discount_amount;
    formData.discount_type = value.discount_type;
  }
});

const statusModel = computed({
  get: () => !!formData.status,
  set: (value) => {
    formData.status = value ? 1 : 0;
  }
});


// Validation Rules 
const rules = {
  code: {
    required,
  },
  discount_amount: {
    required,
  },
  discount_type: {
    required,
  },
  start_date: {
    required,
  },
  end_date: {
    required,
  },
  usage_limit: {
    required,
  },
  usage_limit_per_user: {
    required,
  },
  products: {
    required,
  },
  categories: {
    required,
  },
  branches: {
    required,
  }
};
const v$ = useVuelidate(rules, formData);


// Form Reset Function 
const resetForm = () => {
  formData.code = "";
  formData.description_localized = {
    ar: "",
    en: ""
  };
  formData.discount_amount = "";
  formData.discount_type = "fixed";
  formData.start_date = new Date();
  formData.end_date = new Date();
  formData.status = 0;
  formData.usage_limit = "1";
  formData.usage_limit_per_user = "1";
  formData.products = [];
  formData.categories = [];
  formData.branches = [];
  
  v$.value.$reset();
};

const buttonAction = ref('create');

const saveCoupon = async () => {
  if (processing.value) return;
  v$.value.$touch();
  if (v$.value.$invalid) return false;
  processing.value = true;

  let payload: Coupons = {
    ...formData,
    start_date: typeof formData.start_date === 'string' ? formData.start_date : formatDate(new Date(formData.start_date)),
    end_date: typeof formData.end_date === 'string' ? formData.end_date : formatDate(new Date(formData.end_date)),
    status: formData.status,
  };
  
  const arrayFields = ['products', 'categories', 'branches'];

  arrayFields.forEach((field) => {
    delete payload[field];
    if (Array.isArray(formData[field])) {
      formData[field].forEach((value, index) => {
        payload[`${field}[${index}]`] = value;
      });
    }
  });
  try {
    await createCoupons(payload);
    if (buttonAction.value === 'create') {
      emit('closed');
    } else {
      resetForm();
    }
  } finally {
    processing.value = false;
  }
  try {
    if (editMode.value)
      await editRecord(payload)
    else
      await createRecord(payload)
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <FullScreenModal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="t('modalHeader.createCoupon')"
    :subtitle="t('modalSubtitle.createCoupon')"
    :icon="DiscountIcon"
    @close="emit('closed')"
    :panel-classes="'w-full bg-white rounded-xl shadow-[0px_8px_8px_-4px_rgba(10,13,18,0.04)] shadow-[0px_20px_24px_-4px_rgba(10,13,18,0.10)] flex flex-col overflow-visible sm:w-full sm:mx-20 h-full my-20'"
  >
    <overlay-loader v-if="processing" :fullScreen="false" />
    
    <form @submit.prevent="saveCoupon" class="flex flex-col gap-6 p-4">
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-3">        <!-- Coupon Code -->
        <div>
          <form-group name="code" :validation="v$">
            <template #default="{ attrs }">
              <TextInput
                :label="t('coupons.code')"
                v-bind="attrs"
                id="code"
                v-model="formData.code"
                :placeholder="`${t('enter')} ${t('coupons.code')}`"
                :hint="t('hints.coupon_code')"
                required
              />
            </template>
          </form-group>
        </div>
        <!-- Discount Amount -->
        <div>
          <form-group name="discount_amount" :validation="v$">
            <template #default="{ attrs }">
              <DiscountInput
                v-model="discountInput"
                :label="t('coupons.discount_amount')"
                :placeholder="t('enter_discount_amount')"
                :hint="t('hints.discount')"
                :min="0"
                :max="discountInput.discount_type === 'percentage' ? 100 : undefined"
                v-bind="attrs"
                required
              />
            </template>
          </form-group>
        </div>
        <!-- Start Date -->
        <div>
          <form-group name="start_date" :validation="v$">
            <template #default="{ attrs }">
              <DateInput
                :label="t('coupons.start_date')"
                v-bind="attrs"
                :is24hr="true"
                v-model="formData.start_date"
                id="start_date"
                @blur="attrs.onblur"
                required
              />
            </template>
          </form-group>
        </div>
        <!-- End Date -->
        <div>
          <form-group name="end_date" :validation="v$">
            <template #default="{ attrs }">
              <DateInput
                :label="t('coupons.end_date')"
                :is24hr="true"
                v-model="formData.end_date"
                id="end_date"
                v-bind="attrs"
                @blur="attrs.onblur"
                required
              />
            </template>
          </form-group>
        </div>
        <!-- Usage Limit -->
        <div>
          <form-group name="usage_limit" :validation="v$">
            <template #default="{ attrs }">
              <NumberInput
                :label="t('coupons.usage_limit')"
                :hint="t('hints.total_usage')"
                v-bind="attrs"
                id="usage_limit"
                v-model="formData.usage_limit"
                :placeholder="`${t('enter')} ${t('coupons.usage_limit')}`"
              />
            </template>
          </form-group>
        </div>
        <!-- Usage Limit Per User -->
        <div>
          <form-group name="usage_limit_per_user" :validation="v$">
            <template #default="{ attrs }">
              <NumberInput
                :label="t('coupons.usage_limit_per_user')"
                v-bind="attrs"
                id="usage_limit_per_user"
                v-model="formData.usage_limit_per_user"
                :placeholder="`${t('enter')} ${t('coupons.usage_limit_per_user')}`"
                :hint="t('hints.usage_per_user')"
              />
            </template>
          </form-group>
        </div>
        <div>
          <form-group name="products" :validation="v$">
            <template #default="{ attrs }">
              <MultiSelectInput
                v-bind="attrs"
                v-model="formData.products"
                :label="t('altNav.services')"
                :hint="t('hints.services_selection')"
                :placeholder="t('form.select')"
                :options="productsOptions"
                required
              />
            </template>
          </form-group>
        </div>
        <div>
          <form-group name="categories" :validation="v$">
            <template #default="{ attrs }">
              <MultiSelectInput
                v-bind="attrs"
                v-model="formData.categories"
                :label="t('altNav.categories')"
                :placeholder="t('form.select')"
                :options="categoriesOptions"
                required
              />
            </template>
          </form-group>
        </div>
        <div>
          <form-group name="branches" :validation="v$">
            <template #default="{ attrs }">
              <MultiSelectInput
                v-bind="attrs"
                v-model="formData.branches"
                :label="t('altNav.branches')"
                :placeholder="t('form.select')"
                :options="branchesOptions"
                required
              />
            </template>
          </form-group>
        </div>
      </div>
        <!-- Description (rich text) -->
      <div>
        <form-group error-name="description">
          <template #default="{ attrs }">
            <MiniEditor
              v-model="formData.description_localized"
              :placeholder="t('coupons.description')"
              :label="t('coupons.description')"
              required
            />
          </template>
        </form-group>
      </div>
      <!-- Status Switch -->
      <div class="flex items-center mt-4">
        <Switch
          id="status"
          v-model="statusModel"
          class="relative inline-flex flex-shrink-0 h-6 transition-colors duration-200 ease-in-out border-2 border-transparent rounded-full cursor-pointer w-11 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          :class="[statusModel ? 'bg-primary-600' : 'bg-gray-200']"
        >
          <span
            aria-hidden="true"
            class="inline-block w-5 h-5 transition duration-200 ease-in-out transform bg-white rounded-full shadow pointer-events-none ring-0"
            :class="[
              statusModel
                ? 'translate-x-5 rtl:-translate-x-5'
                : 'translate-x-0',
            ]"
          />
        </Switch>
        <LabelInput for="status" class="ms-3">{{ t("coupons.status") }}</LabelInput>
      </div>

      <!-- Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center mt-4">
        <BaseButton
          type="submit"
          :label="t('form.create')"
          variant="filled"
          size="lg"
          :loading="processing"
          :block="true"
          class="sm:w-[280px] lg:w-[480px]"
          @mousedown.prevent="buttonAction = 'create'"
        />

        <BaseButton
          type="submit"
          :label="t('save_and_add_new')"
          variant="stroke"
          size="lg"
          :loading="processing"
          :icon="ArrowLeftIcon"
          iconPosition="right"
          :block="true"
          class="sm:w-[280px] lg:w-[480px]"
          @mousedown.prevent="buttonAction = 'createAndAdd'"
        />
      </div>
    </form>
  </FullScreenModal>
</template>
