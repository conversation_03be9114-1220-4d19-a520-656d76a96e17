<script setup lang="ts">
import { ref, computed } from 'vue';
import type { PropType } from 'vue';
import { storeToRefs } from 'pinia';
import { useLocalesStore } from '@/stores/locales';
import type { Coupons } from '@/types/coupons';
import { useVuelidate } from '@vuelidate/core';
import { required } from '@/utils/i18n-validators';
import { useCoupons } from '@/stores/coupons';
import { Switch } from '@headlessui/vue';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  coupon: {
    type: Object as PropType<Coupons>,
    required: true
  }
});

const emit = defineEmits(['close']);
const { getLocale } = storeToRefs(useLocalesStore());
const { locale, t } = useI18n();
const { updateCouponInfo } = useCoupons();

const processing = ref(false);

// Form data
const formData = reactive({
  code: props.coupon.code,
  discount_amount: props.coupon.discount_amount,
  discount_type: props.coupon.discount_type,
  start_date: new Date(props.coupon.start_date),
  end_date: new Date(props.coupon.end_date),
  usage_limit: props.coupon.usage_limit,
  usage_limit_per_user: props.coupon.usage_limit_per_user
});

// Validation rules
const rules = {
  code: { required },
  discount_amount: { required },
  discount_type: { required },
  start_date: { required },
  end_date: { required },
  usage_limit: { required },
  usage_limit_per_user: { required }
};

const v$ = useVuelidate(rules, formData);

// Computed property for discount input
const discountInput = computed({
  get: () => ({
    discount_amount: formData.discount_amount,
    discount_type: formData.discount_type
  }),
  set: (value) => {
    formData.discount_amount = value.discount_amount;
    formData.discount_type = value.discount_type;
  }
});


const handleSubmit = async () => {
  v$.value.$touch();
  if (v$.value.$invalid) return;

  try {
    processing.value = true;
    const payload = {
      ...formData,
      start_date: formData.start_date.toISOString(),
      end_date: formData.end_date.toISOString(),
      usage_limit: String(formData.usage_limit),
      usage_limit_per_user: String(formData.usage_limit_per_user)
    };
    await updateCouponInfo(payload, props.coupon.uuid!);
    emit('close');
  } finally {
    processing.value = false;
  }
};
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="t('edit_coupon')"
    @close="$emit('close')"
  >
    <div class="flex flex-col gap-4 p-4">
      <overlay-loader v-if="processing" :fullScreen="false" />

      <form @submit.prevent="handleSubmit" class="flex flex-col gap-4">
        <!-- Coupon Code -->
        <div class="w-full">
          <TextInput
            v-model="formData.code"
            :label="t('coupons.code')"
            :placeholder="t('enter') + ' ' + t('coupons.code')"
            :hint="t('hints.coupon_code')"
            required
          />
        </div>

        <!-- Discount Amount -->
        <div class="w-full">
          <DiscountInput
            v-model="discountInput"
            :label="t('coupons.discount_amount')"
            :placeholder="t('enter_discount_amount')"
            :hint="t('hints.discount')"
            :min="0"
            :max="discountInput.discount_type === 'percentage' ? 100 : undefined"
            required
          />
        </div>

        <!-- Start Date -->
        <div class="w-full">
          <DateInput
            v-model="formData.start_date"
            :label="t('coupons.start_date')"
            :is24hr="true"
            required
          />
        </div>

        <!-- End Date -->
        <div class="w-full">
          <DateInput
            v-model="formData.end_date"
            :label="t('coupons.end_date')"
            :is24hr="true"
            required
          />
        </div>

        <!-- Usage Limit -->
        <div class="w-full">
          <NumberInput
            v-model="formData.usage_limit"
            :label="t('coupons.usage_limit')"
            :hint="t('hints.total_usage')"
            :placeholder="t('enter') + ' ' + t('coupons.usage_limit')"
            required
          />
        </div>

        <!-- Usage Limit Per User -->
        <div class="w-full">
          <NumberInput
            v-model="formData.usage_limit_per_user"
            :label="t('coupons.usage_limit_per_user')"
            :hint="t('hints.usage_per_user')"
            :placeholder="t('enter') + ' ' + t('coupons.usage_limit_per_user')"
            required
          />
        </div>

        <!-- Action Buttons -->
        <div class="flex w-full gap-3 mt-4">
          <BaseButton
            type="submit"
            variant="filled"
            size="md"
            class="w-full"
            :label="t('form.save')"
          />
        </div>
      </form>
    </div>
  </Modal>
</template> 