<script setup lang="ts">
import type { Coupons } from '@/types/coupons';
import type { PropType } from 'vue';
import EditIcon from '@/components/Icons/EditIcon.vue';

const props = defineProps({
  coupon: {
    type: Object as PropType<Coupons>,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['edit']);

function formatDateAndTime(dateStr: string | Date | undefined) {
  if (!dateStr) return '-';
  const d = new Date(dateStr);
  if (isNaN(d.getTime())) return dateStr;
  return d.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric' }) +
    ' | ' + d.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' });
}
</script>

<template>
  <div class="grid gap-6 p-4 w-full bg-gray-50 rounded-xl">
    <div class="bg-white rounded-lg shadow-sm p-4 w-full">
      <div class="flex justify-between items-center mb-2">
        <span class="text-lg font-bold text-neutral-900">{{$t('coupon_details') }}</span>
        <slot name="edit">
          <LilActionBtn
            :icon="EditIcon"
            :label="$t('form.edit')"
            @click="$emit('edit')"
          />
        </slot>
      </div>
      <div class="flex flex-row gap-x-8 flex-wrap gap-2">
        <!-- Basic Info Column -->
        <div class="flex flex-col gap-2 flex-1 min-w-[180px]">
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('coupons.code') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ coupon.code }}</span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('coupons.discount_amount') }}:</span>
            <span class="text-neutral-800 font-normal text-start">
              {{ coupon.discount_amount }}
              {{ coupon.discount_type === 'percentage' ? '%' : '' }}
            </span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('coupons.usage_limit') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ coupon.usage_limit }}</span>
          </div>
        </div>
        
        <!-- Usage Limits Column -->
        <div class="flex flex-col gap-2 flex-1 min-w-[180px]">
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('coupons.usage_limit_per_user') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ coupon.usage_limit_per_user }}</span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('coupons.start_date') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ formatDateAndTime(coupon.start_date) }}</span>
          </div>
          <div class="flex flex-row justify-between items-center text-sm">
            <span class="text-neutral-800 font-medium text-end">{{ $t('coupons.end_date') }}:</span>
            <span class="text-neutral-800 font-normal text-start">{{ formatDateAndTime(coupon.end_date) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template> 