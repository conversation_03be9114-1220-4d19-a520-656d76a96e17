<script setup lang="ts">
import { ref } from 'vue';
import type { PropType } from 'vue';
import { storeToRefs } from 'pinia';
import { useLocalesStore } from '@/stores/locales';
import type { Coupons } from '@/types/coupons';
import { useCoupons } from '@/stores/coupons';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  coupon: {
    type: Object as PropType<Coupons>,
    required: true
  }
});

const emit = defineEmits(['close']);
const { getLocale } = storeToRefs(useLocalesStore());
const { locale, t } = useI18n();
const { updateCouponDescription } = useCoupons();

const processing = ref(false);
const description = ref<{ ar: string; en: string }>({
  ar: typeof props.coupon.description === 'object' ? props.coupon.description.ar : props.coupon.description || '',
  en: typeof props.coupon.description === 'object' ? props.coupon.description.en : props.coupon.description || ''
});

const handleSubmit = async () => {
  if (!props.coupon.uuid) return;
  
  try {
    processing.value = true;
    await updateCouponDescription({ description: description.value }, props.coupon.uuid);
    emit('close');
  } finally {
    processing.value = false;
  }
};
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="isOpen"
    :title="t('edit_description')"
    @close="$emit('close')"
  >
    <div class="flex flex-col gap-4 p-4">
      <overlay-loader v-if="processing" :fullScreen="false" />

      <form @submit.prevent="handleSubmit" class="flex flex-col gap-4">
        <!-- Description Editor -->
        <div class="w-full">
          <MiniEditor
            v-model="description"
            :placeholder="t('coupons.description')"
            required
          />
        </div>

        <!-- Action Buttons -->
        <div class="flex w-full gap-3 mt-4">
          <BaseButton
            type="submit"
            variant="filled"
            :loading="processing"
            size="md"
            class="w-full"
            :label="t('form.save')"
          />
        </div>
      </form>
    </div>
  </Modal>
</template> 