<script lang="ts" setup>
import type { PropType } from 'vue';
import { computed } from 'vue';
import type { Coupons } from '@/types/coupons';
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue';
import LilActionBtn from '@/components/Buttons/LilActionBtn.vue';
import TrashIcon from '@/components/Icons/TrashIcon.vue';
import PlusIcon from "@heroicons/vue/20/solid/PlusIcon";


const props = defineProps({
  coupon: {
    type: Object as PropType<Coupons>,
    required: true,
  },
});

const emit = defineEmits(['edit', 'delete-item']);
const { t, locale } = useI18n();

const deleteItem = (type: 'product' | 'category' | 'branch', item: any) => {
  emit('delete-item', { type, item });
};
</script>

<template>
  <DisclosureWrapper :title="t('coupons.applicable_items')">
    <template #header-actions>
      <LilActionBtn
        :icon="PlusIcon"
        @click="$emit('edit')"
      />
    </template>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 pt-2">
      <!-- Column for Services/Products/Packages -->
      <div>
        <h4 class="pb-2 mb-2 px-3 py-2 text-gray-400 text-base border-b bg-gray-50 ">{{ t('fields.services') }}</h4>
        <div v-if="coupon.products && coupon.products.length > 0" class="space-y-2">
          <div v-for="(item, index) in coupon.products" :key="index" class="flex items-center justify-between px-3 py-2 text-sm border-b ">
            <span>{{ item.name[locale] }}</span>
            <LilActionBtn
              :icon="TrashIcon"
              class="text-rose-500"
              @click="deleteItem('product', item)"
              transparent
            />
          </div>
        </div>
        <div v-else class="text-sm text-gray-400">{{ t('dashboard.booking.no_service') }}</div>
      </div>

      <!-- Column for Categories -->
      <div>
        <h4 class="pb-2 mb-2 px-3 py-2  text-gray-400 text-base  border-b bg-gray-50 ">{{ t('altNav.categories') }}</h4>
        <div v-if="coupon.categories && coupon.categories.length > 0" class="space-y-2">
          <div v-for="(item, index) in coupon.categories" :key="index" class="flex items-center justify-between px-3 py-2 text-sm border-b ">
            <span>{{ item.name[locale] }}</span>
            <LilActionBtn
              :icon="TrashIcon"
              class="text-rose-500"
              @click="deleteItem('category', item)"
              transparent
            />
          </div>
        </div>
        <div v-else class="text-sm text-gray-400">{{ t('dashboard.booking.no_category') }}</div>
      </div>

      <!-- Column for Branches -->
      <div>
        <h4 class="pb-2 mb-2 px-3 py-2 text-gray-400 text-base border-b bg-gray-50 ">{{ t('altNav.branches') }}</h4>
        <div v-if="coupon.branches && coupon.branches.length > 0" class="space-y-2">
          <div v-for="(item, index) in coupon.branches" :key="index" class="flex items-center justify-between px-3 py-2 text-sm border-b ">
            <span>{{ item.name[locale] }}</span>
            <LilActionBtn
              :icon="TrashIcon"
              class="text-rose-500"
              @click="deleteItem('branch', item)"
              transparent
            />
          </div>
        </div>
        <div v-else class="text-sm text-gray-400">{{ t('dashboard.booking.no_branch') }}</div>
      </div>
    </div>
  </DisclosureWrapper>
</template> 