<script lang="ts" setup>
import type { ComputedRef } from 'vue'
import { storeToRefs } from 'pinia'
import type { Coupons, header } from '@/types'
import { formatDate } from '@/composables/dateFormat'
import { useCoupons } from '@/stores/coupons'
const emit = defineEmits(['openEditModal'])
const { processing, getCoupons } = storeToRefs(useCoupons())
const { t } = useI18n()
const headers: ComputedRef<header[]> = computed(() => [
  {
    title: t('coupons.code'),
  },
  {
    title: t('coupons.discount_type'),
  },
  {
    title: t('coupons.discount_amount'),
  },
  {
    title: t('status'),
  },
  {
    title: t('coupons.startAt'),
  },
  {
    title: t('coupons.endAt'),
  },
])

const openEditModal = (coupon: Coupons) => {
  emit('openEditModal', coupon)
}
</script>

<template>
  <generic-table
    :is-loading="processing"
    :data="getCoupons"
    :headers="headers"
    item-key="uuid"
    :on-row-click="openEditModal"
    tr-class="cursor-pointer"
  >
    <template #row="{ item }">
      <grid-td>
        {{ item.code }}
      </grid-td>
      <grid-td>
        {{ item.discount_type == 'fixed' ? $t("coupons.fixed_discount") : $t("coupons.percentage_discount") }}
      </grid-td>
      <grid-td>
        {{ item.discount_amount }}
      </grid-td>
      <grid-td>
        {{ item.status ? $t("coupons.status") : $t("coupons.inactive") }}
      </grid-td>
      <grid-td>
        {{ formatDate(new Date(item.start_date)) }}
      </grid-td>

      <grid-td>
        {{ formatDate(new Date(item.end_date)) }}
      </grid-td>
    </template>
  </generic-table>
</template>
