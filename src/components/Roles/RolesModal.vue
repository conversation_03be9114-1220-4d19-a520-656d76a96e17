<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import type { PropType } from 'vue'
import { makeArrayEmpty } from '../../utils/makeArrOfErrEmpty'
import Modal from '../Common/Modal.vue'
import { useRoles } from '@/stores/roles'
import type { Roles } from '@/types/roles'
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  role: {
    type: Object as PropType<Roles | null>,
    default: () => ({}),
  },
})

const emit = defineEmits(['close'])

const { locale } = useI18n()
const { role } = toRefs(props)
const { getLocale } = storeToRefs(useLocalesStore())
const { createRoles, getAllPermission, updateRoles, deleteRoles } = useRoles()
const formData = reactive({
  name: '',
  permissions: [],
  editable: true,
})
const rules = {
  name: {
    required,
  },
}
const processing = ref(false)
const errHandle = reactive<{ [key: string]: string[] }>({
})
watch(role, (val: Roles) => {
//   if (val?.name) {
  formData.name = val?.name || ''
  formData.editable = val?.editable || false
  formData.permissions = val?.permissions.map(item => item.uuid) || []
//   }
})
const $v = useVuelidate(rules, formData)
const submitForm = () => {
  $v.value.$touch()
  if ($v.value.$invalid)
    return
  makeArrayEmpty(errHandle)

  processing.value = true
  if (!role.value?.name) {
    formData.permissions = formData.permissions.slice(1)
    createRoles(formData).then(() => {
      emit('close')
    })
      .catch((err) => {
        if (err.errors)
          for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        processing.value = false
      })
  }
  else {
    updateRoles(formData, role.value?.uuid).then(() => {
      emit('close')
    })
      .catch((err) => {
        if (err.errors)
          for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        processing.value = false
      })
  }
}
const deleteRole = () => {
  processing.value = true
  deleteRoles(role.value?.uuid).finally(() => {
    processing.value = false
    emit('close')
  })
}
const permissions = ref<{ name: string; uuid: string;group: string }[]>([])
const groups = ref()
const permissionGroups = computed(() => {
  const values = permissions.value
  return values.reduce((groups, permission) => {
    const groupName = permission.group
    if (!groups[groupName])
      groups[groupName] = []

    groups[groupName].push(permission)
    return groups
  }, {})
})
onMounted(async () => {
  processing.value = true

  getAllPermission().then((res) => {
    permissions.value = res.data
    groups.value = permissionGroups.value
  }).finally(() => {
    processing.value = false
  })
})

const availablePermissions = computed(() => {
  return Object.values(permissionGroups.value).flat().filter(per => Boolean(per))
})
const allSelected = computed(() => {
  return availablePermissions.value.length === formData.permissions.length
})
</script>

<template>
  <Modal :dir="getLocale(locale)?.direction" :open="showModal" title="roles" @close="$emit('close')">
    <overlay-loader v-if="processing" />
    <form
      :class="[
        getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
      ]" @submit.prevent="submitForm"
    >
      <div v-if="!formData.editable">
        <ul class="relative px-3 py-2 mb-2 text-sm text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
          <li class="mt-2 font-bold">
            {{ $t('defaults roles cannot be edited or deleted.') }}
          </li>
        </ul>
      </div>
      <err-validations :err-handle="errHandle" />

      <div class="grid grid-cols-1 gap-4">
        <div>
          <TextInput
            id="code"
            v-model="formData.name"
            :label="$t('form.name')" :disabled="!formData.editable"
            :class="[formData.editable ? 'bg-gray-50' : 'bg-gray-200']"
            class="block w-full py-3 leading-tight text-gray-700 border rounded appearance-none ps-4 pe-4 focus:outline-none focus:bg-white focus:border-gray-500"
            :placeholder="`${$t('formPlaceHolder.name')}`"
            required
          />
          <p v-for="error of $v.name.$errors" :key="error.$uid" class="error-message">
            {{ $t(`${error.$message}`) }}
          </p>
        </div>
      </div>
      <div class="mt-2">
        <CheckInput
          id="all"
          class="w-6 h-7"
          type="checkbox"
          :model-value="allSelected"
          @update:model-value="allSelected ? formData.permissions = [] : formData.permissions = availablePermissions.map((per) => per.uuid)"
        />
        <LabelInput
          class="inline-block"
          for="all"
        >
          {{ $t('all') }}
        </LabelInput>
      </div>
      <!-- select All btn -->

      <div class="grid h-full grid-cols-1 gap-2 mt-4 sm:grid-cols-2">
        <div v-for="(group, groupName) in groups" :key="groupName">
          <div v-for="(permission, index) in group" :key="permission.uuid">
            <input
              :id="permission?.uuid"
              v-model="formData.permissions"
              :disabled="!formData.editable"
              :class="[formData.editable ? 'bg-gray-50' : 'bg-gray-200']"
              type="checkbox"
              :value="permission?.uuid"
              class="w-5 h-5 border-gray-300 rounded text-primary-600 focus:ring-primary-600 "
            >
            <label class="ms-2" :for="permission?.uuid">{{ $t(`permissions.${permission?.name.replaceAll('.', '_')}`) }}</label>
          </div>
        </div>
      </div>

      <div class="mt-8">
        <div class="flex items-center justify-end">
          <div
            v-if="role?.name"
            class="flex gap-2"
          >
            <BaseButton
              v-if="formData.editable"
              type="button"
              class="w-1/2 py-3 mx-auto hover:bg-red-700"
              custome-bg="bg-red-600"
              show-icon
              :processing="processing"
              @click="deleteRole"
            >
              {{ $t("form.delete") }}
            </BaseButton>
            <BaseButton
              v-if="formData.editable"
              type="submit"
              class="w-1/2 py-3 mx-auto hover:bg-primary-800"
              custome-bg="bg-primary-700"
              show-icon
              :processing="processing"
            >
              {{ $t("form.update") }}
            </BaseButton>
          </div>
          <BaseButton
            v-else
            type="submit"
            class="py-3 hover:bg-green-700"
            custome-bg="bg-green-600"
            show-icon
            :processing="processing"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </Modal>
</template>
