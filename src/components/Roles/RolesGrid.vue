<script lang="ts" setup>
import type { ComputedRef, PropType } from 'vue'
import { storeToRefs } from 'pinia'
import type { Roles } from '../../types/roles'
import { useRoles } from '@/stores/roles'
import type { header } from '@/types'

const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['openModal'])

const { roles } = storeToRefs(useRoles())
const { t } = useI18n()
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('settings.teamusers.name'),
    },
  ]
})

const openModal = (role: Roles) => {
  emit('openModal', role)
}
</script>

<template>
  <generic-table
    :is-loading="isLoading"
    :data="roles"
    :headers="headers"
    tr-class="cursor-pointer"
    :on-row-click="openModal"
  >
    <template #row="{ item }">
      <grid-td>
        {{ item.name }}
      </grid-td>
    </template>
  </generic-table>
</template>
