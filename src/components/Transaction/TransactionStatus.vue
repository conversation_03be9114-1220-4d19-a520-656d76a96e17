<script setup>
const props = defineProps({
  status: {
    type: String,
    default: '',
  },
})
const statusClass = computed(() => {
  switch (props.status) {
    case 'confirmed':
      return 'bg-green-100 text-green-800'
    case 'refused':
      return 'bg-red-100 text-red-800'
    case 'pending':
      return 'bg-gray-100 text-gray-800'
  }
})
</script>

<template>
  <span class="inline-flex items-center rounded px-2.5 py-0.5 text-sm font-medium" :class="statusClass">{{ $t(`transactions.status.${status}`) }}</span>
</template>
