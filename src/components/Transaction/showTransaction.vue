<script  lang="ts" setup>
import { storeToRefs } from 'pinia'
import useTransaction from '@/composables/useTransaction'
import { useTransactionState } from '@/stores/transaction'
import { useBank } from '@/stores/bank'
import excelExport from '@/composables/useExcelExport'

const props = defineProps({
  reFetch: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['showModal'])

const { processingExport, getExcel } = excelExport('Transaction')

const paymenet = useBank()
const payments = ref([])
const { locale, t } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const selectedTransaction = ref<object>({})
const { tableData, fetchTransactionPage } = useTransaction()
const { updateTrans, fetchBookingTransactionById } = useTransactionState()
const showModalTransaction = ref(false)
const processing = ref(false)
const status = ref(['refused', 'pending', 'confirmed'])
const range: any = ref([])
const filterTransaction = ref('')
const route = useRoute()
const transactionList = ref([])
const loadingTransactionTable = ref(false)

watch(
  () => props.reFetch,
  (val) => {
    if (val) {
      loadingTransactionTable.value = true
      fetchBookingTransactionById(route.params.id)
        .then((res) => {
          transactionList.value = res.data.data
        })
        .finally(() => {
          loadingTransactionTable.value = false
        })
    }
  },
)

onMounted(() => {
  loadingTransactionTable.value = true
  fetchBookingTransactionById(route.params.id).then((res) => {
    transactionList.value = res.data.data
  }).finally(() => {
    loadingTransactionTable.value = false
  })
})
const openModal = (selectedTrans: Object) => {
  selectedTransaction.value = selectedTrans
  showModalTransaction.value = true
}
const updateTransStatus = (status: string) => {
  processing.value = true
  updateTrans(selectedTransaction?.value.id, status).then(() => {
    fetchTransactionPage()
  }).finally(() => {
    processing.value = false
    showModalTransaction.value = false
  })
}

const headers = computed(() => {
  return [
    {
      title: t('transaction.transaction_number'),

    },
    {
      title: t('transaction.date'),
    },
    {
      title: t('transaction.payment_method'),

    },
    {
      title: t('transaction.amount'),
    },
    {
      title: t('transaction.status'),
    },
    {
      title: '#',
    },
  ]
})
</script>

<template>
  <div>
    <Modal :dir="getLocale(locale)?.direction" :open="showModalTransaction" title="showTransaction" @close="showModalTransaction = false">
      <div
        class="relative mt-10" :class="[
          getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
        ]"
      >
        <overlay-loader v-if="processing" :full-screen="false" />

        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div class="flex flex-col">
            <TextInput
              id="grid-first-name"
              v-model="selectedTransaction.date"
              :label="$t('Date')"
              :placeholder="$t('Date')"
              disabled
            />
          </div>
          <div class="flex flex-col">
            <TextInput
              id="grid-first-name"
              v-model="selectedTransaction.amount"
              :label="$t('dashboard.booking.amount')"
              :placeholder="$t('dashboard.booking.amount')"
              disabled
            />
          </div>
          <div class="flex flex-col">
            <TextInput
              id="grid-first-name"
              :label="$t('dashboard.booking.paymentMethod')"
              :placeholder="$t('dashboard.booking.paymentMethod')"
              :value="$t(`paymentMethod.${selectedTransaction.payment_method.slug}`)"
              disabled
            />
          </div>
          <div class="flex flex-col">
            <TextInput
              id="grid-first-name"
              :label="$t('booking.status')"
              :placeholder="$t('booking.status')" :value="$t(`transaction.${selectedTransaction.status}`)"
              :class="[selectedTransaction.status === 'confirmed' ? 'border border-green-400 text-green-400' : selectedTransaction.status === 'refused' ? 'border border-red-400 text-red-400' : '']"
              disabled
            />
          </div>

          <div v-if="selectedTransaction.recipt_file" class="flex flex-col">
            <label
              class="block mb-2 text-xs font-bold tracking-wide text-gray-700" for="grid-first-name"
            >
              {{ $t("transaction.attachments") }}
            </label>
            <a
              target="_blank" :href="selectedTransaction.recipt_file"
              class="inline-flex"
            >
              <DocumentIcon class="w-8 h-8 ms-2 -me-0.5" aria-hidden="true" />
            </a>
          </div>
        </div>
      </div>
      <div v-if="selectedTransaction.status === 'pending'" class="flex gap-4 mt-6">
        <BaseButton
          class="inline-flex hover:bg-green-700" custome-bg="bg-green-600" show-icon :processing="processing"
          @click="updateTransStatus('confirmed')"
        >
          {{ $t("form.approveTrans") }}
        </BaseButton>
        <BaseButton
          class="inline-flex hover:bg-red-700" custome-bg="bg-red-600" :processing="processing"
          @click="updateTransStatus('refused')"
        >
          {{ $t("form.rejectTrans") }}
        </BaseButton>
      </div>
    </modal>
    <div v-if="transactionList?.length > 0" class="flex flex-col gap-4 mt-8">
      <div class="flex justify-between items-center">
        <h2 class="font-semibold sm:text-2xl">
          {{ $t('settings.navigation.payement') }}
        </h2>
        <BaseButton
          type="button" class="inline-flex text-sm bg-green-600 w-fit disabled:bg-green-300"
          @click="emits('showModal')"
        >
          {{ $t('bookingItems.bookPay') }}
        </BaseButton>
      </div>
      <div class="">
        <generic-table
          :headers="headers"
          :data="transactionList"
          item-key="id"
        >
          <template #row="{ item }">
            <grid-td>{{ item.transaction_no }}</grid-td>
            <grid-td>{{ item.date }}</grid-td>
            <grid-td>{{ item.payment_method?.name }}</grid-td>
            <grid-td>{{ item.amount }}</grid-td>
            <grid-td>
              <span v-if="item?.status === 'refused'" class="inline-flex items-center px-2.5 py-0.5 text-sm font-medium text-red-800 bg-red-100 rounded">{{ $t(`transaction.refused`) }}</span>
              <span v-if="item?.status === 'pending'" class="inline-flex items-center px-2.5 py-0.5 text-sm font-medium text-yellow-800 bg-yellow-100 rounded">{{ $t(`transaction.pending`) }}</span>
              <span v-if="item?.status === 'confirmed'" class="inline-flex items-center px-2.5 py-0.5 text-sm font-medium text-green-800 bg-green-100 rounded">{{ $t('transaction.confirmed') }}</span>
            </grid-td>
            <grid-td>
              <BaseButton
                class="inline-flex hover:bg-gray-700" custome-bg="bg-gray-800" show-icon type="button"
                @click="openModal(item)"
              >
                {{ $t("form.showtransaction") }}
              </BaseButton>
            </grid-td>
          </template>
        </generic-table>

        <Pagination
          v-if="tableData.transactionList.length" :pagination-meta="tableData.paginationMeta"
          :pagination-links="tableData.paginationLinks" @change="fetchTransactionPage"
        />
      </div>
    </div>
    <div v-else>
      <h2 class="mb-2 font-semibold sm:text-2xl">
        {{ $t('settings.navigation.payement') }}
      </h2>
      <empty-state text="bookPay" btn-text="register" @click="emits('showModal')" />
    </div>
  </div>
</template>

<style>

</style>
