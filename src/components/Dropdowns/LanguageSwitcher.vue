<script setup lang="ts">
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/vue'
import { storeToRefs } from 'pinia'
import { SELECTED_LOCALE } from '@/constants'

const { getLocales, getLocale } = storeToRefs(useLocalesStore())
const { updateLang } = useOnBoardingStore()
const { locale } = useI18n()
watch(locale, (value) => {
  localStorage.setItem(SELECTED_LOCALE, JSON.stringify(value))
  updateLang({ locale: value })
})
</script>

<template>
  <Listbox v-model="locale" as="div">
    <div class="relative">
      <ListboxButton
        class="relative inline-flex items-center justify-between w-16 py-3 text-sm border-none rounded-full shadow-none cursor-pointer ps-5 pe-5 lg:w-24 text-start bg-gray-50 sm:p-2 sm:text-md"
      >
        <span class="hidden lg:block">{{ getLocale(locale)?.textSymbol }}</span>

        <!-- TODO: Find a better way to load svg icons dynamically -->
        <img v-if="getLocale(locale)?.id === 'ar'" width="24" height="24" class="rounded-full" src="../../assets/icons/language/ar.svg" :alt="getLocale(locale)?.textSymbol">
        <img v-else-if="getLocale(locale)?.id === 'en'" width="24" height="24" class="rounded-full" src="../../assets/icons/language/en.svg" :alt="getLocale(locale)?.textSymbol">
      </ListboxButton>

      <transition
        leave-active-class="transition duration-100 ease-in"
        leave-from-class="opacity-100" leave-to-class="opacity-0"
      >
        <ListboxOptions
          class="absolute z-10 w-full py-1 mt-1 overflow-auto text-sm rounded-md shadow-lg bg-tertiary max-h-60 sm:text-md "
        >
          <ListboxOption
            v-for="localLocale in getLocales" :key="localLocale.id"
            v-slot="{ active, selected }" as="template" :value="localLocale.id"
          >
            <li
              class="relative py-2 select-none ps-2 pe-2 cursor-pointer"
              :class="[
                active ? 'text-white bg-primary' : 'text-black',
              ]"
            >
              <div
                class="flex items-center justify-between truncate" :class="[
                  selected ? 'font-semibold' : 'font-normal',
                ]"
              >
                <!-- TODO: Find a better way to load svg icons dynamically -->
                <img v-if="localLocale?.id === 'ar'" width="24" height="24" class="rounded-full" src="../../assets/icons/language/ar.svg" :alt="localLocale?.textSymbol">
                <img v-else-if="localLocale?.id === 'en'" width="24" height="24" class="rounded-full" src="../../assets/icons/language/en.svg" :alt="localLocale?.textSymbol">
                {{ localLocale.textSymbol }}
                <span class="sr-only"> {{ localLocale.textSymbol }}</span>
              </div>
            </li>
          </ListboxOption>
        </ListboxOptions>
      </transition>
    </div>
  </Listbox>
</template>
