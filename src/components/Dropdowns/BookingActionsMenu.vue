<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue'
import { PencilIcon as EditIcon } from '@heroicons/vue/24/outline'
import { ArrowPathIcon as RepeatIcon } from '@heroicons/vue/20/solid'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import TrashIcon from '@/components/Icons/TrashIcon.vue'
const props = defineProps({
  item: Object,
  getOrderDetails: Object,
  items: Array,
})
const { t } = useI18n()

const hasAvailableActions = computed(() => {
  // Check if any action is available
  const hasEditAction = props.item.booking && ['confirmed', 'completed', 'unapproved'].includes(props.item.booking.status)
  const hasRecurringAction = props.item.booking && !props.item.booking.is_recurring && !props.item.booking.recurring_appointemnts?.length
  const hasCancelAction = props.item.booking
  const hasDeleteAction = !props.getOrderDetails.invoices && props.items?.length > 1

  return hasEditAction || hasRecurringAction || hasCancelAction || hasDeleteAction
})

const buttonRef = ref(null)
const menuRef = ref(null)
const menuStyles = ref({ display: 'none' })
const isMenuOpen = ref(false)
function setOpen(val) {
  isMenuOpen.value = val
  return ''
}

watch(
  isMenuOpen,
  async (isOpen) => {
    if (isOpen) {
      await nextTick()
      let buttonEl = buttonRef.value
      if (buttonEl && buttonEl.$el)
        buttonEl = buttonEl.$el
      if (buttonEl && typeof buttonEl.getBoundingClientRect === 'function') {
        const rect = buttonEl.getBoundingClientRect()
        menuStyles.value = {
          position: 'fixed',
          top: `${rect.bottom + 4}px`,
          left: `${rect.left}px`,
          zIndex: 9999,
          display: 'block',
          minWidth: `${rect.width}px`,
        }
      }
      else {
        menuStyles.value = { position: 'fixed', top: '100px', left: '100px', zIndex: 9999, display: 'block' }
      }
    }
    else {
      menuStyles.value = { display: 'none' }
    }
  },
  { immediate: true },
)
</script>

<template>
  <Menu v-if="hasAvailableActions" v-slot="{ open }" as="div" class="inline-block relative w-full text-left">
    <MenuButton ref="buttonRef" class="flex justify-center items-center w-8 h-8 rounded-full hover:bg-gray-100 focus:outline-none">
      <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="1.5" />
        <circle cx="19.5" cy="12" r="1.5" />
        <circle cx="4.5" cy="12" r="1.5" />
      </svg>
    </MenuButton>
    <teleport to="body">
      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="opacity-0 transform scale-95"
        enter-to-class="opacity-100 transform scale-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="opacity-100 transform scale-100"
        leave-to-class="opacity-0 transform scale-95"
      >
        <MenuItems
          v-if="isMenuOpen"
          ref="menuRef"
          :style="menuStyles"
          class="fixed z-[9999] w-44 mt-2 origin-top-right bg-white rounded-xl shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none p-2 flex flex-col gap-2 rtl:text-right ltr:text-left"
        >
          <MenuItem v-if="item.booking && ['confirmed', 'completed', 'unapproved'].includes(item.booking.status)" v-slot="{ active }">
            <button
              class="flex gap-2 justify-end items-center px-4 py-2 w-full text-sm"
              :class="{ 'bg-gray-100': active }"
              @click.stop="$emit('open-edit-modal', item.booking.uuid)"
            >
              {{ $t('form.edit') }}
              <EditIcon class="w-5 h-5" />
            </button>
          </MenuItem>
          <MenuItem v-if="item.booking && !Boolean(item.booking.is_recurring) && !(item.booking.recurring_appointemnts?.length > 0)" v-slot="{ active }">
            <button
              class="flex gap-2 justify-end items-center px-4 py-2 w-full text-sm"
              :class="{ 'bg-gray-100': active }"
              @click.stop="$emit('open-recurring-modal', item.booking.uuid)"
            >
              {{ $t('recurring_appointment') }}
              <RepeatIcon class="w-5 h-5" />
            </button>
          </MenuItem>
          <MenuItem v-if="item.booking" v-slot="{ active }">
            <button
              class="flex items-center justify-end w-full px-4 py-2 text-sm text-[#E53935] gap-2"
              :class="{ 'bg-gray-100': active }"
              @click.stop="$emit('cancel-booking', item.booking.uuid)"
            >
              {{ $t('bookingItems.cancel') }}
              <span class="w-5 h-5">
                <svg fill="none" stroke="#E53935" stroke-width="2" viewBox="0 0 24 24" class="w-5 h-5"><circle cx="12" cy="12" r="10" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 9l-6 6m0-6l6 6" /></svg>
              </span>
            </button>
          </MenuItem>
          <MenuItem v-if="!getOrderDetails.invoices && items?.length > 1" v-slot="{ active }">
            <button
              class="flex items-center justify-end w-full px-4 py-2 text-sm text-[#E53935] gap-2"
              :class="{ 'bg-gray-100': active }"
              @click.stop="$emit('delete-item', item.sale_service)"
            >
              {{ $t('delete_item') }}
              <TrashIcon class="w-5 h-5" />
            </button>
          </MenuItem>
        </MenuItems>
      </transition>
    </teleport>
    <input type="hidden" :value="setOpen(open)">
  </Menu>
</template>
