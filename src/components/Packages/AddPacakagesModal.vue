<script setup lang="ts">
import type { PropType } from 'vue'
import { Switch } from '@headlessui/vue'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import { PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'
import useVuelidate from '@vuelidate/core'
import Multiselect from 'vue-multiselect'
import 'vue-multiselect/dist/vue-multiselect.css'
import { storeToRefs } from 'pinia'
import { convertToHoursAndMinutes } from '../../utils/time'
import { minLength, required, requiredIf } from '@/utils/i18n-validators'

import type { HoursMinutes, PackageServices, Service, Staff } from '@/types'
const props = defineProps({
  service: {
    type: Object as PropType<Service>,
    default: null,
  },
  showModal: {
    type: Boolean,
    default: false,
  },
  random: {
    type: String,
    default: '#000000',
  },
  teamStaff: {
    type: Array as PropType<Staff[]>,
    default: () => [],
  },
})
const emit = defineEmits(['created', 'closed', 'updated'])
const { fetchCategories, createCategory } = useCategoryStore()
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { createPackage, updatePackage, fetchServices } = useServicesStore()
const { userInfo } = useAuthStore()
const { getUserInfo } = storeToRefs(useAuthStore())
const tagStore = useTagStore()
const { tags: tagsList } = storeToRefs(tagStore)
const { fetchTags } = useTagStore()
const editMode = ref(false)
const processing = ref(false)
const servicesOptions = ref([])
const default_price = 0
const formData = reactive<Omit<Service, 'uuid'>>({
  name_localized: {
    ar: '',
    en: '',
  },
  description_localized: {
    ar: '',
    en: '',
  },
  slug: '',
  is_public: true,
  color: props.random,
  price: default_price,
  sort_order: 0,
  service_type: 'package',
  package_valid_period: '',
  package_max_number_of_available: '',
  category_id: '',
  display_on_booking_page: true,
  location: 'on-site',
  tags: [],
  team_id: getUserInfo.value.teams[0]?.uuid || '',
  base_link: '',
  services: [
    {
      id: '',
      quantity: 0,
    },
  ],
})
const serviceObject: PackageServices = {
  id: '',
  quantity: 0,
}
const { service } = toRefs(props)
onMounted(() => {
  updateServicesOptions(formData.team_id)
})
function updateServicesOptions(team) {
  fetchServices('*', {
    team,
  }).then((res) => {
    servicesOptions.value = res.data.map((service) => {
      return {
        label: service.name,
        value: service.uuid,
      }
    })
  })
}
watch(
  () => formData.team_id,
  (team) => {
    updateServicesOptions(team)
  },
)
watch(
  service,
  (value: Service) => {
    if (value) {
      const {
        // duration,
        name_localized,
        category_id,
        imageLink,
        description_localized,
        slug,
        color,
        price,
        sort_order,
        // tags,
        team_id,
        is_public,
        display_on_booking_page,
        staff,
        package_valid_period,
        package_max_number_of_available,
        base_link,
        services,
      } = value
      formData.name_localized = name_localized || { ar: '', en: '' }
      formData.category_id = category_id?.uuid || ''
      formData.imageLink = imageLink || ''
      formData.description_localized = description_localized || {
        ar: '',
        en: '',
      }
      formData.slug = slug || ''
      // formData.staff_users = staff || [];
      formData.color = color || '#000000'
      formData.display_on_booking_page = display_on_booking_page
      formData.is_public = is_public
      formData.sort_order = sort_order
      formData.base_link = base_link
      formData.package_valid_period = package_valid_period
      formData.package_max_number_of_available
        = package_max_number_of_available
      formData.services = services?.length
        ? [...services]
        : [
            {
              id: '',
              quantity: 0,
            },
          ]

      formData.price = price || default_price
      // formData.image = "";
      // formData.tags = tags || [];
      formData.team_id = team_id || ''
      // setEditMode
      editMode.value = true
    }
  },
  { immediate: true, deep: true },
)

const closeModal = () => {
  emit('closed')
}
const rules = {
  name_localized: {
    ar: {
      required: requiredIf(() => !formData.name_localized.en.trim()),
      minLength: minLength(3),
    },
    en: {
      required: requiredIf(() => !formData.name_localized.ar.trim()),
      minLength: minLength(3),
    },
  },
  slug: {
    required: requiredIf((val) => {
      return editMode.value
    }),
  },
  price: {
    required,
  },
  team_id: {
    required,
  },
  services: {
    required,
  },
  category_id: {
    required,
  },
}

const v$ = useVuelidate(rules, formData)
// category modal logic

const categories = ref<Object[]>([])
onMounted(() => {
  fetchCategories().then(({ data }) => {
    categories.value = data
  })
})
const showAddCategoryForm = ref(false)
const categoryData = reactive({
  name_localized: { ar: '', en: '' },
  type: 'package',
  team_id: getUserInfo.value.teams[0].uuid,
  processing: false,
})
const cateValidation = reactive({
  name_localized: {
    ar: {
      required: requiredIf(() => !categoryData.name_localized.en.trim()),
    },
    en: {
      required: requiredIf(() => !categoryData.name_localized.ar.trim()),
    },
  },
  team_id: {
    required,
  },
})
const v1$ = useVuelidate(cateValidation, categoryData)
const createNewCategory = async () => {
  v1$.value.$touch()
  if (v1$.value.$invalid)
    return false
  categoryData.processing = true
  createCategory({
    name_localized: JSON.stringify(categoryData.name_localized),
    team_id: categoryData.team_id,
    description_localized: JSON.stringify({ ar: '', en: '' }),
    type: 'package',
  })
    .then(({ data }: { data: { uuid: string } }) => {
      categories.value.push(data)
      formData.category_id = data.uuid
    })
    .finally(() => {
      categoryData.team_id = getUserInfo.value.teams[0].uuid
      categoryData.processing = false
      showAddCategoryForm.value = false
      v1$.value.$reset()
    })
}

watch(
  () => formData.team_id,
  (val) => {
    formData.category_id = ''
    fetchCategories().then(({ data }) => {
      categories.value = data
    })
  },
)

const getTeam = computed(() => {
  return getUserInfo.value.teams
})
const resetForm = () => {
  for (const key in formData)
    formData[key] = ''

  formData.name_localized = { ar: '', en: '' }
  // formData.category_id = "";
  // formData.imageLink = "";
  formData.description_localized = { ar: '', en: '' }
  formData.slug = ''
  formData.color = '#000000'
  formData.price = default_price
  formData.is_public = true
  formData.sort_order = 0
  formData.service_type = 'package'
  // formData.image = "";
  formData.services = [
    {
      id: '',
      quantity: 0,
    },
  ];
  (formData.team_id = getUserInfo.value.teams[0]?.uuid || ''),
  (formData.location = 'on-site')
  formData.display_on_booking_page = true
  // formData.tags = [];
  editMode.value = false
  v$.value.$reset()
}
const resetCategoryForm = () => {
  categoryData.name = ''
  categoryData.type = 'service'
  categoryData.processing = false
  v1$.value.$reset()
}
const createRecord = async (payload: Service) => {
  return createPackage(payload).then((res) => {
    resetForm()
    emit('created', res.data)
  })
}

const editRecord = async (payload: Service) => {
  processing.value = true
  return updatePackage(service.value?.uuid, payload).then((res) => {
    resetForm()
    emit('updated', res)
    processing.value = false
    closeModal()
  })
}
const removeService = (index: number) => {
  formData.services.splice(index, 1)
}
const addNewService = () => {
  formData.services.push({ ...serviceObject })
}
const isPublicService = computed(() => formData.is_public)

const saveService = async () => {
  v$.value.$touch()

  if (v$.value.$invalid)
    return false

  processing.value = true
  try {
    formData.display_on_booking_page = formData.display_on_booking_page ? 1 : 0
    // formData.tags = Array.isArray(formData.tags) ? formData.tags.map((tag) => tag.id)?.join(",") : "";

    if (formData.imageLink === service?.value?.imageLink)
      delete formData.imageLink
    const payload: Service = {
      ...(formData as Service),
    }

    delete payload.base_link
    if (editMode.value) {
      await editRecord({
        ...payload,
        name_localized: JSON.stringify(formData.name_localized),
        description_localized: JSON.stringify(formData.description_localized),
      })
    }
    else {
      await createRecord({
        ...payload,
        name_localized: JSON.stringify(formData.name_localized),
        description_localized: JSON.stringify(formData.description_localized),
      })
    }
  }
  catch (error) {
    console.error(error)
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    title="package"
    @close="closeModal"
  >
    <overlay-loader v-if="processing" :full-screen="false" />
    <modal
      v-if="showAddCategoryForm"
      :open="showAddCategoryForm"
      title="category"
      :dir="getLocale(locale)?.direction"
      @close="showAddCategoryForm = false"
    >
      <form
        :class="[
          getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
        ]"
        @submit.prevent="createNewCategory"
      >
        <overlay-loader v-if="processing" :full-screen="false" />

        <div class="grid grid-cols-6 gap-6">
          <div class="col-span-3">
            <label
              class="block mb-2 text-sm font-bold tracking-wide text-gray-700"
              for="category-name"
            >
              {{ $t("form.name") }}
            </label>
            <LangInput
              v-model="categoryData.name_localized"
              :placeholder="$t('form.name')"
              :v$="v1$"
            />
          </div>
          <div class="col-span-3">
            <div class="">
              <form-group :validation="v1$" name="team_id">
                <template #default="{ attrs }">
                  <SelectInput
                    v-bind="attrs"
                    id="team_id"
                    v-model="categoryData.team_id"
                    :label="$t('modalPlacholder.branch')"
                    :placeholder="$t('modalPlacholder.branch')"
                    class="py-3 w-full text-gray-700 rounded-md border-gray-300"
                  >
                    <option hidden selected value="">
                      {{ $t("form.select") }}
                    </option>
                    <option
                      v-for="team in getTeam"
                      :key="team?.uuid"
                      :value="team?.uuid"
                    >
                      {{ team.name }}
                    </option>
                  </SelectInput>
                </template>
              </form-group>
            </div>
          </div>
        </div>
        <div class="mt-5 sm:mt-6">
          <BaseButton
            type="submit"
            show-icon
            class="mx-auto w-1/2 d-block hover:bg-green-700"
            custome-bg="bg-green-600"
            :processing="categoryData.processing"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </form>
    </modal>
    <form
      class="flex flex-wrap gap-3 mb-4 w-full lg:basis-1/4 text-start"
      @submit.prevent="saveService"
    >
      <div class="full">
        <label
          class="mb-2 w-full flex items-center text-start text-[#261E27] text-base"
          for="service-name"
        >
          {{ $t("form.name") }}<span class="text-red-600">*</span>
        </label>
        <LangInput
          id="service-name"
          v-model="formData.name_localized"
          :placeholder="$t('modalPlacholder.packageName')"
          :v$="v$"
        />
      </div>
      <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <div class="">
          <div class="">
            <form-group :validation="v$" name="team_id">
              <template #default="{ attrs }">
                <SelectInput
                  v-bind="attrs"
                  id="team_id"
                  v-model="formData.team_id"
                  :label="$t('modalPlacholder.branch')"
                  :placeholder="$t('modalPlacholder.branch')"
                  class="py-3 w-full text-gray-700 rounded-md border-gray-300"
                >
                  <option hidden selected value="">
                    {{ $t("form.select") }}
                  </option>
                  <option
                    v-for="team in getTeam"
                    :key="team?.uuid"
                    :value="team?.uuid"
                  >
                    {{ team.name }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
        </div>

        <div class="">
          <div class="relative">
            <form-group :validation="v$" name="price">
              <template #default="{ attrs }">
                <NumberInput
                  v-bind="attrs"
                  id="service-price"
                  v-model="formData.price"
                  :label="$t('price')"
                  class="block py-3 w-full text-gray-700 rounded-md focus:border-primary-500 focus:ring-primary-500 focus:outline-none focus:bg-white"
                  :placeholder="$t('price')"
                  step="0.01"
                />
              </template>
            </form-group>
            <span class="absolute top-2 end-4">{{
              $t(`currenices.${userInfo.tenant?.currency}`)
            }}</span>
          </div>
        </div>
      </div>

      <div class="full">
        <LabelInput for="description">
          {{ $t("description") }}
        </LabelInput>
        <LangInput
          id="service-name"
          v-model="formData.description_localized"
          type="textarea"
          :placeholder="$t('description')"
          class="mt-2"
          :v$="v$"
        />
      </div>

      <div v-if="formData.team_id" class="full">
        <form-group :validation="v$" name="category_id">
          <template #default="{ attrs }">
            <div class="flex mt-1 rounded-md">
              <SelectInput
                v-bind="attrs"
                id="category"
                v-model="formData.category_id"
                :label="$t('form.category')"
                :placeholder="$t('form.category')"
              >
                <option value="">
                  {{ $t("form.select") }}
                </option>
                <option
                  v-for="category of categories.filter(
                    (cat) => cat.team.uuid == formData.team_id,
                  )"
                  :key="category.uuid"
                  :value="category.uuid"
                >
                  {{ category.name_localized?.ar || category.name_localized?.en || category.name }}
                </option>
              </SelectInput>
              <button
                type="button"
                class="inline-flex justify-center self-end px-4 py-3 text-sm font-medium bg-gray-300 border border-transparent rounded-e-md border-primary-500 text-primary-800 hover:bg-primary-800 hover:text-white focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2 text-start"
                @click="showAddCategoryForm = true"
              >
                <span>{{ $t("New") }}</span>
              </button>
            </div>
          </template>
        </form-group>
      </div>

      <div v-if="editMode == true" class="full">
        <form-group :validation="v$" name="slug">
          <template #default="{ attrs }">
            <div class="relative">
              <div
                class="flex mt-1 rounded-md ltr:flex-row-reverse rtl:flex-row"
              >
                <TextInput
                  v-bind="attrs"
                  id="service-slug"
                  v-model="formData.slug"
                  :label="$t('fields.slug')"
                  :placeholder="$t('fields.slug')"
                  custom-classes="w-full !border-s-0 text-end ltr:text-start disabled:bg-gray-200 rounded-none border-e"
                  @click="
                    $event.target.setSelectionRange(
                      $event.target.value.length,
                      $event.target.value.length,
                    )
                  "
                />
                <div
                  class="flex justify-center items-center px-2 w-auto bg-gray-200 border border-gray-300 shadow-sm center-items"
                >
                  <span class="text-sm text-start" dir="auto">
                    {{ formData.base_link }}
                  </span>
                </div>
              </div>

              <span class="text-sm">{{ $t("slug_package") }}</span>
              <br>
              <span v-if="v$.slug.$dirty" class="text-sm">{{
                $t("slug_rule")
              }}</span>
            </div>
          </template>
        </form-group>
      </div>
      <div class="">
        <div class="flex">
          <div class="flex-1 px-2 w-1/2 min-w-0">
            <form-group :validation="v$" name="package_valid_period">
              <template #default="{ attrs }">
                <NumberInput
                  v-bind="attrs"
                  id="hours"
                  v-model="formData.package_valid_period"
                  :label="$t('modalPlacholder.package_valid_period')"
                  step="1"
                  :placeholder="$t('package_valid_period_example')"
                />
              </template>
            </form-group>
          </div>

          <div class="flex-1 w-1/2 min-w-0">
            <form-group :validation="v$" name="package_max_number_of_available">
              <template #default="{ attrs }">
                <NumberInput
                  v-bind="attrs"
                  id="hours"
                  v-model="formData.package_max_number_of_available"
                  :label="$t('modalPlacholder.package_max_number_of_available')"
                  step="1"
                  :placeholder="$t('package_max_number_of_available_example')"
                />
              </template>
            </form-group>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-2">
        <div class="relative">
          <form-group :validation="v$" name="sort_order">
            <template #default="{ attrs }">
              <NumberInput
                v-bind="attrs"
                id="sort-order"
                v-model="formData.sort_order"
                :label="$t('form.sort_order_package')"
                :placeholder="$t('form.sort_order_package')"
              />
            </template>
          </form-group>
        </div>
        <div class="mx-3 my-4">
          <LabelInput for="display_on_booking_page">
            {{ $t("displayOnBookingPage") }}
          </LabelInput>
          <Switch
            id="display_on_booking_page"
            v-model="formData.display_on_booking_page"
            class="inline-flex relative flex-shrink-0 mt-1 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            :class="[
              formData.display_on_booking_page
                ? 'bg-primary-600'
                : 'bg-gray-200',
            ]"
          >
            <span
              aria-hidden="true"
              class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
              :class="[
                formData.display_on_booking_page
                  ? 'translate-x-5 rtl:-translate-x-5'
                  : 'translate-x-0',
              ]"
            />
          </Switch>
        </div>
      </div>
      <div class="grid">
        <div class="flex gap-2">
          <div class="sm:col-span-2">
            <h2 class="text-xl font-medium text-gray-900">
              {{ $t("packages.services") }}
            </h2>
            <div
              v-for="(item, index) in formData.services"
              :key="`services-${index}`"
              class="grid gap-5 sm:grid-cols-3"
            >
              <div>
                <form-group :error-name="`services.${index}.id`">
                  <template #default="{ attrs }">
                    <BaseComboBox
                      :key="`services-${index}`"
                      v-bind="attrs"
                      v-model="item.id"
                      place-holder="serviceName"
                      required
                      arial-label="Search"
                      class="flex-1"
                      :options="servicesOptions"
                    >
                      {{ $t("booking.service") }}
                    </BaseComboBox>
                  </template>
                </form-group>
              </div>
              <div>
                <form-group :error-name="`services.${index}.quantity`">
                  <template #default="{ attrs }">
                    <NumberInput
                      v-bind="attrs"
                      :id="`serviceQuantity-${index}`"
                      v-model="item.quantity"
                      :label="$t('modalPlacholder.quantity')"
                      :placeholder="$t('modalPlacholder.quantity')"
                    />
                  </template>
                </form-group>
              </div>

              <div
                class="flex gap-4 justify-center items-center pt-6 sm:justify-start"
              >
                <button
                  v-if="formData.services.length > 1 && index !== 0"
                  type="button"
                  class="inline-flex text-gray-400 bg-white rounded-md hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  @click="removeService(index)"
                >
                  <TrashIcon class="w-6 h-6 text-red-400" aria-hidden="true" />
                </button>
                <button
                  v-if="formData.services.length - 1 === index"
                  type="button"
                  class="inline-flex text-gray-400 bg-white rounded-md hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  @click="addNewService"
                >
                  <PlusIcon class="w-6 h-6 text-gray-400" aria-hidden="true" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-between mx-auto w-full">
        <BaseButton
          type="submit"
          class="py-3 mx-auto w-1/2 hover:bg-primary-800"
          custome-bg="bg-primary-700"
          show-icon
          :processing="processing"
        >
          {{ editMode ? $t("form.update") : $t("form.create") }}
        </BaseButton>
      </div>
    </form>
  </Modal>
</template>

<style scoped>
form > div:not(:last-child) {
  flex: 1 0 48%;
}

form > div.full {
  flex: 1 0 100%;
}

@media (max-width: 768px) {
  form > div:not(:last-child) {
    flex: 1 0 100% !important;
  }
}
</style>
