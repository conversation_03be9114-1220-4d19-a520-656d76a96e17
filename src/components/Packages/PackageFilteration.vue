<script setup lang="ts">
import { storeToRefs } from 'pinia'
import VueTailwindDatepicker from 'vue-tailwind-datepicker'
import { locatizedOption } from '@/composables/useDatePicker'
import SearchInput from '@/components/FormControls/Inputs/SearchInput.vue'
const emit = defineEmits(['changed', 'clear'])

const { fetchAllPackages } = useServicesStore()

const { fetchCustomerByNameOrPhone } = useCustomerStore()
const { getLocale } = storeToRefs(useLocalesStore())
const formatDate = ref({
  date: 'DD-MM-YYYY',
  month: 'MMM',
})

const packages = ref([])
const customers = ref([])

const customer = ref('')
const packageType = ref('')
const team = ref('')
const packageDate: any = ref([])
const packageNumber = ref('')
const status = ref('')

const teamDebounced = debouncedRef(team, 500)
const customerDebounced = debouncedRef(customer, 500)
const packageDebounced = debouncedRef(packageType, 500)
const packageDateDebounced = debouncedRef(packageDate, 500)
const packageNumDebounced = debouncedRef(packageNumber, 500)
const statusDebounced = debouncedRef(status, 500)

const { locale } = useI18n()
interface Payload {
  customer: string
  team: string
  packageType: string
  packageNum: string
  PurchasedDate: string
  status: string
}

watch([customerDebounced, teamDebounced, packageDebounced, packageNumDebounced, packageDateDebounced, statusDebounced], (val) => {
  const [customer, team, packageType, packageNumber, packageDate, status] = val
  if (!customer && !team && !packageType && !packageNumber && !packageDate && !status)
    emit('clear', true)

  const payload: Payload = {
    customer,
    team,
    packageType,
    packageNumber,
    from: packageDate.length ? packageDate[0] : '',
    to: packageDate.length ? packageDate[1] : '',
    status,
  }
  emit('changed', payload)
})

onMounted(async () => {
  await fetchAllPackages().then((res) => {
    packages.value = res.data.map((service) => {
      return {
        label: service.name_localized?.[locale.value] ?? service.name,
        value: service.uuid,
      }
    })
  })
})

const searchCustomers = async (name = '') => {
  fetchCustomerByNameOrPhone(1, `search=${name}`)
    .then((res) => {
      customers.value = res.data
    })
    .finally(() => {

    })
}

const customerOptions = computed(() => {
  return customers.value.map(customer => ({
    label: `${customer.first_name} ${customer.last_name} [ ${customer.phone} ]`,
    value: customer.uuid,
  }))
})
</script>

<template>
  <div class="mt-4">
    <div class="grid grid-cols-1 gap-x-4 gap-y-6 mt-6 md:grid-cols-3">
      <div class="">
        <BaseComboBox
          :model-value="customer"
          arial-label="Search"
          :options="customerOptions"
          :class="`block w-full text-gray-700 border-gray-300 rounded-none w-100 focus:border-primary-500 focus:ring-primary-500 ${getLocale(locale)?.direction === 'rtl' ? 'text-right' : ''}`"
          server-filter
          place-holder="search_phone_or_name"
          @search="searchCustomers"
          @update:model-value="customer = $event"
        >
          {{ $t("booking.customer") }}
        </BaseComboBox>
      </div>
      <div class="flex relative flex-col flex-grow items-stretch focus-within:z-10">
        <SearchInput
          id="package-number"
          v-model="packageNumber"
          :label="$t('package_number')"
          :placeholder="$t('package_number')"
          custom-classes="border-gray-300 focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
        />
      </div>
      <div class="flex relative flex-col flex-grow items-stretch focus-within:z-10">
        <label for="purchased_date" class="block mb-2 text-xs font-bold tracking-wide text-gray-700  ">
          {{ $t("purchased_date") }}
        </label>
        <!-- <DateInput v-model="packageDate" id="purchased_date" /> -->
        <VueTailwindDatepicker
          v-model="packageDate"
          :formatter="formatDate"
          :placeholder="$t('purchased_date')"
          use-range
          class="block rounded-md border-gray-300 sm:text-sm"
          :i18n="getLocale(locale)?.id === 'ar' ? 'ar-sa' : 'en'"
          :dir="getLocale(locale)?.direction === 'rtl' ? 'ltr' : 'rtl'"
          :options="locatizedOption"
        />
      </div>
      <div class="flex relative flex-col flex-grow items-stretch focus-within:z-10">
        <BaseComboBox
          v-model="packageType"
          place-holder="packageName"
          arial-label="Search"
          class="flex-1"
          :options="packages"
        >
          {{ $t("modalPlacholder.packageName") }}
        </BaseComboBox>
      </div>

      <div class="flex relative flex-col flex-grow items-stretch focus-within:z-10">
        <SelectInput
          id="status"
          v-model="status"
          :label="$t('altNav.status')"
          :placeholder="$t('altNav.status')"
        >
          <option value="">
            {{ $t("all") }}
          </option>
          <option value="active">
            {{ $t("active") }}
          </option>
          <option value="inactive">
            {{ $t("inactive") }}
          </option>
        </SelectInput>
      </div>

      <filter-with-team v-model="team" />
    </div>
  </div>
</template>
