<script>
export default {
  name: 'OffersIcon',
}
</script>

<template>
  <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.2083 8.49146H3.45825C3.11307 8.49146 2.83325 8.77128 2.83325 9.11646V11.6165C2.83325 11.9616 3.11307 12.2415 3.45825 12.2415H17.2083C17.5534 12.2415 17.8333 11.9616 17.8333 11.6165V9.11646C17.8333 8.77128 17.5534 8.49146 17.2083 8.49146Z" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M16.5833 12.2415V17.8665C16.5833 18.0322 16.5174 18.1912 16.4002 18.3084C16.283 18.4256 16.124 18.4915 15.9583 18.4915H4.70825C4.54249 18.4915 4.38352 18.4256 4.26631 18.3084C4.1491 18.1912 4.08325 18.0322 4.08325 17.8665V12.2415" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M10.3333 8.49146V18.4915" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M10.3333 8.49142C10.3333 8.49142 12.985 8.49142 13.8689 7.60754C14.2202 7.25584 14.4174 6.77903 14.4173 6.28194C14.4172 5.78484 14.2196 5.30815 13.8681 4.95665C13.5166 4.60515 13.0399 4.40761 12.5428 4.40747C12.0457 4.40733 11.5689 4.60459 11.2172 4.95589C10.3333 5.83977 10.3333 8.49142 10.3333 8.49142ZM10.3333 8.49142C10.3333 8.49142 7.68169 8.49142 6.79781 7.60754C6.44651 7.25584 6.24925 6.77903 6.24939 6.28194C6.24953 5.78484 6.44706 5.30815 6.79856 4.95665C7.15006 4.60515 7.62676 4.40761 8.12386 4.40747C8.62095 4.40733 9.09776 4.60459 9.44946 4.95589C10.3333 5.83977 10.3333 8.49142 10.3333 8.49142Z" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
  </svg>
</template>

<style scoped>
svg path {
  @apply stroke-current;
}

a:hover svg path,
a:focus svg path,
a.router-link-active svg path {
  @apply stroke-white;
}
</style>
