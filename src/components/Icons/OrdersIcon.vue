<script>
export default {
  name: 'OrdersIcon',
}
</script>

<template>
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.66663 1.66663V4.16663" stroke="currentColor" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M13.3334 1.66663V4.16663" stroke="currentColor" stroke-width="1.25" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M2.91663 7.57495H17.0833" stroke="currentColor" stroke-width="1.6" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M17.5 7.08329V14.1666C17.5 16.6666 16.25 18.3333 13.3333 18.3333H6.66667C3.75 18.3333 2.5 16.6666 2.5 14.1666V7.08329C2.5 4.58329 3.75 2.91663 6.66667 2.91663H13.3333C16.25 2.91663 17.5 4.58329 17.5 7.08329Z" stroke="currentColor" stroke-width="1.6" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M13.079 11.4167H13.0864" stroke="currentColor" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M13.079 13.9167H13.0864" stroke="currentColor" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M9.9962 11.4167H10.0037" stroke="currentColor" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M9.9962 13.9167H10.0037" stroke="currentColor" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M6.91197 11.4167H6.91945" stroke="currentColor" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M6.91197 13.9167H6.91945" stroke="currentColor" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round" />
  </svg>
</template>

<style scoped>
svg path {
  @apply stroke-current;
}

a:hover svg path,
a:focus svg path,
a.router-link-active svg path {
  @apply stroke-white;
}
</style>
