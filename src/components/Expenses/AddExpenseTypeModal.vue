<script setup lang="ts">
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { required } from '@/utils/i18n-validators'
import type { ExpenseType } from '@/types'
import { useExpenseTypesStore } from '@/stores/expenseTypes'
const props = defineProps({
  expenseType: {
    type: Object as PropType<ExpenseType>,
    default: null,
  },
  showModal: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['created', 'closed', 'updated'])
const { createExpenseType, updateExpenseType } = useExpenseTypesStore()
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const processing = ref(false)
const editMode = ref(false)

const formData = reactive({
  title: '',
})

const { expenseType } = toRefs(props)

const rules = {
  title: {
    required,
  },
}

const v$ = useVuelidate(rules, formData)

watch(expenseType, (value: ExpenseType) => {
  if (!value)
    return
  formData.title = value.title || ''
  editMode.value = true
}, { immediate: true })

const resetForm = () => {
  formData.title = ''
  editMode.value = false
  v$.value.$reset()
}

const createRecord = async (payload) => {
  return createExpenseType(payload).then((res) => {
    emit('created', formData)
    resetForm()
  })
}

const editRecord = async (payload) => {
  return updateExpenseType(expenseType.value.uuid, payload)
    .then((res) => {
      resetForm()
      emit('updated', res.data)
    })
}

const closeModal = () => {
  emit('closed')
}

const saveExpenseType = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return false
  processing.value = true
  try {
    if (editMode.value)
      await editRecord(formData)
    else
      await createRecord(formData)
  }
  finally {
    processing.value = false
  }
}

defineExpose({
  saveExpenseType,
  closeModal,
})
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction" :open="showModal" title="expense_type" panel-classes="w-full mx-20 lg:mx-0  !mx-4"
    @close="closeModal"
  >
    <form class="grid grid-cols-1 gap-5 mb-4 w-full text-start" @submit.prevent="saveExpenseType">
      <overlay-loader v-if="processing" :full-screen="false" />
      <div class="w-full">
        <labelInput for="expenseTypeName" class="mb-1">
          {{ $t("form.name") }} <span class="text-red-600">*</span>
        </labelInput>

        <form-group :validation="v$" name="title">
          <template #default="{ attrs }">
            <TextInput v-bind="attrs" id="expenseTypeName" v-model="formData.title" />
          </template>
        </form-group>
      </div>

      <div class="mt-6">
        <BaseButton
          type="submit" class="mx-auto" :class="[expenseType?.uuid ? 'bg-gray-800' : 'hover:bg-green-700']"
          :custome-bg="[expenseType?.uuid ? 'bg-gray-700' : 'bg-green-600']" show-icon :processing="processing"
        >
          <span>{{ editMode ? $t("form.update") : $t("form.create") }}</span>
        </BaseButton>
      </div>
    </form>
  </Modal>
</template>
