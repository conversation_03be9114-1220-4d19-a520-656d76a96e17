<script setup lang="ts">
import type { ComputedRef, PropType } from 'vue'
import { PencilIcon, TrashIcon } from '@heroicons/vue/24/outline'
import type { ExpenseType, header } from '@/types'

const props = defineProps({
  expenseTypes: {
    type: Array as PropType<ExpenseType[]>,
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['edit', 'delete'])
const { t } = useI18n()
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.name'),
    },
    {
      title: '',
    },
  ]
})

const editExpenseType = (expenseType: ExpenseType) => {
  emit('edit', expenseType)
}

const deleteExpenseType = (expenseType: ExpenseType) => {
  emit('delete', expenseType)
}
</script>

<template>
  <generic-table
    :headers="headers"
    :data="expenseTypes"
    item-key="uuid"
    :is-loading="isLoading"
  >
    <template #row="{ item }">
      <grid-td class="py-4 pr-3 pl-4 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">
        {{ item.title }}
      </grid-td>
      <grid-td class="flex relative gap-2 py-4 pr-4 pl-3 text-sm font-medium text-right whitespace-nowrap">
        <button class="px-4 py-2 text-white text-gray-500 rounded-md transition-colors bg-primary-500 hover:bg-primary-600" @click="editExpenseType(item)">
          {{ $t("form.edit") }}
        </button>
        <button class="px-4 py-2 text-white bg-red-500 rounded-md transition-colors hover:bg-red-600" @click="deleteExpenseType(item)">
          {{ $t("form.delete") }}
        </button>
      </grid-td>
    </template>
  </generic-table>
</template>
