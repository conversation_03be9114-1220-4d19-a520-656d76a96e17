<script setup lang="ts">
import type { PropType } from 'vue'
import { InformationCircleIcon } from '@heroicons/vue/24/solid'
import type { GeneralSettings } from '@/types'
const props = defineProps({
  inputs: {
    type: Array as PropType<GeneralSettings[]>,
  },
  err: {
    type: Array as PropType<{ [key: string]: string }[]>,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  errorObjectName: {
    type: String,
    default: '',
    required: false,
  },
})
const emit = defineEmits(['submit'])
watch(() => props, (val) => {
}, { deep: true })
const formData = reactive({})
const handleSubmit = () => {
  emit('submit', formData)
}
const { inputs } = toRefs(props)
watch(inputs, (val) => {
  for (const prop in val)
    formData[prop] = val[prop].value
})
const getType = (inputType: string) => {
  const type = {
    integer: 'number',
    boolean: 'checkbox',
    text: 'text',
  }
  return type?.[inputType] || 'text'
}
const handleInput = (event) => {
}
</script>

<template>
  <form class="relative" @submit.prevent="handleSubmit">
    <OverlayLoader v-if="isLoading" :full-screen="false" />
    <div v-for="(item, index) of inputs" :key="index" class="flex flex-col pt-2 group">
      <div v-if="item.htmlTag === 'input' && (item.type == 'number' || item.type == 'integer') " class="flex flex-col sm:pt-2">
        <div class="mt-3 sm:col-span-2 sm:mt-2">
          <form-group :error-name="`${errorObjectName}.${item.id}`">
            <template #default="{ attrs }">
              <NumberInput
                v-bind="attrs"
                :id="item.label"
                v-model="formData[item.id]"
                :model-value="item.value === null ? formData[item.id] : !formData[item.id] ? item.value : formData[item.id]"
                :label="item.label"
                custom-classes="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                @update:model-value="formData[item.id] = $event"
              />
            </template>
          </form-group>
        </div>
      </div>
      <div v-if="item.htmlTag === 'input' && item.type == 'text' " class="flex flex-col sm:pt-2">
        <div class="mt-3 sm:col-span-2 sm:mt-2">
          <form-group :error-name="`${errorObjectName}.${item.id}`">
            <template #default="{ attrs }">
              <TextInput
                v-bind="attrs"
                :id="item.label"
                :model-value="item.value === null ? formData[item.id] : !formData[item.id] ? item.value : formData[item.id]"
                :label="item.label"
                custom-classes="block w-full rounded-md border-gray-300 focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                @update:model-value="formData[item.id] = $event"
              />
            </template>
          </form-group>
        </div>
      </div>
      <div v-if="item.htmlTag === 'input' && item.type === 'boolean' ">
        <div class="flex relative gap-4 pt-2 pb-1 w-fit">
          <div class="flex items-center h-5">
            <form-group :error-name="`${errorObjectName}.${item.id}`">
              <template #default="{ attrs }">
                <input
                  v-bind="attrs"
                  :id="item.label" v-model="formData[item.id]" :checked="item.value" type="checkbox" aria-describedby="offers-description" name="offers" class="w-4 h-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500"
                >
              </template>
            </form-group>
          </div>
          <div class="flex-1 min-w-0 text-sm">
            <LabelInput for="offers">
              {{ item.label }}
            </LabelInput>
          </div>
        </div>
      </div>
      <div v-if="item.htmlTag === 'textarea'" class="flex flex-col sm:pt-2">
        <LabelInput :for="item.label" class="  mb-2 w-full flex items-center text-start text-[#261E27] text-base     sm:mt-px sm:pt-2">
          {{ item.label }}
        </LabelInput>
        <div class="mt-1 sm:col-span-2 sm:mt-0">
          <form-group :error-name="`${errorObjectName}.${item.id}`">
            <template #default="{ attrs }">
              <TextareaInput
                v-bind="attrs"
                :id="item.label"
                v-model="formData[item.id]"
                @change="formData[item.id] = $event.target.value"
              />
            </template>
          </form-group>
        </div>
      </div>
      <div v-if="item.htmlTag === 'select'" class="flex relative flex-col flex-grow items-stretch focus-within:z-10">
        <LabelInput :for="item.label" class="  mb-2 w-full flex items-center text-start text-[#261E27] text-base     sm:mt-px sm:pt-2">
          {{ item.label }}
        </LabelInput>
        <form-group :error-name="`${errorObjectName}.${item.id}`">
          <template #default="{ attrs }">
            <SelectInput
              v-bind="attrs"
              :id="item.label"
              v-model="formData[item.id]"
              required
              @change="formData[item.id] = $event.target.value"
            >
              <option
                v-for="(option, key) of item.options"
                :key="option"
                :value="key"
              >
                {{ option }}
              </option>
            </SelectInput>
          </template>
        </form-group>
      </div>
      <span v-if="item.help" class="flex gap-1 items-center mt-0.5 text-xs text-blue-400 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
        <InformationCircleIcon class="w-4 h-4" />
        {{ item.help }}
      </span>
    </div>
    <err-validations v-if="err?.length" :err-handle="err" />
    <BaseButton
      class="inline-flex mt-7 mb-7 hover:bg-gray-800 w-fit"
      custome-bg="bg-gray-700"
      show-icon
      type="submit"
    >
      {{ $t("form.update") }}
    </BaseButton>
  </form>
</template>

<style>

</style>
