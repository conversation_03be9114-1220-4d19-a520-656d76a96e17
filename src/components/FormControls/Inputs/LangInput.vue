<script setup lang="ts">
import { computed, defineComponent, ref } from 'vue'
import type { Component } from 'vue'
import TextInput from './TextInput.vue'
import TextareaInput from './TextareaInput.vue'
import InfoIcon from '@/components/Icons/InfoIcon.vue'

interface Props {
  modelValue: Record<string, string>
  type?: 'input' | 'textarea'
  placeholder?: string
  label?: string
  v$?: any
  id?: string
  hint?: string
  error?: boolean
  disabled?: boolean
  required?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])
const { locale } = useI18n()
const currentLang = ref(locale.value)
const languages = ['en', 'ar']

// Language configuration with flags and labels
const langConfig = {
  en: {
    flag: '🇺🇸',
    label: 'English',
    dir: 'ltr',
    shortLabel: 'EN',
  },
  ar: {
    flag: '🇸🇦',
    label: 'العربية',
    dir: 'rtl',
    shortLabel: 'ع',
  },
} as const

const currentValue = computed({
  get() {
    return props.modelValue[currentLang.value] || ''
  },
  set(value) {
    emit('update:modelValue', {
      ...props.modelValue,
      [currentLang.value]: value,
    })
  },
})

const switchLanguage = (lang: string) => {
  currentLang.value = lang
}

// Default hint if not provided
const defaultHint = 'You can add both English and Arabic by clicking the language button.'
const showHint = computed(() => props.hint || defaultHint)

// Form validation names based on type
const validationNames = computed(() => ({
  name: props.type === 'textarea'
    ? ['description_localized.ar', 'description_localized.en']
    : ['name_localized.ar', 'name_localized.en'],
  errorName: props.type === 'textarea' ? 'description' : 'name_localized',
}))

// Common input classes
const inputClasses = computed(() => [
  props.error && 'border-red-500 focus:ring-red-500 focus:border-red-500',
  props.disabled && 'bg-gray-50 cursor-not-allowed opacity-75',
  props.required && 'required',
])

// Get the appropriate input component based on type
const InputComponent = computed<Component>(() =>
  props.type === 'textarea' ? TextareaInput : TextInput,
)
</script>

<template>
  <div class="inline-flex flex-col justify-start items-end w-full">
    <!-- Dynamic Label -->
    <label
      v-if="props.label"
      class="mb-2 w-full flex items-center text-start text-[#261E27] text-base font-tajawal font-normal"
      :for="props.id"
    >
      <span class="relative group text-[#FABF35] me-1 cursor-pointer align-middle">
        <InfoIcon class="w-4 h-4 inline-block align-middle" />
        <span class="absolute bottom-full z-10 px-2 py-1 text-xs text-white whitespace-nowrap bg-black rounded shadow-lg opacity-0 transition-opacity pointer-events-none start-1/2 -ms-1 group-hover:opacity-100">
          {{ $t('hints.langinput') }}
        </span>
      </span>
      {{ props.label }}
      <span v-if="props.required" class="text-red-600 ms-1">*</span>
    </label>

    <div class="relative group w-full">
      <form-group
        :validation="props.v$"
        :name="validationNames.name"
        :error-name="validationNames.errorName"
      >
        <template #default="{ attrs }">
          <div class="relative">
            <component
              :is="InputComponent"
              :id="props.id"
              v-model="currentValue"
              :placeholder="props.placeholder"
              :dir="langConfig[currentLang].dir"
              v-bind="attrs"
              :class="inputClasses"
            />
            <!-- Language Toggle Button -->
            <div
              class="absolute bg-white p-1"
              :class="[
                currentLang === 'ar' ? 'end-3 flex-row-reverse' : 'start-3',
                props.type === 'textarea' ? 'top-5' : 'top-1/2 transform -translate-y-1/2',
              ]"
              style="display: flex; align-items: center;"
            >
              <button
                type="button"
                class="flex items-center gap-1 text-xs text-blue-500 hover:text-blue-600 transition-colors"
                @click="switchLanguage(currentLang === 'en' ? 'ar' : 'en')"
              >
                <span class="text-xs text-gray-400">
                  {{ langConfig[currentLang].shortLabel }}
                </span>
                <LangIcon class="w-4 h-4" />
              </button>
            </div>
          </div>
        </template>
      </form-group>
    </div>
  </div>
</template>

<style scoped>
/* RTL support for Arabic */
:dir(rtl) input,
:dir(rtl) textarea {
  text-align: right;
}
</style>
