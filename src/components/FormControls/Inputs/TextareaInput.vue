<script lang="ts" setup>
defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  id: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  customClasses: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  hint: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['update:modelValue'])
function handleInput(event) {
  emit('update:modelValue', event.target.value)
}
</script>

<template>
  <div class="inline-flex flex-col justify-start items-end w-full">
    <!-- Dynamic Label -->
    <label
      v-if="label"
      class="mb-2 w-full flex items-center text-start text-[#261E27] text-base   font-tajawal font-normal"
      :for="id"
    >
      <span v-if="hint" class="relative group text-[#FABF35] me-1 cursor-pointer"> <InfoIcon class="w-4 h-4 inline-block align-middle" /> <span class="absolute start-1/2 bottom-full -ms-1  px-2 py-1 bg-black text-white text-xs rounded shadow-lg whitespace-nowrap opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity z-10"> {{ hint }}</span></span>      {{ label }}
      <span v-if="required" class="text-red-600 ms-1">*</span>
    </label>

    <Textarea
      :id="id"
      rows="4"
      class="inline-flex flex-1 px-4 py-3 w-full rounded border-base-gray text-start"
      :class="customClasses"
      :placeholder="placeholder"
      :value="modelValue"
      @input="handleInput"
    />
  </div>
</template>
