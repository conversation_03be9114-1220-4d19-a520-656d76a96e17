<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import InfoIcon from '@/components/Icons/InfoIcon.vue'
import AmountIcon from '@/components/Icons/Amount.vue'
import PercentageIcon from '@/components/Icons/Percentage.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
    // { amount: number|string, type: 'fixed'|'percent' }
  },
  id: { type: String, default: '' },
  label: { type: String, default: '' },
  placeholder: { type: String, default: '' },
  min: { type: Number, default: 0 },
  max: { type: Number, default: null },
  disabled: { type: Boolean, default: false },
  required: { type: Boolean, default: false },
  hint: { type: String, default: '' },
  customClass: {
    type: [String, Object],
    default: ''
  },
  onblur: {
    type: Function,
    default: null
  }
});

// Compute max value based on type
const effectiveMax = computed(() => {
  return props.modelValue.discount_type === 'percentage' ? 100 : props.max;
});

const emit = defineEmits(['update:modelValue']);

const updateAmount = (e: Event) => {
  const value = (e.target as HTMLInputElement).value;
  if (props.modelValue.discount_type === 'percentage' && Number(value) > 100) {
    emit('update:modelValue', { ...props.modelValue, discount_amount: '100' });
    return;
  }
  emit('update:modelValue', { ...props.modelValue, discount_amount: value });
};

const toggleType = () => {
  const newType = props.modelValue.discount_type === 'percentage' ? 'fixed' : 'percentage';
  emit('update:modelValue', { 
    ...props.modelValue, 
    discount_type: newType,
    // Reset amount when switching types to prevent confusion
    discount_amount: ''
  });
};

const typeIcon = computed(() =>
  props.modelValue.discount_type === 'percentage' ? PercentageIcon : AmountIcon
);

const typeDescription = computed(() =>
  props.modelValue.discount_type === 'percentage' ? 'خصم نسبة مئوية' : 'خصم مبلغ'
);

const handleBlur = () => {
  if (props.onblur) {
    props.onblur();
  }
};
</script>

<template>
  <div class="flex flex-col w-full">
    <!-- Dynamic Label -->
    <div class="flex items-center gap-1.5 mb-2">     
      <div v-if="hint" class="relative inline-block">
        <button
          ref="buttonRef"
          type="button"
          class="flex items-center justify-center w-4 h-4 text-[#FABF35] hover:text-[#e5ab20] transition-colors duration-200"
          @mouseenter="showTooltip = true"
          @mouseleave="showTooltip = false"
          @focus="showTooltip = true"
          @blur="showTooltip = false"
        >
          <InfoIcon class="w-full h-full" />
        </button>

        <Transition
          enter-active-class="transition duration-200 ease-out"
          enter-from-class="transform scale-95 opacity-0"
          enter-to-class="transform scale-100 opacity-100"
          leave-active-class="transition duration-150 ease-in"
          leave-from-class="transform scale-100 opacity-100"
          leave-to-class="transform scale-95 opacity-0"
        >
          <div
            v-show="showTooltip"
            ref="tooltipRef"
            class="fixed z-[9999] w-max max-w-[200px] sm:max-w-[250px] px-2 py-1 text-xs text-white bg-black rounded"
            :style="tooltipStyle"
          >
            {{ hint }}
            <div class="absolute w-2 h-2 bg-black transform rotate-45 left-1/2 -translate-x-1/2 bottom-[-4px]" />
          </div>
        </Transition>
      </div>
      <label
        v-if="label"
        class="text-[#261E27] text-sm sm:text-base font-tajawal font-normal"
        :for="id"
      >  
        {{ label }}
        <span v-if="required" class="text-red-600 ms-1">*</span>
      </label>
    </div>
    
    <div class="flex overflow-hidden items-stretch rounded border transition-all w-full h-[48px]"
         :class="[
           customClass,
           'border-gray-300 focus-within:border-blue-500 focus-within:ring-blue-500'
         ]">
      <!-- Input field -->
      <input
        :id="id"
        type="number"
        :min="min"
        :max="effectiveMax"
        :disabled="disabled"
        :placeholder="placeholder"
        class="inline-flex flex-1 px-2 sm:px-4 py-2 sm:py-3 w-full border-none text-start text-sm sm:text-base custom-number-input disabled:opacity-50 disabled:cursor-not-allowed"
        :title="disabled ? 'يجب عليك إضافة عنصر بالأساسية لتتمكن من إضافة الخصم' : ''"
        :value="modelValue.amount"
        @input="updateAmount"
        @blur="handleBlur"
      />

      <!-- Type toggle button -->
      <button
        type="button"
        :disabled="disabled"
        class="flex flex-row items-center justify-center min-w-[40px] sm:min-w-[60px] max-w-[40px] border-s border-gray-200 bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer group h-full disabled:opacity-50 disabled:cursor-not-allowed"
        :title="`${typeDescription} (اضغط للتبديل)`"
        aria-label="تبديل نوع الخصم"
        @click="toggleType"
      >
        <div class="flex flex-row gap-0.5 sm:gap-1 justify-center items-center w-full h-full">
          <component :is="typeIcon" class="w-4 h-4 sm:w-6 sm:h-6 text-gray-500" />
          <svg
            class="w-3 h-3 sm:w-4 sm:h-4 text-gray-400 opacity-70 transition-opacity group-hover:opacity-100"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path
              d="M10 5v10m0 0l-2-2m2 2l2-2m-2-8l-2 2m2-2l2 2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </button>
    </div>
  </div>
</template>

<style scoped>
.custom-number-input::-webkit-inner-spin-button,
.custom-number-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.custom-number-input {
  -moz-appearance: textfield;
}
</style>
