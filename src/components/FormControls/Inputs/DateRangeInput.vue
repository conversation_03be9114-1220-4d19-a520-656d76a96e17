<script setup lang="ts">
import VueTailwindDatepicker from 'vue-tailwind-datepicker'
import { storeToRefs } from 'pinia'
import { useLocalesStore } from '@/stores/locales'
import { locatizedOption } from '@/composables/useDatePicker'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  label: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  customClasses: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  formatter: {
    type: Object,
    default: () => ({
      date: 'DD-MM-YYYY',
      month: 'MMM',
    }),
  },
})

const emit = defineEmits(['update:modelValue'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const classes = computed(() => {
  return `w-full h-12 block w-full rounded-md focus:border-primary-500 focus:ring-primary-500 sm:text-sm border-gray-300 ${props.customClasses}`
})
</script>

<template>
  <div>
    <!-- Dynamic Label -->
    <label
      v-if="label"
      class=" mb-2 w-full flex items-center text-start text-[#261E27] text-base   font-tajawal font-normal"
      :for="id"
    >
      {{ label }}
      <span v-if="required" class="text-red-600 ms-1">*</span>
    </label>

    <!-- Input Container -->
    <VueTailwindDatepicker
      :formatter="formatter"
      :model-value="modelValue"
      :placeholder="placeholder"
      use-range
      :class="[classes, $attrs.class]"
      :i18n="getLocale(locale)?.id === 'ar' ? 'ar-sa' : 'en'"
      :id="id"
      :dir="getLocale(locale)?.direction === 'rtl' ? 'ltr' : 'rtl'"
      :options="locatizedOption"
      v-bind="$attrs"
      @update:model-value="emit('update:modelValue', $event)"
    />
  </div>
</template>
