<script lang="ts" setup>
import type { PropType } from 'vue'
import { VueTelInput } from 'vue3-tel-input'
import { storeToRefs } from 'pinia'
import { useAuthStore } from '@/stores/auth'
import 'vue3-tel-input/dist/vue3-tel-input.css'
defineProps({
  modelValue: {
    type: String as PropType<String | null>,
    default: '',
  },
  showLabel: {
    type: Boolean,
    default: true,
  },
  label: {
    type: String,
    default: 'phone',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  showWhats: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'inputValid', 'focus', 'blur'])

const { getUserInfo } = storeToRefs(useAuthStore())

const isInvalidPhone = ref(true)

const updatePhone = (
  _: string,
  phoneObject: { valid: boolean; number: string; formatted: string; country: Object; nationalNumber: string },
) => {
  const phoneInformation: any = phoneObject
  if (!phoneObject?.country)
    return
  if (!phoneInformation?.nationalNumber) {
    phoneInformation.nationalNumber = ''
    phoneInformation.countryCode = phoneObject.country.iso2
  }
  emit('update:modelValue', phoneObject.valid ? phoneObject.number : '', phoneInformation)
  emit('inputValid', phoneObject.valid)
  isInvalidPhone.value = !phoneObject.valid
}

const defaultCountry = computed(() => {
  return getUserInfo?.value?.tenant?.country || 'SA'
})

function handleFocus() {
  emit('focus')
}

function handleBlur() {
  emit('blur')
}
</script>

<template>
  <div class="inline-flex flex-col justify-end items-end w-full h-full no-padding">
    <!-- Dynamic Label -->
    <div
      v-if="showLabel"
      class="mb-2 w-full flex flex-col justify-center text-start text-[#261E27] text-base font-tajawal font-normal"
    >
      {{ $t(`${label}`) }}
      <a
        v-if="modelValue && showWhats"
        :href="`https://wa.me/${modelValue}`"
        target="_blank"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24">
          <path
            fill="#25d366"
            d="M19.05 4.91A9.816 9.816 0 0 0 12.04 2c-5.46 0-9.91 4.45-9.91 9.91c0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21c5.46 0 9.91-4.45 9.91-9.91c0-2.65-1.03-5.14-2.9-7.01zm-7.01 15.24c-1.48 0-2.93-.4-4.2-1.15l-.3-.18l-3.12.82l.83-3.04l-.2-.31a8.264 8.264 0 0 1-1.26-4.38c0-4.54 3.7-8.24 8.24-8.24c2.2 0 4.27.86 5.82 2.42a8.183 8.183 0 0 1 2.41 5.83c.02 4.54-3.68 8.23-8.22 8.23zm4.52-6.16c-.25-.12-1.47-.72-1.69-.81c-.23-.08-.39-.12-.56.12c-.17.25-.64.81-.78.97c-.14.17-.29.19-.54.06c-.25-.12-1.05-.39-1.99-1.23c-.74-.66-1.23-1.47-1.38-1.72c-.14-.25-.02-.38.11-.51c.11-.11.25-.29.37-.43s.17-.25.25-.41c.08-.17.04-.31-.02-.43s-.56-1.34-.76-1.84c-.2-.48-.41-.42-.56-.43h-.48c-.17 0-.43.06-.66.31c-.22.25-.86.85-.86 2.07c0 1.22.89 2.4 1.01 2.56c.12.17 1.75 2.67 4.23 3.74c.59.26 1.05.41 1.41.52c.59.19 1.13.16 1.56.1c.48-.07 1.47-.6 1.67-1.18c.21-.58.21-1.07.14-1.18s-.22-.16-.47-.28z"
          />
        </svg>
      </a>
    </div>

    <!-- Input Container -->
    <div class="w-full">
      <!-- Input Field -->
      <VueTelInput
        v-if="defaultCountry"
        id="phone"
        :default-country="defaultCountry"
        :auto-default-country="false"
        :value="modelValue"
        mode="international"
        :class="{ 'error-input': !isInvalidPhone && isInvalidPhone }"
        :input-options="{
          placeholder: $t('formPlaceHolder.phoneNumber'),
        }"
        :dropdown-options="{
          showFlags: true,
          showDialCodeInSelection: true,
        }"
        :auto-format="true"
        :valid-characters-only="false"
        :disabled="disabled"
        :style="{ width: '100%' }"
        class="w-full px-4 py-3 rounded  inline-flex flex-1 text-start outline-none "
        @input="updatePhone"
        @focus="handleFocus"
        @blur="handleBlur"
      />
    </div>
  </div>
</template>

<style lang="css">
.no-padding {
  padding: 0 !important;
}
.vue-tel-input {
  @apply text-xs border-gray-300 rounded-md focus:border-black   !important;
}
.vti__dropdown {
  @apply p-2 !important;
}
.vti__input {
  @apply outline-none focus:border-none py-2 sm:text-sm rounded-md !important;
}
.vti__input:focus {
  @apply border-gray-300 focus:border-black !important;
}
.vti__dropdown {
  padding: 0px !important;
}
.vue-tel-input {
  font-size: 16px !important;
  gap: 5px;
  max-height: 50px !important;
  border-radius: 4px !important;
}
[dir="rtl"] .vti__dropdown-list.above {
  right: 0px !important;
  left: auto;
  top: -235px;
}
[dir="rtl"] .vti__dropdown-list.below {
  right: 0px !important;
  left: auto;
  top: 40px;
}
[dir="ltr"] .vti__dropdown-list.above {
  left: 0px !important;
  right: auto;
  top: -235px;
}
@media (max-width: 768px) {
  .vti__dropdown-list.above,
  .vti__dropdown-list.below {
    max-width: 250px;
    max-width: 250px;
  }
}

.vue-tel-input:focus-within{
  @apply shadow-none !important;
}
</style>
