<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'

interface FileWithPreview extends File {
  preview?: string
}

const props = defineProps({
  modelValue: {
    type: Array as () => FileWithPreview[],
    default: () => [],
  },
  label: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  hint: {
    type: String,
    default: '',
  },
  accept: {
    type: String,
    default: 'image/*',
  },
  maxSize: {
    type: Number,
    default: 5, // in MB
  },
  sizeNote: {
    type: String,
    default: '',
  },
  formatNote: {
    type: String,
    default: '',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])
const { t } = useI18n()

const isDragging = ref(false)
const fileInput = ref<HTMLInputElement | null>(null)

const isImageAccept = computed(() => {
  if (!props.accept)
    return true
  return props.accept
    .split(',')
    .every(type => type.trim().startsWith('image/'))
})

const allFilesAreImages = computed(() => {
  return (
    props.modelValue.length > 0
    && props.modelValue.every(f => f.type && f.type.startsWith('image/'))
  )
})

const useImagePreview = computed(() => {
  return isImageAccept.value && allFilesAreImages.value
})

function handleFiles(files: FileList) {
  const newFiles = Array.from(files).map((file) => {
    if (file.type.startsWith('image/')) {
      const fileWithPreview = file as FileWithPreview
      fileWithPreview.preview = URL.createObjectURL(file)
      return fileWithPreview
    }
    return file
  })
  if (props.multiple) {
    emit('update:modelValue', [...props.modelValue, ...newFiles])
  } else {
    // Only keep the last file selected
    emit('update:modelValue', newFiles.slice(0, 1))
  }
}

function handleDrop(e: DragEvent) {
  e.preventDefault()
  isDragging.value = false
  if (e.dataTransfer?.files)
    handleFiles(e.dataTransfer.files)
}

function handleDragOver(e: DragEvent) {
  e.preventDefault()
  isDragging.value = true
}

function handleDragLeave(e: DragEvent) {
  e.preventDefault()
  isDragging.value = false
}

function handleFileInput(e: Event) {
  const input = e.target as HTMLInputElement
  if (input.files) {
    handleFiles(input.files)
    input.value = ''
  }
}

function removeFile(index: number) {
  const newFiles = [...props.modelValue]
  const removedFile = newFiles[index]
  if (removedFile.preview)
    URL.revokeObjectURL(removedFile.preview)

  newFiles.splice(index, 1)
  emit('update:modelValue', newFiles)
}

function formatFileSize(bytes: number) {
  if (bytes === 0)
    return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
}

const triggerFileInput = () => {
  fileInput.value?.click()
}
</script>

<template>
  <div class="block w-full">
    <!-- Dynamic Label -->
    <label
      v-if="label"
      class="mb-2 w-full flex items-center text-start text-[#261E27] text-base font-tajawal font-normal"
      :for="id"
    >
      <span
        v-if="hint"
        class="relative group text-[#FABF35] me-1 cursor-pointer align-middle"
      >
        🛈
        <span
          class="absolute bottom-full z-10 px-2 py-1 text-xs text-white whitespace-nowrap bg-black rounded shadow-lg opacity-0 transition-opacity pointer-events-none start-1/2 -ms-1 group-hover:opacity-100"
        >
          {{ hint }}
        </span>
      </span>
      {{ label }}
      <span v-if="required" class="text-red-600 ms-1">*</span>
    </label>

    <!-- File Upload Area -->
    <div class="w-full">
      <div
        class="flex justify-center items-center w-full"
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragleave="handleDragLeave"
      >
        <label
          :for="id"
          class="flex flex-col items-center justify-center w-full min-h-[170px] border border-dashed border-[#1DB5E4] rounded-[10px] cursor-pointer bg-white hover:bg-gray-50 p-6 transition-colors"
          :class="{ 'border-primary-500 bg-primary-50': isDragging }"
        >
          <UploadFile class="mb-2 w-10 h-10" />

          <div class="flex flex-col gap-1 justify-center items-center">
            <span class="text-base font-medium text-center text-black">
              <span v-if="required && !label" class="text-red-600 ms-1">*</span>
              {{ t("uploadArea.instruction") }}
              <span class="font-bold underline cursor-pointer text-[#1DB5E4]">
                {{ t("uploadArea.link") }}
              </span>
            </span>
            <span class="mt-1 text-xs text-center text-gray-400">
              {{ t("uploadArea.sizeNote", { maxSize }) }} <br>
              {{ t("uploadArea.formatNote", { formatNote }) }}
            </span>
          </div>
          <input
            :id="id"
            ref="fileInput"
            type="file"
            class="hidden"
            :accept="accept"
            :multiple="multiple"
            @change="handleFileInput"
          >
        </label>
      </div>

      <!-- File Preview Area -->
      <div
        v-if="modelValue.length > 0 && useImagePreview"
        class="mt-4 space-y-2"
      >
        <div
          v-for="(file, index) in modelValue"
          :key="index"
          class="flex gap-2 justify-between items-center p-2 bg-white rounded-lg border pe-3"
        >
          <div class="flex overflow-hidden gap-3 items-center">
            <div class="bg-gray-100 rounded aspect-square shrink-0">
              <img
                v-if="file.preview"
                :src="file.preview"
                :alt="file.name"
                class="w-10 h-10 rounded-[inherit] object-cover"
              >
              <svg
                v-else
                class="w-10 h-10 text-gray-300"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <rect
                  x="3"
                  y="3"
                  width="18"
                  height="18"
                  rx="4"
                  fill="currentColor"
                />
                <path
                  d="M8 16l2-2.5 2 2.5 3-4 3 4.5"
                  stroke="#fff"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <div class="flex flex-col gap-0.5 min-w-0">
              <p class="truncate text-[13px] font-medium">
                {{ file.name }}
              </p>
              <p class="text-xs text-gray-400">
                {{ formatFileSize(file.size) }}
              </p>
            </div>
          </div>
          <button
            class="flex justify-center items-center w-5 h-5 rounded-full transition-colors hover:bg-red-100"
            aria-label="Remove file"
            type="button"
            @click="removeFile(index)"
          >
            <CloseIcon />
          </button>
        </div>
      </div>
      <!-- File/mixed preview mode -->
      <div v-else-if="modelValue.length > 0" class="flex flex-wrap gap-2 mt-4">
        <div
          v-for="(file, index) in modelValue"
          :key="index"
          class="flex items-center px-1 py-1 mb-2 text-sm font-medium text-gray-800 bg-gradient-to-r from-teal-200 to-blue-200 rounded-full shadow-sm"
          style="max-width: 100%"
        >
          <span class="truncate max-w-[160px]">{{ file.name }}</span>
          <button
            class="flex justify-center items-center w-5 h-5 rounded-full transition-colors hover:bg-red-100"
            title="Remove"
            type="button"
            @click="removeFile(index)"
          >
            <CloseIcon />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.drag-over {
  border-color: theme("colors.primary.500");
  background-color: theme("colors.primary.50");
}

/* File pill styles */
.file-pill {
  @apply flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-teal-200 to-blue-200 text-gray-800 text-sm font-medium shadow-sm mb-2;
  max-width: 100%;
}

.file-pill .truncate {
  max-width: 160px;
}

.file-remove-btn {
  @apply ml-2 flex items-center justify-center w-5 h-5 rounded-full border border-red-300 bg-white text-red-500 hover:bg-red-100 transition-colors;
}
</style>
