<script lang="ts" setup>
const props = defineProps({
  imgSrc: {
    type: String || null,
  },
})

const emit = defineEmits<{ (e: 'upload', file: File): void }>()

const fileImage = ref<File | null>(null)
const onInputChange = (event) => {
  if (event.target.files.length === 1 && event.target.files[0].type.includes('image'))
    fileImage.value = event.target.files[0]
  emit('upload', event.target.files[0])
}
const events = ['dragenter', 'dragleave', 'dragover', 'drop']
onMounted(() => {
  events.forEach(event =>
    document.body.addEventListener(event, e => e.preventDefault()),
  )
})
onUnmounted(() => {
  events.forEach(event =>
    document.body.addEventListener(event, e => e.preventDefault()),
  )
})
function handleDrop(e: DragEvent): void {
  const file = e.dataTransfer?.files as FileList
  if (file.length === 1 && file[0].type.includes('image'))
    fileImage.value = file[0]
  emit('upload', file[0])
}
const clickInput = () => {
  document.getElementById('draggable-file-upload')?.click()
}
</script>

<template>
  <div
    class="z-10 border-2 border-dashed cursor-pointer banner"
    :style="{
      'background-image': `url(${imgSrc})`,
      'background-size': 'cover',
    }"
    @click="clickInput"
  >
    <div
      class="flex justify-center px-6 pt-5 pb-6 mt-1 rounded-md border-gray-300 cursor-pointer"
      @drop.prevent="handleDrop"
    >
      <input
        v-if="imgSrc?.length !== 0"
        id="draggable-file-upload"
        accept="image/*"
        type="file"
        class="sr-only"
        maxlength="1"
        @change="onInputChange"
      >
      <div
        v-if="imgSrc?.length === 0"
        class="space-y-1 text-center bg-white opacity-80"
      >
        <svg
          class="mx-auto w-12 h-12 text-gray-400"
          stroke="currentColor"
          fill="none"
          viewBox="0 0 48 48"
          aria-hidden="true"
        >
          <path
            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        <div class="flex justify-center text-base text-black">
          <label
            for="draggable-file-upload"
            class="relative mx-1 font-medium bg-white rounded-md cursor-pointer  text-primary-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 hover:text-primary-500"
          >
            <span>Upload a file</span>
            <input
              id="draggable-file-upload"
              accept="image/*"
              type="file"
              class="sr-only"
              maxlength="1"
              @change="onInputChange"
            >
          </label>
          <p class="pl-1">
            or drag and drop
          </p>
        </div>
        <p class="text-xs text-gray-500">
          PNG, JPG, GIF 900x300
        </p>
      </div>
    </div>
  </div>
</template>

<style>
.banner {
  background-size: cover;
  background-position: 50%;
  min-height: 150px;
}
</style>
