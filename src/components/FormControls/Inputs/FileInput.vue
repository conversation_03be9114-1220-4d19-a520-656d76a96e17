<script lang="ts" setup>
import type { PropType } from 'vue'

const props = defineProps({
  modelValue: {
    type: File as PropType<File | null>,
    default: null,
  },
  link: {
    type: String as PropType<String | null>,
  },
  name: {
    type: String,
    required: false,
    default: 'file',
  },
  label: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])

const file = ref()
const triggerFile = () => {
  file.value.click()
}

const onInputChange = (event) => {
  emit('update:modelValue', event.target.files[0])
}
</script>

<template>
  <div class="inline-flex flex-col justify-start items-end w-full">
    <!-- Dynamic Label -->
    <label
      v-if="label"
      class="mb-2 w-full flex items-center text-start text-[#261E27] text-lg font-tajawal font-normal"
      :for="id || name"
    >
      {{ label }}
      <span v-if="required" class="text-red-600 ms-1">*</span>
    </label>

    <div
      class="flex overflow-x-auto justify-between items-center py-3 w-full border ps-4 pe-4"
      :class="{ 'border-green-400 border-2 rounded-sm': !!modelValue }"
    >
      <span v-if="modelValue" class="font-bold text-gray-600">{{ modelValue.name }}</span>
      <input
        :id="id || name"
        ref="file"
        type="file"
        accept="image/jpeg,image/gif,image/png,application/pdf"
        class="hidden"
        @change="onInputChange"
      >
      <button
        type="button"
        class="py-2 font-medium leading-4 text-gray-700 rounded-md border border-gray-300 ms-5 ps-3 pe-3 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        @click="triggerFile"
      >
        {{ $t("form.upload") }}
      </button>
    </div>
  </div>
</template>
