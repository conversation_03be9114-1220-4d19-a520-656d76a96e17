<script setup lang="ts">
defineProps({
  modelValue: {
    type: [String, Number],
    required: true,
  },
  id: {
    type: String,
    default: '',
  },
  customClasses: {
    type: String,
    default: '',
  },
  notNeedDefaultOption: {
    type: Boolean,
    default: false,
  },
  label: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  hint: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['update:modelValue'])
function handleInput(event: Event) {
  emit('update:modelValue', (event.target as HTMLInputElement).value)
}
</script>

<template>
  <div class="inline-flex flex-col justify-start items-end w-full">
    <!-- Dynamic Label -->
    <label
      v-if="label"
      class="mb-2 w-full flex items-center text-start text-[#261E27] text-base   font-tajawal"
      :for="id"
    >
      <span v-if="hint" class="relative group text-[#FABF35] me-1 cursor-pointer"> <InfoIcon class="w-4 h-4 inline-block align-middle" /> <span class="absolute start-1/2 bottom-full -ms-1  px-2 py-1 bg-black text-white text-xs rounded shadow-lg whitespace-nowrap opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity z-10"> {{ hint }}</span></span>      {{ label }}
      <span v-if="required" class="text-red-600 ms-1">*</span>
    </label>

    <select
      :id="id"
      :value="modelValue"
      class="w-full px-4   rounded  h-[48px]  text-start  border-base-gray"
      :class="customClasses"
      @input="handleInput"
    >
      <option v-if="!notNeedDefaultOption" value="" selected hidden class="sm:text-sm">
        {{ $t("form.select") }}
      </option>
      <slot />
    </select>
    <slot name="append" />
  </div>
</template>

<style lang="scss"></style>
