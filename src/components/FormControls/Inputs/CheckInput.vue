<script lang="ts">
import { computed, defineComponent } from 'vue'

export default defineComponent({
  name: 'CheckInput',
})
</script>

<script lang="ts" setup>
interface Props {
  id: string
  modelValue: boolean
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
})

const emit = defineEmits(['update:modelValue'])

function checked(e: Event) {
  const target = e.target as HTMLInputElement
  emit('update:modelValue', target.checked)
}

const sizeClasses = computed(() => {
  const sizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  }
  return sizes[props.size]
})
</script>

<template>
  <input
    :id="id"
    :checked="modelValue"
    type="checkbox"
    class="rounded border-2 border-gray-300 checked:border-primary-600 text-primary-600 focus:ring-primary-600 focus:ring-2 focus:ring-offset-2 transition-all duration-200 ease-in-out cursor-pointer hover:border-primary-500 me-3"
    @change="checked" :class="[
      sizeClasses,
    ]"
  >
</template>
