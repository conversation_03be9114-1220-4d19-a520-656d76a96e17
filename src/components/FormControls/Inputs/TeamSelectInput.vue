<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useAuthStore } from '@/stores/auth'

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: 'team-select',
  },
  placeholder: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])
const { modelValue } = toRefs(props)
const { userInfo } = storeToRefs(useAuthStore())

function handleChange(event: Event) {
  emit('update:modelValue', (event.target as HTMLSelectElement).value)
}
</script>

<template>
  <div>
    <!-- Dynamic Label -->

    <SelectInput
      :id="id"
      v-model="modelValue"
      :label="label"
      :required="required"
      @change="handleChange"
    >
      <option
        v-for="team in userInfo.teams"
        :key="team.uuid"
        :value="team.uuid"
      >
        {{ team.name }}
      </option>
    </SelectInput>
  </div>
</template>
