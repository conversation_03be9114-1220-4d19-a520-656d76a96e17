<script lang="ts" setup>
defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  id: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  customClasses: {
    type: String,
    default: '',
  },

  label: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  hint: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur'])

function handleInput(event: Event) {
  emit('update:modelValue', (event.target as HTMLInputElement).value)
}

function handleFocus() {
  emit('focus')
}

function handleBlur() {
  emit('blur')
}
</script>

<template>
  <div class="inline-flex flex-col justify-start items-end w-full h-full">
    <!-- Dynamic Label -->
    <label
      v-if="label"
      class="mb-2 w-full flex items-center text-start text-[#261E27] text-base "
      :for="id"
    >
      <span v-if="hint" class="relative group text-[#FABF35] me-1 cursor-pointer"> <InfoIcon class="w-4 h-4 inline-block align-middle" /> <span class="absolute start-1/2 bottom-full -ms-1  px-2 py-1 bg-black text-white text-xs rounded shadow-lg whitespace-nowrap opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity z-10"> {{ hint }}</span></span>      {{ label }}
      <span v-if="required" class="text-red-600 ms-1">*</span>
    </label>

    <!-- Input Container -->
    <div
      class="relative gap-2.5 justify-start items-center w-full"
    >
      <!-- Prefix Slot -->
      <div v-if="$slots.prefix" class="flex pointer-events-none">
        <slot name="prefix" />
      </div>

      <!-- Input Field -->
      <input
        :id="id"
        type="text"
        :placeholder="placeholder"
        :value="modelValue"
        class="w-full px-4 py-3 rounded max-h-[48px]  inline-flex flex-1 text-start  border-base-gray"
        :class="[customClasses, $attrs.class]"
        v-bind="$attrs"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      >

      <!-- Suffix Slot -->
      <div v-if="$slots.suffix" class="flex items-center pointer-events-none">
        <slot name="suffix" />
      </div>
    </div>
  </div>
</template>

