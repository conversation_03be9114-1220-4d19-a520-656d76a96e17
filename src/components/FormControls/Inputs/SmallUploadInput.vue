<script lang="ts" setup>
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'

const props = defineProps({
  modelValue: {
    type: File as PropType<File | null | string>,
    default: null,
  },
  uploadType: {
    type: String,
  },
  accept: {
    type: String,
    default: 'image/*,application/pdf',
  },
  hint: {
    type: String,
    default: '',
  },
  defaultLink: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'imageUpload'])
const hint = computed(() => props.hint)
const link = ref('')

watch(
  () => props.defaultLink,
  (newVal) => {
    link.value = newVal
  },
  { immediate: true },
)

const showClearButton = ref(false)
const onInputChange = (event) => {
  const url = URL.createObjectURL(event.target.files[0])
  emit('update:modelValue', event.target.files[0])
  emit('imageUpload', url)
  link.value = url
  showClearButton.value = true
}
const clearImage = () => {
  emit('update:modelValue', null)
  emit('imageUpload', null)
  showClearButton.value = false
  link.value = ''
}
</script>

<template>
  <div>
    <div class="flex items-center">
      <button
        type="button"
        class="flex items-center gap-2 px-4 py-2 border-2 border-sky-300 rounded-xl bg-sky-50 text-sky-500 hover:bg-sky-100 transition"
        @click="$refs.uploadedPhoto.click()"
      >
        <UploadFile />
        <span>{{ label }}</span>
      </button>
      <input
        ref="uploadedPhoto"
        hidden
        :accept="accept"
        type="file"
        @change="onInputChange"
      >
    </div>
    <div class="mt-2 text-gray-400 text-sm">
      {{ hint }}
    </div>
  </div>
</template>
