<script setup>
import { defineExpose, ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: [File, String, null],
    default: null,
  },
  maxSize: {
    type: Number,
    default: 500 * 1024, // 500 KB
  },
  accept: {
    type: String,
    default: 'image/png, image/jpeg, image/jpg',
  },
  dimensions: {
    type: String,
    default: '100x100',
  },
  requirements: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])
const imageUrl = ref(null)
const fileInput = ref(null)

defineExpose({ clearImage })

watch(
  () => props.modelValue,
  (val) => {
    if (val instanceof File)
      imageUrl.value = URL.createObjectURL(val)
    else if (typeof val === 'string' && val)
      imageUrl.value = val
    else
      imageUrl.value = null
  },
  { immediate: true },
)

function onFileChange(event) {
  const file = event.target.files[0]
  if (!file)
    return
  if (file.size > props.maxSize) {
    alert('حجم الصورة اكبر من المسموح')
    return
  }
  if (!props.accept.split(',').map(t => t.trim()).includes(file.type)) {
    alert('صيغة الصورة غير مدعومة')
    return
  }
  emit('update:modelValue', file)
}

function clearImage() {
  emit('update:modelValue', null)
  // Reset the file input so the same file can be selected again
  if (fileInput.value)
    fileInput.value.value = ''
}
</script>

<template>
  <div class="flex flex-col-reverse items-center justify-center w-full gap-2 mb-6 sm:flex-row-reverse sm:items-center sm:justify-between sm:gap-2">
    <div class="w-full">
      <p class="avatar-requirements-text text-center sm:text-right text-xs sm:text-sm text-gray-400 w-full" v-html="requirements" />
    </div>
    <div class="flex items-center justify-center flex-shrink-0">
      <label class="cursor-pointer inline-block">
        <input
          ref="fileInput"
          type="file"
          :accept="accept"
          hidden
          @change="onFileChange"
        >
        <div class="relative flex items-center justify-center w-24 h-24 sm:w-28 sm:h-28 rounded-full shadow-sm overflow-hidden bg-none border border-gray-200 group">
          <div v-if="imageUrl" class="w-full h-full relative flex items-center justify-center z-10">
            <img :src="imageUrl" alt="avatar" class="w-full h-full object-cover rounded-full block">
            <!-- Modern clear button: overlay with trash icon, appears on hover -->
            <button
              type="button"
              class="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20"
              aria-label="مسح الصورة"
              @click.stop.prevent="clearImage"
            >
              <trashIcon class="w-6 h-6 text-white" />
            </button>
          </div>
          <template v-else>
            <div class="absolute w-full h-full z-0 bg-[repeating-conic-gradient(#e9e9e9_0%_25%,_#f5f5f5_0%_50%)] [background-size:16px_16px]" />
            <div class="flex items-center justify-center absolute inset-0 z-10">
              <div class="rounded-full bg-[#b8c3cc33] w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center">
                <AddAvatarIcon class="w-7 h-7 sm:w-8 sm:h-8 text-secondary" />
              </div>
            </div>
          </template>
        </div>
      </label>
    </div>
  </div>
</template>
