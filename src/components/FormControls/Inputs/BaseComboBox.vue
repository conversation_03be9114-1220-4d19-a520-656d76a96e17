<script setup lang="ts">
import { type PropType, computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import {
  CheckIcon,
  ChevronUpDownIcon,
  XMarkIcon,
} from '@heroicons/vue/20/solid'

import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxLabel,
  ComboboxOption,
  ComboboxOptions,
} from '@headlessui/vue'

interface Option {
  label: string
  value: string | number
  [key: string]: string | number | boolean
}
const props = defineProps({
  options: {
    type: Array as PropType<Array<Option>>,
    default: () => [],
  },
  error: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: [Array<String>, String],
    default: '',
  },
  serverFilter: {
    type: Boolean,
    default: false,
  },
  required: {
    type: <PERSON>olean,
    default: false,
  },
  multiple: {
    type: <PERSON><PERSON><PERSON>,
    default: false,
  },
  placeHolder: {
    type: String,
  },
  customClasses: {
    type: String,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:modelValue', 'search', 'change', 'selected'])
const router = useRouter()
const query = ref('')
const filteredOptions = computed(() => {
  return props.serverFilter || !query.value
    ? props.options
    : props.options?.filter(option =>
      option.label.toLowerCase().includes(query.value.toLowerCase()),
    )
})

const handleValueChange = (value: string) => {
  emit('update:modelValue', value)
}
const queryServer = useDebounceFn(() => {
  emit('search', query.value)
}, 500)
const handleQueryChange = (
  e: Event & {
    target: HTMLInputElement
  },
) => {
  query.value = e.target.value
  if (props.serverFilter && query.value.length >= 3)
    queryServer()
}
const displayOption = (value: string | string[]) => {
  if (Array.isArray(value)) {
    return value
      .map(
        value =>
          filteredOptions.value.find(option => value === option.value)
            ?.label || '',
      )
      .join(', ')
  }
  return (
    filteredOptions.value.find(option => value === option.value)?.label || ''
  )
}

const comboboxButton = ref(null)
const openSelect = () => {
  comboboxButton.value.el.click()
}

const clear = () => {
  query.value = ''
  emit('update:modelValue', '')
}
const position = computed(() => {
  if (!comboboxButton.value?.el)
    return 'bottom'

  const buttonRect = comboboxButton.value.el.getBoundingClientRect()
  const spaceBelow = window.innerHeight - buttonRect.bottom
  const spaceAbove = buttonRect.top

  if (window.innerHeight < 1000) {
    // When viewport height is small, choose direction with most space
    return spaceBelow > spaceAbove ? 'bottom' : 'top'
  }

  // Default to bottom unless very little space below
  return spaceBelow < 200 ? 'top' : 'bottom'
})

const dropdownRef = ref(null)
const inputWrapperRef = ref(null)
const dropdownStyles = ref({})

function updateDropdownPosition() {
  if (!inputWrapperRef.value || !dropdownRef.value)
    return
  const rect = inputWrapperRef.value.getBoundingClientRect()
  dropdownStyles.value = {
    'position': 'absolute',
    'left': `${rect.left + window.scrollX}px`,
    'top': `${rect.bottom + window.scrollY + 4}px`,
    'width': `${rect.width}px`,
    'zIndex': 9999,
    'overflow-y': 'scroll',
    'max-height': '200px',
  }
}

onMounted(() => {
  window.addEventListener('scroll', updateDropdownPosition, true)
  window.addEventListener('resize', updateDropdownPosition)
})
onBeforeUnmount(() => {
  window.removeEventListener('scroll', updateDropdownPosition, true)
  window.removeEventListener('resize', updateDropdownPosition)
})
watch([() => filteredOptions.value.length], async () => {
  await nextTick()
  updateDropdownPosition()
})
</script>

<template>
  <div class="flex flex-col justify-start items-end w-full min-w-0">
    <label
      v-if="$slots.default"
      class="mb-2 w-full flex items-center text-start text-[#261E27] text-base   font-tajawal font-normal"
    >
      <slot />
      <span v-if="props.required" class="text-red-600 ms-1">*</span>
    </label>
    <Combobox
      :model-value="props.modelValue"
      :disabled="disabled"
      :multiple="multiple"
      as="div"
      class="w-full"
      @update:model-value="handleValueChange"
    >
      <div ref="inputWrapperRef" class="relative w-full min-w-0">
        <ComboboxInput
          class="px-4 py-3 w-full min-w-0 rounded outline-none text-start border-base-gray"
          :class="[{ 'error-input': props.error }, customClasses]"
          :display-value="displayOption"
          :placeholder="$t(`form.${placeHolder}`)"
          @change="handleQueryChange($event)"
          @blur="$attrs?.onblur"
          @click="openSelect"
        />
        <ComboboxButton
          ref="comboboxButton"
          class="flex absolute top-1/2 justify-end items-center px-2 -translate-y-1/2 cursor-default end-0 focus:outline-none w-fit"
        >
          <slot name="inner-icon">
            <ChevronUpDownIcon class="w-5 h-5 text-gray-400" aria-hidden="true" />
          </slot>
          <slot name="inner-icon">
            <XMarkIcon
              v-if="props.modelValue && !disabled"
              class="w-6 h-6 text-red-800 cursor-pointer focus:border-primary-500"
              aria-hidden="true"
              @click="clear"
            />
          </slot>
          <div v-if="loading" class="">
            <svg class="w-5 h-5 text-blue-500 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
          </div>
        </ComboboxButton>
        <Teleport to="body">
          <ComboboxOptions
            v-if="filteredOptions?.length"
            ref="dropdownRef"
            class="z-30 py-1 mt-1 w-full text-base bg-white rounded-lg ring-1 ring-black ring-opacity-5 shadow-xl focus:outline-none sm:text-sm"
            :style="dropdownStyles"
          >
            <ComboboxOption
              v-for="(filteredOption, idx) in filteredOptions"
              :key="filteredOption.value"
              v-slot="{ active, selected }"
              :value="filteredOption.value"
              as="template"
              @click="emit('selected', filteredOption.value)"
            >
              <li
                class="relative py-2 rounded-md transition-colors duration-100 cursor-pointer select-none ps-9 pe-3 hover:bg-primary-50"
                :class="[
                  active ? 'bg-primary-100 text-primary-900' : 'text-gray-900',
                  selected ? 'font-semibold bg-primary-50' : '',
                  idx !== 0 ? 'border-t border-gray-100' : '',
                ]"
              >
                <span class="block" :class="[selected && 'font-semibold']">
                  {{ filteredOption.label }}
                </span>
                <span
                  v-if="selected"
                  class="flex absolute inset-y-0 items-center start-0 ps-3 text-primary-600"
                >
                  <CheckIcon class="w-5 h-5" aria-hidden="true" />
                </span>
              </li>
            </ComboboxOption>
          </ComboboxOptions>
          <!-- <div v-else class="flex z-30 justify-center items-center py-4 mt-1 w-full text-base text-gray-400 bg-white rounded-lg ring-1 ring-black ring-opacity-5 shadow-xl focus:outline-none sm:text-sm" :style="dropdownStyles">
            {{ $t('form.no_results') || 'لا توجد نتائج' }}
          </div> -->
        </Teleport>
      </div>
    </Combobox>
  </div>
</template>

<style scoped>
</style>
