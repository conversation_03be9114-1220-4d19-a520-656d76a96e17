<script lang="ts" setup>
import type { PropType } from 'vue'
import { storeToRefs } from 'pinia'
import { useServerErrors } from '@/stores/errors'

const prop = defineProps({
  modelValue: {
    type: File as PropType<File | null>,
    default: null,
  },

  link: {
    type: String as PropType<String | null>,
  },
  validation: {
    type: Object,
    required: false,
  },
  name: {
    type: String,
    required: false,
    default: 'image',
  },
  showClearBtn: {
    type: Boolean,
    required: false,
    default: false,
  },
})
const emit = defineEmits(['update:modelValue', 'imageCleared'])
const { getServerErrors } = storeToRefs(useServerErrors())
const file = ref()
const triggerFile = () => {
  file.value.click()
}

const onInputChange = (event) => {
  const url = URL.createObjectURL(event.target.files[0])
  emit('update:modelValue', event.target.files[0])
  emit('update:link', url)
}
const clearImage = () => {
  emit('update:modelValue', null)
  emit('imageCleared')
}
</script>

<template>
  <div
    class="w-full flex items-center justify-between border ps-4 pe-4 flex-wrap h-[60px] rounded-lg"
    :class="{ 'border-green-400 border-2 rounded-sm': !!modelValue }"
  >
    <span class="overflow-hidden w-10 h-10 bg-gray-100 rounded-full">
      <img v-if="link" width="150" height="150" :src="link">
      <svg
        v-else
        class="w-full h-full text-gray-300"
        fill="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"
        />
      </svg>
    </span>
    <span v-if="modelValue" class="font-bold text-gray-600 truncate d-block">{{
      modelValue.name
    }}</span>
    <input
      ref="file"
      type="file"
      accept="image/*"
      class="hidden"
      @change="onInputChange"
    >
    <div class="flex gap-2 justify-between items-center">
      <button
        type="button"
        class="py-2 font-medium leading-4 text-gray-700 rounded-md border border-gray-300 shadow-sm ms-5 ps-3 pe-3 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        @click="triggerFile"
      >
        {{ $t("form.upload") }}
      </button>
      <base-button
        v-if="showClearBtn && (modelValue || link)"
        type="button"
        class="text-sm font-medium text-white cursor-pointer hover:bg-red-800"
        @click="clearImage"
      >
        <span>{{ $t("settings.bookingpage.clear") }}</span>
      </base-button>
    </div>
    <span
      v-if="getServerErrors"
      :class="getServerErrors[name]?.[0] ? 'error-message' : ''"
    >
      {{ getServerErrors[name]?.[0] }}
    </span>
  </div>
</template>
