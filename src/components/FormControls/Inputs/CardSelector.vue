<script setup>
defineProps({
  option: {
    type: String,
    required: true,
  },
  active: {
    type: Boolean,
    default: false,
  },
  icon: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <div
    class="card-selector min-w-[120px] w-full sm:w-auto sm:min-w-[160px] md:min-w-[180px]
             h-10 sm:h-12 px-3 sm:px-4 md:px-6 py-2 rounded-lg
             outline outline-1 outline-[#BFC2FF]
             inline-flex justify-center items-center gap-2
             hover:bg-[#EDF0FF] transition-colors duration-200
             text-sm sm:text-base leading-normal tracking-wide
             cursor-pointer"
    :class="{
      'bg-[#EDF0FF] outline-2 outline-secondary': active,
      'hover:bg-[#EDF0FF]': !active,
    }"
  >
    <img v-if="icon" :src="icon" :alt="option" class="w-5 h-5">
    <slot v-else name="icon" />
    <div class="text-center whitespace-nowrap overflow-hidden text-ellipsis">
      {{ $t(option) }}
    </div>
  </div>
</template>

<style scoped>
.card-selector {
  direction: rtl;
}
</style>
