<script lang="ts" setup>
import { useI18n } from 'vue-i18n'

const props = defineProps({
  modelValue: {
    type: [Number, String],
    required: true,
  },
  id: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  customClasses: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  hint: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  min: {
    type: [Number, String],
    default: undefined,
  },
  max: {
    type: [Number, String],
    default: undefined,
  },
  currency: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])

const { t } = useI18n()

function handleInput(event: Event) {
  emit('update:modelValue', (event.target as HTMLInputElement).value)
}

function increment() {
  let value = Number(props.modelValue) || 0
  const max = props.max !== undefined ? Number(props.max) : undefined
  value++
  if (max !== undefined && value > max)
    value = max
  emit('update:modelValue', value)
}

function decrement() {
  let value = Number(props.modelValue) || 0
  const min = props.min !== undefined ? Number(props.min) : undefined
  value--
  if (min !== undefined && value < min)
    value = min
  emit('update:modelValue', value)
}
</script>

<template>
  <div class="inline-flex flex-col justify-start items-end w-full number-input-group">
    <!-- Dynamic Label -->
    <label
      v-if="label"
      class="mb-2 w-full flex items-center text-start text-[#261E27] text-base   font-tajawal font-normal"
      :for="id"
    >
      <span v-if="hint" class="relative group text-[#FABF35] me-1 cursor-pointer align-middle"> <InfoIcon class="inline-block w-4 h-4 align-middle" /> <span class="absolute bottom-full z-10 px-2 py-1 text-xs text-white whitespace-nowrap bg-black rounded shadow-lg opacity-0 transition-opacity pointer-events-none start-1/2 -ms-1 group-hover:opacity-100"> {{ hint }}</span></span>
      {{ label }}
      <span v-if="required" class="text-red-600 ms-1">*</span>
    </label>

    <!-- Input Container -->
    <div
      class="relative gap-2.5 justify-start items-center w-full custom-number-input-container"
    >
      <!-- Prefix Slot -->
      <div v-if="$slots.prefix" class="flex pointer-events-none">
        <slot name="prefix" />
      </div>

      <!-- Input Field -->
      <input
        :id="id"
        type="number"
        :placeholder="placeholder"
        :value="modelValue"
        :min="min"
        :max="max"
        step="any"
        class="w-full px-4 py-3 rounded-[4px] inline-flex flex-1 text-start border-base-gray custom-number-input h-[48px] number-input-with-currency"
        :class="customClasses"
        v-bind="$attrs"
        :style="currency ? `padding-${t('dir') === 'rtl' ? 'right' : 'left'}: 4.5rem` : ''"
        @input="handleInput"
      >

      <!-- Currency Label -->
      <span v-if="currency" class="absolute text-gray-500 top-1/2 -translate-y-1/2" :class="[t('dir') === 'rtl' ? 'right-10' : 'left-10']" style="pointer-events: none;">
        {{ t(`currenices.${currency}`) }}
      </span>

      <!-- Custom Arrows -->
      <div class="flex absolute top-0 z-10 flex-col justify-center items-stretch h-full bg-white opacity-0 transition-opacity duration-150 pointer-events-none custom-arrows end-0">
        <button type="button" class="flex flex-1 justify-center items-center border-b border-gray-200 arrow-btn hover:bg-gray-100" @mousedown.prevent="increment">
          <svg width="16" height="10" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 2L13 7H3L8 2Z" fill="#261E27" /></svg>
        </button>
        <button type="button" class="flex flex-1 justify-center items-center arrow-btn hover:bg-gray-100" @mousedown.prevent="decrement">
          <svg width="16" height="10" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 8L3 3H13L8 8Z" fill="#261E27" /></svg>
        </button>
      </div>

      <!-- Suffix Slot -->
      <div v-if="$slots.suffix" class="flex items-center pointer-events-none">
        <slot name="suffix" />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Hide default arrows */
.custom-number-input::-webkit-outer-spin-button,
.custom-number-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.custom-number-input[type="number"] {
  -moz-appearance: textfield;
}

/* Custom arrows only on hover/focus */
.custom-number-input-container:hover .custom-number-input,
.custom-number-input-container:focus-within .custom-number-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.custom-number-input-container:hover .custom-arrows,
.custom-number-input-container:focus-within .custom-arrows {
  opacity: 1;
  pointer-events: auto;
}
.custom-arrows {
  width: 32px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  background: transparent;
  border-radius: 0 4px 4px 0;
  box-shadow: none;
}
.arrow-btn {
  width: 100%;
  height: 50%;
  padding: 0;
  border-right: 1px solid #d1d5db;
  background: transparent;
  cursor: pointer;
  transition: background 0.15s;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.arrow-btn:active {
  background: #f3f4f6;
}

/* Add padding to input if currency is present */
.number-input-with-currency {
  /* Default: no extra padding */
}
</style>
