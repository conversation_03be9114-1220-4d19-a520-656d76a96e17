<script setup lang="ts">
import { Switch } from '@headlessui/vue'
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  label: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['update:modelValue'])
const modelValueProxy = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emit('update:modelValue', val),
})
</script>

<template>
  <div class="flex items-center gap-2 ">
    <Switch
      :id="id"
      v-model="modelValueProxy"
      class="inline-flex relative flex-shrink-0 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none  "
      :class="modelValueProxy ? 'bg-blue-600' : 'bg-gray-200'"
      v-bind="$attrs"
    >
      <span
        aria-hidden="true"
        class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
        :class="[
          modelValueProxy ? 'translate-x-5 rtl:-translate-x-5' : 'translate-x-0',
        ]"
      />
    </Switch>
    <label
      v-if="label"
      :for="id"
      class="text-base  text-gray-800 cursor-pointer select-none"
    >
      {{ label }}
    </label>
  </div>
</template>
