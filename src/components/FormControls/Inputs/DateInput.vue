<script setup lang="ts">
import { computed, ref } from 'vue'
import CalendarsIcon from '@/components/Icons/CalendarsIcon.vue'

const props = defineProps({
  modelValue: [String, Date],
  id: { type: String, default: '' },
  label: { type: String, default: '' },
  placeholder: { type: String, default: '' },
  min: { type: String, default: '' },
  max: { type: String, default: '' },
  required: { type: Boolean, default: false },
  hint: { type: String, default: '' },
  error: { type: [String, Boolean], default: '' },
  disabled: { type: Boolean, default: false },
  customClass: {
    type: [String, Object],
    default: ''
  },
  onblur: {
    type: Function,
    default: null
  }
});

const emit = defineEmits(['update:modelValue'])
const inputRef = ref<HTMLInputElement | null>(null)

const inputValue = computed({
  get() {
    if (!props.modelValue)
      return ''
    if (typeof props.modelValue === 'string')
      return props.modelValue
    const d = new Date(props.modelValue)
    return d.toISOString().slice(0, 10)
  },
  set(val) {
    emit('update:modelValue', val)
  },
})

const openCalendar = () => {
  inputRef.value?.showPicker?.(); // works in modern browsers
  inputRef.value?.focus();
};

const handleInput = (e: Event) => {
  const target = e.target as HTMLInputElement;
  inputValue.value = target.value;
};

const handleBlur = () => {
  if (props.onblur) {
    props.onblur();
  }
};
</script>

<template>
  <div class="flex flex-col w-full">
    <label
      v-if="label"
      :for="id"
      class="mb-2 flex items-center text-start text-[#261E27] text-base font-tajawal font-normal"
    >
      {{ label }}
      <span v-if="required" class="text-red-600 ms-1">*</span>
    </label>

    <div class="relative w-full">
      <!-- Date input -->
      <input
        :id="id"
        ref="inputRef"
        type="date"
        class="w-full h-12 rounded-[4px] border text-base font-tajawal focus:outline-none appearance-none transition-all"
        :class="[
          customClass,
          'border-base-gray focus:ring-2 focus:ring-primary-400 focus:border-primary-400'
        ]"
        :placeholder="placeholder"
        :min="min"
        :max="max"
        :required="required"
        :disabled="disabled"
        :value="inputValue"
        @input="handleInput"
        @blur="handleBlur"
        :aria-invalid="!!error"
        :aria-describedby="error ? id + '-error' : undefined"
      />

      <!-- Custom calendar icon on the right -->
      <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        <CalendarsIcon class="w-5 h-5" />
      </div>

      <!-- Transparent button to open date picker (right side) -->
      <button
        type="button"
        class="absolute inset-y-0 right-0 w-10 cursor-pointer bg-transparent border-none focus:outline-none"
        aria-label="Open calendar"
        @click="openCalendar"
      />
    </div>

    <div v-if="error" :id="`${id}-error`" class="text-xs text-red-600 mt-1">
      {{ error }}
    </div>
    <div v-else-if="hint" class="text-xs text-gray-400 mt-1">
      {{ hint }}
    </div>
  </div>
</template>

<style>
input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
  display: none;
}
</style>
