<script setup lang="ts">
defineProps({
  value: {
    type: String,
    required: true,
  },
})

const showTooltip = ref(false)
const copied = ref(false)
const copyInput = ref<HTMLInputElement | null>(null)
const copyContent = () => {
  const input = copyInput.value.$el as HTMLInputElement
  if (input) {
    input.select()
    navigator.clipboard.writeText(input.value)
    copied.value = true
    showTooltip.value = true
    setTimeout(() => {
      copied.value = false
      showTooltip.value = false
    }, 2000)
  }
}
</script>

<template>
  <div class="max-w-full min-w-[16rem]">
    <div class="relative">
      <label for="npm-install-copy-button" class="sr-only">Label</label>
      <TextInput
        ref="copyInput"
        class="disabled:bg-gray-100 "
        :model-value="value"
        disabled
        readonly
      />
      <button
        class="absolute inline-flex items-center justify-center p-2 text-gray-500 -translate-y-1/2 rounded-lg end-2 top-1/2 hover:bg-gray-100"
        type="button"
        @click="copyContent"
      >
        <span
          class="inline-flex items-center"
          :class="[!copied ? 'block' : 'hidden']"
        >
          <svg
            class="w-3.5 h-3.5"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 18 20"
          >
            <path
              d="M16 1h-3.278A1.992 1.992 0 0 0 11 0H7a1.993 1.993 0 0 0-1.722 1H2a2 2 0 0 0-2 2v15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2Zm-3 14H5a1 1 0 0 1 0-2h8a1 1 0 0 1 0 2Zm0-4H5a1 1 0 0 1 0-2h8a1 1 0 1 1 0 2Zm0-5H5a1 1 0 0 1 0-2h2V2h4v2h2a1 1 0 1 1 0 2Z"
            />
          </svg>
        </span>
        <span
          id="success-icon"
          class="inline-flex items-center"
          :class="[copied ? 'block' : 'hidden']"
        >
          <svg
            class="w-3.5 h-3.5 text-primary-700"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 16 12"
          >
            <path
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M1 5.917 5.724 10.5 15 1.5"
            />
          </svg>
        </span>
      </button>
      <div
        class="absolute z-10 inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm top-0 -translate-y-full start-[calc(100%-3rem)] transform"
        :class="[showTooltip ? 'visible' : 'invisible']"
      >
        <span :class="[copied ? 'block' : 'hidden']">Copied!</span>
      </div>
    </div>
  </div>
</template>
