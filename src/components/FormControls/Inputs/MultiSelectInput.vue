<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from "vue";

interface Option {
  id: string | number
  name: string
}

const isOpen = ref(false);
const search = ref('');
const inputRef = ref<HTMLInputElement | null>(null);
const wrapperRef = ref<HTMLElement | null>(null);
const isDirty = ref(false); // Track if the field has been interacted with

const props = defineProps({
  modelValue: {
    type: Array as () => (string | number)[],
    required: true,
  },
  options: {
    type: Array as () => Option[],
    required: true,
  },
  placeholder: {
    type: String,
    default: "اختر",
  },
  label: String,
  required: {
    type: Boolean,
    default: false
  },
  hint: String,
  id: String,
  customClass: {
    type: [String, Object],
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'blur']);

const selected = computed<Option[]>(() =>
  props.options.filter((opt: Option) => props.modelValue.includes(opt.id)),
)

const unselected = computed<Option[]>(() =>
  props.options.filter((opt: Option) => !props.modelValue.includes(opt.id)),
)

const filteredOptions = computed<Option[]>(() => {
  const q = search.value.trim().toLowerCase()
  const all = [...selected.value, ...unselected.value]
  if (!q)
    return all
  return all.filter(opt => opt.name.toLowerCase().includes(q))
})

function openDropdown() {
  isOpen.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

function closeDropdown() {
  isOpen.value = false;
  search.value = "";
  isDirty.value = true; // Mark as dirty when user finishes interaction
  if (props.onblur) {
    props.onblur();
  }
  emit('blur');
}

function toggleDropdown() {
  isOpen.value ? closeDropdown() : openDropdown()
}

function selectOption(id: string | number) {
  if (!props.modelValue.includes(id)) {
    emit('update:modelValue', [...props.modelValue, id])
  }
  else {
    emit(
      'update:modelValue',
      props.modelValue.filter(v => v !== id),
    )
  }
}

function removeTag(id: string | number) {
  emit(
    'update:modelValue',
    props.modelValue.filter(v => v !== id),
  )
}

function clearAll() {
  emit('update:modelValue', [])
}

// Tag overflow logic
const maxTags = ref(3)
const visibleTags = computed<Option[]>(() => {
  if (selected.value.length <= maxTags.value)
    return selected.value
  return selected.value.slice(0, maxTags.value)
})

const hasOverflow = computed(() => selected.value.length > maxTags.value)

function updateMaxTags() {
  if (!wrapperRef.value)
    return
  const width = wrapperRef.value.offsetWidth
  maxTags.value = Math.max(1, Math.floor((width - 100) / 90))
}

// Close dropdown when clicking outside
function handleClickOutside(event: MouseEvent) {
  if (wrapperRef.value && !wrapperRef.value.contains(event.target as Node))
    closeDropdown()
}

// When value changes, consider the field as interacted with
watch(() => props.modelValue, () => {
  if (props.modelValue.length > 0) {
    isDirty.value = true;
  }
});

watch(() => props.modelValue, updateMaxTags, { immediate: true });
watch(isOpen, val => { if (!val) search.value = ''; });

onMounted(() => {
  window.addEventListener('resize', updateMaxTags)
  document.addEventListener('mousedown', handleClickOutside)
  nextTick(updateMaxTags)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateMaxTags)
  document.removeEventListener('mousedown', handleClickOutside)
})
</script>

<template>
  <div class="flex flex-col justify-start items-end w-full" dir="rtl">
    <label
      v-if="label"
      :for="id"
      class="mb-2 w-full flex items-center text-start text-[#261E27] text-base font-tajawal font-normal"
    >
      <span
        v-if="hint"
        class="relative group text-[#FABF35] me-1 cursor-pointer align-middle"
      >
        <InfoIcon class="inline-block w-4 h-4 align-middle" />
        <span
          class="absolute bottom-full z-10 px-2 py-1 text-xs text-white whitespace-nowrap bg-black rounded shadow-lg opacity-0 transition-opacity pointer-events-none start-1/2 -ms-1 group-hover:opacity-100"
        >
          {{ hint }}
        </span>
      </span>
      {{ label }}
      <span v-if="required" class="text-red-600 ms-1">*</span>
    </label>

    <div ref="wrapperRef" class="relative w-full">
      <!-- Input area -->
      <div
        class="flex items-start w-full min-h-[44px] rounded border bg-white text-base px-3 py-2 cursor-pointer transition"
        :class="[
          customClass,
          $attrs.class,
          'border-gray-300 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500'
        ]"
        @click="toggleDropdown"
        @blur="$emit('blur')"
        tabindex="0"
        @keydown.enter.prevent="toggleDropdown"
        @keydown.space.prevent="toggleDropdown"
      >
        <div class="flex flex-wrap gap-2 items-center max-h-20 overflow-y-auto flex-1">
          <template v-if="selected.length">
            <span
              v-for="tag in visibleTags"
              :key="tag.id"
              class="inline-flex flex-row gap-1 items-center px-2 my-1 text-base text-black bg-blue-50 rounded-xl border border-blue-300"
            >
              <button
                @click.stop="removeTag(tag.id)"
                aria-label="إزالة"
                class="flex justify-center items-center w-4 h-4 rounded-full transition hover:bg-red-50"
              >
                <svg width="16" height="16" viewBox="0 0 20 20" fill="none">
                  <circle
                    cx="10"
                    cy="10"
                    r="8"
                    stroke="#E94B6A"
                    stroke-width="2"
                  />
                  <path
                    d="M7 7L13 13"
                    stroke="#E94B6A"
                    stroke-width="2"
                    stroke-linecap="round"
                  />
                  <path
                    d="M13 7L7 13"
                    stroke="#E94B6A"
                    stroke-width="2"
                    stroke-linecap="round"
                  />
                </svg>
              </button>
              <span>{{ tag.name }}</span>
            </span>
            <span v-if="hasOverflow" class="px-2 text-sm text-gray-500">
              +{{ selected.length - maxTags }}
            </span>
          </template>
          <span v-else class="text-gray-400">{{ placeholder }}</span>
        </div>
        <span class="text-gray-400 pointer-events-none ms-2 flex-shrink-0 flex items-center h-[32px]">
          <template v-if="isOpen">
            <!-- Search icon -->
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-4 h-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 104.5 4.5a7.5 7.5 0 0012.15 12.15z"
              />
            </svg>
          </template>
          <template v-else>
            <!-- Down arrow -->
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-4 h-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </template>
        </span>
      </div>

      <!-- Dropdown -->
      <div
        v-if="isOpen"
        class="overflow-auto absolute right-0 left-0 z-50 mt-1 max-h-60 bg-white rounded-lg border border-gray-200 shadow-lg"
      >
        <button
          v-if="selected.length"
          class="px-4 py-2 w-full text-sm font-medium text-right text-red-500 border-b border-gray-100 transition hover:bg-red-50"
          @click.stop="clearAll"
        >
          {{ $t("tags.clearAll") }}
        </button>
        <ul class="divide-y divide-gray-100">
          <li
            v-for="opt in filteredOptions"
            :key="opt.id"
            class="flex justify-between items-center px-4 py-2 text-right transition cursor-pointer hover:bg-blue-50"
            :class="{ 'bg-blue-50': props.modelValue.includes(opt.id) }"
            @click.stop="selectOption(opt.id)"
          >
            <span class="flex-1 truncate">{{ opt.name }}</span>
            <span class="ml-2">
              <svg
                v-if="props.modelValue.includes(opt.id)"
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4 text-blue-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="3"
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <svg
                v-else
                xmlns="http://www.w3.org/2000/svg"
                class="w-4 h-4 text-gray-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <circle cx="12" cy="12" r="9" stroke-width="2" />
              </svg>
            </span>
          </li>
          <li
            v-if="!filteredOptions.length"
            class="px-4 py-2 text-center text-gray-400"
          >
            {{ $t("tags.noResults") }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Hide native input spinner/arrows for number fields if any */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance: textfield;
}
input {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent;
}

/* Only target the search input inside your component */
input[ref="inputRef"] {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent;
}
</style>
