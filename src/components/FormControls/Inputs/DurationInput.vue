<script lang="ts" setup>
import { ref, useAttrs, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: [Number, Object],
    required: true,
  },
  id: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  hint: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  customClasses: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])

const hours = ref<string | number>('')
const minutes = ref<string | number>('')

watch(() => props.modelValue, (newVal) => {
  if (typeof newVal === 'number') {
    if (newVal === 0) {
      hours.value = ''
      minutes.value = ''
    }
    else {
      hours.value = Math.floor(newVal / 60) || ''
      minutes.value = newVal % 60 || ''
    }
  }
  else if (newVal && typeof newVal === 'object') {
    hours.value = newVal.hours || ''
    minutes.value = newVal.minutes || ''
  }
}, { immediate: true })

function updateValue() {
  const hoursNum = Number(hours.value) || 0
  const minutesNum = Number(minutes.value) || 0

  // Clamp values
  if (hoursNum < 0)
    hours.value = 0
  if (hoursNum > 23)
    hours.value = 23
  if (minutesNum < 0)
    minutes.value = 0
  if (minutesNum > 59)
    minutes.value = 59

  emit('update:modelValue', hoursNum * 60 + minutesNum)
}

function inc() {
  const hoursNum = Number(hours.value) || 0
  if (hoursNum < 23)
    hours.value = hoursNum + 1

  updateValue()
}

function dec() {
  const hoursNum = Number(hours.value) || 0
  if (hoursNum > 0)
    hours.value = hoursNum - 1

  updateValue()
}
const attrs = useAttrs()
</script>

<template>
  <div class="inline-flex flex-col justify-start items-end w-full duration-input-group">
    <label
      v-if="label"
      class="mb-2 w-full flex items-center text-start text-[#261E27] text-base font-tajawal font-normal"
      :for="id"
    >
      <span v-if="hint" class="relative group text-[#FABF35] me-1 cursor-pointer align-middle">
        <InfoIcon class="w-4 h-4 inline-block align-middle" />
        <span class="absolute bottom-full z-10 px-2 py-1 text-xs text-white whitespace-nowrap bg-black rounded shadow-lg opacity-0 transition-opacity pointer-events-none start-1/2 -ms-1 group-hover:opacity-100">
          {{ hint }}
        </span>
      </span>
      {{ label }}
      <span v-if="required" class="text-red-600 ms-1">*</span>
    </label>
    <div
      class="rounded-[4px] text-center border border-gray-300 relative w-full custom-duration-input-container flex"
      :class="[attrs.class, customClasses && customClasses]"
    >
      <div class="flex flex-1 min-w-0 h-full">
        <input
          :id="id ? `${id}-minutes` : undefined"
          v-model.number="minutes"
          type="number"
          placeholder="الدقيقة"
          min="0"
          max="59"
          class="flex-1 min-w-0 h-full border-none custom-duration-input focus:outline-none"
          @input="updateValue"
        >
        <span class="flex items-center px-1 text-xl font-bold text-black">:</span>
        <input
          :id="id ? `${id}-hours` : undefined"
          v-model.number="hours"
          type="number"
          placeholder="الساعة"
          min="0"
          max="23"
          class="flex-1 min-w-0 h-full border-none custom-duration-input focus:outline-none"
          @input="updateValue"
        >
      </div>
      <div class="flex absolute top-0 z-10 flex-col justify-center items-stretch h-full bg-transparent opacity-0 transition-opacity duration-150 pointer-events-none custom-arrows end-0" style="width: 32px;">
        <button type="button" class="flex flex-1 justify-center items-center border-b border-gray-200 arrow-btn hover:bg-gray-100" @mousedown.prevent="inc">
          <svg width="16" height="10" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 2L13 7H3L8 2Z" fill="#261E27" /></svg>
        </button>
        <button type="button" class="flex flex-1 justify-center items-center arrow-btn hover:bg-gray-100" @mousedown.prevent="dec">
          <svg width="16" height="10" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 8L3 3H13L8 8Z" fill="#261E27" /></svg>
        </button>
      </div>
      <!-- Reserve space for arrows so inputs never shrink under them -->
      <div class="w-8 h-full opacity-0 pointer-events-none shrink-0" />
    </div>
  </div>
</template>

<style scoped>
.custom-duration-input::-webkit-outer-spin-button,
.custom-duration-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.custom-duration-input[type="number"] {
  -moz-appearance: textfield;
}

.custom-duration-input {
  width: 2.5rem;
  height: 100%;
  text-align: center;
  background: transparent;
  outline: none;
  box-shadow: none;
  border: none;
  font-size: 1rem;
  /* Remove border on focus */
}

.custom-duration-input-container {
  min-width: 120px;
  height: 3rem; /* h-12 */
  display: flex;
  align-items: center;
  position: relative;
  transition: box-shadow 0.2s, border-color 0.2s;
}

.custom-duration-input-container:focus-within {
  border-color: #3559fa;
}

.custom-duration-input-container:hover .custom-arrows,
.custom-duration-input-container:focus-within .custom-arrows {
  opacity: 1;
  pointer-events: auto;
}
.custom-arrows {
  width: 32px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  background: transparent;
  border-radius: 0 4px 4px 0;
  box-shadow: none;
}
.arrow-btn {
  width: 100%;
  height: 50%;
  padding: 0;
  border-right: 1px solid #d1d5db;
  background: transparent;
  cursor: pointer;
  transition: background 0.15s;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.arrow-btn:active {
  background: #f3f4f6;
}
</style>
