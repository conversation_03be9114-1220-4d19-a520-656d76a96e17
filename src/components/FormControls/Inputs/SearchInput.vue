<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  customClasses: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])

const classes = computed(() => {
  return `w-full px-4 py-3 rounded-[4px] outline outline-[1px] outline-offset-[-1px] inline-flex flex-1 text-start outline-none border-gray-300 ${props.customClasses}`
})
</script>

<template>
  <div>
    <label v-if="label" :for="id" class="mb-2 w-full flex items-center text-start text-[#261E27] text-base   font-tajawal font-normal">{{ label }}</label>
    <input
      :id="id"
      type="search"
      :value="modelValue"
      :placeholder="placeholder"
      :class="classes"
      @input="$emit('update:modelValue', $event.target.value)"
    >
  </div>
</template>
