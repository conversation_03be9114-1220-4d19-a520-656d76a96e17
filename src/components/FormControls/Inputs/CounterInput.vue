<script lang="ts" setup>
const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: 1,
  },
  placeholder: {
    type: String,
    default: '',
  },
  customClasses: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])

const currentValue = ref(Math.max(1, Number(props.modelValue)))

function decrement() {
  if (currentValue.value > 1) {
    currentValue.value--
    emit('update:modelValue', currentValue.value)
  }
}

function increment() {
  currentValue.value++
  emit('update:modelValue', currentValue.value)
}

const containerClasses = computed(() => `w-full inline-flex items-center rounded-md ${props.customClasses}`)
</script>

<template>
  <div class="w-full h-[46px]">
    <div :class="containerClasses">
      <button
        type="button"
        class="p-3 bg-gray-100 border border-gray-300 hover:bg-gray-200 rounded-s-lg  h-[46px] focus:ring-gray-100 focus:ring-2 focus:outline-none"
        @click="decrement"
      >
        <svg class="w-3 h-3 text-gray-900" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 2">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h16" />
        </svg>
      </button>
      <div
        class="bg-white

      border border-gray-300 h-[46px] text-center text-gray-900 text-sm flex items-center justify-center px-3 min-w-[3rem] w-full max-w-full"
        :placeholder="placeholder"
      >
        {{ modelValue }}
      </div>
      <button
        type="button"
        class="p-3 bg-gray-100 border border-gray-300 hover:bg-gray-200 rounded-e-lg  h-[46px] focus:ring-gray-100 focus:ring-2 focus:outline-none"
        @click="increment"
      >
        <svg class="w-3 h-3 text-gray-900" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 1v16M1 9h16" />
        </svg>
      </button>
    </div>
  </div>
</template>
