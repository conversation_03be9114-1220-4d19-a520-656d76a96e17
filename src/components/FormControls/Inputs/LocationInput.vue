<script setup lang="ts">
import { ref, watch } from 'vue'
import { GoogleMap, Marker } from 'vue3-google-map'
import { MapPinIcon } from '@heroicons/vue/24/outline'

const API_KEY = import.meta.env.VITE_GOOGLE_MAP_KEY

const props = defineProps({
  modelValue: Object, // { latitude, longitude }
  label: String,
  placeholder: String,
  showMap: { type: Boolean, default: true },
})
const emit = defineEmits(['update:modelValue'])

const showMapModal = ref(false)
const localValue = ref(props.modelValue)
const inputString = ref('')

watch(() => props.modelValue, (val) => {
  localValue.value = val
  inputString.value = val && val.latitude && val.longitude
    ? `${val.latitude}, ${val.longitude}`
    : ''
}, { immediate: true })

function openMap() {
  if (props.showMap) showMapModal.value = true
}
function closeMap() {
  showMapModal.value = false
}
function setLocation(location) {
  emit('update:modelValue', location)
  closeMap()
}

// For the map modal
const mapLocation = ref(localValue.value || { latitude: 24.774265, longitude: 46.738586 })
watch(showMapModal, (val) => {
  if (val) mapLocation.value = localValue.value || { latitude: 24.774265, longitude: 46.738586 }
})
function setMarker(event) {
  mapLocation.value = {
    latitude: event.latLng.lat(),
    longitude: event.latLng.lng(),
  }
}
function confirm() {
  setLocation(mapLocation.value)
}

function onInput(e) {
  inputString.value = e.target.value
  const [lat, lng] = inputString.value.split(',').map(s => parseFloat(s.trim()))
  if (!isNaN(lat) && !isNaN(lng)) {
    emit('update:modelValue', { latitude: lat, longitude: lng })
  }
}
</script>

<template>
  <div class="inline-flex flex-col justify-start items-end w-full h-full">
    <!-- Dynamic Label -->
    <label
      v-if="label"
      class="mb-2 w-full flex items-center text-start text-[#261E27] text-base "
      :for="id"
    >
      <span v-if="hint" class="relative group text-[#FABF35] me-1 cursor-pointer"> <InfoIcon class="w-4 h-4 inline-block align-middle" /> <span class="absolute start-1/2 bottom-full -ms-1  px-2 py-1 bg-black text-white text-xs rounded shadow-lg whitespace-nowrap opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity z-10"> {{ hint }}</span></span>      {{ label }}
      <span v-if="required" class="text-red-600 ms-1">*</span>
    </label>

    <!-- Input Container -->
    <div
      class="relative gap-2.5 justify-start items-center w-full"
    >
      <!-- Prefix Slot -->
      <div v-if="$slots.prefix" class="flex pointer-events-none">
        <slot name="prefix" />
      </div>

      <!-- Input Field with Map Icon on the Left (RTL) -->
      <div class="relative flex items-center w-full">
        <input
          class="w-full px-4 py-3 rounded max-h-[48px] inline-flex flex-1 text-start border-base-gray"
          :placeholder="placeholder"
          v-model="inputString"
          @input="onInput"
        />
        <!-- Map Icon: clickable, on the left in RTL -->
        <button type="button" @click="openMap" class="absolute left-3 flex items-center justify-center p-0 m-0 bg-transparent border-0 cursor-pointer">
          <MapPinIcon class="w-5 h-5 text-gray-800" />
        </button>
      </div>
    </div>
    <!-- Fullscreen Map Modal -->
    <div v-if="showMapModal && showMap" class="fixed inset-0 z-50 bg-white flex flex-col">
      <div class="flex justify-between items-center p-4 border-b">
        <span class="font-bold">اختر الموقع على الخريطة</span>
        <button @click="closeMap" class="text-gray-500 text-2xl">×</button>
      </div>
      <div class="flex-1">
        <GoogleMap
          :api-key="API_KEY"
          style="width: 100%; height: 100%"
          :center="{ lat: +mapLocation.latitude, lng: +mapLocation.longitude }"
          :zoom="15"
          @click="setMarker"
        >
          <Marker
            :options="{
              position: {
                lat: +mapLocation.latitude,
                lng: +mapLocation.longitude,
              },
              draggable: true,
            }"
            @dragend="setMarker"
          />
        </GoogleMap>
      </div>
      <div class="p-4 flex justify-end border-t">
        <button class="btn btn-primary" @click="confirm">تأكيد</button>
      </div>
    </div>
  </div>
</template>