<!-- components/UrlInput.vue -->
<script lang="ts" setup>
defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  id: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  customClasses: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  domainPrefix: {
    type: String,
    default: '/ www.mahjoz',
  },
  hint: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])

function handleInput(event: Event) {
  emit('update:modelValue', (event.target as HTMLInputElement).value)
}
</script>

<template>
  <div class="inline-flex flex-col justify-start items-end w-full">
    <!-- Label -->
    <div
      v-if="label"
      class="mb-2 w-full flex flex-col justify-center text-start text-[#261E27] text-base font-tajawal font-normal"
    >
      <span v-if="hint" class="relative group text-[#FABF35] me-1 cursor-pointer"> <InfoIcon class="w-4 h-4 inline-block align-middle" /> <span class="absolute start-1/2 bottom-full -ms-1  px-2 py-1 bg-black text-white text-xs rounded shadow-lg whitespace-nowrap opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity z-10"> {{ hint }}</span></span>      {{ label }}
      <span v-if="required" class="text-base font-medium text-rose-600">*</span>
    </div>

    <!-- Input Container -->
    <div class="flex flex-col gap-2 w-full">
      <div
        class="flex items-center h-12 rounded border transition-colors"
        :class="[
          errorMessage ? 'border-red-300' : 'border-neutral-300 hover:border-neutral-400',
          customClasses,
        ]"
      >
        <!-- Input Field -->
        <input
          :id="id"
          type="text"
          :value="modelValue"
          class="inline-flex flex-1 px-4 py-3 w-full rounded border outline-none outline text-start border-base-gray"
          :placeholder="placeholder"
          v-bind="$attrs"
          @input="handleInput"
          @focus="$emit('focus')"
          @blur="$emit('blur')"
        >

        <!-- Domain Suffix -->
        <div class="flex justify-center items-center w-36 h-full bg-indigo-50 rounded-r">
          <span class="text-base font-medium text-neutral-500">.mahjoz.net</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
input:focus {
  outline: none;
}

input:invalid {
  box-shadow: none;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px white inset;
}
</style>
