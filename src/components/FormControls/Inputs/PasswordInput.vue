<script lang="ts" setup>
import { ref } from 'vue'
import { EyeIcon, EyeSlashIcon } from '@heroicons/vue/24/outline'

defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  id: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  customClasses: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'password',
  },
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur'])

const showPassword = ref(false)

function handleInput(event: Event) {
  emit('update:modelValue', (event.target as HTMLInputElement).value)
}

function handleFocus() {
  emit('focus')
}

function handleBlur() {
  emit('blur')
}
</script>

<template>
  <div class="inline-flex flex-col justify-start items-end w-full">
    <!-- Dynamic Label -->
    <div
      v-if="label"
      class="mb-2 w-full flex flex-col justify-center text-start text-[#261E27] text-base font-tajawal "
    >
      {{ label }}
    </div>

    <!-- Input Container -->
    <div
      class="flex relative gap-2.5 justify-end items-center w-full"
    >
      <!-- Input Field -->
      <input
        :id="id"
        :type="showPassword ? 'text' : type"
        :placeholder="placeholder"
        :value="modelValue"
        class="w-full px-4 py-3 rounded border-1 max-h-[48px] border-gray-300 inline-flex flex-1 text-start outline-none"
        :class="customClasses"
        v-bind="$attrs"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      >

      <!-- Toggle Password Visibility -->
      <span v-if="type === 'password'" class="flex absolute inset-y-0 items-center cursor-pointer end-3">
        <EyeIcon v-if="showPassword" class="w-5 h-5" @click="showPassword = !showPassword" />
        <EyeSlashIcon v-else class="w-5 h-5" @click="showPassword = !showPassword" />
      </span>
    </div>
  </div>
</template>
