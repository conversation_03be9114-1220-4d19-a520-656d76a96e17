<script lang="ts" setup>
defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  id: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  customClasses: {
    type: String,
    default: '',
  },

})
const emit = defineEmits(['update:modelValue'])
function handleInput(event) {
  emit('update:modelValue', event.target.value)
}
</script>

<template>
  <input
    :id="id"
    type="radio"
    :checked="$attrs.value === modelValue"
    class="h-4 w-4 border-gray-300 text-primary-600 focus:ring-primary-600"
    :class="customClasses"
    @change="handleInput"
  >
</template>
