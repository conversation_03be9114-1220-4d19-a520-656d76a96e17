<script setup lang="ts">
import { ClipboardIcon } from '@heroicons/vue/20/solid'
const props = defineProps({
  link: {
    type: String,
    default: '',
  },
  withText: {
    type: Boolean,
    default: true,
  },
})
const facebook = defineAsyncComponent(
  () => import('@/components/SoicalMediaIcons/facebook.vue'),
)
const x = defineAsyncComponent(
  () => import('@/components/SoicalMediaIcons/x.vue'),
)
const linkedin = defineAsyncComponent(
  () => import('@/components/SoicalMediaIcons/LinkedIn.vue'),
)
const whatsapp = defineAsyncComponent(
  () => import('@/components/SoicalMediaIcons/whatsapp.vue'),
)

const networks = reactive({
  //   email: "mailto:?subject=@t&body=@u%0D%0A@d" ,
  //   messenger: "fb-messenger://share/?link=@u",
  //   telegram: "https://t.me/share/url?url=@u&text=@t%0D%0A@d",
  facebook: {
    link: 'https://www.facebook.com/sharer/sharer.php?u=@u',
    icon: facebook,
  },
  linkedin: {
    link: 'https://www.linkedin.com/sharing/share-offsite/?url=@u',
    icon: linkedin,
  },
  x: {
    link: 'https://x.com/intent/tweet?text=&url=@u',
    icon: x,
  },
  whatsapp: {
    link: 'https://api.whatsapp.com/send?text=%0D%0A@u',
    icon: whatsapp,
  },
})
const linkCopyActivate = ref(false)
const replacePlaceholders = function (network: { key: string }) {
  return network.link.replace('@u', props.link)
}
const copyUrl = () => {
  linkCopyActivate.value = true
  navigator.clipboard.writeText(props.link)
  setTimeout(() => {
    linkCopyActivate.value = false
  }, 1000)
}
</script>

<template>
  <div class="flex gap-5 items-center w-full flex-wrap">
    <a
      v-for="(network, key) in networks"
      :key="key"
      class="flex items-center justify-center bg-white-800 text-white bg-[#1da1f2] rounded-md me-2 overflow-hidden rtl:justify-end"
      :href="replacePlaceholders(network)"
      target="_blank"
      :class="`network-${key}`"
      @click.stop
    >
      <component :is="network.icon" class="w-10 h-10 p-2 bg-[#0003] flex-1" />
      <span v-if="withText" class="p-2">
        {{ $t(`shareTo.${key}`) }}
      </span>
    </a>
    <!-- copy Link -->
    <a
      class="flex items-center justify-center bg-white-800 text-white bg-red-100 rounded-md me-2  rtl:justify-end relative"
      href="#"
      target="_blank"
      @click.stop.prevent="copyUrl"
    >
      <div
        class="absolute bottom-full flex flex-col items-center w-max"
        :class="{ hidden: !linkCopyActivate }"
      >
        <span
          class="relative z-50 p-2 text-xs leading-none bg-primary-100 text-primary font-medium space-no-wrap shadow-lg"
        >
          {{ $t("copied_link") }}
        </span>
        <div class="w-3 h-3 -mt-2 rotate-45 bg-primary-100" />
      </div>
      <ClipboardIcon class="w-10 h-10 p-2 flex-1 bg-primary-300 rounded-s-md" />
      <span v-if="withText" class="p-2 bg-primary-600 rounded-e-md">
        {{ $t(`CopyLink`) }}
      </span>
    </a>
  </div>
</template>

<style scoped>
.network-facebook {
  background-color: #3b5998;
}
.network-linkedin {
  background-color: #0077b5;
}
.network-x {
  background-color: #000000;
}
.network-whatsapp {
  background-color: #25d366;
}
</style>
