<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useServerErrors } from '@/stores/errors'
import { accessDeepObject } from '@/utils'
const props = defineProps({
  name: {
    type: [String, Array] as PropType<string | [string, string]>,
    required: false,
  },
  validation: {
    type: Object,
    required: false,
  },
  errorName: {
    type: String,
    default: null,
    required: false,
  },
})
const { getServerErrors } = storeToRefs(useServerErrors())
const { clearServerErrors } = useServerErrors()
const inputBlured = () => {
  if (!props.validation || !props.name)
    return
  if (Array.isArray(props.name)) {
    props.name.forEach((name) => {
      accessDeepObject(props?.validation, name)?.$touch()
    })
  }
  else {
    accessDeepObject(props?.validation, props.name)?.$touch()
  }
}
const hasErrors = computed(() => {
  if (!props.name && !props.errorName)
    return false
  if (Array.isArray(props.name)) {
    return props.name.some(
      name =>
        accessDeepObject(props.validation, name)?.$errors.length
        || getServerErrors.value?.[props.errorName ?? name]?.[0],
    )
  }
  if (hasFrontEndValidation.value) {
    return (
      accessDeepObject(props.validation, props.name)?.$errors.length
      || getServerErrors.value?.[props.errorName ?? props.name]?.[0]
    )
  }
  else {
    return getServerErrors.value?.[props.errorName ?? props.name]?.[0]
  }
})
const getFirstError = computed(() => {
  if (!props.name && !props.errorName)
    return ''
  if (Array.isArray(props.name)) {
    return props.name.map(
      name =>
        accessDeepObject(props.validation, name)?.$errors[0]?.$message
        || getServerErrors.value?.[props.errorName ?? name]?.[0]
        || '',
    )
  }
  if (hasFrontEndValidation.value) {
    return (
      accessDeepObject(props.validation, props.name)?.$errors[0]?.$message
      || getServerErrors.value?.[props.errorName ?? props.name]?.[0]
    )
  }
  else {
    return getServerErrors.value?.[props.errorName ?? props.name]?.[0]
  }
})

const hasFrontEndValidation = computed(() => {
  return props.validation?.$errors?.length && props.name
})

onUnmounted(() => {
  clearServerErrors()
})
</script>

<template>
  <div class="w-full">
    <slot
      :attrs="{
        onblur: inputBlured,
        class: { 'error-input': hasErrors },
      }"
      :has-errors="hasErrors"
    />
    <span v-if="hasErrors" class="error-message">
      {{
        Array.isArray(name)
          ? getFirstError[1] && getFirstError[1] != getFirstError[0]
            ? `${getFirstError[0] ? `${getFirstError[0]} او` : ""}  ${
              getFirstError[1]
            }`
            : getFirstError[0]
          : getFirstError
      }}
    </span>
  </div>
</template>
