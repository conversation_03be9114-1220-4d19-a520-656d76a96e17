<script lang="ts" setup>
import { PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import type { ComputedRef } from 'vue'
import type { header } from '@/types'
const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const { getCustomerAttachment, deleteAttachment } = useCustomerStore()
const route = useRoute()
const attachments = ref<{ [key: string]: string }[]>([])
const processing = ref(false)
const showConfModal = ref(false)
const selectedUuid = ref('')
const showModal = ref(false)
const getAttachments = () => {
  processing.value = true
  getCustomerAttachment(route.params.id as string)
    .then((res) => {
      attachments.value = res?.data
    })
    .finally(() => {
      processing.value = false
    })
}
onMounted(async () => {
  getAttachments()
})

const showConfirmationModal = (uuid: string) => {
  selectedUuid.value = uuid
  showConfModal.value = true
}
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('title'),
    },
    {
      title: t('booking.created_at'),
    },
    {
      title: '#',
    },
  ]
})
const attachmentRemoved = () => {
  showConfModal.value = false
  getAttachments()
}
</script>

<template>
  <div class="pt-8 mt-2">
    <customer-attachment-modal
      :show-modal="showModal"
      @close="showModal = false"
      @created="getAttachments"
    />
    <confirmation-modal
      v-if="showConfModal"
      :dir="getLocale(locale)?.direction"
      :is-open="showConfModal"
      :api-call="deleteAttachment"
      :record-id="selectedUuid"
      @removed="attachmentRemoved"
      @closed="showConfModal = false"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.msg") }}
      </p>
    </confirmation-modal>
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("dashboard.booking.attachment") }}
      </h1>
      <BaseButton
        v-if="attachments.length"
        class="inline-flex w-auto hover:bg-green-700"
        custome-bg="bg-green-600"
        @click="showModal = true"
      >
        {{ $t("form.upload") }}
        <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </div>
    <div class="flex flex-col mt-8">
      <overlay-loader v-if="processing" :full-screen="false" />

      <generic-table
        v-if="attachments.length"
        :headers="headers"
        :data="attachments"
      >
        <template #row="{ item }">
          <grid-td>{{ item.title }}</grid-td>
          <grid-td>{{ item.created_at }}</grid-td>
          <grid-td class="flex gap-2 items-center">
            <BaseButton
              class="inline-flex w-auto hover:bg-red-700"
              custome-bg="bg-red-600"
              @click="showConfirmationModal(item.id)"
            >
              <TrashIcon class="w-4 h-4" aria-hidden="true" />
            </BaseButton>
            <a
              :href="item.path"
              class="flex justify-center items-center px-2 py-2 font-medium text-white bg-green-600 rounded-md shadow-sm transition w-fit group sm:px-4 disabled:bg-primary-300 disabled:cursor-not-allowed hover:bg-green-700"
              download
              target="_blank"
            >
              <download-icon class="w-4 h-4" aria-hidden="true" />
            </a>
          </grid-td>
        </template>
      </generic-table>
      <empty-state
        v-else
        text="attachment"
        btn-text="upload"
        @show-hours="showModal = true"
      />
    </div>
  </div>
</template>
