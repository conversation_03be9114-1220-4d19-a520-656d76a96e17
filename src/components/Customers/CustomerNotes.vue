<script lang="ts" setup>
import { PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'
import type { ComputedRef } from 'vue'
import type { Notes, header } from '@/types'
const { getCustomerNotes, deleteNotes } = useCustomerStore()
const route = useRoute()
const notes = ref<Notes[]>([])
const processing = ref(false)
const { t } = useI18n()
const showModal = ref(false)
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('table.date'),
    },
    {
      title: t('table.createdBy'),
    },
    {
      title: t('table.note'),
      className: 'w-1/2',
    },
    {
      title: '#',
    },
  ]
})
const getNotes = () => {
  processing.value = true
  getCustomerNotes(route.params.id as string)
    .then((res) => {
      notes.value = res?.data
    })
    .finally(() => {
      processing.value = false
    })
}
onMounted(() => {
  getNotes()
})
const deleteNote = (uuid: string) => {
  processing.value = true
  deleteNotes(uuid, route.params.id as string)
    .then(() => {
      getNotes()
    })
    .finally(() => {
      processing.value = false
    })
}
</script>

<template>
  <div class="pt-8 mt-2">
    <customer-notes-modal
      :show-modal="showModal"
      @close="showModal = false"
      @created="getNotes"
    />
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("booking.notes") }}
      </h1>
      <BaseButton
        v-if="notes.length"
        class="inline-flex w-auto hover:bg-green-700"
        custome-bg="bg-green-600"
        @click="showModal = true"
      >
        {{ $t("form.create") }}
        <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </div>
    <div class="mt-8">
      <div class="flex flex-col mt-8">
        <overlay-loader v-if="processing" :full-screen="false" />

        <generic-table v-if="notes.length" :headers="headers" :data="notes">
          <template #row="{ item }">
            <grid-td>{{ item.created_at }}</grid-td>
            <grid-td>{{ item.creator?.name }}</grid-td>
            <grid-td class="flex-1">
              {{ item.content }}
            </grid-td>
            <grid-td>
              <BaseButton
                class="inline-flex w-auto hover:bg-red-700"
                custome-bg="bg-red-600"
                @click="deleteNote(item.uuid)"
              >
                {{ $t("form.delete") }}
                <TrashIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
              </BaseButton>
            </grid-td>
          </template>
        </generic-table>
        <empty-state v-else text="note" @show-hours="showModal = true" />
      </div>
    </div>
  </div>
</template>
