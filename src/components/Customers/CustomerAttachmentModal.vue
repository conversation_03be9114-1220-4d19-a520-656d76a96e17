<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { minLength, required } from '@/utils/i18n-validators'
import { useCustomerStore } from '@/stores/customer'
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['close', 'created'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { createAttachment } = useCustomerStore()
const formData = reactive<{ [key: string]: string | File }>({
  title: '',
  file: '' as File | string,
})
const rules = {
  title: {
    required,
    minLength: minLength(3),
  },
  file: {
    required,
  },
}
const processing = ref(false)
const route = useRoute()
const $v = useVuelidate(rules, formData)
const submitForm = () => {
  $v.value.$touch()
  if ($v.value.$invalid)
    return
  processing.value = true
  createAttachment(route.params.id as string, formData)
    .then(() => {
      resetForm()
      emit('created')
    })
    .finally(() => {
      processing.value = false
    })
}
const resetForm = () => {
  for (const prop in formData) formData[prop] = ''
  $v.value.$reset()
}
const closeModal = () => {
  resetForm()
  emit('close')
}
</script>

<template>
  <modal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    title="attachment"
    @close="closeModal"
  >
    <form class="text-start" @submit.prevent="submitForm">
      <overlay-loader v-if="processing" :full-screen="false" />

      <div class="flex flex-col gap-4">
        <div>
          <form-group :validation="$v" name="title">
            <template #default="{ attrs }">
              <TextInput
                v-bind="attrs"
                id="title"
                v-model="formData.title"
                :label="$t('title')"
                :placeholder="$t('formPlaceHolder.attachTitle')"
                custom-classes="mt-1"
              />
            </template>
          </form-group>
        </div>

        <div class="md:col-span-2 col-span-1 overflow-x-visible">
          <LabelInput for="service-description">
            {{ $t("form.uploadFile") }}
          </LabelInput>
          <FileInput v-model="formData.file" :link="null" name="file" />
          <span class="text-sm"> (jpg, png, pdf, gif, jpeg, 2MB max)</span>
        </div>
      </div>
      <div class="mt-8">
        <div class="flex items-center justify-end">
          <BaseButton
            type="submit"
            class="py-3 hover:bg-green-700"
            custome-bg="bg-green-600"
            show-icon
            :processing="processing"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </modal>
</template>
