<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { minLength, required } from '@/utils/i18n-validators'
import { useCustomerStore } from '@/stores/customer'

const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['close', 'created'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { createNotes } = useCustomerStore()
const formData = reactive<{ [key: string]: string }>({
  content: '',
})
const rules = {
  content: {
    required,
    minLength: minLength(3),
  },
}
const processing = ref(false)
const route = useRoute()
const $v = useVuelidate(rules, formData)
const resetForm = () => {
  for (const prop in formData) formData[prop] = ''
  $v.value.$reset()
}
const submitForm = () => {
  $v.value.$touch()
  if ($v.value.$invalid)
    return
  processing.value = true
  createNotes(route.params.id, formData)
    .then(() => {
      resetForm()
      emit('created')
    })
    .finally(() => {
      processing.value = false
    })
}
const closeModal = () => {
  resetForm()
  emit('close')
}
</script>

<template>
  <modal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    title="notes"
    @close="closeModal"
  >
    <form class="text-start relative" @submit.prevent="submitForm">
      <overlay-loader v-if="processing" :full-screen="false" />
      <div class="grid grid-cols-2 gap-4">
        <div class="col-span-2">
          <form-group :validation="$v" name="content">
            <template #default="{ attrs }">
              <TextareaInput
                v-bind="attrs"
                id="grid-last-name"
                v-model="formData.content"
                :placeholder="`${$t('formPlaceHolder.note')}`"
              />
            </template>
          </form-group>
        </div>
      </div>
      <div class="mt-8">
        <div class="flex items-center justify-end">
          <BaseButton
            type="submit"
            class="py-3 hover:bg-green-700"
            custome-bg="bg-green-600"
            show-icon
            :processing="processing"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </modal>
</template>
