<script setup lang="ts">
import { useCustomerStore } from '@/stores/customer'

const store = useCustomerStore()

const processing = ref(true)

const customerName = ref('')

const loadCustomers = async (params: Object = {}) => {
  processing.value = true
  await store.fetchCustomers({ ...params, name: customerName.value })
  processing.value = false
}

watch(customerName, (val) => {
  if (!val) {
    loadCustomers({
      page: 1,
    })
  }
})

onMounted(() => {
  loadCustomers({
    page: 1,
  })
})
</script>

<template>
  <div class="w-full lg:basis-1/4">
    <div class="flex justify-center">
      <div class="relative flex w-full mb-4 input-group">
        <input
          v-model="customerName"
          type="search"
          class="
              form-control
              relative
              flex-auto
              min-w-0
              block
              w-full
              px-3
              py-1.5
              text-base
              font-normal
              text-gray-700
              bg-white bg-clip-padding
              border border-solid border-gray-300
              transition
              ease-in-out
              m-0
              focus:text-gray-700
              focus:bg-white
              focus:border-primary-600
              focus:outline-none
            "
          :placeholder="$t('formPlaceHolder.search')"
          aria-label="Search"
          aria-describedby="button-addon2"
        >
        <button
          id="button-addon2"
          class="
            btn
            inline-block
            px-6
            py-2.5
            bg-primary-600
            disabled:bg-primary-300 disabled:cursor-not-allowed
            text-white
            font-medium
            text-xs
            leading-tight
            uppercase
            rounded
            shadow-md
            hover:bg-primary-700 hover:shadow-lg
            focus:bg-primary-700 focus:shadow-lg focus:outline-none focus:ring-0
            active:bg-primary-800 active:shadow-lg
            transition
            duration-150
            ease-in-out
            items-center
          "
          type="button"
          @click="loadCustomers"
        >
          <svg
            aria-hidden="true"
            focusable="false"
            data-prefix="fas"
            data-icon="search"
            class="w-4"
            role="img"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path
              fill="currentColor"
              d="M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"
            />
          </svg>
        </button>
      </div>
    </div>
    <div class="grid grid-cols-1 gap-4">
      <div v-if="processing">
        <the-loader />
      </div>

      <div
        v-for="person in store.getCustomerList"
        v-else
        :key="person.uuid"
        class="
          relative
          flex
          items-center
          py-5
          space-x-3
          bg-white
          border border-gray-300
          rounded-lg

          ps-6
          pe-6
          focus-within:ring-2
          focus-within:ring-primary-500
          focus-within:ring-offset-2
          hover:border-gray-400
        "
      >
        <div class="flex-shrink-0">
          <img
            class="w-10 h-10 rounded-full"
            :src="person.imageUrl"
            :alt="person.fullname"
          >
        </div>
        <div class="flex-1 min-w-0">
          <a href="#" class="focus:outline-none">
            <span class="absolute inset-0" aria-hidden="true" />
            <p class="text-sm font-medium text-gray-900">
              {{ person.fullname }}
            </p>
            <p class="text-sm text-gray-500 truncate">{{ person.email }}</p>
            <p class="text-sm text-gray-300 truncate">{{ person }}</p>
          </a>
        </div>
      </div>
    </div>
    <Pagination
      :links="store.getCustomerLinks"
      @next="
        loadCustomers({
          page: store.getCurrentPage + 1,
        })
      "
      @prev="
        loadCustomers({
          page: store.getCurrentPage - 1,
        })
      "
    />
  </div>
</template>
