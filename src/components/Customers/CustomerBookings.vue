<script setup lang="ts">
import type { PropType } from 'vue'
import type { Booking, Customer } from '@/types'

const props = defineProps({
  bookings: {
    type: Array as PropType<Booking[]>,
    default: () => [],
  },
  bookingsLoading: {
    type: Boolean,
    default: false,
  },
  customer: Object as PropType<Customer>,
})
const emits = defineEmits(['openOrderModal'])

const redirectToBooking = (item: Booking) => {
  emits('openOrderModal', item.order_id)
}
const i18n = useI18n()
const headers = computed(() => {
  return [
    {
      title: i18n.t('booking.booking_number'),
    },
    {
      title: i18n.t('dashboard.booking.staff'),
    },
    {
      title: i18n.t('dashboard.booking.service'),
    },
    {
      title: i18n.t('dashboard.booking.date'),
    },
    {
      title: i18n.t('dashboard.booking.start'),
    },
    {
      title: i18n.t('dashboard.booking.end'),
    },
    {
      title: i18n.t('price'),
    },
    {
      title: i18n.t('booking.status'),
    },
    {
      title: '#',
    },
  ]
})
</script>

<template>
  <div>
    <div class="mt-2">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-semibold text-gray-900">
            {{ $t("notifications.booking") }}
          </h1>
        </div>
      </div>
      <div class="flex flex-col mt-8">
        <div class="">
          <bookings-grid
            :booking-list="bookings" book-customer="bookCustomer"
            :is-loading="bookingsLoading"
            :on-row-click="redirectToBooking"
          >
            <template #actions="{ item }">
              <BaseButton
                class="inline-flex" custome-bg="bg-gray-700" show-icon
                @click="redirectToBooking(item)"
              >
                {{ $t('form.showBooking') }}
              </BaseButton>
            </template>
          </bookings-grid>
        </div>
      </div>
    </div>
  </div>
</template>
