<script setup lang="ts">
import type { PropType } from '@vue/runtime-core'
import { TrashIcon } from '@heroicons/vue/24/outline'
import type { Customer } from '@/types'

const props = defineProps({
  customer: {
    type: Object as PropType<Customer>,
    default: null,
  },
})

const emit = defineEmits(['created', 'updated', 'deleted'])

const formData = reactive<Omit<Customer, 'uuid'>>({
  first_name: '',
  last_name: '',
  email: '',
  phone: '',
  phone_country: '',
  address: '',
  imageUrl: '',
  note: '',
})

const state = reactive({
  processing: false,
})

const { customer } = toRefs(props)

watch(customer, (value: Customer) => {
  formData.first_name = value?.first_name || ''
  formData.last_name = value?.last_name || ''
  formData.email = value?.email || ''
  formData.phone = value?.phone || ''
  formData.phone_country = value?.phone_country || ''
  formData.address = value?.address || ''
  formData.note = value?.note || ''
})
const { createCustomer, updateCustomer, removeCustomer } = useCustomerStore()
const formDsiabled = useCustomerStore()
const setPhoneNumber = (
  phoneNumber: string,
  phoneObject: { countryCode: string },
) => {
  formData.phone = phoneNumber
  formData.phone_country = phoneObject.countryCode
}
const errHandle = reactive({
  firstName: '',
  lastName: '',
})
const create = async () => {
  errHandle.firstName = ''
  errHandle.lastName = ''
  if (state.processing)
    return
  state.processing = true
  if (customer.value?.uuid) {
    return updateCustomer(customer.value.uuid, formData)
      .then((res) => {
        emit('updated', res)
      }).catch((e) => {
        if (e.errors.first_name)
          errHandle.firstName = e.errors.first_name[0]

        if (e.errors.last_name)
          errHandle.lastName = e.errors.last_name[0]
      })
      .finally(() => {
        state.processing = false
      })
  }
  createCustomer(formData)
    .then((res) => {
      resetInput()
      emit('created', res)
    }).catch((e) => {
      if (e.errors.first_name)
        errHandle.firstName = e.errors.first_name[0]

      if (e.errors.last_name)
        errHandle.lastName = e.errors.last_name[0]
    })
    .finally(() => {
      state.processing = false
    })
}

const deleteCustomer = () => {
  if (!customer.value?.uuid || state.processing)
    return
  state.processing = true
  removeCustomer(customer.value.uuid)
    .then(() => {
      emit('deleted', customer.value)
      resetInput()
      formDsiabled.formDsiabled = true
    })
    .finally(() => {
      state.processing = false
    })
}
function resetInput() {
  formData.first_name = ''
  formData.last_name = ''
  formData.email = ''
  formData.phone = ''
  formData.phone_country = ''
  formData.address = ''
  formData.note = ''
}
function disabledInput(status: boolean) {
  if (status) {
    document.querySelectorAll('form input').forEach((e: HTMLInputElement) => {
      e.disabled = true
    })
    const ele = document.querySelector('form textarea') as HTMLTextAreaElement
    ele.disabled = true
  }
  else {
    document.querySelectorAll('form input').forEach((e: HTMLInputElement) => {
      e.disabled = false
    })
    const ele = document.querySelector('form textarea') as HTMLTextAreaElement
    ele.disabled = false
  }
}
onMounted(() => {
  formDsiabled.formDsiabled = true
  if (formDsiabled.formDsiabled)
    disabledInput(true)
})
const formdis = computed(() => {
  return formDsiabled.formDsiabled
})
watch(formdis, (newval, oldVal) => {
  if (!newval)
    disabledInput(false)
  else disabledInput(true)
})
</script>

<template>
  <form class="flex flex-col w-full gap-4 mb-4" @submit.prevent="create">
    <div class="grid gap-5 lg:grid-cols-2">
      <div>
        <TextInput
          id="grid-first-name"
          v-model="formData.first_name" :label="$t('form.firstName')" class="
            block
            w-full
            py-3
            leading-tight
            text-gray-700
            border
            rounded
            appearance-none
            ps-4
            pe-4
            focus:outline-none focus:bg-white
          "
          :placeholder="$t('formPlaceHolder.firstName')"
        />
        <ErrMsg :msg="errHandle.firstName" />
      </div>
      <div>
        <TextInput
          id="grid-last-name"
          v-model="formData.last_name" :label="$t('form.lastName')" class="
            block
            w-full
            py-3
            leading-tight
            text-gray-700
            border
            rounded
            appearance-none
            ps-4
            pe-4
            focus:outline-none focus:bg-white focus:border-gray-500
          "
          :placeholder="$t('formPlaceHolder.lastName')"
        />
        <ErrMsg :msg="errHandle.lastName" />
      </div>
      <PhoneInput :model-value="formData.phone" class="w-full mb-6" label="form.phone" @update:model-value="setPhoneNumber" />
      <div>
        <TextInput
          id="grid-first-name"
          v-model="formData.email"
          :label="$t('email.enter')"
          autocomplete="email"
          :placeholder="$t('formPlaceHolder.email')"
        />
      </div>
    </div>
    <div>
      <TextInput
        id="grid-last-name"
        v-model="formData.address"
        :label="$t('form.address')"
        :placeholder="$t('form.addressHere')"
      />
    </div>

    <div class="flex flex-wrap">
      <TextareaInput
        id="message"
        v-model="formData.note"
        :label="$t('form.note')"
        :rows="7"
        custom-classes="block p-2.5 w-full text-sm text-gray-900 rounded-lg border border-gray-300"
        :placeholder="$t('form.message')"
      />
    </div>
    <div class="flex justify-between w-full ms-auto sm:w-1/3">
      <slot name="cancel-button" />
      <BaseButton
        v-if="customer" type="button" class="inline-flex bg-red-600 hover:bg-red-700 disabled:bg-red-300 me-2"
        :processing="state.processing" @click="deleteCustomer"
      >
        {{ $t("form.delete") }}
        <TrashIcon class="ms-2 -me-0.5 h-4 w-4" aria-hidden="true" />
      </BaseButton>
      <BaseButton v-if="customer" class="inline-flex edit bg-primary-600" show-icon :processing="state.processing">
        {{ $t("form.update") }}
      </BaseButton>
    </div>
  </form>
</template>
