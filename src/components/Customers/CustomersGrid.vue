<script setup lang="ts">
import type { PropType } from 'vue'
import type { Customer } from '@/types'

const props = defineProps({
  customerslist: {
    type: Array as PropType<Customer[]>,
    default: () => [],
  },
})

const slot = useSlots()

const hasActions = computed(() => !!slot?.actions)
</script>

<template>
  <grid>
    <template #grid-header>
      <tr>
        <grid-th>{{ $t("form.name") }}</grid-th>
        <grid-th>{{ $t("form.phone") }}</grid-th>
        <grid-th>{{ $t("email.title") }}</grid-th>
        <grid-th v-if="hasActions">
          #
        </grid-th>
      </tr>
    </template>
    <tr v-for="customer in customerslist" :key="customer.uuid" class="cursor-pointer" @click="$router.push({ name: 'customer', params: { id: customer.uuid } })">
      <td class="flex gap-1 items-center py-2 pr-2 pl-2 text-sm whitespace-nowrap sm:pl-6">
        <div>
          <svg v-if="customer.imageUrl === null" xmlns="http://www.w3.org/2000/svg" class="w-10" viewBox="0 0 24 24"><path fill="#e1e1e1" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z" /></svg>
          <img v-else id="image" class="w-10 h-10 rounded-full" :src="customer.imageUrl" :link="null">
        </div>
        <span>
          {{ customer?.first_name }} {{ customer?.last_name }}
        </span>
      </td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="customer?.phone">
            {{ customer?.phone }}
          </span>
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          <span v-if="customer?.email">
            {{ customer?.email }}
          </span>
          <span v-else class="text-zinc-400">
            -
          </span>
        </div>
      </grid-td>
      <grid-td v-if="hasActions">
        <slot name="actions" :customer="customer" />
      </grid-td>
    </tr>
    <tr v-if="!customerslist.length">
      <grid-td colspan="6">
        <div class="text-gray-900">
          {{ $t("pagination.empty") }}
        </div>
      </grid-td>
    </tr>
  </grid>
</template>
