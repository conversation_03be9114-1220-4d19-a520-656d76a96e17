<script setup lang="ts">
import type { PropType } from 'vue'
import MultiSelectInput from '@/components/FormControls/Inputs/MultiSelectInput.vue'
import type { Tag } from '@/types'
interface Payload {
  customer: string
  phone: string
  tags: string[]
  team: string
  block: number
}
const props = defineProps({
  tags: {
    type: Array as PropType<Tag[]>,
    default: () => [],
  },
})

const emit = defineEmits(['changed'])
const customerName = ref('')
const team = ref('')
const block = ref()
const phoneNumberFilter = ref('')
const tagsSelectedList = ref([])
const customer = debouncedRef(customerName, 500)
const phone = debouncedRef(phoneNumberFilter, 500)
const tagsSelect = debouncedRef(tagsSelectedList, 500)
const teamDebounced = debouncedRef(team, 500)
const blockDebounced = debouncedRef(block, 500)

watch([customer, phone, tagsSelect, teamDebounced, blockDebounced], (val) => {
  const [customer, phone, tags, team, block] = val
  const payload: Payload = {
    customer,
    phone,
    tags,
    team,
    block,
  }
  emit('changed', payload)
})
const setPhoneNumber = (
  phoneNumber: string,
  phoneObject: { countryCode: string; nationalNumber: string },
) => {
  phoneNumberFilter.value = phoneObject?.nationalNumber ?? ''
  //   formData.phone_country = phoneObject.countryCode
}
</script>

<template>
  <div class="mt-4">
    <div class="grid grid-cols-1 mt-6 gap-y-6 gap-x-4 md:grid-cols-4">
      <SearchInput
        id="customer-name"
        v-model="customerName"
        :label="$t('form.name')"
        :placeholder="$t('formPlaceHolder.client')"
      />

      <PhoneInput
        class="w-full"
        :model-value="phoneNumberFilter"
        label="form.phone"
        @update:model-value="setPhoneNumber"
      />
      <div class="w-full">
        <LabelInput for="tags">
          {{ $t("form.tags") }}
        </LabelInput>
        <div>
          <MultiSelectInput
            id="tags"
            v-model="tagsSelectedList"
            :options="tags"
            :placeholder="$t('tags.selectOption')"
          />
        </div>
      </div>
      <div v-if="$route.name === 'customers'">
        <label for="staff" class=" w-full flex items-center text-start text-[#261E27] text-base       ">
          {{ $t("customers.Customer_status") }}
        </label>
        <div>
          <SelectInput id="block" v-model="block" custom-classes="mt-2">
            <option value="">
              {{ $t("form.select") }}
            </option>
            <option value="1">
              {{ $t("form.blocked") }}
            </option>
            <option value="0">
              {{ $t("form.unblocked") }}
            </option>
          </SelectInput>
        </div>
      </div>
      <filter-with-team v-if="$route.name === 'staffs'" v-model="team" />
    </div>
  </div>
</template>

<style lang="css"></style>
