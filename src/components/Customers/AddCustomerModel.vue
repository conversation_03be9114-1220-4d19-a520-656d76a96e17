<script setup lang="ts">
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import type { PropType } from 'vue'
import Multiselect from 'vue-multiselect'
import { email, minLength, required } from '@/utils/i18n-validators'
import 'vue-multiselect/dist/vue-multiselect.css'
import type { Customer, Tag } from '@/types'
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  tags: {
    type: Array as PropType<Tag[]>,
    default: () => [],
  },
  customer: {
    type: Object as PropType<Customer>,
    default: () => null,
  },
})
const emit = defineEmits(['created', 'closed', 'updated'])
const { customer } = toRefs(props)
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const processing = ref(false)
const rules = {
  first_name: {
    required,
    minLength: minLength(3),
  },
  email: {
    email,
  },
}
const formData = reactive<Omit<Customer, 'uuid'>>({
  first_name: '',
  last_name: '',
  email: '',
  phone: '',
  address: '',
  phone_country: '',
  note: '',
  tags: [],
})
const v$ = useVuelidate(rules, formData)
const editMode = ref(false)
watch(customer, (val: Customer) => {
  if (!val)
    return
  const { first_name, last_name, email, phone, phone_country, address, note, tags } = val
  formData.first_name = first_name || ''
  formData.last_name = last_name || ''
  formData.email = email || ''
  formData.phone = phone || ''
  formData.phone_country = phone_country || ''
  formData.address = address || ''
  formData.note = note || ''
  formData.tags = tags || []
  editMode.value = true
},
{ immediate: true },
)

const resetForm = () => {
  formData.first_name = ''
  formData.last_name = ''
  formData.email = ''
  formData.phone = ''
  formData.phone_country = ''
  formData.address = ''
  formData.note = ''
  formData.tags = []
  v$.value.$reset()
}
const setPhoneNumber = (phoneNumber: string, phoneObject: { countryCode: string }) => {
  formData.phone = phoneNumber
  formData.phone_country = phoneObject.countryCode
}
const { createCustomer, updateCustomer } = useCustomerStore()

const createRecord = async (payload) => {
  return createCustomer(payload).then((res: any) => {
    emit('created', res.data)
  })
}
const editRecord = async (payload) => {
  return updateCustomer(customer.value.uuid, payload)
    .then((res: any) => {
      editMode.value = false
      emit('updated', res.data)
      closeModal()
    })
}

const saveCustomer = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return false
  processing.value = true
  try {
    const payload = {
      ...formData,
      tags: formData.tags?.map(tag => tag.id).join(','),
    }
    if (editMode.value)
      await editRecord(payload)
    else
      await createRecord(payload)

    resetForm()
  }
  finally {
    processing.value = false
  }
}
const closeModal = () => {
  emit('closed')
}
</script>

<template>
  <div>
    <Modal
      :dir="getLocale(locale)?.direction"
      :open="showModal"
      :title="editMode ? 'editCustomer' : 'customer'"
      @close="closeModal"
    >
      <form
        class="relative py-5 text-start"
        @submit.prevent="saveCustomer"
      >
        <overlay-loader v-if="processing" :full-screen="false" />

        <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
          <div>
            <form-group :validation="v$" name="first_name">
              <template #default="{ attrs }">
                <TextInput
                  id="grid-first-name"
                  v-bind="attrs"
                  v-model="formData.first_name"
                  :label="$t('form.firstName')"
                  required
                  :placeholder="$t('formPlaceHolder.firstName')"
                  class="mt-1"
                />
              </template>
            </form-group>
          </div>
          <div>
            <form-group :validation="v$" name="last_name">
              <template #default="{ attrs }">
                <TextInput
                  id="grid-last-name"
                  v-bind="attrs"
                  v-model="formData.last_name"
                  :label="$t('form.lastName')"
                  :placeholder="$t('formPlaceHolder.lastName')"
                  class="mt-1"
                />
              </template>
            </form-group>
          </div>
          <form-group name="phone">
            <template #default="{ attrs }">
              <PhoneInput
                class="w-full"
                :model-value="formData.phone"
                v-bind="attrs"
                label="form.phone"
                @update:model-value="setPhoneNumber"
              />
            </template>
          </form-group>

          <div>
            <form-group :validation="v$" name="email">
              <template #default="{ attrs }">
                <TextInput
                  id="email"
                  v-model="formData.email"
                  v-bind="attrs"
                  :label="$t('email.enter')"
                  :placeholder="$t('formPlaceHolder.email')"
                  class="mt-1"
                />
              </template>
            </form-group>
          </div>
        </div>
        <div class="py-4">
          <TextInput
            id="address"
            v-model="formData.address"
            :label="$t('form.address')"
            :placeholder="$t('formPlaceHolder.address')"
            class="mt-1"
          />
        </div>
        <div class="py-4">
          <LabelInput for="tags">
            {{ $t("form.tags") }}
          </LabelInput>
          <Multiselect
            v-model="formData.tags"
            :multiple="true"
            id="tags"
            track-by="id"
            :clear-on-select="false"
            :close-on-select="false"
            label="name"
            :options="tags"
            :placeholder="$t('tags.selectOption')"
            :select-label="$t('tags.selectLabel')"
            :selected-label="$t('tags.selectedLabel')"
            :deselect-label="$t('tags.deselectLabel')"
            class="mt-1"
          />
        </div>
        <div class="py-4">
          <LabelInput for="message">
            {{ $t("form.note") }}
          </LabelInput>
          <textarea
            id="message"
            v-model="formData.note"
            :rows="7"
            class="block p-2.5 w-full text-sm text-gray-900 rounded-lg border border-gray-300"
            :placeholder="$t('form.message')"
          />
        </div>
        <div class="mt-8">
          <div class="flex justify-center items-center">
            <BaseButton
              type="submit"
              class="py-3 mx-auto w-1/2 hover:bg-primary-800"
              custome-bg="bg-primary-700"
              show-icon
              :processing="processing"
            >
              {{ editMode ? $t("form.update") : $t("form.create") }}
            </BaseButton>
          </div>
        </div>
      </form>
    </Modal>
  </div>
</template>
