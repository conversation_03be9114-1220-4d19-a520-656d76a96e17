<script setup lang="ts">
import type { PropType } from 'vue'
import {
  Listbox,
  ListboxButton,
} from '@headlessui/vue'
import { formatDateAndTime } from '@/composables/dateFormat'
const props = defineProps({
  list: {
    type: Array as PropType<any[]>,
    default: () => [] as any,
  },
  loadingInvoices: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['openOrderModal'])
const router = useRouter()
const { t } = useI18n()
const headers = computed(() => {
  return [
    {
      title: t('booking.booking_number'),
    },
    {
      title: t('form.sale_date'),
    },
    // {
    //   title: t("form.no_of_items"),
    // },
    {
      title: t('form.location'),
    },
    {
      title: t('form.status'),
    },
    {
      title: t('form.price'),
    },
    {
      title: t('pos.invoice'),
    },
  ]
})
function openInvoicePdf(id) {
  window.open(`/preview-invoices/${id}`, '_blank')
}
const openInvoiceDetail = ({ id }) => {
  emits('openOrderModal', id)
}
</script>

<template>
  <div class="pt-8 mt-2">
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("orders") }}
      </h1>
    </div>
    <div class="flex flex-col mt-8">
      <generic-table
        :headers="headers" :is-loading="loadingInvoices" :data="list" item-key="id"
        :on-row-click="openInvoiceDetail" tr-class="cursor-pointer"
      >
        <template #row="{ item }">
          <grid-td>
            <div class="text-gray-900">
              <span v-if="item?.InvoiceNum">
                {{ item?.InvoiceNum }}
              </span>
              <span v-else class="text-zinc-400"> - </span>
            </div>
          </grid-td>
          <grid-td>
            <div class="text-gray-900">
              <span v-if="item?.created_at">
                {{ formatDateAndTime(item?.created_at) }}
              </span>
              <span v-else class="text-zinc-400"> - </span>
            </div>
          </grid-td>
          <!-- <grid-td>
                  <div class="text-gray-900">
                    <span v-if="item?.items">
                      {{ item?.items }} {{ $t("item", { count: item?.items }) }}
                    </span>
                    <span v-else class="text-zinc-400"> - </span>
                  </div>
                </grid-td> -->
          <grid-td>
            <div class="text-gray-900">
              <span v-if="item?.branch">
                {{ item?.branch?.name }}
              </span>
              <span v-else class="text-zinc-400"> - </span>
            </div>
          </grid-td>
          <grid-td>
            <div class="text-gray-900">
              <sapn
                class="p-1 px-2 text-sm rounded-full" :style="{
                  'background-color': item.status.bg_color
                    ? item.status.bg_color
                    : '#000000',
                  'color': item.status.text_color
                    ? item.status.text_color
                    : '#ffffff',
                }"
              >
                {{ item.status.label }}
              </sapn>
            </div>
          </grid-td>
          <grid-td>
            <div class="text-gray-900">
              <span v-if="item?.total">
                {{ item?.currency }} {{ item?.total }}
              </span>
              <span v-else class="text-zinc-400"> - </span>
            </div>
          </grid-td>
          <grid-td>
            <button
              v-if="item.has_invoice" class="flex justify-between items-center bg-sky-500 rounded-lg border border-gray-200 shadow-md cursor-pointer text-start focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm sm:px-2 sm:py-1"
              @click.stop.prevent="openInvoicePdf(item.id)"
            >
              <span class="flex items-center rtl:left-auto rtl:right-0">
                <icons name="print" color="#FFF" />
              </span>
            </button>
          </grid-td>
        </template>
      </generic-table>
    </div>
  </div>
</template>
