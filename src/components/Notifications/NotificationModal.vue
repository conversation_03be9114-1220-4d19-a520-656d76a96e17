<script setup lang="ts">
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import type { Template } from '@/types'
import Notifications from '@/services/Notifications'
import { required, requiredIf } from '@/utils/i18n-validators'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  templateSelected: {
    type: Object,
    default: null,
    required: false,
  },
  actionSelected: {
    type: Object,
    default: null,
    required: false,
  },
  selectedTab: {
    type: Object,
  },
  activeChannels: {
    type: Array,
    default: () => ['email'],
  },
})
const emit = defineEmits(['created', 'updated', 'update:showModal', 'deleted'])
const { createNotificationTemplate, updateNotificationTemplate, deleteNotificationTemplate } = useNotificationTemplate()
const formData = reactive<Template>({
  title: '',
  channel: 'email',
  content: '',
  action_id: '',
})
const rules = {
  title: {
    required: requiredIf(() => formData.channel == 'email'),
  },
  content: {
    required,
  },
}
const variables = ref({})
const editMode = ref(false)
const editorVars = reactive([])
const processing = ref(false)
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const reRenderEditor = ref(true)

const v$ = useVuelidate(rules, formData)
const { actionSelected, templateSelected } = toRefs<any>(props)
watch(
  () => actionSelected.value,
  (val) => {
    if (val?.action_id) {
      formData.action_id = val.action_id
      variables.value = val.variables
    }
  },
  { immediate: true },
)
watch(
  () => templateSelected.value,
  (val: Template) => {
    if (val?.template_id) {
      const { title, channel, content, template_id } = val
      formData.title = title
      formData.channel = channel
      formData.content = content
      formData.template_id = template_id
      editMode.value = true
    }
  },
  { immediate: true },
)

const addDataToEditor = (key) => {
  // to change the value of the prop
  // if (formData.channel == "email") {
  // to pass vars into editor
  //  editorVars.push(key);
  // } else {
  formData.content += key
  // }
}
const resetForm = () => {
  formData.title = ''
  formData.content = ''
  v$.value.$reset()
  editMode.value = false
}

const createRecord = async () => {
  return createNotificationTemplate(formData).then((res) => {
    resetForm()
    emit('created')
  })
}

const updateRecord = async () => {
  return updateNotificationTemplate(formData.template_id as string, formData).then((res) => {
    emit('update:showModal', false)
  })
}

const save = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return
  processing.value = true
  try {
    if (editMode.value)
      await updateRecord()
    else
      await createRecord()

    resetForm()
    reRenderEditor.value = !reRenderEditor.value
  }
  finally {
    processing.value = false
  }
}
const deleteRecord = async () => {
  processing.value = true
  try {
    await deleteNotificationTemplate(formData.template_id as string, props.selectedTab.value)
    emit('deleted', formData)
    emit('update:showModal', false)
    resetForm()
  }
  finally {
    processing.value = false
  }
}

const channelChanged = () => {
  // resetting content and vars
  formData.content = ''
  editorVars.splice(0, editorVars.length)
}
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="editMode ? 'editNotification' : 'addNotification'"
    @close="$emit('update:showModal', false)"
  >
    <h2 class="mb-4">
      {{ $t(selectedTab.name) }} - {{ actionSelected.name }}
    </h2>
    <form
      class="relative flex flex-col gap-4 mb-4 text-start"
      @submit.prevent="save"
    >
      <OverlayLoader v-if="processing" :full-screen="false" />
      <div class="w-full">
        <label class="mb-2 w-full flex items-center text-start text-[#261E27] text-base">
          {{ $t("create_notification_for") }}
        </label>

        <fieldset class="mt-4">
          <form-group error-name="channel">
            <template #default="{ attrs }">
              <div class="gap-3 space-y-4 sm:flex sm:items-center sm:space-y-0" v-bind="attrs">
                <div v-for="(available, channel) of activeChannels" class="flex items-center" :available="channel">
                  <input
                    :id="channel"
                    v-model="formData.channel"
                    :disabled="!available"
                    :name="channel"
                    type="radio"
                    class="w-4 h-4 border-gray-300 cursor-pointer text-primary-600 focus:ring-transparent hover:ring disabled:cursor-not-allowed"
                    :value="channel"
                    :title="!available ? $t('required_apps') : ''"
                    @change="channelChanged"
                  >

                  <label
                    :for="channel"
                    class="block text-sm font-medium leading-6 text-gray-900 cursor-pointer ms-2"
                  >{{ $t(`form.${channel}`) }}</label>
                </div>
              </div>
            </template>
          </form-group>
        </fieldset>
      </div>
      <div v-if="formData.channel == 'email' " class="w-full">
        <form-group :validation="v$" name="title">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="title-notification"
              v-model="formData.title"
              :label="$t('form.notification_title') "
              class="block w-full py-3 leading-tight text-gray-700 rounded appearance-none ps-4 pe-4 focus:outline-none focus:bg-white"
              :placeholder="`${$t('enter')} ${$t('form.notification_title')}`"
            />
          </template>
        </form-group>
      </div>
      <div class="w-full">
        <form-group :validation="v$" name="content">
          <template #default="{ attrs }">
            <TextareaInput
              v-bind="attrs"
              id="notification-content"
              v-model="formData.content"
              :label="$t('form.notification_content')"
              :placeholder="`${$t('enter')} ${$t('form.notification_content')}`"
              required
              :rows="5"
              name="notification-content"
              custom-classes="block w-full rounded-md border-0 py-1.5 text-gray-900   ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            />
          </template>
        </form-group>
        <!-- <div
          class="w-full overflow-hidden border-2 border-gray-300 rounded-md h-100"

        >
          <Editor
            :reRenderEditor="reRenderEditor"
            :content="formData.content"
            :form="formData"
            :editorVars="editorVars"
          ></Editor>
        </div> -->
      </div>

      <div class="w-full">
        <label
          class="mb-2 w-full flex items-center text-start text-[#261E27] text-base"
          for="notification-content"
        >
          {{ $t("variables") }}
        </label>
        <button
          v-for="(value, key) in variables"
          type="button"
          class="rounded-full bg-white px-4 py-2.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 mx-1 mb-1"
          :index="key"
          @click="addDataToEditor(key)"
        >
          {{ value }}
        </button>
      </div>

      <div class="flex justify-center w-full gap-4">
        <BaseButton
          type="submit"
          class="py-3 hover:bg-primary-800"
          custome-bg="bg-primary-700"
          show-icon
          :processing="processing"
        >
          {{ editMode ? $t("form.update") : $t("form.create") }}
        </BaseButton>
        <BaseButton
          v-if="editMode"
          class="py-3 hover:bg-red-700"
          custome-bg="bg-red-600"
          show-icon
          type="button"
          @click="deleteRecord"
        >
          {{ $t("form.delete") }}
        </BaseButton>
      </div>
    </form>
  </Modal>
</template>
