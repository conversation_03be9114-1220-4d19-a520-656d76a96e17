<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import type { PropType } from 'vue'
import { useTaxes } from '@/stores/taxes'
import type { Taxes } from '@/types/taxes'
const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  role: {
    type: Object as PropType<Taxes | null>,
    default: () => ({}),
  },
})

const emit = defineEmits(['close'])

const { locale } = useI18n()
const { role } = toRefs(props)
const { getLocale } = storeToRefs(useLocalesStore())
const { createTaxes, updateTaxes, deleteTaxes } = useTaxes()
const formData = reactive({
  name: '',
  rate: 0,
  status: true,
})
const rules = {
  name: {
    required,
  },
  rate: {
    required,
  },
}
const processing = ref(false)
const errHandle = reactive<{ [key: string]: string[] }>({
  name: [],
  rate: [],
})
watch(role, (val: Taxes) => {
  formData.name = val?.name || ''
  formData.rate = val?.rate || 0
  formData.status = val?.status || true
})
const $v = useVuelidate(rules, formData)
const submitForm = () => {
  $v.value.$touch()
  if ($v.value.$invalid)
    return

  processing.value = true
  if (!role.value?.uuid) {
    createTaxes(formData).then(() => {
      emit('close')
    })
      .catch((err) => {
        if (err.errors)
          for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        processing.value = false
      })
  }
  else {
    updateTaxes(formData, role.value?.uuid).then(() => {
      emit('close')
    })
      .catch((err) => {
        if (err.errors)
          for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        processing.value = false
      })
  }
}
const deleteTax = () => {
  if (role.value?.uuid) {
    processing.value = true
    deleteTaxes(role.value?.uuid).finally(() => {
      processing.value = false
      emit('close')
    })
  }
}
</script>

<template>
  <modal :dir="getLocale(locale)?.direction" :open="showModal" title="taxes" @close="$emit('close')">
    <!-- <overlay-loader v-if="processing" /> -->
    <form
      :class="[
        getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
      ]" @submit.prevent="submitForm"
    >
      <err-validations v-if="!!errHandle.name.length || !!errHandle.rate.length" :err-handle="errHandle" />
      <div class="grid grid-cols-1 gap-4">
        <div>
          <TextInput
            id="grid-last-name"
            v-model="formData.name"
            required
            :label="$t('form.name')"
            :placeholder="`${$t('formPlaceHolder.name')}`"
          />
          <p v-for="error of $v.name.$errors" :key="error.$uid" class="error-message">
            {{ $t(`${error.$message}`) }}
          </p>
        </div>

        <div>
          <NumberInput
            id="grid-last-name"
            v-model="formData.rate"
            required
            :label="$t('form.rate')"
            class="
             block
             w-full
             py-3
             leading-tight
             text-gray-700
             border
             rounded
             appearance-none
             ps-4
             pe-4
             focus:outline-none focus:bg-white focus:border-gray-500
           "
            step="0.01" min="0.01" max="99.99"
            :placeholder="`${$t('formPlaceHolder.rate')}`"
          />
          <p v-for="error of $v.name.$errors" :key="error.$uid" class="error-message">
            {{ $t(`${error.$message}`) }}
          </p>
        </div>
      </div>

      <div class="mt-8">
        <div class="flex items-center justify-end">
          <div
            v-if="role?.name"
            class="flex gap-2 justify-between"
          >
            <BaseButton
              type="button"
              class="w-1/2 mx-auto py-3  hover:bg-red-700"
              custome-bg="bg-red-600"
              show-icon
              :processing="processing"
              @click="deleteTax"
            >
              {{ $t("form.delete") }}
            </BaseButton>

            <BaseButton
              type="submit"
              class="w-1/2 mx-auto py-3  hover:bg-primary-800"
              custome-bg="bg-primary-700"
              show-icon
              :processing="processing"
            >
              {{ $t("form.update") }}
            </BaseButton>
          </div>

          <BaseButton
            v-else
            type="submit"
            class=" py-3  hover:bg-green-700"
            custome-bg="bg-green-600"
            show-icon
            :processing="processing"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </modal>
</template>
