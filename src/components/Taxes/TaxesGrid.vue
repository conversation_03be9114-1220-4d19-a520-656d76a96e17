<script lang="ts" setup>
import type { ComputedRef, PropType } from 'vue'
import { storeToRefs } from 'pinia'
import type { Taxes } from '../../types/taxes'
import { useTaxes } from '@/stores/taxes'
import type { header } from '@/types'

const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['openModal', 'deleteTax'])

const { taxes } = storeToRefs(useTaxes())
const { t } = useI18n()
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.name'),
    },
  ]
})
const openModal = (tax: Taxes) => {
  emit('openModal', tax)
}
</script>

<template>
  <generic-table
    :is-loading="isLoading"
    :data="taxes"
    :headers="headers"
    tr-class="cursor-pointer"
    :on-row-click="openModal"
  >
    <template #row="{ item }">
      <grid-td>
        {{ item.name }}
      </grid-td>
    </template>
  </generic-table>
</template>
