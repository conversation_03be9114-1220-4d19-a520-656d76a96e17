<script setup lang="ts">
defineProps({
  heightClass: {
    type: String,
    default: 'h-16',
  },
  barCount: {
    type: Number,
    default: 5,
  },
})
</script>

<template>
  <div role="status" class="mt-2 space-y-4 animate-pulse sm:mt-6 sm:space-y-6">
    <div v-for="i in barCount" :key="i" class="w-full bg-gray-200 rounded-full" :class="heightClass" />
    <span class="sr-only">{{ $t('notifications.loading') }}</span>
  </div>
</template>
