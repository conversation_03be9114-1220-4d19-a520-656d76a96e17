<script setup lang="ts">
import type { PropType } from 'vue'
import { CalendarIcon, ComputerDesktopIcon, GlobeAltIcon } from '@heroicons/vue/24/solid'
import type { Booking, Paginate } from '@/types'
import { formatDateAndTime } from '@/composables/dateFormat'
const props = defineProps({
  appointmentsList: {
    type: Object as PropType<Paginate<Booking>>,
    default: () => {},
  },

  isLoading: {
    type: Boolean,
    default: false,
  },
  // TODO : fix default later
  onRowClick: {
    type: Function,
    default: null,
  },
})

const emit = defineEmits(['pageChanged'])
const { t } = useI18n()
const router = useRouter()
const selectedOrderId = ref<string>()
const openAppointmentDetailsModal = ref(false)
const selectedAppointmentId = ref<string>()
const showAppointmentDetailsModal = (item: Booking) => {
  openAppointmentDetailsModal.value = true
  selectedAppointmentId.value = item.uuid
  selectedOrderId.value = item.order_id
}

function closeAppointmentDetailsModal() {
  openAppointmentDetailsModal.value = false
  selectedAppointmentId.value = ''
}
const headers = computed(() => {
  return [
    {
      title: t('booking.booking_number'),
    },
    {
      title: t('status'),
    },
    {
      title: t('dashboard.booking.date'),
    },
    {
      title: t('price'),
    },
    {
      title: t('source'),
    },
  ]
})
</script>

<template>
  <main-booking-modal
    :is-open="openAppointmentDetailsModal"
    :item-id="selectedAppointmentId"
    :order-id="selectedOrderId"
    @closed="closeAppointmentDetailsModal"
    @start-loading="() => {}"
    @end-loading="() => {}"
    @refresh-events="$emit('pageChanged', 1)"
  />
  <generic-table
    :headers="headers"
    :data="appointmentsList.data"
    tr-class="cursor-pointer"
    :on-row-click="showAppointmentDetailsModal"
    :is-loading="isLoading"
  >
    <template #row="{ item }">
      <grid-td> # {{ item?.booking_number }} </grid-td>
      <grid-td :default-style="false" class="flex items-center py-2">
        <booking-status
          class="flex items-center px-2 py-1 rounded-md"
          :book-status="item?.status"
        />
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          {{ formatDateAndTime(item.created_at) }}
        </div>
      </grid-td>
      <grid-td>
        <price-format
          :form-data="{
            price: item?.price,
            currency: item?.currency || '',
          }"
        />
      </grid-td>

      <grid-td>
        <ComputerDesktopIcon
          v-if="item.source == 'pos'"
          class="inline-block w-5 h-5"
        />
        <GlobeAltIcon
          v-else-if="item.source == 'booking-page'"
          class="inline-block w-5 h-5"
        />
        <CalendarIcon v-else-if="item.source == 'calendar'" class="inline-block w-5 h-5" />
      </grid-td>
    </template>
  </generic-table>
  <Pagination
    v-if="appointmentsList.data?.length"
    :pagination-meta="appointmentsList.meta"
    :pagination-links="appointmentsList.links"
    @change="$emit('pageChanged', $event)"
  />
</template>
