<script lang="ts" setup>
import { <PERSON>u, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { ChevronDownIcon } from '@heroicons/vue/20/solid'
import dayjs from 'dayjs'
const emit = defineEmits(['rangeSelected'])
const rangeFilter = reactive({
  from: dayjs().subtract(6, 'days').format('YYYY-MM-DD'),
  to: dayjs().add(1, 'days').format('YYYY-MM-DD'),
})
const ranges = [
  {
    label: 'this_day',
    value: '1',
  },
  {
    label: 'this_week',
    value: '7',
  },
  {
    label: 'this_month',
    value: '30',
  },
  {
    label: 'this_year',
    value: '365',
  },
]
const rangeSelect = ref(ranges[0])

function setRange(range) {
  rangeSelect.value = range
  rangeFilter.from = dayjs().subtract(range.value, 'days').format('YYYY-MM-DD')
  rangeFilter.to = dayjs().add(1, 'days').format('YYYY-MM-DD')
  emit('rangeSelected', rangeFilter)
}
</script>

<template>
  <Menu as="div" class="relative">
    <MenuButton
      type="button"
      class="flex items-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
    >
      {{ $t(rangeSelect.label) }}
      <ChevronDownIcon class="-mr-1 h-5 w-5 text-gray-400" aria-hidden="true" />
    </MenuButton>
    <transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <MenuItems
        class="absolute right-0 z-10 mt-3 w-36 origin-top-right overflow-hidden rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
      >
        <div class="py-1">
          <MenuItem
            v-for="(range, index) in ranges"
            :key="index"
            v-slot="{ active }"
          >
            <a
              href="#"
              class="block px-4 py-2 text-sm"
              :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700']"
              @click="setRange(range)"
            >{{ $t(range.label) }}
            </a>
          </MenuItem>
        </div>
      </MenuItems>
    </transition>
  </Menu>
</template>
