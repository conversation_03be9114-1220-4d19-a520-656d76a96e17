<script setup lang="ts">
import type { PropType } from 'vue'
import {
  CalendarIcon,
  ComputerDesktopIcon,
  GlobeAltIcon,
} from '@heroicons/vue/24/solid'
import type { Order, Paginate } from '@/types'
import { formatDateAndTime } from '@/composables/dateFormat'
const props = defineProps({
  ordersList: {
    type: Object as PropType<Paginate<Order>>,
    default: () => {},
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  // TODO : fix default later
  onRowClick: {
    type: Function,
    default: null,
  },
})

const emit = defineEmits(['pageChanged'])
const { t } = useI18n()
const slot = useSlots()
const router = useRouter()
const selectedOrderId = ref<string>()
const openOrderDetailsModal = ref(false)
function openOrderDetails(item: Order) {
  openOrderDetailsModal.value = true
  selectedOrderId.value = item.id
}
const closeOrderModal = () => {
  openOrderDetailsModal.value = false
  selectedOrderId.value = ''
}
const headers = computed(() => {
  return [
    {
      title: t('booking.booking_number'),
    },
    // { title: t("dashboard.booking.customer") },
    // {
    //   title: t("welcome.form.branch_name"),
    // },
    {
      title: t('status'),
    },
    {
      title: t('dashboard.booking.date'),
    },
    // {
    //   title: t("quantity"),
    // },
    {
      title: t('price'),
    },
    {
      title: t('source'),
    },
  ]
})
</script>

<template>
  <main-booking-modal
    :is-open="openOrderDetailsModal"
    :order-id="selectedOrderId"
    @closed="closeOrderModal"
    @start-loading="() => {}"
    @end-loading="() => {}"
    @refresh-events="$emit('pageChanged', 1)"
  />
  <generic-table
    :headers="headers"
    :data="ordersList.data"
    tr-class="cursor-pointer"
    :on-row-click="openOrderDetails"
    :is-loading="isLoading"
  >
    <template #row="{ item }">
      <grid-td> # {{ item?.OrderNum }} </grid-td>
      <!-- <grid-td
        :defaultStyle="false"
        class="py-2 text-sm text-black whitespace-nowrap"
      >
        <div class="flex items-center">
          <div class="">
            {{ `${item?.customer?.first_name} ${item?.customer?.last_name}` }}
          </div>
        </div>
      </grid-td>
      <grid-td>
        <span>
          {{ item.branch?.name }}
        </span>
      </grid-td> -->

      <grid-td :default-style="false" class="flex items-center py-2">
        <span
          class="flex items-center px-2 py-1 rounded-md"
          :style="{
            'background-color': item?.status?.bg_color,
            'color': item?.status?.text_color,
          }"
        >
          {{ item?.status?.label }}
        </span>
      </grid-td>
      <grid-td>
        <div class="text-gray-900">
          {{ formatDateAndTime(item.created_at) }}
        </div>
      </grid-td>
      <!-- <grid-td>
        {{ item?.items }}
      </grid-td> -->
      <grid-td>
        <price-format
          :form-data="{
            price: item?.total,
            currency: item?.currency || '',
          }"
        />
      </grid-td>
      <grid-td>
        <ComputerDesktopIcon
          v-if="item.source == 'pos'"
          class="inline-block w-5 h-5"
        />
        <GlobeAltIcon
          v-else-if="item.source == 'bookingPage'"
          class="inline-block w-5 h-5"
        />
        <CalendarIcon
          v-else-if="item.source == 'calendar'"
          class="inline-block w-5 h-5"
        />
      </grid-td>
    </template>
  </generic-table>
  <div class="px-4">
    <Pagination
      v-if="ordersList.data?.length"
      :pagination-meta="ordersList.meta"
      :pagination-links="ordersList.links"
      @change="$emit('pageChanged', $event)"
    />
  </div>
</template>
