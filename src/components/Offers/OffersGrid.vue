<script lang="ts" setup>
import type { ComputedRef } from 'vue'
import { storeToRefs } from 'pinia'
import type { Offers, header } from '@/types'
import { formatDate } from '@/composables/dateFormat'
import { useOffers } from '@/stores/offers'
const emit = defineEmits(['openSlider'])
const { getOffers, processing } = storeToRefs(useOffers())
const { t } = useI18n()

const headers: ComputedRef<header[]> = computed(() => [
  {
    title: t('offers.name'),
  },
  {
    title: t('offers.start_at'),
  },
  {
    title: t('offers.end_at'),
  },
])
const openEditModal = (item: Offers) => {
  emit('openEditModal', item)
}
</script>

<template>
  <generic-table
    :is-loading="processing"
    :data="getOffers"
    :headers="headers"
    item-key="uuid"
    :on-row-click="openEditModal"
    tr-class="cursor-pointer"
  >
    <template #row="{ item }">
      <grid-td>
        {{ item.name }}
      </grid-td>
      <grid-td>
        {{ formatDate(new Date(item.start_at)) }}
      </grid-td>
      <grid-td>
        {{ formatDate(new Date(item.end_at)) }}
      </grid-td>
      <!-- <grid-td>
        <span
          class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"
          v-if="item.status"
        >
          {{ $t("active") }}
        </span>
        <span
          class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800"
          v-else
        >
          {{ $t("noActive") }}
        </span>
      </grid-td> -->
    </template>
  </generic-table>
</template>
