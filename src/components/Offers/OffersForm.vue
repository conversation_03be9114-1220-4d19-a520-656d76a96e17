<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import type { PropType } from 'vue'
import type { Offers } from '../../types/offers'
import { formatDate } from '@/composables/dateFormat'
import { useOffers } from '@/stores/offers'
import i18n from '@/i18n'
import type { Service } from '@/types'
import { makeArrayEmpty } from '@/utils/makeArrOfErrEmpty'

const props = defineProps({
  showModal: {
    type: Boolean,
    default: false,
  },
  offers: {
    type: Object as PropType<Offers | null>,
    default: null,
  },
  servicesOptions: {
    type: Array as PropType<Service[]>,
    default: () => ([]),
  },
})

const emit = defineEmits(['close', 'create'])

const { locale } = useI18n()
const { offers } = toRefs(props)
const { getLocale } = storeToRefs(useLocalesStore())
const { createOffers, updateOffers, deleteOffers } = useOffers()
const { offers: offerss } = storeToRefs(useOffers())
const formData = reactive<Offers>({
  name: '',
  start_at: new Date(),
  end_at: new Date(),
  status: true,
  booking_page: true,
  webapp: true,
  services: [],
})
if (offers.value) {
  formData.name = offers.value.name || ''
  formData.start_at = offers.value.start_at || new Date()
  formData.end_at = offers.value.end_at || new Date()
  formData.status = offers.value.status || true
  formData.booking_page = offers.value.booking_page || true
  formData.webapp = offers.value.booking_page || true
  formData.services = offers.value.services || []
}
const rules = {
  name: {
    required,
  },
  start_at: {
    required,
  },
  end_at: {
    required,
  },
  status: {
    required,
  },
  booking_page: {
    required,
  },
  webapp: {
    required,
  },
  services: {
    required,
  },
}
const processing = ref(false)
const errHandle = reactive<{ [key: string]: string[] }>({
})
const closeModal = () => {
  makeArrayEmpty(errHandle)
  emit('close')
}
const $v = useVuelidate(rules, formData)
const submitForm = () => {
//   $v.value.$touch()
//   if ($v.value.$invalid)
//     return
  makeArrayEmpty(errHandle)
  formData.start_at = formatDate(formData.start_at)
  formData.end_at = formatDate(formData.end_at)
  processing.value = true
  if (!offers.value) {
    createOffers(formData).then(() => {
      closeModal()
    })
      .catch((err) => {
        if (err.errors)
          for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        processing.value = false
      })
  }
  else {
    formData.services = formData.services.map((service) => {
      return {
        id: service.id,
        offer_price: service.offer_price,
      }
    })
    updateOffers(formData, offers.value?.uuid).then(() => {
      closeModal()
    })
      .catch((err) => {
        if (err.errors)
          for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        processing.value = false
      })
  }
}
const deleteCouponsFun = () => {
  processing.value = true

  deleteOffers(offers.value?.uuid).finally(() => {
    processing.value = false
    emit('close')
  })
}
const setDateStartDate = (date: string) => {
  formData.end_at = new Date(date)
}
const setDateEndDate = (date: string) => {
  formData.end_at = new Date(date)
}
</script>

<template>
  <div>
    <overlay-loader v-if="processing" />
    <form
      :class="[
        getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
      ]"
      @submit.prevent="submitForm"
    >
      <err-validations :err-handle="errHandle" />

      <div class="flex flex-col gap-6">
        <div>
          <TextInput
            id="name"
            v-model="formData.name"
            :label="$t('offers.name')"
            required
            class="block w-full py-3 leading-tight text-gray-700 border rounded appearance-none ps-4 pe-4 focus:outline-none focus:bg-white focus:border-gray-500"
            :placeholder="`${$t('enter')} ${$t('offers.name')}`"
          />
          <p
            v-for="error of $v.name.$errors"
            :key="error.$uid"
            class="error-message"
          >
            {{ $t(`${error.$message}`) }}
          </p>
        </div>
        <div>
          <label
            for="start_at"
            class="block mb-1 text-xs font-bold tracking-wide text-gray-700  "
          >{{ $t("offers.start_at") }} <span class="text-red-600">*</span></label>
          <v-date-picker
            mode="date"
            is24hr
            :model-value="formData.start_at"
            :locale="i18n.global.locale.value"
            :first-day-of-week="1"
            @update:model-value="setDateStartDate"
          >
            <template #default="{ inputValue, inputEvents }">
              <input
                id="start_at"
                :class="{ 'error-input': $v.end_at.$errors.length }" class="block w-full px-3 py-2 border border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" :value="inputValue" mode="mode"
                v-on="inputEvents"
              >
            </template>
          </v-date-picker>
          <p
            v-for="error of $v.start_at.$errors"
            :key="error.$uid"
            class="error-message"
          >
            {{ $t(`${error.$message}`) }}
          </p>
        </div>
        <div>
          <label
            for="end_at"
            class="block mb-1 text-xs font-bold tracking-wide text-gray-700
          >{{ $t("offers.end_at") }} <span class="text-red-600"
          >*</span></label>
          <v-date-picker
            mode="date"
            is24hr
            :model-value="formData.end_at"
            :locale="i18n.global.locale.value"
            :first-day-of-week="1"
            @update:model-value="setDateEndDate"
          >
            <template #default="{ inputValue, inputEvents }">
              <input
                id="end_date"
                :class="{ 'error-input': $v.end_at.$errors.length }" class="block w-full px-3 py-2 border border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" :value="inputValue" mode="mode"
                v-on="inputEvents"
              >
            </template>
          </v-date-picker>
          <p
            v-for="error of $v.end_at.$errors"
            :key="error.$uid"
            class="error-message"
          >
            {{ $t(`${error.$message}`) }}
          </p>
        </div>
        <div class="flex gap-2 items-center">
          <input
            id="status"
            v-model="formData.status"
            class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            type="checkbox"
          >
          <label
            for="status"
            class=" mb-1 text-xs font-bold tracking-wide text-gray-700  "
          >{{ $t("offers.status") }}
          </label>
          <p
            v-for="error of $v.status.$errors"
            :key="error.$uid"
            class="error-message"
          >
            {{ $t(`${error.$message}`) }}
          </p>
        </div>
        <div class="flex gap-2 items-center">
          <input
            id="booking_page"
            v-model="formData.booking_page"
            class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            type="checkbox"
          >
          <label
            for="booking_page"
            class=" mb-1 text-xs font-bold tracking-wide text-gray-700  "
          >{{ $t("offers.booking_page") }}
          </label>
          <p
            v-for="error of $v.booking_page.$errors"
            :key="error.$uid"
            class="error-message"
          >
            {{ $t(`${error.$message}`) }}
          </p>
        </div>
        <div class="flex gap-2 items-center">
          <input
            id="webapp"
            v-model="formData.webapp"
            class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            type="checkbox"
          >
          <label
            for="webapp"
            class=" mb-1 text-xs font-bold tracking-wide text-gray-700
          >{{ $t("offers.webapp") }}
            </label
          >
            <p
              v-for="error of $v.webapp.$errors"
              :key="error.$uid"
              class="error-message"
            >
              {{ $t(`${error.$message}`) }}
            </p>
          </label>
        </div>
        <OffersServices v-model="formData.services" :services-options="servicesOptions" />

        <div class="border-t pt-8">
          <div class="flex items-center justify-end">
            <div v-if="offers" class="flex w-full justify-end gap-4">
              <BaseButton
                class=" py-3 hover:bg-red-700"
                custome-bg="bg-red-600"
                show-icon
                :processing="processing"
                @click="deleteCouponsFun"
              >
                {{ $t("form.delete") }}
              </BaseButton>
              <BaseButton
                class=" py-3 hover:bg-indigo-800"
                custome-bg="bg-indigo-700"
                show-icon
                type="submit"
                :processing="processing"
              >
                {{ $t("form.update") }}
              </BaseButton>
            </div>
            <BaseButton
              v-else
              type="submit"
              class="w-1/2 ms-auto py-3 hover:bg-green-700"
              custome-bg="bg-green-600"
              show-icon
              :processing="processing"
            >
              {{ $t("form.create") }}
            </BaseButton>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>
