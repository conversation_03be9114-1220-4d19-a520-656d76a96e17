<script setup lang="ts">
import type { PropType } from '@vue/runtime-core'
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'
import { required } from '@/utils/i18n-validators'
import type { Offers, OffersServices } from '@/types'
const props = defineProps({
  offer: {
    type: Object as PropType<Offers>,
    default: null,
  },
  showModal: {
    type: Boolean,
    default: false,
  },
  servicesOptions: {
    type: Array as PropType<ServiceOption[]>,
    default: () => [],
  },
})
const emit = defineEmits(['closed'])
const { createOffers, updateOffers, deleteOffers } = useOffers()
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const processing = ref(false)
const editMode = ref(false)
const formData = reactive<Offers>({
  uuid: '',
  name: '',
  start_at: new Date(),
  end_at: new Date(),
  // booking_page: false,
  // webapp: false,
  status: true,
  services: [
    {
      id: '',
      offer_price: 0,
    },
  ],
})

const serviceObject: OffersServices = {
  id: '',
  offer_price: 0,
}

const rules = {
  name: {
    required,
  },
  start_at: {
    required,
  },
  end_at: {
    required,
  },
}
const v$ = useVuelidate(rules, formData)

interface ServiceOption {
  label: string
  value: string
  price: number
}

const { offer } = toRefs(props)
watch(
  offer,
  (value: Offers) => {
    if (!value?.uuid)
      return
    const { name, start_at, end_at, booking_page, webapp, services, uuid, status } = value
    formData.uuid = uuid
    formData.name = name
    formData.start_at = start_at
    formData.end_at = end_at
    // formData.booking_page = Boolean(booking_page);
    // formData.webapp = Boolean(webapp);
    formData.status = status
    formData.services = services?.length
      ? services
      : [
          {
            id: '',
            offer_price: 0,
          },
        ]
    editMode.value = true
  },
  { immediate: true },
)

const resetForm = () => {
  for (const key in formData)
    formData[key] = ''

  formData.status = true
  // formData.booking_page = false;
  // formData.webapp = false;
  formData.services = [
    {
      id: '',
      offer_price: 0,
    },
  ]
  v$.value.$reset()
}

const createRecord = async (payload: Offers) => {
  return createOffers(payload).then(() => {
    resetForm()
  })
}
const editRecord = async (payload: Offers) => {
  return updateOffers(payload, payload.uuid as string).then(() => {
    editMode.value = false
    resetForm()
    emit('closed')
  })
}
const deleteRecord = () => {
  if (!formData.uuid)
    return
  processing.value = true
  deleteOffers(formData.uuid)
    .then(() => {
      emit('closed')
    })
    .finally(() => {
      processing.value = false
    })
}

const saveOffer = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return false
  processing.value = true
  try {
    if (editMode.value)
      await editRecord(formData)
    else
      await createRecord(formData)
  }
  finally {
    processing.value = false
  }
}
const removeService = (index: number) => {
  formData.services.splice(index, 1)
}
const addNewService = () => {
  formData.services.push({ ...serviceObject })
}

const getServiceOriginalPrice = (id: string) => {
  const ser = props.servicesOptions.find(item => item.value === id)

  return ser?.price || 0.00
}

const selectedServices = computed(() => {
  return formData.services.map(item => item.id)
})
</script>

<template>
  <Modal
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="editMode ? 'updateOffer' : 'createOffer'"
    @close="emit('closed')"
  >
    <form class="text-start py-5 mb-4 text-start relative" @submit.prevent="saveOffer">
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
        <div class="">
          <form-group name="name" :validation="v$">
            <template #default="{ attrs }">
              <TextInput
                v-bind="attrs"
                id="name"
                v-model="formData.name"
                :label="$t('offers.name')"
                :placeholder="`${$t('enter')} ${$t('offers.name')}`"
              />
            </template>
          </form-group>
        </div>

        <div class="">
          <form-group name="start_at" :validation="v$">
            <template #default="{ attrs }">
              <DateInput
                v-bind="attrs"
                id="start_at"
                v-model="formData.start_at"
                :label="$t('offers.start_at')"
                :is24hr="true"
                @blur="attrs.onblur"
              />
            </template>
          </form-group>
        </div>
        <div class="">
          <form-group name="end_at" :validation="v$">
            <template #default="{ attrs }">
              <DateInput
                v-bind="attrs"
                id="end_at"
                v-model="formData.end_at"
                :label="$t('offers.end_at')"
                :is24hr="true"
                @blur="attrs.onblur"
              />
            </template>
          </form-group>
        </div>
        <div class="col-span-1">
          <div>
            <div class="flex flex-col gap-6 mt-3">
              <div class="flex items-center">
                <CheckInput id="status" v-model="formData.status" custom-classes="me-2" />
                <LabelInput for="status">
                  {{ $t("offers.status") }}
                </LabelInput>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex gap-2">
        <div class="pt-6 sm:col-span-2">
          <h2 class="text-xl font-medium text-gray-900">
            {{ $t("booking.services") }}
          </h2>
          <div
            v-for="(service, index) in formData.services"
            :key="`services-${index}`"
            class="grid gap-5 mt-3 sm:grid-cols-3"
          >
            <div>
              <form-group :error-name="`services.${index}.id`">
                <template #default="{ attrs }">
                  <BaseComboBox
                    :key="`services-${index}`"
                    v-bind="attrs"
                    v-model="service.id"
                    place-holder="serviceName"
                    required
                    arial-label="Search"
                    class="flex-1"
                    :options="servicesOptions"
                  >
                    {{ $t("booking.service") }}
                  </BaseComboBox>
                </template>
              </form-group>
            </div>

            <div>
              <form-group>
                <template #default="{ attrs }">
                  <NumberInput
                    :id="`servicePrice-${index}`"
                    :label="$t('booking.price')"
                    v-bind="attrs"
                    readonly
                    :value="getServiceOriginalPrice(service.id)"
                    custom-classes="mt-1"
                  />
                </template>
              </form-group>
            </div>

            <div>
              <form-group :error-name="`services.${index}.offer_price`">
                <template #default="{ attrs }">
                  <NumberInput
                    v-bind="attrs"
                    :id="`OfferservicePrice-${index}`"
                    v-model="service.offer_price"
                    :label="$t('booking.price')"
                    :max="getServiceOriginalPrice(service.id)"
                    custom-classes="mt-1"
                  />
                </template>
              </form-group>
            </div>

            <div class="flex items-center justify-center gap-4 pt-6 sm:justify-start">
              <button
                v-if="formData.services.length > 1 && index !== 0"
                type="button"
                class="inline-flex text-gray-400 bg-white rounded-md hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                @click="removeService(index)"
              >
                <TrashIcon class="w-6 h-6 text-red-400" aria-hidden="true" />
              </button>
              <button
                v-if="formData.services.length - 1 === index"
                type="button"
                class="inline-flex text-gray-400 bg-white rounded-md hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                @click="addNewService"
              >
                <PlusIcon class="w-6 h-6 text-gray-400" aria-hidden="true" />
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="w-full">
        <div class="flex w-full gap-4 justify-center mt-6">
          <BaseButton
            v-if="editMode"
            class="md:col-span-2 col-span-4"
            custome-bg="bg-red-600"
            show-icon
            type="button"
            :processing="processing"
            @click="deleteRecord"
          >
            {{ $t("form.delete") }}
          </BaseButton>
          <BaseButton
            class="md:col-span-2 col-span-4"
            :customebg="[editMode ? 'bg-gray-700' : 'bg-green-600']"
            show-icon
            type="submit"
            :processing="processing"
          >
            {{ editMode ? $t("form.update") : $t("form.create") }}
          </BaseButton>
        </div>
      </div>
    </form>
  </Modal>
</template>
