<script setup lang="ts">
import type { PropType } from 'vue'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/24/outline'
import { TailwindPagination } from 'laravel-vue-pagination'
import { storeToRefs } from 'pinia'
import type { PaginationLinks, PaginationMeta } from '@/types'

const props = defineProps({
  paginationMeta: {
    type: Object as PropType<PaginationMeta>,
    default: () => ({
      current_page: 1,
      from: 1,
      last_page: 1,
      links: [],
      path: '',
      per_page: 1,
      to: 15,
      total: 1,
    }),
  },
  paginationLinks: {
    type: Object as PropType<PaginationLinks>,
    default: () => ({
      first: '',
      last: '',
      prev: null,
      next: null,
    }),
  },
})
const emit = defineEmits(['change'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())

const currentRow = ref<string[]>([])
onMounted(() => {
  const maxNum = props.paginationMeta.current_page + 4
  if (props.paginationMeta.last_page > 5) {
    for (let i = props.paginationMeta.current_page; i <= maxNum; i++)
      currentRow.value.push(i.toString())
  }
  else {
    for (let i = 1; i <= props.paginationMeta.last_page; i++)
      currentRow.value.push(i.toString())
  }
})
const getResults = (page = 1) => {
  if (page > props.paginationMeta.last_page)
    return
  emit('change', page)
}
const keepLength = ref(false)
watch(keepLength, (val) => {
  keepLength.value = Boolean(val)
})
</script>

<template>
  <div
    class="flex items-center justify-between mx-4 mt-4"
    :dir="getLocale(locale)?.direction === 'rtl' ? 'rtl' : 'ltr'"
  >
    <div>
      <p class="text-sm text-gray-700">
        {{ $t("pagination.total") }} :
        <span class="font-medium">{{ paginationMeta.total }}</span>

        ( {{ paginationMeta.from }} - {{ paginationMeta.to }} )
      </p>
    </div>
    <TailwindPagination
      :data="{
        ...paginationMeta,
        next_page_url: paginationLinks.next,
        first_page_url: paginationLinks.first,
        last_page_url: paginationLinks.last,
        prev_page_url: paginationLinks.prev,
        range: 2,
      }"
      :limit="5"
      :keep-length="keepLength"
      @pagination-change-page="getResults"
    />
  </div>
</template>
