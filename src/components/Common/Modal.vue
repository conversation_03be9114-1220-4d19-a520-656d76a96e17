<script setup lang="ts">
import { ref } from 'vue'
import {
  Dialog,
  DialogPanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { CheckIcon, XCircleIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
  },
  param: {
    type: String,
    default: '',
  },
  panelClasses: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['close'])
const completeButtonRef = ref(null)
</script>

<template>
  <TransitionRoot as="template" appear :show="open">
    <Dialog as="div" :initial-focus="completeButtonRef" class="relative z-50 w-full max-w-4xl h-full xl:z-20 md:h-auto" @close="emit('close')">
      <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="overflow-x-auto overflow-y-auto fixed inset-0 z-10">
        <div class="flex justify-center items-center p-4 min-h-full text-center sm:p-0">
          <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" enter-to="opacity-100 translate-y-0 sm:scale-100" leave="ease-in duration-200" leave-from="opacity-100 translate-y-0 sm:scale-100" leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <DialogPanel class="relative p-6 w-full max-w-xl bg-white rounded-lg shadow-xl sm:w-10/12" :class="panelClasses">
              <slot name="header">
                <button
                  class="absolute top-4 left-4 z-10"
                  aria-label="Close"
                  @click="$emit('close')"
                >
                  <img src="@/assets/icons/actions/close.svg" alt="Close" class="w-9 h-9">
                </button>
                <h1 v-if="props?.title" class="mb-4 font-medium text-gray-900 text-h2">
                  {{ $t(`modalHeader.${props?.title}`, { param }) }}
                </h1>
              </slot>
              <slot />
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
