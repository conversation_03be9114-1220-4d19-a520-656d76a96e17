<script setup>
// Props for customization
const props = defineProps({
  images: { type: Array, default: () => [] }, // Array of image URLs
  intervalDuration: { type: Number, default: 3000 }, // Auto-slide interval in milliseconds
})

// Refs
const currentIndex = ref(0) // Current image index
const interval = ref(null) // Interval for auto-sliding

// Compute the current image based on the current index
const currentImage = computed(() => {
  // Get the image path directly from props
  const imgPath = props.images[currentIndex.value]
  return new URL(`../../assets/main_slider/${imgPath}`, import.meta.url).href
})

// Auto-slide functionality
const startAutoSlide = () => {
  interval.value = setInterval(() => {
    currentIndex.value = (currentIndex.value + 1) % props.images.length // Increment index
  }, props.intervalDuration)
}

// Stop auto-slide functionality
const stopAutoSlide = () => {
  if (interval.value) {
    clearInterval(interval.value)
    interval.value = null
  }
}

// Start auto-slide when the component mounts
onMounted(() => {
  startAutoSlide()
})

// Stop auto-slide when the component unmounts
onUnmounted(() => {
  stopAutoSlide()
})
</script>

<template>
  <div class="flex flex-row justify-center items-center w-full h-full">
    <!-- Display Image -->
    <img
      :src="currentImage"
      alt="Slider Image"
    >
  </div>
</template>
