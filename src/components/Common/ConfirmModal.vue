<script lang="ts" setup>
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: 'Confirm modal',
  },
  body: {
    type: String,
    default: '',
  },
  actionClasses: {
    type: String,
    default: '',
  },
})
defineEmits(['close', 'confirm', 'cancel'])
</script>

<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" class="relative z-[100]" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="overflow-y-auto fixed inset-0">
        <div
          class="flex justify-center items-center p-4 min-h-full text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="overflow-hidden p-6 w-full max-w-xl text-left align-middle bg-white rounded-2xl shadow-xl transition-all transform"
            >
              <DialogTitle
                as="h3"
                class="text-2xl font-medium leading-6 text-center text-gray-900"
              >
                {{ title }}
              </DialogTitle>
              <div class="mt-2">
                <div class="mt-5 text-end">
                  <slot name="body">
                    {{ body }}
                  </slot>
                </div>
                <div
                  class="flex gap-2 justify-end items-center mt-5 w-full"
                  :class="actionClasses"
                >
                  <button
                    type="button"
                    class="inline-flex justify-center px-4 py-2 text-sm font-medium text-red-900 bg-red-100 rounded-md border border-transparent hover:bg-red-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2"
                    @click="$emit('cancel')"
                  >
                    {{ $t('cancel') }}
                  </button>
                  <button
                    type="button"
                    class="inline-flex justify-center px-4 py-2 text-sm font-medium text-blue-900 bg-blue-100 rounded-md border border-transparent hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                    @click="$emit('confirm')"
                  >
                    {{ $t('confirm') }}
                  </button>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>
