<script setup lang="ts">
import { useGlobalDropdown } from '@/composables/useGlobalDropdown'

interface Props {
  triggerClass?: string
  menuClass?: string
  menuWidth?: string
  disabled?: boolean
  id?: string // Optional custom ID, will generate one if not provided
}

const props = withDefaults(defineProps<Props>(), {
  triggerClass: '',
  menuClass: '',
  menuWidth: 'w-56',
  disabled: false,
  id: '',
})

const emit = defineEmits<{
  opened: []
  closed: []
}>()

// Generate unique ID for this dropdown instance
const dropdownId = props.id || `dropdown-${Math.random().toString(36).substr(2, 9)}`

// Use global dropdown management
const {
  registerDropdown,
  unregisterDropdown,
  openDropdown: globalOpenDropdown,
  closeDropdown: globalCloseDropdown,
  isDropdownOpen,
} = useGlobalDropdown()

const showDropdown = computed(() => isDropdownOpen(dropdownId))
const dropdownRef = ref<HTMLElement>()
const buttonRef = ref<HTMLElement>()

const toggleDropdown = (event: Event) => {
  if (props.disabled)
    return

  event.stopPropagation()

  if (showDropdown.value)
    closeDropdown()
  else
    openDropdownInstance()
}

const openDropdownInstance = () => {
  globalOpenDropdown(dropdownId)
  emit('opened')
  nextTick(() => {
    positionDropdown()
  })
}

const closeDropdown = () => {
  globalCloseDropdown(dropdownId)
  emit('closed')
}

const positionDropdown = () => {
  if (!dropdownRef.value || !buttonRef.value)
    return

  const rect = buttonRef.value.getBoundingClientRect()
  const dropdownHeight = 300 // Approximate dropdown height
  const viewportHeight = window.innerHeight
  const spaceBelow = viewportHeight - rect.bottom

  // If not enough space below, position above
  if (spaceBelow < dropdownHeight)
    dropdownRef.value.classList.add('dropdown-above')
  else
    dropdownRef.value.classList.remove('dropdown-above')
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  closeDropdown()
}

onMounted(() => {
  // Register this dropdown instance
  registerDropdown(dropdownId, closeDropdown)
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // Cleanup
  unregisterDropdown(dropdownId)
  document.removeEventListener('click', handleClickOutside)
})

// Expose methods for parent component
defineExpose({
  closeDropdown,
  openDropdown: openDropdownInstance,
  isOpen: showDropdown,
  dropdownId,
})
</script>

<template>
  <div class="relative">
    <!-- Trigger slot - the button/element that opens the dropdown -->
    <div
      ref="buttonRef"
      class="cursor-pointer" :class="[
        { 'cursor-not-allowed opacity-50': disabled },
        triggerClass,
      ]"
      @click="toggleDropdown"
    >
      <slot
        name="trigger"
        :is-open="showDropdown"
        :toggle="toggleDropdown"
        :close="closeDropdown"
      >
        <!-- Default trigger if no slot provided -->
        <button
          class="flex gap-2 justify-center items-center px-3 py-1.5 text-sm font-medium rounded-lg border border-gray-200 shadow-sm transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500"
          :disabled="disabled"
        >
          <span>Menu</span>
          <svg
            class="ml-1 w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-180': showDropdown }"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </slot>
    </div>

    <!-- Dropdown menu -->
    <div
      v-if="showDropdown"
      ref="dropdownRef"
      class="dropdown-menu overflow-hidden absolute right-0 z-20 mt-2 bg-white rounded-xl border border-gray-200 ring-1 ring-black ring-opacity-5 shadow-xl" :class="[
        menuWidth,
        menuClass,
      ]"
      @click.stop
    >
      <!-- Menu content slot -->
      <slot
        name="content"
        :close="closeDropdown"
        :is-open="showDropdown"
      >
        <!-- Default content if no slot provided -->
        <div class="py-2">
          <div class="px-4 py-3 text-sm text-gray-700">
            No content provided
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<style scoped>
.dropdown-menu {
  transition: all 0.2s ease-in-out;
  max-height: 300px;
  overflow-y: auto;
}

.dropdown-above {
  bottom: 100% !important;
  top: auto !important;
  margin-top: 0 !important;
  margin-bottom: 0.5rem !important;
}

/* Custom scrollbar for dropdown */
.dropdown-menu::-webkit-scrollbar {
  width: 4px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
