<script setup lang="ts">
import type { PropType } from 'vue'
import * as OutlineIcons from '@heroicons/vue/24/outline'
const props = defineProps({
  modelValue: String,
  tabs: Array as PropType<{ label: string; value: string; href: string; icon?: string }[]>,
})

const emit = defineEmits(['update:modelValue'])

// const mapIcons: { [key: string]: any } = {
//   InformationCircleIcon,
// }

const updateValue = (event) => {
  emit('update:modelValue', event)
}
const updateValueSm = (event) => {
  emit('update:modelValue', event.target.value)
}
</script>

<template>
  <div class="block">
    <div class="border-b border-gray-200">
      <nav class="flex -mb-px space-x-2" aria-label="Tabs">
        <a
          v-for="tab in tabs"
          :key="tab.label"
          :href="tab.href"
          class="flex items-center justify-center gap-1 px-1 py-4 text-sm font-medium border-b-2 cursor-pointer whitespace-nowrap" :class="[modelValue === tab.value ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700']"
          :aria-current="modelValue === tab.value ? 'page' : undefined"
          @click="updateValue(tab.value)"
        ><component :is="OutlineIcons[tab.icon]" v-if="(tab.icon && OutlineIcons[tab.icon])" class="w-[1.5rem]" /> <span>{{ tab.label }} </span></a>
      </nav>
    </div>
  </div>
</template>
