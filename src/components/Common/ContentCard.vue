<script setup lang="ts">
import type { PropType } from 'vue';
import { PencilSquareIcon } from '@heroicons/vue/24/outline';
import DisclosureWrapper from '@/components/Common/DisclosureWrapper.vue';
import LilActionBtn from '@/components/Buttons/LilActionBtn.vue';
import EditIcon from '@/components/Icons/EditIcon.vue';

type Note = {
  user: string;
  date: string;
  note: string;
};

type Content = string | { ar: string; en: string } | Note[];

defineProps({
  title: {
    type: String,
    required: true,
  },
  content: {
    type: [String, Object, Array] as PropType<Content>,
    required: true,
  },
  showEditButton: {
    type: Boolean,
    default: true,
  },
  emptyText: {
    type: String,
    default: 'No content available.',
  },
});

const emit = defineEmits(['edit']);

function isObject(value: any): value is { ar: string, en: string } {
  return typeof value === 'object' && value !== null && !Array.isArray(value) && 'ar' in value && 'en' in value;
}

function isArray(value: any): value is Note[] {
    return Array.isArray(value);
}
</script>

<template>
  <DisclosureWrapper :title="title">
    <template #header-actions>
      <LilActionBtn
        v-if="showEditButton"
        :icon="EditIcon"
        @click="$emit('edit')"
      />
    </template>
    
    <div class="flex gap-2 items-center w-full overflow-x-auto">
      <div class="w-full mt-4">
        <!-- Case 1: Multi-language Object -->
        <div v-if="isObject(content)" class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <h4 class="mb-2 font-semibold text-gray-600">{{ $t('arabic') }}</h4>
            <div class="text-sm font-medium text-neutral-800 break-words whitespace-pre-line w-full max-h-40 overflow-y-auto" v-html="content.ar || '-'"></div>
          </div>
          <div>
            <h4 class="mb-2 font-semibold text-gray-600">{{ $t('english') }}</h4>
            <div class="text-sm font-medium text-neutral-800 break-words whitespace-pre-line w-full max-h-40 overflow-y-auto" v-html="content.en || '-'"></div>
          </div>
        </div>

        <!-- Case 2: Array of Notes -->
        <div v-else-if="isArray(content) && content.length > 0" class="space-y-4">
          <div v-for="(note, index) in content" :key="index" class="pb-4 border-b last:border-b-0">
            <div class="flex justify-between items-center mb-1">
              <span class="font-semibold text-gray-800">{{ note.user }}</span>
              <span class="text-xs text-gray-400">{{ note.date }}</span>
            </div>
            <p class="text-sm font-medium text-neutral-800 break-words whitespace-pre-line w-full max-h-40 overflow-y-auto">{{ note.note }}</p>
          </div>
        </div>

        <!-- Case 3: Simple String -->
        <div v-else-if="typeof content === 'string' && content" class="text-sm font-medium text-neutral-800 break-words whitespace-pre-line w-full max-h-40 overflow-y-auto" v-html="content"></div>

        <!-- Empty State -->
        <span v-else class="text-neutral-400">{{ emptyText }}</span>
      </div>
    </div>
  </DisclosureWrapper>
</template> 