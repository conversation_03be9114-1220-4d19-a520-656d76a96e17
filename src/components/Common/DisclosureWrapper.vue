<script lang="ts" setup>
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'
import { ChevronUpIcon } from '@heroicons/vue/20/solid'

defineProps({
  title: {
    type: String,
    required: true,
  },
  defaultOpen: {
    type: Boolean,
    default: true,
  },
})
</script>

<template>
  <div class="px-2 md:px-6 py-4 bg-white rounded-lg outline outline-1 outline-offset-[-1px] outline-gray-200">
    <Disclosure v-slot="{ open }" as="div" :default-open="defaultOpen">
      <DisclosureButton
        :class="open ? 'border-b border-gray-200 pb-4' : ''"
        class="flex items-center w-full cursor-pointer select-none gap-2"
      >
        <span class="text-base font-normal text-neutral-800">{{ title }}</span>
        <div class="flex items-center gap-2 ms-auto">
          <slot name="header-actions" />
          <ChevronUpIcon
            :class="open ? 'transform rotate-180' : ''"
            class="w-5 h-5 text-gray-500 transition-transform duration-200"
          />
        </div>
      </DisclosureButton>
      <DisclosurePanel>
        <div class="w-full overflow-x-auto overflow-y-auto max-h-[200px] md:max-h-none custom-scrollbar">
          <slot />
        </div>
      </DisclosurePanel>
    </Disclosure>
  </div>
</template>

<style scoped>
/* Modern, clean scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}
</style>
