<script setup lang="ts">
import { PlusIcon } from '@heroicons/vue/20/solid'
import { storeToRefs } from 'pinia'

const props = defineProps({
  text: {
    type: String,
    required: true,
  },
  btnText: {
    type: String,

  },
})
const emit = defineEmits(['showHours'])
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
</script>

<template>
  <div class="text-center mt-5 bg-neutral-100 py-10 cursor-pointer">
    <svg
      class="mx-auto h-12 w-12 text-gray-400"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      aria-hidden="true"
    >
      <path
        vector-effect="non-scaling-stroke"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"
      />
    </svg>
    <p class="mt-1 text-base text-gray-500">
      {{ $t(`emptyState.${text}`) }}
    </p>
    <div class="mt-4">
      <base-button
        type="button"
        class="mx-auto w-fit inline-block hover:bg-green-700"
        custome-bg="bg-green-600"
        @click="$emit('showHours')"
      >
        <PlusIcon
          class="h-5 w-5"
          aria-hidden="true"
          :class="[getLocale(locale)?.direction === 'rtl' ? 'ml-2' : 'mr-2']"
        />
        <span v-if="btnText">{{ $t(`emptyState.${btnText}`) }}</span>
        <span v-else>
          {{ $t("form.create") }}
        </span>
      </base-button>
    </div>
  </div>
</template>

