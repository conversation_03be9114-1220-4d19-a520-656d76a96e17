<script setup lang="ts">
import { ref } from 'vue'
import {
  Dialog,
  DialogPanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { CheckIcon, XCircleIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
  },
  param: {
    type: String,
    default: '',
  },
  panelClasses: {
    type: String,
    default: '',
  },
  subtitle: {
    type: String,
    default: '',
  },
  // Pass a component (e.g. icon) to render in the header
  icon: {
    type: [Object, Function, String],
    default: null,
  },
})

const emit = defineEmits(['close'])
const completeButtonRef = ref(null)
</script>

<template>
  <TransitionRoot as="template" appear :show="open">
    <Dialog as="div" :initial-focus="completeButtonRef" class="relative z-50 w-full h-full xl:z-20 md:h-auto" @close="emit('close')">
      <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <!-- wrapper: NO overflow utilities, just centering -->
      <div class="fixed inset-0 z-10 flex items-center justify-center p-4">
        <!-- single scroll container -->
        <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" enter-to="opacity-100 translate-y-0 sm:scale-100" leave="ease-in duration-200" leave-from="opacity-100 translate-y-0 sm:scale-100" leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
          <DialogPanel
            :class="panelClasses"
            class="relative w-full max-h-[calc(100vh-4rem)] overflow-y-auto bg-white rounded-lg shadow-xl sm:w-7/12"
          >
            <template v-if="$slots.header">
              <slot name="header" />
            </template>
            <template v-else>
              <div class="w-full sticky top-0 z-20 bg-white rounded-t-lg">
                <div class="flex relative gap-2 justify-between items-center px-4 py-3 w-full md:px-6 md:py-4">
                  <div class="flex gap-3 items-center min-w-0">
                    <div v-if="icon" class="p-2 bg-white rounded-[10px] shadow outline outline-1 outline-offset-[-1px] outline-neutral-300 flex items-center">
                      <template v-if="typeof icon === 'string'">
                        <img :src="icon" alt="icon" class="w-8 h-8 object-contain rounded">
                      </template>
                      <template v-else>
                        <component :is="icon" class="w-8 h-8 text-neutral-800" />
                      </template>
                    </div>

                    <div class="flex flex-col flex-1 items-start min-w-0">
                      <div class="text-lg font-bold truncate text-neutral-800">
                        {{ title }}<span v-if="param"> #{{ param }}</span>
                      </div>
                      <div v-if="subtitle" class="text-sm font-normal tracking-tight leading-tight truncate text-neutral-500">
                        {{ subtitle }}
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Close"
                    class="flex justify-center items-center w-10 h-10 hover:text-gray-700 focus:outline-none"
                    style="line-height: 1;"
                    @click="$emit('close')"
                  >
                    <img src="@/assets/icons/actions/close.svg" alt="Close">
                  </button>
                </div>
                <div class="w-full border-b border-gray-200" />
              </div>
            </template>
            <!-- content: no overflow here -->
            <div class="overflow-y-auto px-4 pt-4 pb-4 md:px-8 md:pt-5 md:pb-4 rounded-lg">
              <slot />
            </div>
          </DialogPanel>
        </TransitionChild>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

