<script setup lang="ts">
const props = defineProps({
  defaultStyle: {
    type: Boolean,
    required: false,
    default: true,
  },
})

const { locale } = useI18n()

const textDir = computed(() => {
  return {
    ar: 'text-right',
    en: 'text-left',
  }[locale.value]
})
</script>

<template>
  <!-- `px-3 py-4 text-sm text-gray-500   ${textDir}` -->
  <td
    :class="[
      {
        'px-3 py-4 text-sm text-gray-500': defaultStyle,
      },
      textDir,
    ]"
  >
    <slot />
  </td>
</template>
