import i18n from '@/i18n'
import dayjs from 'dayjs'
import 'dayjs/locale/ar'
import relativeTime from 'dayjs/plugin/relativeTime'
import localizedFormat from 'dayjs/plugin/localizedFormat'
import updateLocale from 'dayjs/plugin/updateLocale'
dayjs.extend(relativeTime)
dayjs.extend(localizedFormat)
dayjs.extend(updateLocale)
try {
  const initLocale = i18n.global?.locale?.value ? JSON.parse(i18n.global.locale.value) : 'en'
  dayjs.locale(initLocale)
}
catch (e) {
  dayjs.locale('en')
}
dayjs.updateLocale('ar', {
  meridiem: (hour, minute, isLowercase) => {
    return hour < 12 ? 'ص' : 'م'
  },
})
