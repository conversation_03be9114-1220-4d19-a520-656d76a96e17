import { readonly, ref } from 'vue'

// Global state to track the currently open dropdown
const currentOpenDropdown = ref<string | null>(null)

// Registry of all dropdown instances with their close functions
const dropdownRegistry = new Map<string, () => void>()

export function useGlobalDropdown() {
  /**
   * Register a dropdown instance
   * @param id Unique identifier for the dropdown
   * @param closeCallback Function to close this specific dropdown
   */
  const registerDropdown = (id: string, closeCallback: () => void) => {
    dropdownRegistry.set(id, closeCallback)
  }

  /**
   * Unregister a dropdown instance (cleanup)
   * @param id Unique identifier for the dropdown
   */
  const unregisterDropdown = (id: string) => {
    dropdownRegistry.delete(id)
    if (currentOpenDropdown.value === id)
      currentOpenDropdown.value = null
  }

  /**
   * Open a specific dropdown and close any other open dropdown
   * @param id Unique identifier for the dropdown to open
   */
  const openDropdown = (id: string) => {
    // Close any currently open dropdown
    if (currentOpenDropdown.value && currentOpenDropdown.value !== id) {
      const closeCallback = dropdownRegistry.get(currentOpenDropdown.value)
      if (closeCallback)
        closeCallback()
    }

    // Set the new dropdown as the currently open one
    currentOpenDropdown.value = id
  }

  /**
   * Close a specific dropdown
   * @param id Unique identifier for the dropdown to close
   */
  const closeDropdown = (id: string) => {
    if (currentOpenDropdown.value === id)
      currentOpenDropdown.value = null
  }

  /**
   * Close all dropdowns
   */
  const closeAllDropdowns = () => {
    if (currentOpenDropdown.value) {
      const closeCallback = dropdownRegistry.get(currentOpenDropdown.value)
      if (closeCallback)
        closeCallback()
    }
    currentOpenDropdown.value = null
  }

  /**
   * Check if a specific dropdown is currently open
   * @param id Unique identifier for the dropdown
   */
  const isDropdownOpen = (id: string) => {
    return currentOpenDropdown.value === id
  }

  /**
   * Get the currently open dropdown ID
   */
  const getCurrentOpenDropdown = () => {
    return currentOpenDropdown.value
  }

  return {
    registerDropdown,
    unregisterDropdown,
    openDropdown,
    closeDropdown,
    closeAllDropdowns,
    isDropdownOpen,
    getCurrentOpenDropdown,
    currentOpenDropdown: readonly(currentOpenDropdown),
  }
}
