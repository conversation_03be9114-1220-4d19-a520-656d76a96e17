import { onMounted, onUnmounted, ref } from 'vue'

export function useMap() {
  const coords = ref({ latitude: 24.774265, longitude: 46.738586 })
  const isSupported = 'navigator' in window && 'geolocation' in navigator
  let watcher: any = null
  onMounted(() => {
    if (isSupported) {
      navigator.geolocation.getCurrentPosition((position) => {
        coords.value = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        }
      })
    }
    navigator.permissions.query({ name: 'geolocation' }).then((result) => {
      result.onchange = () => {
        if (result.state === 'denied') {
          coords.value = {
            latitude: 24.774265,
            longitude: 46.738586,
          }

          watcher = navigator.geolocation.watchPosition(
            (position) => {
              (coords.value = position.coords)
            },
          )
        }
        navigator.geolocation.getCurrentPosition((position) => {
          coords.value = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          }
        })
      }
    })
  })
  const calculateCenter = (paths) => {
    let lat = 0
    let lng = 0
    paths.forEach((el) => {
      lat += el.lat
      lng += el.lng
    })
    return { lat: lat / paths.length, lng: lng / paths.length }
  }

  onUnmounted(() => {
    if (watcher)
      navigator.geolocation.clearWatch(watcher)
  })

  return { coords, isSupported, calculateCenter }
}
