import router from '@/router'
import { AUTH_TOKEN } from '@/constants'
import { useAuthStore } from '@/stores/auth'

const useLogin = async (authToken: string): Promise<string> => {
  try {
    const { getProfile } = useAuthStore()

    localStorage.setItem(AUTH_TOKEN, authToken)
    await getProfile()
    localStorage.setItem('userAuth', JSON.stringify(true))
    return Promise.resolve('OK')
  }
  catch (error) {
    return Promise.reject(error)
  }
}

export default useLogin
