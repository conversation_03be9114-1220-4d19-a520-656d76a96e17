import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { fetchSettings, updateAccountSetting, updateNotifications } = useAccountSettingStore()
const { showNotification } = useNotifications()

const settings = (page: string, callback: Function) => {
  const settingPage = ref(page)

  const processing = ref<boolean>(true)

  const currentSettings = ref({})
  const errHandle = reactive<{ [key: string]: string[] }>({
  })
  const fetchCurrentSettings = async () => {
    fetchSettings(settingPage.value).then((res: any) => {
      return callback(res)
    })
      .finally(() => {
        processing.value = false
      })
  }
  const updateSettings = async (payload: Object) => {
    processing.value = true
    for (const prop in errHandle)
      errHandle[prop] = []
    updateAccountSetting(settingPage.value, payload).then(({ data }) => {
      showNotification({
        title: 'Success',
        type: 'success',
        message: 'Settings updated successfully',
      })
      callback(data)
    }).catch((err) => {
      for (const prop in err.errors)
        errHandle[prop] = err.errors[prop]
    }).finally(() => {
      processing.value = false
    })
  }

  const updateNotificationSetting = async (payload: object): Promise<any> => {
    const { data } = await updateNotifications(payload)

    showNotification({
      title: 'Success',
      type: 'success',
      message: i18n.global.t('operations.updated_notifications_settings'),
    })

    callback(data)
  }

  onMounted(() => {
    fetchCurrentSettings()
  })

  return {
    processing, currentSettings, updateSettings, fetchCurrentSettings, updateNotificationSetting, errHandle,
  }
}

export default settings
