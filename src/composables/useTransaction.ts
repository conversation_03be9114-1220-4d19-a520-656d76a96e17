import { useTransactionState } from './../stores/transaction'
import type { Booking, PaginationLinks, PaginationMeta } from '@/types'

const { fetchTransactions } = useTransactionState()

const booking = () => {
  const tableData = reactive({
    transactionList: [],
    paginationMeta: {
      current_page: 1,
      from: 1,
      last_page: 1,
      links: [],
      path: '',
      per_page: 15,
      to: 15,
      total: 1,
    } as PaginationMeta,
    paginationLinks: {
      first: '',
      last: '',
      prev: null,
      next: null,
    } as PaginationLinks,
    processing: false,
    selectedBooking: null as Booking | null,
  })

  const fetchTransactionPage = async (page = 1, filters = '') => {
    tableData.processing = true
    try {
      const res = await fetchTransactions(page, filters)
      tableData.transactionList = res.data
      tableData.paginationMeta = res.meta
      tableData.paginationLinks = res.links
      return res
    }

    catch (error) {}

    finally {
      tableData.processing = false
    }
  }

  return {
    tableData,
    fetchTransactionPage,
  }
}

export default booking
