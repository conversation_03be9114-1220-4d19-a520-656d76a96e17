import i18n from '@/i18n'
const locatizedOption = computed(() => {
  return {
    shortcuts: {
      today: i18n.global.t('today'),
      yesterday: i18n.global.t('yesterday'),
      past: (days: number) => {
        return i18n.global.t('days_ago', { days })
      },
      currentMonth: i18n.global.t('this_month'),
      pastMonth: i18n.global.t('last_month'),
    },
    footer: {
      apply: '',
      cancel: '',
    },
  }
},
)
export { locatizedOption }
