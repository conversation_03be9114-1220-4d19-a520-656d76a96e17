import { storeToRefs } from 'pinia'
import type { PaginationLinks, PaginationMeta, Service, Staff } from '@/types'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
const { showNotification } = useNotifications()

const paginationMeta = {
  current_page: 1,
  from: 1,
  last_page: 1,
  links: [],
  path: '',
  per_page: 15,
  to: 15,
  total: 1,
}

const paginationLinks = {
  first: '',
  last: '',
  prev: null,
  next: null,
}

const {
  fetchStaff,
  fetchStaffServices,
  toggleStaffService,
  fetchStaffBoundaries,
  toggleStaffBoundaries,
} = useStaffStore()

const tableData = reactive({
  staffList: [] as Staff[],
  paginationMeta: paginationMeta as PaginationMeta,
  paginationLinks: paginationLinks as PaginationLinks,
  processing: false,
  selectedStaff: null as Staff | null,
  selectedStaffList: [] as Staff[],
  selectedIndex: null as number | null,
  selectAllStaff: false,
})
const staff = () => {
  const currentToggle = ref()
  const currentStaffBoundary = ref()

  const tableServicData = reactive<{
    ServiceList: Service[]
    paginationMeta: PaginationMeta
    paginationLinks: PaginationLinks
    processing: boolean
    BoundaryList: []
  }>({
    ServiceList: [],
    paginationMeta: {
      current_page: 0,
      from: 0,
      last_page: 0,
      links: [],
      path: '',
      per_page: 0,
      to: 0,
      total: 0,
    },
    paginationLinks: {
      first: '',
      last: '',
      prev: null,
      next: null,
    },
    processing: false,
    BoundaryList: [],
  })

  const createNew = () => {
    tableData.selectedIndex = null
    tableData.selectedStaff = null
  }

  const fetchStaffPage = async (
    page: string | number = 1,
    searchValue = '',
    per_page: number | string = 20,
  ) => {
    tableData.processing = true
    if (!tableData.selectedIndex)
      createNew()
    return fetchStaff(page, {
      searchValue,
      perPage: per_page == 'all' ? 100 : per_page,
    })
      .then((res) => {
        const staffStore = useStaffStore()
        const { staffList } = storeToRefs(staffStore)
        // @ts-expect-error
        staffList.value = res.data
        tableData.staffList = res.data
        tableData.paginationMeta = res.meta
        tableData.paginationLinks = res.links
      })
      .finally(() => {
        tableData.processing = false
      })
  }

  const fetchServices = async (staff: Staff, page = 1) => {
    tableServicData.processing = true
    fetchStaffServices(staff.uuid, page)
      .then((res) => {
        tableServicData.ServiceList = res.data
        tableServicData.paginationMeta = res.meta
        tableServicData.paginationLinks = res.links
      })
      .finally(() => {
        tableServicData.processing = false
      })
  }

  const toggleService = async (staff: Staff, service: Service) => {
    currentToggle.value = service.uuid
    toggleStaffService(staff, service)
      .then((res: 'added' | 'removed') => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.toggled'),
        })

        tableServicData.ServiceList.map((serv: Service) => {
          if (serv.uuid === service.uuid)
            serv.selected = res === 'added'

          return serv
        })
      })
      .catch(() => {
        tableServicData.ServiceList = [...tableServicData.ServiceList].map(
          (item) => {
            if (item.uuid === service.uuid)
              item.selected = !item.selected
            return item
          },
        )
      })
      .finally(() => {
        currentToggle.value = null
      })
  }

  const fetchStaffBoundariesList = async (staff: Staff, page: number | string = 1) => {
    tableServicData.processing = true;
    try {
      let { data, meta, links } = await fetchStaffBoundaries(staff.uuid, page);
      console.log("data ", data);
      tableServicData.BoundaryList = data;
      tableServicData.paginationMeta = meta;
      tableServicData.paginationLinks = links;
      console.log("tableServicData.BoundaryList ", tableServicData.BoundaryList);
      return data;
    } finally {
      tableServicData.processing = false;
    }
  }

  const toggleStaffBoundary = async (staff: Staff, boundary_id: string) => {
    currentStaffBoundary.value = boundary_id;
    console.log("tableServicData.BoundaryList ", tableServicData.BoundaryList);
    toggleStaffBoundaries(staff.uuid, boundary_id)
      .then((res: 'added' | 'removed') => {
        showNotification({
          title: i18n.global.t('Success'),
          type: 'success',
          message: i18n.global.t('operations.toggled'),
        })

        tableServicData.BoundaryList.map((boundaries) => {
          if (boundary_id === boundaries.uuid)
            boundaries.selected = res === 'added'

          return boundaries
        })
      })
      .catch(() => {
        tableServicData.BoundaryList = [...tableServicData.BoundaryList].map(
          (item) => {
            if (item.uuid === boundaries.uuid)
              item.selected = !item.selected
            return item
          },
        )
      })
      .finally(() => {
        currentStaffBoundary.value = null
      })
  }
  return {
    tableData,
    tableServicData,
    createNew,
    fetchStaffPage,
    fetchServices,
    currentToggle,
    toggleService,
    fetchStaffBoundariesList,
    toggleStaffBoundary,
    currentStaffBoundary,
  }
}

export default staff
