import useNotifications from '@/composables/useNotifications'
const { fetchInterval, updateInterval } = useTeamStore()
const { showNotification } = useNotifications()

const settings = (page: string, callback: Function, teamId: string) => {
  const settingPage = ref(page)

  const processing = ref()

  const currentSettings = ref({})

  const fetchCurrentSettings = async () => {
    processing.value = false
    fetchInterval(teamId).then((res: any) => {
      return callback(res)
    })
      .finally(() => {
        processing.value = false
      })
  }
  const updateSettings = async (payload: Object) => {
    processing.value = true
    updateInterval(payload, teamId).then(({ data }) => {
      showNotification({
        title: 'Success',
        type: 'success',
        message: 'Settings updated successfully',
      })
      callback(data)
    }).finally(() => {
      processing.value = false
    })
  }
  onMounted(() => {
    fetchCurrentSettings()
  })

  return {
    processing, currentSettings, fetchCurrentSettings, updateInterval, updateSettings, settingPage,
  }
}

export default settings
