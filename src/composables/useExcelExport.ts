import { ref } from 'vue'
import ExportService from '@/services/ExportService'
import type { ExportableModels } from '@/types'

const createDownloadLink = (
  fileName: string,
  data: BlobPart,
): Promise<HTMLAnchorElement> => {
  return new Promise((resolve) => {
    const url = window.URL.createObjectURL(new Blob([data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `${fileName}.xlsx`)
    link.setAttribute('id', fileName)
    document.body.appendChild(link)
    resolve(link)
  })
}

const processingExport = ref(false)

const excelExport = (model: ExportableModels, filters?: Object) => {
  const getExcel = async (plain = false, headers: any): Promise<any> => {
    processingExport.value = true

    try {
      const { data } = await ExportService.excelExport(model, filters, headers)

      if (plain)
        return data

      // const fileName = `exported-model-${model.toLocaleLowerCase()}`

      // createDownloadLink(fileName, data)
      //   .then((link) => {
      //     link.click()
      //     return link
      //   })
      //   .then((link) => {
      //     link.remove()
      //   })
    }
    finally {
      processingExport.value = false
    }
  }

  return {
    processingExport,
    getExcel,
  }
}

export default excelExport
