const timeForms = () => {
  const x = 15 // minutes interval
  const timesFroms: string[] = [] // from time array
  const timesTo: string[] = [] // to time array
  let ttFrom = 0 // start time
  let ttTo = 0 // start time
  const ap = ['AM', 'PM'] // AM-PM

  // loop to increment the time and push results in array
  for (let i = 0; ttFrom < 24 * 59.7; i++) {
    const hh = Math.floor(ttFrom / 60) // getting hours of day in 0-24 format
    const mm = (ttFrom % 60) // getting minutes of the hour in 0-55 format
    timesFroms[i] = `${(`${hh % 24 < 10 ? `0${hh % 24}` : hh % 24}`).slice(-2)}:${(`${mm < 10 ? `0${mm}` : mm}`)}`
    ttFrom = ttFrom + x
  }
  for (let i = 0; ttTo < 24 * 60; i++) {
    const hh = Math.floor(ttTo / 60) // getting hours of day in 0-24 format
    const mm = (ttTo % 60) // getting minutes of the hour in 0-55 format
    timesTo[i] = `${(`${hh % 24 < 10 ? `0${hh % 24}` : hh % 24}`).slice(-2)}:${(`${mm < 10 ? `0${mm}` : mm}`)}`
    ttTo = ttTo + x
  }
  timesTo[timesTo.length] = 'end_of_day'
  return {
    timesFroms, timesTo,
  }
}
export default timeForms
