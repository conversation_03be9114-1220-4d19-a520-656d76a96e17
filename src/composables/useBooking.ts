import type { Booking, PaginationLinks, PaginationMeta } from '@/types'

const { fetchEvents, fetchEventById } = useEventStore()

const booking = () => {
  const tableData = reactive({
    bookingList: [] as Booking[],
    paginationMeta: {
      current_page: 1,
      from: 1,
      last_page: 1,
      links: [],
      path: '',
      per_page: 15,
      to: 15,
      total: 1,
    } as PaginationMeta,
    paginationLinks: {
      first: '',
      last: '',
      prev: null,
      next: null,
    } as PaginationLinks,
    processing: false,
    selectedBooking: null as Booking | null,
  })

  const fetchEventpage = async (calendar = false, page = 1, filters = {}) => {
    tableData.processing = true
    try {
      const res = await fetchEvents(calendar, page, filters)
      tableData.bookingList = res.data
      tableData.paginationMeta = res.meta
      tableData.paginationLinks = res.links
      return res
    }
    catch (error) {}
    finally {
      tableData.processing = false
    }
  }

  return {
    tableData,
    fetchEventById,
    fetchEventpage,
  }
}

export default booking
