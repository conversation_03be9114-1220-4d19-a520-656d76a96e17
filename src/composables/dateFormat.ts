import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import { useAuthStore } from '@/stores/auth'
const { userInfo } = storeToRefs(useAuthStore())

const isValidDate = (date: string | Date) => {
  return dayjs(date).isValid()
}

const formatTime = (time: string | Date) => {
  if (!isValidDate(time))
    return ''
  if (userInfo.value.tenant?.time_format === 'hour24')
    return dayjs(new Date(time)).format('HH:mm')

  else
    return dayjs(new Date(time)).format('hh:mm A')
}
const formatTimeWithOutAmPm = (time: string | Date) => {
  if (!isValidDate(time))
    return ''
  if (userInfo.value.tenant?.time_format === 'hour24')
    return dayjs(new Date(time)).format('HH:mm')

  else
    return dayjs(new Date(time)).format('hh:mm')
}
const formatDate = (date: string | Date) => {
  if (!isValidDate(date))
    return ''
  return dayjs(new Date(date)).format('YYYY-M-D')
}

const formatDateAndTime = (date: string | Date) => {
  if (!isValidDate(date))
    return ''
  const fomattedDate = formatDate(date)
  const formattedTime = formatTime(date)
  return `${fomattedDate} ${formattedTime}`
}

const customDateFormat = (date: string | Date, format: string) => {
  if (!isValidDate(date))
    return ''
  if (/[HHmm]/.test(format)) {
    const formatDependOnTalet = userInfo.value.tenant?.time_format === 'hour24' ? format.replaceAll('h', 'H').replace('A', '') : format.replaceAll('H', 'h')
    return dayjs(new Date(date)).format(formatDependOnTalet)
  }
  return dayjs(new Date(date)).format(format)
}

export { formatTime, formatDate, formatDateAndTime, formatTimeWithOutAmPm, customDateFormat }
