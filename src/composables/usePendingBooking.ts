import type { PaginationLinks, PaginationMeta, PendingBooking } from '@/types'
import i18n from '@/i18n'

const { showNotification } = useNotifications()
const { fetchPendingBookings, updateBooking } = usePendingStore()

const pendingBookings = () => {
  const pendingData = reactive({
    pendingBookings: [] as PendingBooking[],
    paginationMeta: {
      current_page: 1,
      from: 1,
      last_page: 1,
      links: [],
      path: '',
      per_page: 15,
      to: 15,
      total: 0,
    } as PaginationMeta,
    paginationLinks: {
      first: '',
      last: '',
      prev: null,
      next: null,
    } as PaginationLinks,
    processing: false,
  })

  const fetchPendingPage = async () => {
    pendingData.processing = true
    try {
      const res = await fetchPendingBookings()
      pendingData.pendingBookings = res.data
      pendingData.paginationMeta = res.meta
      return res
    }
    catch (error) {}
    finally {
      pendingData.processing = false
    }
  }

  const updatePendingStatus = async (uuid: string, action: 'confirm' | 'reject') => {
    await updateBooking(uuid, action).then(() => {
      showNotification({
        title: i18n.global.t('Booking Change Status'),
        type: 'success',
        message: i18n.global.t(`Booking ${action}ed successfully`),
      })
    })
  }

  return {
    pendingData,
    fetchPendingPage,
    updatePendingStatus,
  }
}

export default pendingBookings
