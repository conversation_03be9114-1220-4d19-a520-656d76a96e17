import useNotifications from '@/composables/useNotifications'
const { fetchInterval, updateInterval } = useStaffStore()
const { showNotification } = useNotifications()

const settings = (page: string, callback: Function, staffId: string) => {
  const settingPage = ref(page)

  const processing = ref()

  const currentSettings = ref({})

  const fetchCurrentSettings = async () => {
    processing.value = false
    fetchInterval(staffId).then((res: any) => {
      return callback(res)
    })
      .finally(() => {
        processing.value = false
      })
  }
  const updateSettings = async (payload: Object) => {
    processing.value = true
    updateInterval(payload, staffId).then(({ data }) => {
      showNotification({
        title: 'Success',
        type: 'success',
        message: 'Settings updated successfully',
      })
      callback(data)
    }).finally(() => {
      processing.value = false
    })
  }
  //   const updateSettings = async (payload: Object) => {
  //     processing.value = true
  //     updateAccountSetting(settingPage.value, payload).then(({ data }) => {
  //       showNotification({
  //         title: 'Success',
  //         type: 'success',
  //         message: 'Settings updated successfully',
  //       })
  //       callback(data)
  //     }).finally(() => {
  //       processing.value = false
  //     })
  //   }

  //   const updateNotificationSetting = async (payload: object): Promise<any> => {
  //     const { data } = await updateNotifications(payload)

  //     showNotification({
  //       title: 'Success',
  //       type: 'success',
  //       message: 'Settings updated successfully',
  //     })

  //     callback(data)
  //   }

  onMounted(() => {
    fetchCurrentSettings()
  })

  return {
    processing, currentSettings, fetchCurrentSettings, updateInterval, updateSettings, settingPage,
  }
}

export default settings
