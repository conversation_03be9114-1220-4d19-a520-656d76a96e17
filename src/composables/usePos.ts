import { storeToRefs } from 'pinia'
import type { PaginationLinks, PaginationMeta, Service, Staff } from '@/types'
import { usePosStore } from '@/stores/pos'
import type { Sale } from '@/types/sale'

const paginationMeta = {
  current_page: 1,
  from: 1,
  last_page: 1,
  links: [],
  path: '',
  per_page: 10,
  to: 15,
  total: 1,
}

const paginationLinks = {
  first: '',
  last: '',
  prev: null,
  next: null,
}

const { fetchSales, fetchCheckout, print } = usePosStore()

const pos = () => {
  const tableData = reactive({
    list: [] as Sale[],
    paginationMeta: paginationMeta as PaginationMeta,
    paginationLinks: paginationLinks as PaginationLinks,
    processing: false,
  })
  const fetchSalePage = async (page = 1, filters = {}) => {
    tableData.processing = true
    const res = await fetchSales(page, filters)
    const posStore = usePosStore()
    const { list } = storeToRefs(posStore)
    list.value = res.data.data
    tableData.list = res.data.data
    tableData.paginationMeta = res.data.meta
    tableData.paginationLinks = res.data.links
    tableData.processing = false
  }

  const fetchCheckoutPage = async (uuid = '') => {
    const posStore = usePosStore()
    const { paymentMethodList, productList, serviceList, taxList, packageList } = storeToRefs(posStore)
    fetchCheckout(uuid).then((res) => {
      paymentMethodList.value = res.paymentsMethod
      productList.value = res.products
      packageList.value = res.packages
      serviceList.value = res.services
      taxList.value = res.taxes
    })
  }

  const printInvoice = async (invoiceId = '') => {
    tableData.processing = true
    const res = await print(invoiceId)
    const WinPrint = window.open('', '', 'left=0,top=0,width=800,height=900,toolbar=0,scrollbars=0,status=0')
    WinPrint.document.write(res.data)
    WinPrint.document.close()
    WinPrint.focus()
    WinPrint.print()
    WinPrint.close()
    tableData.processing = false
  }
  return {
    tableData,
    fetchSalePage,
    fetchCheckoutPage,
    printInvoice,
  }
}

export default pos
