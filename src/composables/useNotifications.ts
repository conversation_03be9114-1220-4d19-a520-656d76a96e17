import { reactive } from 'vue'

import { ChatBubbleBottomCenterIcon, CheckCircleIcon, ExclamationCircleIcon, ExclamationTriangleIcon, FlagIcon, InformationCircleIcon, QuestionMarkCircleIcon } from '@heroicons/vue/24/solid'
import type { FunctionalComponent } from 'vue'

const typeAndColorMapping = {
  success: {
    icon: CheckCircleIcon,
    style: 'bg-[#d4edda] text-green-800 border border-[#d4edda]',
  },
  error: {
    icon: ExclamationCircleIcon,
    style: 'bg-[#f8d7da] text-[#721c24] border border-[#f5c6cb]',
  },
  warning: {
    icon: ExclamationTriangleIcon,
    style: 'bg-[#fff3cd] text-yellow-800 border border-[#ffeeba]',
  },
  info: {
    icon: InformationCircleIcon,
    style: 'bg-[#d1ecf1] text-[#0c5460] border border-[#bee5eb]',
  },
} as {
  [key: string]: {
    [key: string]: string | FunctionalComponent
  }
}

interface Notification {
  id?: number
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  icon?: FunctionalComponent
  style?: string
}

const notifications: Notification[] = reactive([]) as Notification[]

const hideNotification = (id: number | undefined) => {
  if (!id)
    return
  const index = notifications.findIndex(item => item.id === id)
  if (index < 0)
    return

  notifications.splice(index, 1)
}

const showNotification = (notification: Notification) => {
  const notifcationId = Math.random() + Date.now()

  const { type = '' } = notification
  const icon: FunctionalComponent = (typeAndColorMapping[type]?.icon || FlagIcon) as FunctionalComponent
  const style: string = (typeAndColorMapping[type]?.style || 'text-gray-400') as string

  notifications.push({
    id: notifcationId,
    ...notification,
    icon,
    style,
  })

  // hide notifcation after 5 seconds
  setTimeout(() => {
    hideNotification(notifcationId)
  }, 5000)
}

export default function useNotifications() {
  return { notifications, showNotification, hideNotification, typeAndColorMapping }
}
