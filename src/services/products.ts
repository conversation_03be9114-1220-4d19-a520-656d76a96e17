import DataService from './Common/DataService'
import type { Products } from '@/types/products'
import type { Paginate } from '@/types'

export default {
  fetchProducts(page: number, params: Object) {
    return DataService.get<Paginate<Products>>('/products', {
      params: { page, ...params },
    })
  },
  createProduct(product: Omit<Products, 'uuid'>) {
    return DataService.post('/products', product, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  fetchProductById(productUuid: string) {
    return DataService.get<Products>(`/products/${productUuid}`)
  },
  deleteProduct(productuuid: string) {
    return DataService.delete(`/products/${productuuid}`)
  },
  updateProduct(
    productuuid: string,
    payload: { body: Omit<Products, 'uuid'> },
  ) {
    return DataService.post(
      `/products/${productuuid}`,
      {
        ...payload.body,
        _method: 'PUT',
      },
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    )
  },
  addImageToProduct(productuuid: string, image: FormData) {
    return DataService.post<Products>(`/products/${productuuid}/image`, image)
  },
  makeMainImage(productUuid: string, imageUuid: string) {
    return DataService.put(`/products/${productUuid}/images/${imageUuid}/make-main-image`)
  },
  removeImageFromProduct(productuuid: string, imageuuid: string) {
    return DataService.delete(`/products/${productuuid}/image/${imageuuid}`)
  },
}
