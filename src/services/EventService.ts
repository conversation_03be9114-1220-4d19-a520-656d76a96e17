import DataService from './Common/DataService'
import convertToFormdata from '@/utils'
import type { Booking, BookingForm, Paginate, TimeSlot } from '@/types'
import type { RecurringAppointment } from '@/types/lookup'

export default {
  fetchEvents(calendar: boolean, page: number, filters?: Object) {
    if (calendar) {
      return DataService.get<Paginate<Booking>>('/bookings-calendar', {
        params: filters,
      })
    }
    else {
      return DataService.get<Paginate<Booking>>(`/bookings?page=${page}`, {
        params: filters,
      })
    }
  },
  fetchLookupData() {
    return DataService.get('/booking/fetch-lookup-data')
  },
  createEvent(payload: Omit<BookingForm, 'uuid'>) {
    const data = convertToFormdata(payload)
    return DataService.post<Booking>('/bookings', data)
  },
  fetchEventById(eventId: string) {
    return DataService.get(`/bookings/${eventId}`)
  },
  updateEvent(payload: BookingForm) {
    const data = convertToFormdata(payload)
    return DataService.post<Booking>(
      `/bookings-update/${payload.bookingId}`,
      data,
    )
  },
  moveEvent(payload: BookingForm) {
    return DataService.put<Booking>(
      `/bookings/move/${payload.bookingId}`,
      payload,
    )
  },
  removeEvent(eventId: string) {
    return DataService.delete(`/bookings/${eventId}`)
  },
  fetchTimeSlots({
    date,
    providersIds,
    servicesIds,
    timezone,
    totalsDuration,
    quantities,
    visibleDays,
  }: {
    date: string
    providersIds: Array<string>
    servicesIds: Array<string>
    timezone?: string
    totalsDuration?: Array<number | string>
    quantities?: Array<number | string>
    visibleDays?: number
  }) {
    console.log(visibleDays)
    const params = new URLSearchParams()
    if (date)
      params.append('date', date)
    if (servicesIds?.length)
      params.append('services', servicesIds.join(','))
    if (providersIds?.length)
      params.append('providers', providersIds.join(','))
    if (visibleDays)
      params.append('visibleDays', String(visibleDays))
    const servicesWithProvider = servicesIds.map((service, index) => ({
      serviceId: service,
      providerId: providersIds[index],
      duration: totalsDuration?.[index] || null,
      quantity: quantities?.[index] || 1,
    }))
    params.append('timezone', timezone || 'Asia/Riyadh')

    const apiUrl = import.meta.env.VITE_API_URL
    return DataService.post(`${apiUrl}/api/v1/get-availability?${params.toString()}`, {
      servicesWithProvider,
    })
  },

  payBookingAmount(uuid: string, data: object) {
    return DataService.put(`/bookings/${uuid}/pay`, data)
  },
  getInovices(uuid: string) {
    return DataService.get(uuid)
  },
  cancelBooking(uuid: string, reasonId: string) {
    return DataService.put(`/bookings/${uuid}/cancel`, { reason_id: reasonId })
  },
  updateBookingStatus(
    bookingUuid: string,
    status: 'confirmed' | 'completed' | 'no-show',
  ) {
    return DataService.put(`/bookings/${bookingUuid}/change-status`, {
      status,
    })
  },
  createRecurredAppointment(
    bookingUuid: string,
    payload: RecurringAppointment,
  ) {
    return DataService.post(`/bookings/${bookingUuid}/recurring`, payload)
  },
}
