import DataService from '../services/Common/DataService'
export default {
  getPages() {
    return DataService.get('/pages')
  },
  deletePage(pageuuid: string) {
    return DataService.delete(`/pages/${pageuuid}`)
  },
  updatePage(formData: any, pageuuid: string) {
    return DataService.put(`/pages/${pageuuid}`, formData)
  },
  addPage(payload: any) {
    return DataService.post('/pages', payload)
  },
  getPageById(pageuuid: string) {
    return DataService.get(`/pages/${pageuuid}`)
  },
}
