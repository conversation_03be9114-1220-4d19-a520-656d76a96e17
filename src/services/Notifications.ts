import type { NotificationTemplates, Template } from '../types/notfication'
import DataServices from './Common/DataService'

export default {
  getNotificationTemplates() {
    return DataServices.get<NotificationTemplates>('/templates')
  },
  createNotificationTemplate(payload: Template) {
    return DataServices.post('/templates', payload).then((response) => {
      return response.data
    })
  },
  updateNotificationTemplate(uuid: string, payload: Template) {
    return DataServices.put(`/templates/${uuid}`, payload).then((response) => {
      return response.data
    })
  },
  deleteNotificationTemplate(uuid: string) {
    return DataServices.delete(`/templates/${uuid}`)
  },
  toggleTemplateStatus(uuid: string, isActive: boolean) {
    return DataServices.post(`/templates/${uuid}/toggle`, { status: isActive })
  },
  fetchAppNotifications() {
    return DataServices.get('/app-notifications')
  },
  readNotification(uuid: string) {
    return DataServices.put(`/app-notification/${uuid}`)
  },
}
