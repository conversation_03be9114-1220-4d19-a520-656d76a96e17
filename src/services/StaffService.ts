import DataService from './Common/DataService'
import type { Paginate, Service, Staff } from '@/types'

export default {
  fetchStaff(page: string | number, filters: Object) {
    return DataService.get<Paginate<Staff>>('/staff', {
      params: { page, ...filters },
    })
  },
  fetchAllStaff() {
    return DataService.get<Staff[]>('/staff/all')
  },
  createStaff(payload: Omit<Staff, 'uuid'>) {
    return DataService.post('/staff', payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  fetchStaffById(staffId: string) {
    return DataService.get<Staff>(`/staff/${staffId}`)
  },
  updateStaff(staffUuid: string, payload: { body: Omit<Staff, 'uuid'> }) {
    return DataService.post<Staff>(
      `/staff/${staffUuid}`,
      {
        ...payload.body,
        _method: 'PUT',
      },
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    )
  },
  removeStaff(staffId: string) {
    return DataService.delete(`/staff/${staffId}`)
  },
  fetchStaffServices(staffUuid: string, page: any) {
    return DataService.get<{ services: Service[] }>(
      `/staff/${staffUuid}/services?page=${page}`,
    )
  },
  toggleStaffService(staff: Staff, service: Service) {
    return DataService.post(`/staff/${staff.uuid}/services`, {
      service_id: service.uuid,
    })
  },

  enableHours(staffId: string) {
    return DataService.post(`staff/${staffId}/workingHour`)
  },
  disbaleHours(staffId: string) {
    return DataService.delete(`staff/${staffId}/workingHour`)
  },
  fetchHours(staffId: string) {
    return DataService.get(`staff/${staffId}/workingHour`)
  },
  fetchStaffBoundaries(staffId: string, page = 1) {
    return DataService.get(`staff/${staffId}/boundaries?page=${page}`)
  },
  toggleStaffBoundaries(staffId: string, boundary_id: string) {
    return DataService.post(`staff/${staffId}/boundaries`, {
      boundary_id,
    })
  },
  createUserStaff(staffId: string, payload: Object) {
    return DataService.post(`/staff/${staffId}/create-user-staff`, payload)
  },
}
