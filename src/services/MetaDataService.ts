import DataService from './Common/DataService'
import type { MetaData } from '@/types/metaData'

export default {
  fetchMetaData(scoped: Boolean = true) {
    let uri = '/metadata'

    if (!scoped)
      uri = `${uri}?withoutScope=1`

    return DataService.get<{ [key: string]: MetaData[] }>(uri)
  },
  fetchTeamMetaData(teamUuid) {
    return DataService.get(`/metadata/${teamUuid}`)
  },
  upsertMetaData(payload: MetaData) {
    return DataService.post('/metadata', payload)
  },

  deleteMetaData(metaDataUuid: string) {
    return DataService.delete(`/metadata/${metaDataUuid}`)
  },
}
