import DataService from './Common/DataService'

export default {
  fetchCategories(type: string, team: string | null) {
    if (!team)
      return DataService.get(`/categories?type=${type}`)
    return DataService.get(`/categories?type=${type}&team=${team}`) // Fix: Use '&' instead of '?'
  },
  createCategory(payload: {
    name: string
    type: string
  }) {
    return DataService.post('/categories', payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  fetchCategoryById(categoryId: string) {
    return DataService.get(`/categories/${categoryId}`)
  },
  updateCategory(categoryId: string, payload: { [key: string]: string }) {
    return DataService.post(`/categories/${categoryId}`, {
      ...payload,
      _method: 'PUT',
    },
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  removeCategory(categoryId: string) {
    return DataService.delete(`/categories/${categoryId}`)
  },
}
