import type { Coupons, CouponInfoPayload } from '../types/coupons'
import DataServices from './Common/DataService'
import convertToFormdata from '@/utils'

interface ApiResponse<T> {
  data: T;
}

export default {
  getListCoupons() {
    return DataServices.get<ApiResponse<Coupons[]>>('/coupons')
  },
  getCoupon(couponUuid: string) {
    return DataServices.get<ApiResponse<Coupons>>(`/coupons/${couponUuid}`)
  },
  deleteCoupons(couponsUuid: string) {
    return DataServices.delete(`/coupons/${couponsUuid}`)
  },
  createCoupons(coupons: Coupons) {
    const data = convertToFormdata(coupons)
    return DataServices.post('/coupons', data)
  },
  updateCouponInfo(coupons: CouponInfoPayload, couponUuid: string) {
    const data = convertToFormdata(coupons)
    return DataServices.put(`/coupons/${couponUuid}`, data)
  },
  updateCouponScope(payload: { products?: string[], categories?: string[], branches?: string[] }, couponUuid: string) {
    return DataServices.put(`/coupons/${couponUuid}/scope`, payload)
  },
  updateCouponDescription(payload: { description: { en: string; ar: string } }, couponUuid: string) {
    return DataServices.put(`/coupons/${couponUuid}/description`, payload)
  },
  updateCouponStatus(payload: { status: number }, couponUuid: string) {
    return DataServices.put(`/coupons/${couponUuid}/status`, payload)
  },
}
