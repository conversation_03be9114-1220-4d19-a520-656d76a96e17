import DataService from './Common/DataService'

export default {
  fetchStaffIntervals(StaffIntervalId: string) {
    return DataService.get(`auth/intervals/staff/${StaffIntervalId}`)
  },
  createStaffIntervals(payload: { StaffIntervalId: string
    body: {
      day: string
      from: string
      to: string
    }
  }) {
    return DataService.post(`auth/intervals/staff/${payload.StaffIntervalId}`, payload.body)
  },
  fetchStaffIntervalsById(StaffIntervalId: string) {
    return DataService.get(`auth/intervals/staff/${StaffIntervalId}`)
  },
  updateStaffIntervals(payload: { StaffIntervalId: string
    body: {
      day: string
      from: string
      to: string
    } }) {
    return DataService.put(`auth/intervals/staff/${payload.StaffIntervalId}`, payload.body)
  },
  removeStaffIntervals(payload: { StaffIntervalId: string
    body: {
      day: string
      from: string
      to: string
    } }) {
    return DataService.delete(`auth/intervals/staff/${payload.StaffIntervalId}`)
  },
}
