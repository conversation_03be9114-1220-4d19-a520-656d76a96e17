import DataService from './Common/DataService'

export default {
  fetchTeams() {
    return DataService.get('/teams')
  },
  createTeam(payload: {
    name: string
    personal_team: boolean
    industry_id: string
  }) {
    return DataService.post('/teams', payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  fetchTeamById(teamId: string) {
    return DataService.get(`/teams/${teamId}`)
  },

  updateTeam(payload: { teamId: string
    body: {
      name: string
      personal_team: string
      industry_id: string
      status: boolean
      timezone: string
      longitude: string
      latitude: string
      imageLink: { file: File | null }
    } }) {
    return DataService.post(`/teams/${payload.teamId}`,
      {
        ...payload.body,
        _method: 'PUT',
      },
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
  },
  removeTeam(teamId: string) {
    return DataService.delete(`/teams/${teamId}`)
  },

  fetchInfo() {
    return DataService.get('/team-user/list-users')
  },
  fetchHours(staffId: string) {
    return DataService.get(`team/${staffId}/workingHour`)
  },
  deleteIntervals(intervalId: string, modelId: string) {
    return DataService.delete(`/intervals/workingHour/${modelId}/${intervalId}`)
  },
  updateInterval(payload: object) {
    return DataService.put(`/intervals/workingHour/${payload.workingHourIntervalId}/${payload.intervalId}`, {
      ...payload.body,
    })
  },
  createUser(payload: object) {
    return DataService.post('/team-user/create-users', payload)
  },
  updateUser(userId: string, payload: Object) {
    return DataService.put(`/team-user/${userId}/update-users`, payload)
  },
  deactivateUser(userId: string) {
    return DataService.put(`/team-user/${userId}/deactivate-user`)
  },
  changeUserToProvider(userId: string) {
    return DataService.put(`/team-user/${userId}/change-to-provider`)
  },
  changeProviderToUser(userId: string) {
    return DataService.put(`/team-user/${userId}/change-to-user`)
  },
}
