import DataService from './Common/DataService'

export default {
  fetchCampaignsList() {
    return DataService.get('/campaigns')
  },
  getCampaignApps() {
    return DataService.get('/campaigns/list-app')
  },
  createCampaign(payload) {
    return DataService.post('/campaigns', payload)
  },
  changeCampaignStatus(campaignId: string) {
    return DataService.put(`/campaigns/${campaignId}/toggle`)
  },
  updateCampaign(payload) {
    return DataService.put(`/campaigns/${payload.uuid}`, payload)
  },
  deleteCampaign(campaignId: string) {
    return DataService.delete(`/campaigns/${campaignId}`)
  },
  checkCampaignCustomers(campaignIds) {
    return DataService.post('/campaigns/checkCustomers', {
      campaignIds,
    })
  },
}
