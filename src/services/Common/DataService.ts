import axios from 'axios'
import { storeToRefs } from 'pinia'
import router from '@/router'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
import {
  AUTH_TOKEN,
  ERR_403,
  ERR_403_Messages,
  ErrTooManyRequests,
  NOT_FOUND_CODE,
  PAGE_EXPIRED,
  SELECTED_LOCALE,
  SERVER_CODE,
  SUBSCRIPTION_ENDS,
  UNAUTHORIZED_CODE,
  VALIDATION_CODE,
} from '@/constants'
import { useServerErrors } from '@/stores/errors'
axios.defaults.withCredentials = true

const instance = axios.create({
  baseURL: `${import.meta.env.VITE_API_URL}/api/v1`,
})

// Create a source for cancelling requests
let cancelTokenSource = axios.CancelToken.source()

function detectLang() {
  const { userInfo } = storeToRefs(useAuthStore())
  return userInfo?.value?.lang || localStorage.getItem(SELECTED_LOCALE) || i18n.global.locale.value || 'ar'
}
instance.interceptors.request.use((request) => {
  if (!request?.headers)
    return request
  request.headers['Accept-Language'] = detectLang()

  let authToken = localStorage.getItem(AUTH_TOKEN)
  if (!authToken && import.meta.env.DEV) {
    authToken = import.meta.env.VITE_API_TOKEN
  }
  if (authToken) {
    request.headers.Authorization = `Bearer ${authToken}`
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.debug('[API] Using token:', authToken)
    }
  }
  // Add cancel token to each request
  request.cancelToken = cancelTokenSource.token

  return request
})

instance.interceptors.response.use(
  (response) => {
    const { clearServerErrors } = useServerErrors()
    const { getServerErrors } = storeToRefs(useServerErrors())

    if (response.status === UNAUTHORIZED_CODE) {
      localStorage.clear()
      return
    }
    if (getServerErrors)
      clearServerErrors()

    return response
  },
  (error) => {
    const { setServerErrors } = useServerErrors()
    const { showNotification } = useNotifications()
    const { getSubscriptionStatus } = storeToRefs(useAuthStore())
    if (error.response.status === UNAUTHORIZED_CODE || error.response.status === PAGE_EXPIRED) {
      localStorage.clear()
      router.push({ name: 'auth', query: { section: 'sign-in' } })
    }
    else if (error.response?.status === NOT_FOUND_CODE) {
      showNotification({
        title: 'Page 404',
        type: 'error',
        message: i18n.global.t('operations.notFound'),
      })
      throw { ...error.response.data }
    }
    else if (error.response.status === SUBSCRIPTION_ENDS) {
      // Cancel all pending requests
      cancelTokenSource.cancel('Subscription ended')
      // Create new source for future requests
      cancelTokenSource = axios.CancelToken.source()

      showNotification({
        title: 'Subscription',
        type: 'error',
        message: i18n.global.t('operations.subscriptionEnded'),
      })
      if (router.currentRoute.value.name === 'plans')
        return
      if (getSubscriptionStatus.value === 'past_due') {
        router.push({ name: 'current-plan', query: { renewNow: true } })
        return
      }
      router.push({ name: 'plans' })
    }
    else if (error.response.status === ERR_403) {
      if (error.response.data?.message === ERR_403_Messages.UnverifiedEmail) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: i18n.global.t('unverified_email.alert'),
        })
        router.push({ name: 'unverified-email' })
      }
      else if (error.response.data?.message === ERR_403_Messages.UnCompletedProfile) {
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: i18n.global.t('uncompleted_profile_alert'),
        })
        router.push({ name: 'onboarding' })
      }
      else {
        showNotification({
          title: 'Error',
          type: 'error',
          message: error.response.data?.message || 'somthing went wrong , please try again later',
        })
      }
    }
    const errorData = error.response.data
    if (error.response.status === VALIDATION_CODE) {
      setServerErrors(errorData?.errors)

      throw { ...errorData }
    }
    else if (error.response.status === SERVER_CODE) {
      showNotification({
        title: 'Error',
        type: 'error',
        message: errorData?.message,
      })
    }
    else if (error.response.status === ErrTooManyRequests) {
      showNotification({
        title: 'Error',
        type: 'error',
        message: i18n.global.t('tooManyRequests'),
      })
    }
    // eslint-disable-next-line no-throw-literal
    throw { ...errorData }
  },
)

export default instance
