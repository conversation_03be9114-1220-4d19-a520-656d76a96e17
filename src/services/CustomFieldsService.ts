import DataService from './Common/DataService'
import type { CustomField } from '@/types/customfields'

export default {
  fetchCustomFields(scoped: Boolean = true) {
    let uri = '/customFields'

    if (!scoped)
      uri = `${uri}?withoutScope=1`

    return DataService.get<{ [key: string]: CustomField[] }>(uri)
  },
  fetchTeamCustomFields(teamUuid) {
    return DataService.get(`/customFields/${teamUuid}`)
  },
  upsertCustomField(payload: CustomField) {
    return DataService.post('/customFields', payload)
  },

  deleteCustomField(customFieldUuid: string) {
    return DataService.delete(`/customFields/${customFieldUuid}`)
  },

}
