import DataService from './Common/DataService'
interface OnBoardingData {
  current_step?: number
  company_name: string
  industry: string
  number_of_employees: string
  staffs: Array<{ name: string }>
  services: Array<{ name: string; duration: string; price: string }>
}

export interface IndustryCollection {
  uuid: string
  name: string
  slug: string
}

export default {

  updateLang(payload: object) {
    return DataService.put('update-locale', payload)
  },
  updateOnboarding(payload: OnBoardingData) {
    return DataService.post('on-boarding', payload)
  },
  getOnBoarding() {
    return DataService.get<OnBoardingData>('on-boarding')
  },
  createAccount(payload: Object) {
    return DataService.post('/auth/create-account', payload)
  },

}
