import { parseClassNames } from '@fullcalendar/core/internal'
import DataService from './Common/DataService'
import type { Attachment, Customer, Notes, Paginate } from '@/types'

export default {
  fetchCustomer(page: number, params: Object) {
    return DataService.get<Paginate<Customer>>('/customers', {
      params: {
        page,
        ...params,
      },
    })
  },

  fetchCustomerByNameOrPhone(page: number, search: string) {
    return DataService.get<Paginate<Customer>>(`/customers_search?page=${page}&${search}`)
  },

  createCustomer(payload: Omit<Customer, 'uuid'>) {
    return DataService.post('/customers', payload)
  },
  fetchCustomerById(customerId: string) {
    return DataService.get<Customer>(`/customers/${customerId}`)
  },
  updateCustomer(customerId: string, payload: { body: Omit<Customer, 'uuid'> }) {
    return DataService.patch<Customer>(`/customers/${customerId}`, payload.body)
  },
  removeCustomer(customerId: string) {
    return DataService.delete(`/customers/${customerId}`)
  },
  fetchCustomerBookings(customerId: string) {
    return DataService.get(`/customers/${customerId}/bookings`)
  },
  fetchCustomerInvoices(customerId: string) {
    return DataService.get(`/customers/${customerId}/orders`)
  },
  getCustomerNotes(customerUuid: string) {
    return DataService.get(`customers/${customerUuid}/notes`)
  },
  deleteNotes(noteUuid: string, customerUuid: string) {
    return DataService.delete(`/customers/${customerUuid}/notes/${noteUuid}`)
  },
  createNotes(customerUuid: string, notes: Notes) {
    return DataService.post(`/customers/${customerUuid}/notes`, notes)
  },
  getCustomerAttachment(customerUuid: string) {
    return DataService.get(`attachments/customer/${customerUuid}/show-attachments`)
  },
  deleteAttachment(attachUuid: string) {
    return DataService.delete(`/attachments/${attachUuid}`)
  },
  createAttachment(customerUuid: string, attaches: Attachment) {
    return DataService.post(`/attachments/customer/${customerUuid}/store`, attaches, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  blockCustomer(customerUuid: string) {
    return DataService.post(`/customers/${customerUuid}/block`)
  },
}
