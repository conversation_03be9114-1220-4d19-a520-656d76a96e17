import DataService from './Common/DataService'

export default {
  fetchTransactions(page: number, filters: object) {
    return DataService.get('/transactions', {
      params: {
        page,
        ...filters,
      },
    })
  },
  updateTransStatus(transUuid: string, status: string) {
    return DataService.put(`/transactions/${transUuid}/${status}`)
  },
  updateTransaction(transUuid: string, formData: { date: string; note: string }) {
    return DataService.post(`/transactions/${transUuid}`, formData)
  },
  fetchBookingTransactionById(uuid: string) {
    return DataService.get(`/bookings/${uuid}/transactions`)
  },
  fetchTransactionById(uuid: string) {
    return DataService.get(`transactions/${uuid}`)
  },
  deleteTransaction(uuid: string) {
    return DataService.delete(`transactions/${uuid}`)
  },
}
