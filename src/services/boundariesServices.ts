import DataService from './Common/DataService'
export default {
  fetchBoundaries(page = 1, filter: string | null = null) {
    if (!filter)
      return DataService.get(`/boundaries?page=${page}`)
    return DataService.get(`/boundaries?${filter}`)
  },
  fetchSingleBoundary(uuid: string) {
    return DataService.get(`/boundaries/${uuid}`)
  },
  createBoundary(payload: any) {
    return DataService.post('/boundaries', payload)
  },
  updateBoundary(uuid: string, payload: any) {
    return DataService.put(`/boundaries/${uuid}`, payload)
  },
  deleteBoundary(uuid: string) {
    return DataService.delete(`/boundaries/${uuid}`)
  },
}
