import DataService from './Common/DataService'
type ReasonType = 'booking-cancel' | 'invoice-refund'
interface Reason {
  uuid: string
  name: string
  type: ReasonType
}

export default {
  getReasons(type?: ReasonType) {
    const params = {
      type,
    }
    return DataService.get('/reasons', {
      params,
    })
  },
  getReason(uuid: string) {
    const api = `/reasons/${uuid}`
    return DataService.get(api)
  },
  createReason(payload: omit<Reason, 'uuid'>) {
    const api = '/reasons'
    return DataService.post(api, payload)
  },
  updateReason(payload: Reason) {
    const api = `/reasons/${payload.uuid}`
    return DataService.put(api, payload)
  },
  deleteReason(uuid: string) {
    const api = `/reasons/${uuid}`
    return DataService.delete(api)
  },
}
