import type { AxiosResponse } from 'axios'
import DataService from './Common/DataService'

export interface Invoice {
  id: number
  created_at: string
  invoice_url: string
}

export interface BillingAddress {
  billing_name: string
  billing_country: string
  billing_address: string
  vat_id: string
}

export default {
  fetchInvoices(page = 1, perPage = 20): Promise<AxiosResponse<Invoice[]>> {
    return DataService.get('/subscription/invoices', { params: { page, per_page: perPage } })
  },

  downloadInvoice(id: number): Promise<AxiosResponse<Blob>> {
    return DataService.get(`/subscription/invoices/${id}/download`, { responseType: 'blob' })
  },

  updateBillingAddress(data: BillingAddress): Promise<AxiosResponse> {
    return DataService.post('/subscription/invoices/billing-address', data)
  },

  getBillingAddress(): Promise<AxiosResponse<BillingAddress>> {
    return DataService.get('/subscription/invoices/billing-address')
  },
}
