import DataService from './Common/DataService'

export default {

  fetchInvoice(uuid: string) {
    return DataService.get(`/pos/${uuid}`)
  },
  fetchPosData(uuid: string) {
    return DataService.get(`/pos/${uuid}/items`)
  },
  createPos(data: Object) {
    return DataService.post('/centralized-orders/pos/create-order', data)
  },
  checkout(uuid: string, data: Object) {
    return DataService.post(`/pos/${uuid}/pay`, data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  checkoutCalculate(data: Object) {
    return DataService.post('/pos/checkout-calculate', data)
  },
  print(uuid: string) {
    return DataService.get(`/pos/${uuid}/invoice`)
  },
  deleteInvoice(uuid: string) {
    return DataService.delete(`/pos/${uuid}`)
  },

  fetchSales(page: number, search: string) {
    return DataService.get(`/invoices?page=${page}&${search}`)
  },

  fetchPaymentLink(orderId: string) {
    return DataService.post(`/pos/orders/${orderId}/generate-payment-link`)
  },

  getInvoiceHtml(uuid: string) {
    return DataService.get(`/print-invoice/${uuid}`)
  },
  previewInvoicePdf(uuid: string) {
    return DataService.get(`/invoices/${uuid}/pdf-invoice`, {
      responseType: 'blob',
    }).then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `${uuid}.pdf`)
      document.body.appendChild(link)
      link.click()
      link.remove()
    })
  },
  downloadInvoicePdf(uuid: string) {
    return DataService.get(`/invoices/${uuid}/pdf-invoice?download`)
  },

  sendInvoicePdf(uuid: string) {
    return DataService.get(`/invoices/${uuid}/send-invoice`)
  },

  sendWhatsAppInvoice(uuid: string) {
    return DataService.get(`/invoices/${uuid}/send-invoice-whatsapp`)
  },

  refundInvoice(uuid: string) {
    return DataService.post(`/pos/${uuid}/refund`)
  },

  getPaymentMethods() {
    return DataService.get('/payments/list-pos')
  },
}
