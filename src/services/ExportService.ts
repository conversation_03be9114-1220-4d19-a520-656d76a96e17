import type { AxiosRequestHeaders } from 'axios'
import DataService from './Common/DataService'
import type { ExportableModels } from '@/types'

export default {
  excelExport(model: ExportableModels, filters?: Object, headers: undefined | AxiosRequestHeaders = {
    'Content-Disposition': `attachment; filename=${model}.xlsx`,
    'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  }) {
    return DataService.get<Promise<any>>('/exports', {
      headers,
      responseType: headers['Content-Type'] === 'application/json' ? 'json' : 'blob',
      params: {
        ...filters,
        export_type: 'xlsx',
        export_source: model,
      },
    })
  },
}
