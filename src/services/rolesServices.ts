import type { Roles } from '../types/roles'
import DataServices from './Common/DataService'

export default {
  getAllRoles() {
    return DataServices.get<Roles[]>('/roles')
  },
  getAllPermission() {
    return DataServices.get('/permissions')
  },
  deleteRoles(couponsUuid: string) {
    return DataServices.delete(`/roles/${couponsUuid}`)
  },
  createRoles(coupons: Roles) {
    return DataServices.post('/roles', coupons)
  },
  updateRoles(coupons: Roles, rolesUuid: string) {
    return DataServices.put(`/roles/${rolesUuid}`, coupons)
  },
}
