import DataService from './Common/DataService'
export default {

  createInterval(workingHourId: string, payload: any) {
    return DataService.post(`/intervals/workingHour/${workingHourId}`, payload)
  },
  updateInterval(workingHourId: string, intervalId: string, payload: any) {
    return DataService.put(
      `/intervals/workingHour/${workingHourId}/${intervalId}`,
      {
        ...payload,
      },
    )
  },
  deleteInterval(workingHourId: string, intervalId: string) {
    return DataService.delete(`/intervals/workingHour/${workingHourId}/${intervalId}`)
  },
}
