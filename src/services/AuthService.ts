import axios from 'axios'
import DataService from './Common/DataService'
import type { DomainsCollection, ForgotPasswordPayload, IndustryCollection, LoginPayload, RegisterUserPayload, ResetPasswordPayload, User, verifyOtpPayload } from '@/types'

export default {
  getCookie() {
    axios.defaults.withCredentials = true
    const URL = import.meta.env.VITE_API_URL
    return axios.get(`${URL}/sanctum/csrf-cookie`)
  },
  login(payload: LoginPayload) {
    return DataService.post('auth/login', payload)
  },
  register(payload: RegisterUserPayload) {
    return DataService.post('auth/register', payload)
  },
  forgotPassword(payload: ForgotPasswordPayload) {
    return DataService.post('auth/forgot-password', payload)
  },
  resetPassword(payload: ResetPasswordPayload) {
    return DataService.post('auth/reset-password', payload)
  },
  verifyOtp(payload: verifyOtpPayload) {
    return DataService.post('auth/verify-otp', payload)
  },
  resendOtp(email: string) {
    return DataService.post('auth/resend-otp', { email })
  },
  fetchUser() {
    return DataService.get<User>('auth/me')
  },
  logout() {
    return DataService.post('auth/logout')
  },
  UpdateUserProfile(payload: { name: string; password: string; profile_photo?: File | null }) {
    return DataService.post('update-profile', payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  updateAccountPassword(payload: { current_password: string; password: string; password_confirmation: string }) {
    return DataService.post('auth/update-password', payload)
  },
  updateAccountEmail(payload: { new_email: string; current_password: string }) {
    return DataService.post('auth/change-email', payload)
  },
  sendVerificationLink() {
    return DataService.post('auth/email/verification-notification')
  },
  sendVerificationEmail(payload) {
    return DataService.post('auth/resend-mail', payload)
  },
  verifyEmailAddress(link: string) {
    return DataService.get(link)
  },
  doImpersonation(payload: object) {
    return DataService.post('auth/impersonate', payload)
  },
  stopImpersonate() {
    return DataService.delete('auth/stop-impersonation')
  },
  updateTenantInfo(payload: { tenant_type: string; tenant_creator_role: string; number_of_employees: string; tenant_benefits: string[] }) {
    return DataService.post('auth/update-tenant-info', {
      ...payload,
      completed_level: 3,
    })
  },
  updateTenantProfile(payload: { name: string; industry_id: string; location: string; domain: string }) {
    return DataService.post('auth/update-tenant-profile', {
      ...payload,
      completed_level: 4,

    })
  },
  completeProfile(payload: { staffs: Array<{ name: string }>; services: Array<{ name: string; price: number }> }) {
    return DataService.post('auth/complete-profile', {
      ...payload,
      completed_level: 5,
    })
  },
  fetchIndustries() {
    return DataService.get<IndustryCollection[]>('fetchIndustries')
  },
  getSuggestedDomains(payload?: { name: string }) {
    return DataService.get<DomainsCollection[]>('auth/suggestion-domains', {
      params: payload,
    })
  },
  updateOnboardingStatus() {
    return DataService.get('auth/onboarding-status')
  },
  skipOnboarding() {
    return DataService.get('/auth/onboarding-skip')
  },
}
