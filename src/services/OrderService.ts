import type { AxiosResponse } from 'axios'
import DataService from '../services/Common/DataService'
import type {
  Order,
  OrderParams,
  OrderStatus,
  Paginate,
  PaginateParams,
} from '@/types'
import convertToFormdata from '@/utils'
export default {
  fetchOrdersStatus() {
    return DataService.get<OrderStatus[]>('/load-orders-status')
  },
  fetchOrders(params: OrderPareturnrams = {}): Promise<AxiosResponse<Paginate<Order>>> {
    return DataService.get('/orders', { params })
  },
  fetchOrderById(orderId: string) {
    return DataService.get<Order>(`/orders/${orderId}`)
  },
  updateOrderStatus(payload: Record<string, any>) {
    return DataService.post('/orders/orderId/change-status', payload)
  },
  fetchUnreadModulesCount() {
    return DataService.get('/load-unread-modules-counts')
  },
  updateOrderMetaData(orderId: string, payload: Record<string, any>) {
    const data = convertToFormdata(payload)
    return DataService.post(`/orders/${orderId}/update-meta-data`, data)
  },
  updateOrderNote(orderId: string, payload: Record<string, any>) {
    return DataService.post(`/orders/${orderId}/update-note`, payload)
  },
  updateOrder(orderId: string, payload: Record<string, any>) {
    return DataService.put(`/orders/${orderId}`, payload)
  },
  updateOrderLocation(orderId: string, payload: Record<string, any>) {
    return DataService.post(`/orders/${orderId}/edit-location`, payload)
  },

  getOrderNotifications(orderId: string) {
    return DataService.get(`/orders/${orderId}/notifications`)
  },

  deleteOrder(orderId: string) {
    return DataService.delete<Order>(`/orders/${orderId}`)
  },
  setAppointment(orderId: string, payload: Record<string, any>) {
    return DataService.post(`/order/${orderId}/createBooking`, payload)
  },
  assignCustomerToOrder(orderId: string, customerId: string) {
    return DataService.put(`/orders/${orderId}/update-customer/${customerId}`)
  },
  createOrderInvoice(orderId: string) {
    return DataService.post<Order>(`/orders/${orderId}/invoice`)
  },
  createOrder(payload: Record<string, any>) {
    const data = convertToFormdata(payload)
    return DataService.post('/orders', data)
  },
  getRefundItems(orderId: string) {
    return DataService.get(`/orders/${orderId}/refund-items`)
  },
  refundOrder(orderId: string, payload: any) {
    return DataService.post(`/orders/${orderId}/refund`, payload)
  },
  refundOrders(params: any) {
    return DataService.get('/refunds')
  },
  addOrderItem(orderId: string, payload: any) {
    return DataService.post<Order>(`/orders/${orderId}/items`, payload)
  },
  deleteOrderItem(orderId: string, item: string) {
    return DataService.delete<Order>(`/orders/${orderId}/${item}`)
  },
  updateOrderDiscount: async (orderId: string, payload: {
    discount_type: string
    discount_amount: number
    apply_all: boolean
    items: string[]
  }) => {
    console.log(payload)
    return await DataService.put<Order>(`/orders/${orderId}/discount`, payload)
  },
  removeOrderDiscount: async (orderId: string) => {
    return await DataService.delete<Order>(`/orders/${orderId}/undiscount`)
  },
}
