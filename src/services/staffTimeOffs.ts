import DataService from './Common/DataService'
export default {
  addTimeOffs(staffUuid: string, payload: any) {
    return DataService.post(`/staff/${staffUuid}/time-offs`, payload)
  },
  getTimeOffs(staffUuid: string) {
    return DataService.get(`/staff/${staffUuid}/time-offs`)
  },
  deleteTimeOffs(staffUuid: string, timeOffsUuid: string) {
    return DataService.delete(`/staff/${staffUuid}/time-offs/${timeOffsUuid}`)
  },
  updateTimeOffs(staffUuid: string, staffTimeOffs: any) {
    return DataService.put(`/staff/${staffUuid}/time-offs/${staffTimeOffs.uuid}`, staffTimeOffs)
  },

  addBreaks(staffUuid: string, payload: any) {
    const reqPayload = {
      start: `${payload.start_time.hours}:${payload.start_time.minutes}`,
      end: `${payload.end_time.hours}:${payload.end_time.minutes}`,
      day: payload.day,
    }
    return DataService.post(`/staff/${staffUuid}/breaks`, reqPayload)
  },
  getBreaks(staffUuid: string) {
    return DataService.get(`/staff/${staffUuid}/breaks`)
  },
  deleteBreaks(staffUuid: string, timeOffsUuid: string) {
    return DataService.delete(`/staff/${staffUuid}/breaks/${timeOffsUuid}`)
  },
  updateBreaks(staffUuid: string, staffTimeOffs: any) {
    staffTimeOffs.start = `${staffTimeOffs.start_time.hours}:${staffTimeOffs.start_time.minutes}`
    staffTimeOffs.end = `${staffTimeOffs.end_time.hours}:${staffTimeOffs.end_time.minutes}`
    delete staffTimeOffs.start_time
    delete staffTimeOffs.end_time
    const id = staffTimeOffs.id
    delete staffTimeOffs.id
    return DataService.put(`/staff/${staffUuid}/breaks/${id}`, staffTimeOffs)
  },
}
