import DataService from './Common/DataService'

export default {
  fetchComments(page: number) {
    return DataService.get(`comments?page=${page}`)
  },

  changeStatus(commentId: string, published: boolean) {
    return DataService.put(`comments/${commentId}`, { published })
  },
  Reply(comment: any, reply: any) {
    return DataService.post(`comments/${comment}/reply`, { reply: reply.content })
  },
}
