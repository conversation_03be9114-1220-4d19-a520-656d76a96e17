import type { AxiosResponse } from 'axios'
import DataService from './Common/DataService'
import type { AppType } from '@/types'

export default {
  fetchApps() {
    return DataService.get('apps')
  },
  installApp(appId: string, payload: object) {
    return DataService.put(`apps/${appId}/toggle`, payload)
  },
  fetchAppCategories(): Promise<AxiosResponse> {
    return DataService.get('app-categories/list')
  },
  fetchAppsByCategory(categoryId: string): Promise<AxiosResponse<AppType>> {
    return DataService.get(`app-categories/${categoryId}/apps`)
  },
  uninstallApp(appId: string) {
    return DataService.delete(`apps/${appId}/uninstall `)
  },
  fetchGoogleCalendarSyncLink(staffId: string) {
    return DataService.get(`apps/${staffId}/googleCalendar`)
  },
  unSyncGoogleCalendar(staffId: string) {
    return DataService.delete(`apps/${staffId}/googleCalendar`)
  },

  getAppConfigurations(appId: string) {
    return DataService.get(`apps/${appId}/configurations`)
  },

  submitConfig(appId: string, payload: object) {
    return DataService.post(`apps/${appId}/configurations`, payload)
  },
}
