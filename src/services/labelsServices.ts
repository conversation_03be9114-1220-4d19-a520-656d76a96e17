import type { Labels } from '../types/labels'
import DataServices from './Common/DataService'

export default {
  getAllLabels() {
    return DataServices.get<Labels[]>('/labels')
  },
  deleteLabels(uuid: string) {
    return DataServices.delete(`/labels/${uuid}`)
  },
  createLabels(payload: Labels) {
    return DataServices.post('/labels', payload)
  },
  updateLabels(payload: Labels, uuid: string) {
    return DataServices.put(`/labels/${uuid}`, payload)
  },
}
