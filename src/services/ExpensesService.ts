import DataService from './Common/DataService'

export default {
  fetchExpenses(page = 1) {
    return DataService.get(`/expenses?page=${page}`)
  },
  createExpense(payload: any) {
    return DataService.post('/expenses', payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  updateExpense(expenseId: string, payload: any) {
    return DataService.post(`/expenses/${expenseId}`, payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  deleteExpense(expenseId: string) {
    return DataService.delete(`/expenses/${expenseId}`)
  },

  fetchExpenseTypes() {
    return DataService.get('/type-expenses')
  },
  createExpenseType(payload: any) {
    return DataService.post('/type-expenses', payload)
  },
  updateExpenseType(expenseTypeId: string, payload: any) {
    return DataService.put(`/type-expenses/${expenseTypeId}`, payload)
  },
  deleteExpenseType(expenseTypeId: string) {
    return DataService.delete(`/type-expenses/${expenseTypeId}`)
  },
}
