import type { Offers } from '../types/offers'
import DataServices from './Common/DataService'

export default {
  getListOffers() {
    return DataServices.get< Offers[]>('/offers')
  },
  deleteOffers(couponsUuid: string) {
    return DataServices.delete(`/offers/${couponsUuid}`)
  },
  createOffers(offers: Offers) {
    return DataServices.post('/offers', offers)
  },
  updateOffers(offers: Offers, uuid: string) {
    return DataServices.put(`/offers/${uuid}`, offers)
  },
}
