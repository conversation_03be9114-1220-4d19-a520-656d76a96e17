import DataService from './Common/DataService'
import type { bankAccount } from '@/types/bankAccount'
import type { newAccount } from '@/types/newAccounts'

export default {
  bankList() {
    return DataService.get('/bankaccounts/list')
  },
  addBankAccountFun(payload: bankAccount) {
    return DataService.post('/bankaccounts', payload)
  },
  fetchPayements() {
    return DataService.get('/payments/list')
  },
  togglePaymentMethod() {
    return DataService.post('/payments/method/on-arrival/toggle')
  },
  getPayOnArrival() {
    return DataService.get('/payments/get-pay-onArrival')
  },
  updateAccount(payload: newAccount) {
    const id = payload.id
    delete payload.id
    return DataService.put(`/bankaccounts/${id}`, payload)
  },
  deleteAccount(id: string) {
    return DataService.delete(`/bankaccounts/${id}`)
  },
}
