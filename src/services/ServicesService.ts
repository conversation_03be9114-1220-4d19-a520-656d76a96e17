import DataService from './Common/DataService'
import type { Paginate, Service, Staff } from '@/types'
import convertToFormdata from '@/utils'
export default {
  fetchService(page: number, params: Object) {
    return DataService.get<Paginate<Service>>('/services', {
      params: { page, ...params },
    })
  },
  fetchAllService() {
    return DataService.get<Service>('/get-all-services', {
    })
  },
  fetchServicesStaff(service: Service) {
    return DataService.get<Paginate<Service>>(`/services/${service.uuid}/staff`)
  },
  removeService(serviceUuid: string) {
    return DataService.delete(`/services/${serviceUuid}`)
  },
  createService(payload: Omit<Service, 'uuid'>) {
    return DataService.post('/services', payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  fetchServiceById(serviceUuid: string) {
    return DataService.get<Service>(`/services/${serviceUuid}`)
  },
  fetchPackageById(packageId: string) {
    return DataService.get<Service>(`/package/${packageId}`)
  },

  updateService(serviceUuid: string, payload: { body: Omit<Service, 'uuid'> }) {
    return DataService.put<Service>(`/services/${serviceUuid}`, payload.body)
  },
  removePakcage(packageUuid: string) {
    return DataService.delete(`/package/${packageUuid}`)
  },
  toggleServicesStaff(service: Service, staff: Staff) {
    return DataService.post(`/services/${service.uuid}/staff`, {
      staff_id: staff.uuid,
    })
  },
  updateServicesStaff(service: Service, staffData: { [key: string]: any }) {
    return DataService.post(`/services/${service.uuid}/updateStaff`, staffData)
  },
  removeServiceStaff(serviceUuid: string, staffId: string) {
    return DataService.delete(`/services/${serviceUuid}/staff`, {
      data: { staff_id: staffId },
    })
  },
  addImageToService(serviceUuid: string, payload: FormData) {
    return DataService.post<Service>(`/services/${serviceUuid}/images`, payload)
  },
  addImagesToService(serviceUuid: string, payload: FormData) {
    return DataService.post<Service>(`/services/${serviceUuid}/uploadedImages`, payload)
  },
  removeImageFromService(serviceUuid: string, imageUuid: string) {
    return DataService.delete(`/services/${serviceUuid}/images/${imageUuid}`)
  },
  makeMainImage(serviceUuid: string, imageUuid: string) {
    return DataService.put(`/services/${serviceUuid}/images/${imageUuid}/make-main-image`)
  },
  fetchPacakges(page: number, params: Object) {
    return DataService.get<Paginate<Service>>('/package', {
      params: { page, ...params },
    })
  },
  fetchAllPackages() {
    return DataService.get<Service>('/get-all-packages', {
    })
  },
  fetchPacakgesSold(params: Record<string, any>) {
    return DataService.get<Paginate<any>>('/purchased-packages', {
      params,
    })
  },
  fetchPacakgeSold(uuid: string) {
    return DataService.get<any>(`/purchased-packages/${uuid}`)
  },
  createPackage(payload: Omit<Service, 'uuid'>) {
    return DataService.post('/package', payload)
  },
  updatePackage(PackageUuid: string, payload: { body: Omit<Service, 'uuid'> }) {
    return DataService.put<Service>(`/package/${PackageUuid}`, payload.body)
  },
  addImageToPackage(PackageUuid: string, payload: FormData) {
    return DataService.post<Service>(`/packages/${PackageUuid}/images`, payload)
  },
  removeImageFromPackage(PackageUuid: string, imageUuid: string) {
    return DataService.delete(`/packages/${PackageUuid}/images/${imageUuid}`)
  },
  makeMainImagePackage(PackageUuid: string, imageUuid: string) {
    return DataService.put(`/packages/${PackageUuid}/images/${imageUuid}/make-main-image`)
  },

  deleteServiceOfPackage(PackageUuid: string, serviceUuid: string) {
    return DataService.delete(`/packages/${PackageUuid}/services/${serviceUuid}`)
  },
}
