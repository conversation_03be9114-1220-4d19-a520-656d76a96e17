import DataService from './Common/DataService'

export default {
  getPaymentMethodsList() {
    return DataService.get('/load-payment-methods')
  },
  getPaymentMethods() {
    return DataService.get('/payment-methods-new')
  },
  getPaymentMethod(uuid: string) {
    return DataService.get(`/payment-methods-new/${uuid}`)
  },
  createPaymentMethods(payload: any) {
    return DataService.post('/payment-methods-new', payload)
  },
  updatePaymentMethods(payload: any) {
    return DataService.put(`/payment-methods-new/${payload.id}`, payload)
  },
  deletePaymentMethods(uuid: string) {
    return DataService.delete(`/payment-methods-new/${uuid}`)
  },
}
