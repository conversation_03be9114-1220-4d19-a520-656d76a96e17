import DataService from './Common/DataService'

export default {
  fetchWorkingHourInterval(payload: { workingHourIntervalId: string }) {
    return DataService.get(`/intervals/workingHour/${payload.workingHourIntervalId}`)
  },
  createWorkingHourInterval(payload: { workingHourIntervalId: string
    body: {
      day: string
      from: string
      to: string
    } }) {
    return DataService.post(`/intervals/workingHour/${payload.workingHourIntervalId}`, payload.body)
  },
  fetchWorkingHourIntervalById(workingHourIntervalId: string) {
    return DataService.get(`/intervals/workingHour/${workingHourIntervalId}`)
  },
  deleteInterval(payload: { workingHourId: string; intervalId: string }) {
    return DataService.delete(`intervals/workingHour/${payload.workingHourId}/${payload.intervalId}`)
  },
  updateWorkingHourInterval(payload: {
    workingHourIntervalId: string
    intervalId: string
    body: {
      day: string
      from: string
      to: string
      active: boolean
    } }) {
    return DataService.put(`/intervals/workingHour/${payload.workingHourIntervalId}/${payload.intervalId}`, payload.body)
  },
}

