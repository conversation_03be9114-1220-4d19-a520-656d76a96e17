import DataService from './Common/DataService'

export default {
  fetchLatetsOrders(page: string) {
    return DataService.get(`/dash/latest-orders?page=${page}`)
  },
  fetchTodayAppointments(page: string | number) {
    return DataService.get(`/dash/today-appointments?page=${page}`)
  },
  fetchSalesStatistics(from: string, to: string) {
    return DataService.get(`/dash/sales?start_date=${from}&end_date=${to}`)
  },
  fetchTopServices() {
    return DataService.get('/dash/services')
  },
  fetchSourceStatistics() {
    return DataService.get('/dash/sources')
  },

}
