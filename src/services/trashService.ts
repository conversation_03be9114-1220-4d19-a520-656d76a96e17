import type { AxiosResponse } from 'axios'
import DataService from './Common/DataService'
import type { Order } from '@/types'
interface TrashedOrdersResponse {
  orders: Order[]
}
export default {
  fetchTrashedOrders(page: string): Promise<AxiosResponse<TrashedOrdersResponse>> {
    return DataService.get<TrashedOrdersResponse>(`/orders/trashed?page=${page}`)
  },
  restoreOrder(orderId: string): Promise<AxiosResponse<any>> {
    return DataService.post<any>(`/orders/restore/${orderId}`)
  },
  forceDeleteOrder(orderId: string): Promise<AxiosResponse<any>> {
    return DataService.delete<any>(`/orders/delete-force/${orderId}`)
  },
}
