import DataService from './Common/DataService'
import type { ServiceMetricsResponse } from '@/types'

export default {
  getServiceMetrics(): Promise<ServiceMetricsResponse> {
    return DataService.get('service-metrics')
  },
  assignService(serviceId: string, staffId: string): Promise<void> {
    const payload = {
      service_id: serviceId,
    }
    return DataService.post(`staff/${staffId}/services`, payload)
  },
}
