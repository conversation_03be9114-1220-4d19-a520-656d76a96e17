import DataService from './Common/DataService'

export default {
  fetchSyncedInvoices(page: number, syncStatus: string) {
    return DataService.get(`/invoices/synced?page=${page}&syncStatus=${syncStatus}`)
  },

  checkWafeqInstallationStatus() {
    return DataService.get('/tenant/checkWafeqInstallationStatus')
  },

  sendInvoiceToWafeq(id: string) {
    return DataService.post(`/invoices/${id}/sendInvoiceToWafeq`)
  },
}
