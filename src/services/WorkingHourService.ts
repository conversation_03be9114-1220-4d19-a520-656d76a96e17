import DataService from './Common/DataService'

export default {
  fetchWorkingHours(model: string, staffId: string) {
    return DataService.get(`/working_hourable/${model}/${staffId}/all`)
  },
  fetchActiveWorkingHour(model: string, staffId: string) {
    return DataService.get(`/working_hourable/${model}/${staffId}`)
  },
  createWorkingHour(model: string, staffId: string, payload: any) {
    return DataService.post(`/working_hourable/${model}/${staffId}`, payload)
  },
  updateWorkingHour(model: string, staffId: string, workingHourId: string, payload: any) {
    return DataService.put(`/working_hourable/${model}/${staffId}/${workingHourId}`, payload)
  },
  deleteWorkingHour(model: string, staffId: string, workingHourId: string) {
    return DataService.delete(`/working_hourable/${model}/${staffId}/${workingHourId}`)
  },
}
