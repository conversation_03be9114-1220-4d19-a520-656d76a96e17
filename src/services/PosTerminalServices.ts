import DataService from './Common/DataService'
import type { Products } from '@/types/products'
import type { Paginate } from '@/types'

export default {
  fetchPosTerminals(page: number, filters: Object) {
    return DataService.get<Paginate<Products>>('/pos-terminal', {
      params: { page, filters },
    })
  },
  createPosTerminal(data: Object) {
    return DataService.post('/pos-terminal', data)
  },
  updatePosTerminal(uuid: string, data: Object) {
    return DataService.put(`/pos-terminal/${uuid}`, data)
  },
  deletePosTerminal(uuid: string) {
    return DataService.delete(`/pos-terminal/${uuid}`)
  },
}
