import WahaService from './Common/WahaService'

export interface CheckStatusResponse {
  status: string
  phone: string | null
  connected: boolean
}

export interface ActivateResponse {
  connected: boolean
}

export interface DeactivateResponse {
  disconnected: boolean
}

export default {
  checkStatus() {
    return WahaService.get<CheckStatusResponse>('/waha/check-status')
  },

  activate() {
    return WahaService.post<ActivateResponse>('/waha/connect')
  },

  deactivate() {
    return WahaService.post<DeactivateResponse>('/waha/disconnect')
  },

  qrCode() {
    return WahaService.get<any>('/waha/qr-code', {
      responseType: 'blob',
    })
  },

}
