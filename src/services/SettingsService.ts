import DataService from './Common/DataService'

export default {
  fetchSettings(page: string) {
    return DataService.get(`/settings/${page}`)
  },
  updateSettings(page: string, payload: any) {
    return DataService.post(`/settings/${page}`, payload, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
  checkDomain(payload: { subdomain: string }) {
    return DataService.post(
      '/settings/booking-page-settings/check-subdomain',
      payload,
    )
  },
  updateDomain(payload: { subdomain: string }) {
    return DataService.post(
      '/settings/booking-page-settings/update-subdomain',
      payload,
    )
  },
  updateNotifications(payload: object) {
    return DataService.post('/settings/notifications', payload)
  },
  notifcationSetting() {
    return DataService.get('/generalsetting')
  },
  getAdvancedSettings(groups: string[]) {
    return DataService.get('/generalsetting/advanced-settings', {
      params: {
        groups,
      },
    })
  },
  updateGeneralCustomeSettings(payload, groupName: string) {
    return DataService.post(`/generalsetting/${groupName}`, payload)
  },

  getImageGallery() {
    return DataService.get('/gallery')
  },
  addImageGallery(payload: object) {
    return DataService.post('/gallery', payload)
  },
  deleteImageGallery(imageUuid: string) {
    return DataService.delete(`/gallery/${imageUuid}`)
  },
  getAllPlans() {
    return DataService.get('/pricing')
  },
  subscriptionPlan(planId: string) {
    return DataService.get(`/subscription/${planId}/checkout`)
  },
  checkoutSubscription(planId: string, priceId: string, payload?: {}) {
    return DataService.post(
      `/subscription/${planId}/checkout/${priceId}`,
      payload,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    )
  },
  RenewSubscription(subscription: string, payload?: {}) {
    return DataService.post(
      `/subscription/renew/${subscription}`,
      payload,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    )
  },
  getCheckout(planId: string, priceId: string, couponCode: string) {
    return DataService.get(`/subscription/${planId}/checkout/${priceId}`, {
      params: {
        coupon: couponCode ?? null,
      },
    })
  },
  getBankForSubscription() {
    return DataService.get('/subscription/get-banks')
  },
  getCurrentSubscription() {
    return DataService.get('/subscription')
  },
  cancelSubscription() {
    return DataService.get('/subscription/cancel')
  },
  resumeCanceledSubscription() {
    return DataService.get('/subscription/resume')
  },
  extendTrail() {
    return DataService.get('/subscription/extend-trail')
  },
  getInvoiceTemplates() {
    return DataService.get('/invoice-templates')
  },

  setDefaultInvoiceTemplate(templateId: string) {
    return DataService.post('/invoice-templates', { id: templateId })
  },
}
