import type { Taxes } from '../types/taxes'
import DataServices from './Common/DataService'

export default {
  getAllTaxes() {
    return DataServices.get<Taxes[]>('/taxes')
  },
  deleteTaxes(uuid: string) {
    return DataServices.delete(`/taxes/${uuid}`)
  },
  createTaxes(payload: Taxes) {
    return DataServices.post('/taxes', payload)
  },
  updateTaxes(payload: Taxes, uuid: string) {
    return DataServices.put(`/taxes/${uuid}`, payload)
  },
}
