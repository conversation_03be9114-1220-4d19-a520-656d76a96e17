import { createRouter, createWebHistory } from 'vue-router'
import asyncRoute from './asyncRoutes/index'
import AuthView from '@/pages/AuthView.vue'
import routePermissions from '@/utils/routePermissions'
import { AUTH_TOKEN } from '@/constants'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: asyncRoute,
  linkActiveClass: 'bg-gradient-to-l from-sky-400 to-green-300 text-white font-bold',

})

export default router
