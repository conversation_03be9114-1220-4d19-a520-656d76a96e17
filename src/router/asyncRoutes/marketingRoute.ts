import type { RouteRecordRaw } from 'vue-router'
import routePermissions, { routeOrders } from '@/utils/routePermissions'
export const marketingRoutes: Array<RouteRecordRaw> = [
  {
    name: 'marketing',
    path: '/marketing',
    redirect: '/reviews',
    meta: {
      permission: routePermissions.marketingModule,
      hasChildren: true,
      showInSidebar: true,
      order: routeOrders.marketing,
    },
    children: [

      {
        path: 'coupons',
        name: 'coupons',
        to: '/marketing/coupons',
        component: () => import('../../pages/CouponsView.vue'),
        meta: {
          permission: routePermissions.coupons,
          showInSidebar: true,
          icon: 'CouponsIcon',

        },
      },
      {
        path: 'offers',
        name: 'offers',
        to: '/marketing/offers',
        component: () => import('../../pages/OffersView.vue'),
        meta: {
          showInSidebar: true,
          permission: routePermissions.offers,
          icon: 'OffersIcon',
        },
      },
      {
        path: 'campaigns',
        name: 'campaigns',
        to: '/marketing/campaigns',
        component: () => import('../../pages/CampaignsView.vue'),
        meta: {
          showInSidebar: true,
          permission: routePermissions.campaigns,
          icon: 'CampaignsIcon',
        },
      },
    ],
  },
]
