import type { RouteRecordRaw } from 'vue-router'
import routePermissions, { routeOrders } from '@/utils/routePermissions'

export const storeRoutes: Array<RouteRecordRaw> = [
  {
    name: 'store',
    path: '/store',
    redirect: '/store/store-panel',
    component: () => import('../../pages/store/StoreView.vue'),
    meta: {
      permission: routePermissions.storeModule,
      showInSidebar: true,
      hasChildren: true,
      order: routeOrders.store,
    },
    children: [
      {
        path: 'store-panel',
        name: 'store-panel',
        to: '/store/store-panel',
        component: () => import('../../pages/store/StorePanel.vue'),
        meta: {
          permission: routePermissions.storeModule,
          showInSidebar: true,
          icon: 'CustomPagesIcon',
        },
      },
      {
        path: 'general',
        name: 'store-general',
        component: () => import('../../pages/store/GeneralSettings.vue'),
        meta: {
          permission: routePermissions.storeModule,
          showInSidebar: false,
          activeRoute: 'store',
        },
      },
      {
        path: 'appearance',
        name: 'store-appearance',
        component: () => import('../../pages/store/Appearance.vue'),
        meta: {
          permission: routePermissions.storeModule,
          showInSidebar: false,
          activeRoute: 'store',
        },
      },
      {
        path: 'gallery',
        name: 'store-gallery',
        component: () => import('../../pages/store/Gallery.vue'),
        meta: {
          permission: routePermissions.storeModule,
          showInSidebar: false,
          activeRoute: 'store',
        },
      },
      {
        path: 'contacts',
        name: 'store-contacts',
        component: () => import('../../pages/store/Contacts.vue'),
        meta: {
          permission: routePermissions.storeModule,
          showInSidebar: false,
          activeRoute: 'store',
        },
      },
      {
        path: 'website-management',
        name: 'store-website-management',
        component: () => import('../../pages/store/WebsiteManagement.vue'),
        meta: {
          permission: routePermissions.storeModule,
          showInSidebar: false,
          activeRoute: 'store',
        },
      },
      {
        path: 'store-settings',
        name: 'store-settings',
        component: () => import('../../pages/store/Settings.vue'),
        meta: {
          permission: routePermissions.storeModule,
          showInSidebar: false,
          activeRoute: 'store',
        },
      },
      {
        path: 'reviews',
        name: 'reviews',
        to: '/store/reviews',
        component: () => import('../../pages/ReviewsView.vue'),
        meta: {
          permission: routePermissions.reviews,
          showInSidebar: true,
          icon: 'ReviewsIcon',
        },
      },
      {
        path: 'analytics',
        name: 'storeAnalytics',
        to: '/store/analytics',
        component: () => import('../../pages/AnalyticsView.vue'),
        meta: {
          showInSidebar: true,
          permission: routePermissions.bookingPage,
          icon: 'AnalyticsIcon',
        },
      },
      {
        path: 'custom-pages',
        name: 'customPages',
        to: '/store/custom-pages',
        component: () => import('../../pages/CustomPagesView.vue'),
        meta: {
          showInSidebar: true,
          permission: routePermissions.customPages,
          icon: 'CustomPagesIcon',
        },
      },
      {
        path: 'custom-pages/create',
        name: 'createCustomPage',
        to: '/store/custom-pages/create',
        component: () => import('../../pages/CustomPageAction.vue'),
        meta: {
          permission: routePermissions.customPages,
          activeRoute: 'customPages',
        },
      },
      {
        path: 'custom-pages/:id',
        name: 'editCustomPage',
        to: '/store/custom-pages/:id',
        component: () => import('../../pages/CustomPageAction.vue'),
        meta: {
          permission: routePermissions.customPages,
          activeRoute: 'customPages',
        },
      },
    ],
  },
]
