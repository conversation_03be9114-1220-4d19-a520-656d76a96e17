import type { RouteRecordRaw } from 'vue-router'
import routePermissions, { routeOrders } from '@/utils/routePermissions'

export const managementsRoutes: Array<RouteRecordRaw> = [
  {
    name: 'management',
    path: '/management',
    redirect: '/management/staffs',
    meta: {
      permission: routePermissions.managementsModule,
      hasChildren: true,
      showInSidebar: true,
      order: routeOrders.management,
    },
    children: [

      {
        path: 'customers',
        name: 'customers',
        to: '/management/customers',
        component: () => import('../../pages/CustomerView.vue'),
        meta: {
          permission: routePermissions.customers,
          showInSidebar: true,
          icon: 'CustomersIcon',
        },
      },
      {
        path: 'customers/:id',
        name: 'customer',
        component: () => import('../../pages/Customers.vue'),
        meta: {
          activeRoute: 'customers',
          permission: routePermissions.customers,
        },
      },
      {
        path: 'staffs',
        name: 'staffs',
        to: '/management/staffs',
        component: () => import('../../pages/StaffView.vue'),
        meta: {
          permission: routePermissions.providers,
          showInSidebar: true,
          icon: 'ProvidersIcon',
        },
      },
      {
        path: 'staffs/:id',
        name: 'staff',
        component: () => import('../../pages/StaffMember.vue'),
        meta: {
          permission: routePermissions.providers,
          activeRoute: 'staffs',
        },
      },
      {
        path: 'services',
        name: 'services',
        to: '/management/services',
        component: () => import('../../pages/ServicesView.vue'),
        meta: {
          permission: routePermissions.services,
          showInSidebar: true,
          icon: 'ServicesIcon',
        },
      },
      {
        path: 'services/:id',
        name: 'service',
        component: () => import('../../pages/ServicePage.vue'),
        meta: {
          permission: routePermissions.services,
          activeRoute: 'services',
        },
      },
      {
        path: 'packages',
        name: 'packages',
        to: '/management/packages',
        component: () => import('../../pages/PackagesPage.vue'),
        meta: {
          activeRoute: 'packages',
          permission: routePermissions.packages,
          showInSidebar: true,
          plugins: ['packages'],
          icon: 'PurchasedPackagesIcon',
        },
      },
      {
        path: 'packages/:id',
        name: 'package',
        component: () => import('../../pages/PackagePage.vue'),
        meta: {
          permission: routePermissions.services,
          activeRoute: 'packages',
          plugins: ['packages'],
        },
      },
      {
        path: 'products',
        name: 'products',
        to: '/management/products',
        component: () => import('../../pages/ProductsView.vue'),
        meta: {
          permission: routePermissions.products,
          showInSidebar: true,
          icon: 'ChatBubbleBottomCenterTextIcon',
        },
      },
      {
        path: 'categories',
        name: 'categories',
        to: '/management/categories',
        component: () => import('../../pages/CategoriesView.vue'),
        meta: {
          showInSidebar: true,
          activeRoute: 'categories',
          permission: routePermissions.categories,
          icon: 'CategoriesIcon',
        },
      },
      {
        path: 'category/:id',
        name: 'category',
        component: () => import('../../pages/CategoryPage.vue'),
        meta: {
          permission: routePermissions.categories,
          activeRoute: 'categories',
        },
      },
      {
        path: 'product/:id',
        name: 'product',
        to: '/management/product/:id',
        component: () => import('../../pages/ProductPage.vue'),
        meta: {
          activeRoute: 'products',
          permission: routePermissions.products,
        },
      },
      {
        path: 'service-metrics',
        name: 'ServiceMetrics',
        component: () => import('@/pages/serviceMetrics.vue'),
        meta: {
          permission: routePermissions.serviceMetrics,
          activeRoute: 'services',
        },
      },
      {
        path: 'boundaries',
        name: 'boundaries',
        to: '/management/boundaries',
        component: () => import('../../pages/BoundariesView.vue'),
        meta: {
          plugins: ['boundaries'],
          permission: routePermissions.boundaries,
          showInSidebar: true,
          icon: 'BoundariesIcon',
        },
      },
      {
        path: 'boundaries/staffs',
        name: 'staffsBoundaries',
        component: () => import('../../pages/StaffsBoundaries.vue'),
        meta: {
          permission: routePermissions.boundaries,
          activeRoute: 'boundaries',
          plugins: ['boundaries'],
        },
      },
      {
        path: 'boundary/:id',
        name: 'boundary',
        component: () => import('../../pages/BoundaryPage.vue'),
        meta: {
          permission: routePermissions.boundaries,
          activeRoute: 'boundaries',
          plugins: ['boundaries'],
        },
      },
      {
        path: 'expenses',
        name: 'expenses',
        to: '/management/expenses',
        component: () => import('../../pages/Expenses.vue'),
        meta: {
          permission: routePermissions.expenses,
          activeRoute: 'expenses',
          showInSidebar: true,
          icon: 'ChatBubbleBottomCenterTextIcon',
        },
      }, {
        path: 'expense-types',
        name: 'expense-types',
        to: '/management/expense-types',
        component: () => import('../../pages/ExpensesTypes.vue'),
        meta: {
          activeRoute: 'expense-types',
          showInSidebar: false,
          icon: 'ChatBubbleBottomCenterTextIcon',
        },
      },

    ],
  },
]
