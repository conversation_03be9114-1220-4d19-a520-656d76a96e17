// import type fomr vue router
import type { RouteRecordRaw } from 'vue-router'
import { marketingRoutes } from './marketingRoute'
import { settingsRoute } from './settingsRoute'
import { storeRoutes } from './storeRoutes'
import { managementsRoutes } from './managementsRoute'
import { salesRoutes } from './salesRoute'
import routePermissions, { routeOrders } from '@/utils/routePermissions'
declare module 'vue-router' {
  interface RouteMeta {
    showInSidebar?: boolean
    icon?: string
    permission?: string[]
    activeRoute?: string
    order?: number
  }
}
type CustomRouteRecordRaw = Omit<RouteRecordRaw, 'meta'> & {
  meta?: RouteRecordRaw['meta'] & { permission?: string[]; showInSidebar?: boolean; icon?: string; activeRoute?: string; order?: number }
  to?: string
}

const asyncRoute: Array<CustomRouteRecordRaw> = [
  {
    path: '/',
    name: 'auth',
    component: () => import('../../layouts/authLayout.vue'),
    redirect: '/login',
    meta: {
      auth: true,
      layout: 'authLayout',
    },
    children: [
      {
        path: '/login',
        name: 'login',
        component: () => import('@/pages/auth/Login.vue'),
      },
      {
        path: '/forgot-password',
        name: 'forgot-password',
        component: () => import('@/pages/auth/ForgotPassword.vue'),
      },
      {
        path: '/signup',
        name: 'signup',
        component: () => import('@/pages/auth/Register.vue'),
      },
      {
        path: '/verify-otp',
        name: 'VerificationOtp',
        component: () => import('@/pages/auth/VerificationOtp.vue'),
      },
      {
        path: '/reset-password',
        name: 'ResetPassword',
        component: () => import('@/pages/auth/ResetPassword.vue'),
      },
    ],
  },
  {
    path: '/onboarding',
    name: 'onboarding',
    component: () => import('../../layouts/OnboardingLayout.vue'),
    meta: {
      layout: 'onboardingLayout',
      auth: true,
    },
    redirect: '/onboarding/update-info',
    children: [
      {
        path: 'update-info',
        name: 'update-info',
        component: () => import('@/pages/onboarding/UpdateInfo.vue'),
      },
      {
        path: 'update-profile',
        name: 'update-profile',
        component: () => import('@/pages/onboarding/UpdateProfile.vue'),
      },
      {
        path: 'complete-profile',
        name: 'complete-profile',
        component: () => import('@/pages/onboarding/CompleteProfile.vue'),
      },
    ],
  },
  {
    path: '/launch-profile',
    name: 'launch-profile',
    component: () => import('@/pages/onboarding/LaunchProfile.vue'),
    meta: {
      layout: 'authLayout',
      standalone: true, // This is a standalone page, not part of onboarding layout
    },
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    to: '/dashboard',
    component: () => import('../../pages/DashboardView.vue'),
    meta: {
      showInSidebar: true,
      icon: 'DashboardIcon',
      order: routeOrders.dashboard,
    },
  },
  {
    path: '/settings/subscriptions-panel/plans',
    name: 'pricing',
    to: '/settings/subscriptions-panel/plans',
    component: () => import('../../pages/settings/Plans.vue'),
    meta: {
      order: routeOrders.subscriptions,
      permission: routePermissions.subscriptions,
    },
  }, // https://app.mahjoz.io/settings/subscriptions-panel/plans

  {
    path: '/sales/transactions/:id',
    name: 'transaction',
    component: () => import('../../pages/TransactionView.vue'),
    meta: {
      permission: routePermissions.transaction,
      activeRoute: 'transactions',
    },
  },

  {
    path: '/verify-your-email',
    name: 'unverified-email',
    component: () => import('../../pages/UnverifiedEmail.vue'),
  },
  {
    path: '/preview-invoices/:id',
    name: 'preview-invoices',
    component: () => import('../../pages/PreviewInvoice.vue'),
  },
  {
    path: '/print-invoice/:id',
    name: 'print-invoice',
    component: () => import('../../pages/PreviewTicket.vue'),
  },

  {
    path: '/orders/:id',
    name: 'order',
    component: () => import('../../pages/OrderView.vue'),
    meta: {
      permission: routePermissions.orders,
      activeRoute: 'orders',
    },
  },
  {
    path: '/calendar',
    name: 'calendar',
    to: '/calendar',
    component: () => import('../../pages/NewCalendarView.vue'),
    meta: {
      permission: routePermissions.calendar,
      showInSidebar: true,
      icon: 'CalendarsIcon',
      order: routeOrders.calendar,
    },
  },
  {
    path: '/bookings',
    name: 'bookings',
    to: '/bookings',
    component: () => import('../../pages/BookingsView.vue'),
    meta: {
      permission: routePermissions.bookings,
      showInSidebar: true,
      icon: 'BookingsIcon',
      order: routeOrders.booking,
    },
  },
  {
    path: '/orders',
    name: 'orders',
    to: '/orders',
    component: () => import('../../pages/OrdersView.vue'),
    meta: {
      permission: routePermissions.orders,
      order: routeOrders.orders,
      showInSidebar: true,
      icon: 'OrdersIcon',
    },
  },
  {
    path: '/notifications',
    name: 'notifications',
    to: '/notifications',
    component: () => import('../../pages/settings/NewNotifications.vue'),
    meta: {
      permission: routePermissions.notifications,
      showInSidebar: true,
      icon: 'NotificationsIcon',
      order: routeOrders.notifications,
    },
  },

  {
    path: '/reports',
    name: 'reports',
    to: '/reports',
    component: () => import('../../pages/ReportsView.vue'),
    meta: {
      showInSidebar: true,
      permission: routePermissions.reports,
      icon: 'ReportsIcon',
      order: routeOrders.reports,
    },
  },
  {
    path: '/mersal-whatsapp',
    name: 'mersal-whatsapp',
    to: '/mersal-whatsapp',
    component: () => import('../../pages/MersalWhatsApp.vue'),
    meta: {
      permission: routePermissions.notifications,
      showInSidebar: true,
      icon: 'NotificationsIcon',
      order: routeOrders.whatsapp,
    },

  },

  {
    path: '/profile',
    name: 'profile',
    component: () => import('../../pages/ProfileView.vue'),
  },
  {
    path: '/payment-success',
    name: 'PaymentSuccess',
    component: () => import('../../pages/PaymentSuccess.vue'),
  },
  {
    path: '/payment-failure',
    name: 'PaymentFailure',
    component: () => import('../../pages/PaymentFailure.vue'),
  },
  {
    path: '/unauthorized',
    name: 'unauthorized',
    component: () => import('../../pages/Unauthorized.vue'),
  },
  {
    path: '/not-found-plugin',
    name: 'not-found-plugin',
    component: () => import('../../pages/NotFoundPlugin.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('../../pages/NotFound.vue'),
  },

  ...marketingRoutes,
  ...settingsRoute,
  ...storeRoutes,
  ...managementsRoutes,
  ...salesRoutes,
]

export default asyncRoute
