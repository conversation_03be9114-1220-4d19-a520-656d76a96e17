import type { RouteRecordRaw } from 'vue-router'
import routePermissions, { routeOrders } from '@/utils/routePermissions'

export const salesRoutes: Array<RouteRecordRaw> = [
  {
    name: 'sales',
    path: '/sales',
    redirect: '/sales/refunded-orders',
    meta: {
      permission: routePermissions.sales,
      hasChildren: true,
      showInSidebar: true,
      order: routeOrders.sales,
    },
    children: [
      {
        path: '/refunded-orders',
        name: 'refunds',
        to: '/refunded-orders',
        component: () => import('../../pages/RefundsView.vue'),
        meta: {
          permission: routePermissions.orders,
          showInSidebar: true,
          icon: 'RefundedOrdersIcon',
        },
      },
      {
        path: '/sales/transactions',
        name: 'transactions',
        to: '/sales/transactions',
        component: () => import('../../pages/TransactionsView.vue'),
        meta: {
          permission: routePermissions.transaction,
          showInSidebar: true,
          icon: 'TransactionsIcon',
        },
      },
      {
        path: '/point-of-sale',
        name: 'point-of-sale',
        to: '/point-of-sale',
        component: () => import('../../pages/PosView.vue'),
        meta: {
          layout: 'posLayout',
          showInSidebar: true,
          plugins: ['pos'],
          icon: 'POSIcon',
        },
      },
      {
        path: '/purchased-packages',
        name: 'purchased-packages',
        to: '/purchased-packages',
        component: () => import('../../pages/PurchasedPackagesView.vue'),
        meta: {
          showInSidebar: true,
          permission: routePermissions.purchasedPackages,
          activeRoute: 'purchased-packages',
          plugins: ['packages'],
          icon: 'PurchasedPackagesIcon',
        },
      },
      {
        path: '/purchased-packages/:id',
        name: 'purchased-package',
        component: () => import('../../pages/PurchasedPackagesPage.vue'),
        meta: {
          activeRoute: 'purchased-packages',
          plugins: ['packages'],
          permission: routePermissions.purchasedPackages,
        },
      },
    ],
  },
]
