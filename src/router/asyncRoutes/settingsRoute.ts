import type { RouteRecordRaw } from 'vue-router'
import routePermissions, { routeOrders } from '@/utils/routePermissions'

export const settingsRoute: Array<RouteRecordRaw> = [
  {
    name: 'settings',
    path: '/settings',
    redirect: '/settings/panel',
    component: () => import('../../pages/settings/SettingsView.vue'),
    meta: {
      permission: routePermissions.settingsModule,
      hasChildren: true,
      showInSidebar: true,
      order: routeOrders.settings,
    },

    children: [
      {
        path: 'panel',
        name: 'settings-panel',
        to: '/settings/panel',
        component: () => import('../../pages/settings/SettingsPanel.vue'),
        meta: {
          permission: routePermissions.settingsModule,
          showInSidebar: true,
          activeRoute: 'settings-panel',
          icon: 'GeneralPanelIcon',

        },
      },
      {
        path: 'store',
        name: 'store-panel',
        to: '/store',
        component: () => import('../../pages/store/StorePanel.vue'),
        meta: {
          showInSidebar: true,
          activeRoute: 'store',
          icon: 'StorePanelIcon',
        },
      },

      {
        path: 'general',
        name: 'settings-general',
        component: () => import('../../pages/settings/GeneralSettings.vue'),
        meta: {
          permission: routePermissions.generalSettings,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'branches',
        name: 'branches',
        component: () => import('../../pages/settings/Branches.vue'),
        meta: {
          permission: routePermissions.branches,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'branches/:id',
        name: 'branche',
        component: () => import('../../pages/settings/BranchePage.vue'),
        meta: {
          permission: routePermissions.branches,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'users',
        name: 'users',
        component: () => import('../../pages/settings/TeamUsers.vue'),
        meta: {
          permission: routePermissions.users,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'roles',
        name: 'roles',
        component: () => import('../../pages/settings/RolesPage.vue'),
        meta: {
          permission: routePermissions.roles,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'taxes',
        name: 'taxes',
        component: () => import('../../pages/settings/TaxesPage.vue'),
        meta: {
          permission: routePermissions.taxes,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'invoicing',
        name: 'invoicing',
        component: () => import('../../pages/settings/InvoicingPage.vue'),
        meta: {
          permission: routePermissions.sales,
          showInSidebar: false,
          activeRoute: 'settings-panel',
        },
      },
      {
        path: 'notifications',
        name: 'notification',
        component: () => import('../../pages/settings/NewNotifications.vue'),
        meta: {
          permission: routePermissions.notifications,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'payments',
        name: 'payments',
        component: () => import('../../pages/settings/PaymentsPage.vue'),
        meta: {
          permission: routePermissions.payments,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'subscription',
        name: 'subscription',
        component: () => import('../../pages/DashboardView.vue'),
        meta: {
          permission: routePermissions.subscriptions,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'tags',
        name: 'tag',
        component: () => import('../../pages/settings/TagsPage.vue'),
        meta: {
          permission: routePermissions.tags,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'metadata',
        name: 'metadata',
        component: () => import('../../pages/settings/MetaData.vue'),
        meta: {
          permission: routePermissions.customFields,
          showInSidebar: false,
          activeRoute: 'settings-panel',
        },
      },
      {
        path: 'reasons',
        name: 'reasons',
        component: () => import('../../pages/settings/CancelationReasons.vue'),
        meta: {
          permission: routePermissions.reasons,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'apps',
        name: 'apps',
        component: () => import('../../pages/settings/AppsPage.vue'),
        meta: {
          permission: routePermissions.apps,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'subscriptions-panel',
        name: 'subscriptions',
        component: () => import('../../pages/settings/SubscriptionsPanel.vue'),
        meta: {
          permission: routePermissions.subscriptions,
          showInSidebar: false,
          activeRoute: 'settings-panel',

        },
      },
      {
        path: 'subscriptions-panel/plans',
        name: 'plans',
        component: () => import('../../pages/settings/Plans.vue'),
        meta: {
          permission: routePermissions.subscriptions,
          showInSidebar: false,
          activeRoute: 'settings-panel',
        },
      },
      {
        path: 'subscriptions-panel/current-plan',
        name: 'current-plan',
        component: () => import('../../pages/settings/CurrentPlan.vue'),
        meta: {
          permission: routePermissions.subscriptions,
          showInSidebar: false,
          activeRoute: 'settings-panel',
        },
      },
      {
        path: 'subscriptions-panel/invoices',
        name: 'subscription-invoices',
        component: () => import('../../pages/settings/SubscriptionsInvoices.vue'),
        meta: {
          permission: routePermissions.subscriptions,
          showInSidebar: false,
          activeRoute: 'settings-panel',
        },
      },
      {
        path: 'trash',
        name: 'trash',
        component: () => import('../../pages/settings/Trash.vue'),
        meta: {
          permission: routePermissions.deletingData,
          showInSidebar: false,
          activeRoute: 'settings-panel',
        },
      },
      {
        path: 'syncedInvoices',
        name: 'syncedInvoices',
        component: () => import('../../pages/settings/SyncedInvoices.vue'),
        meta: {
          permission: [],
          showInSidebar: false,
          activeRoute: 'settings-panel',
        },
      },
      {
        path: 'advanced',
        name: 'settings-advanced',
        component: () => import('../../pages/settings/AdvancedSettings.vue'),
        meta: {
          showInSidebar: false,
          activeRoute: 'settings-panel',
        },
      },
      {
        path: 'plugins',
        name: 'plugins',
        component: () => import('../../pages/PluginsView.vue'),
        meta: {
          permission: routePermissions.plugins,
          showInSidebar: false,
          activeRoute: 'settings-panel',
        },
      },
      {
        path: 'pos-terminals',
        name: 'pos-terminals',
        component: () => import('../../pages/settings/PosTerminals.vue'),
        meta: {
          permission: routePermissions.settingsModule,
          showInSidebar: false,
          activeRoute: 'settings-panel',
        },
      },
    ],
  },
]
