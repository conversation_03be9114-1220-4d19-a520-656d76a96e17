<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import mainSlider from '@/components/Common/mainSlider.vue'

const router = useRouter()
const route = useRoute()

// Dynamically compute the active tab based on the current route
const activeTab = computed(() => route.name === 'signup' ? 'signup' : 'login')

// Array of image URLs
const sliderImages = ref([
  'register_1.png',
  'register_2.png',
  'register_3.png',
  'register_4.png',
  'register_5.png',
])
</script>

<template>
  <div class="flex flex-col justify-center items-center w-full h-full md:flex-row">
    <div class="p-4 mx-auto w-full max-w-lg sm:p-8 md:p-12 lg:p-16 md:max-w-none">
      <!-- Logo -->
      <img
        class="mx-auto mb-6 w-auto h-8 sm:h-10 md:h-12 sm:mb-8 md:mb-12 lg:mb-16 md:mx-0"
        src="../assets/logo.svg"
        alt="Your Company"
      >

      <!-- Tabs for Login and Sign-Up (Conditionally Rendered) -->
      <div
        v-if="route.name !== 'forgot-password' && route.name !== 'VerificationOtp' && route.name !== 'ResetPassword'"
        class="flex overflow-hidden flex-row items-center mb-6 w-full rounded-lg border-2 sm:mb-8 md:mb-10 lg:mb-12 border-secondary"
      >
        <!-- Login Tab -->
        <div
          class="flex-1 flex flex-row items-center justify-center p-3 sm:p-4 cursor-pointer" :class="[
            activeTab === 'login'
              ? 'text-white bg-secondary'
              : 'text-gray-700 hover:bg-gray-300',
          ]"
          @click="router.push({ name: 'login' })"
        >
          <div class="relative text-base leading-6 sm:leading-8 sm:text-h3">
            {{ $t("form.signIn") }}
          </div>
        </div>

        <!-- Sign-Up Tab -->
        <div
          class="flex-1 flex flex-row items-center justify-center p-3 sm:p-4 cursor-pointer" :class="[
            activeTab === 'signup'
              ? 'text-white bg-secondary'
              : 'text-gray-700 hover:bg-gray-300',
          ]"
          @click="router.push({ name: 'signup' })"
        >
          <div class="relative text-base leading-6 sm:leading-8 sm:text-h3">
            {{ $t("form.signupFor") }}
          </div>
        </div>
      </div>

      <!-- Login, Sign-Up, or Forgot Password Form -->
      <transition name="fade" mode="out-in">
        <router-view />
      </transition>
    </div>

    <!-- Slider -->
    <div class="hidden p-8 w-full md:block lg:p-16">
      <mainSlider :images="sliderImages" :interval-duration="2000" />
    </div>
  </div>
</template>

<style scoped>
/* Ensure logo is visible across all screen sizes */
img {
  display: block;
  max-width: 100%;
}

@media (max-height: 600px) {
  img.h-8 {
    height: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .mb-6 {
    margin-bottom: 0.75rem;
  }
}

/* Fade transition styling */
.fade-enter-active,
.fade-leave-active {
  transition: opacity .7s ease-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
