<script setup lang='ts'>
import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const authStore = useAuthStore()
const { locale } = useI18n()
// import UpdateInfo from "@/pages/onboarding/UpdateInfo.vue"
// import UpdateProfile from "@/pages/onboarding/UpdateProfile.vue"
// import CompleteProfile from "@/pages/onboarding/CompleteProfile.vue"

const UpdateInfo = defineAsyncComponent(() => import('@/pages/onboarding/UpdateInfo.vue'))
const UpdateProfile = defineAsyncComponent(() => import('@/pages/onboarding/UpdateProfile.vue'))
const CompleteProfile = defineAsyncComponent(() => import('@/pages/onboarding/CompleteProfile.vue'))

// Watch for locale changes to update document direction
watch(locale, (newLocale) => {
  document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr'
  document.documentElement.lang = newLocale
})

// Determine which component to show based on the current route
const currentComponent = computed(() => {
  const routeName = route.name as string

  if (routeName === 'update-info')
    return UpdateInfo
  else if (routeName === 'update-profile')
    return UpdateProfile
  else if (routeName === 'complete-profile')
    return CompleteProfile

  // Default to UpdateInfo if no matching route
  return UpdateInfo
})
</script>

<template>
  <div class="flex h-screen" :dir="locale === 'ar' ? 'rtl' : 'ltr'">
    <!-- Sidebar -->
    <OnboardingSideBar />

    <!-- Main Content Area -->
    <div class="overflow-y-auto flex-1 p-6 pt-20 md:p-8 lg:p-10 md:pt-8">
      <div class="mx-auto max-w-8xl">
        <component :is="currentComponent" />
      </div>
    </div>
  </div>
</template>
