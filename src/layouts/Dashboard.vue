<script setup lang="ts">
import { ref } from 'vue'
import {
  Dialog,
  DialogPanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import {
  Bars3BottomLeftIcon,
  CalendarIcon,
  Cog8ToothIcon,
  DocumentChartBarIcon,
  FolderIcon,
  HomeIcon,
  InboxIcon,
  UserGroupIcon,
  UsersIcon,
  XMarkIcon,
} from '@heroicons/vue/24/outline'
import { MagnifyingGlassIcon } from '@heroicons/vue/20/solid'

const navigation = [
  { name: 'homepage.title', href: 'dashboard', icon: HomeIcon, current: true },
  { name: 'homepage.calendar', href: 'calendar', icon: CalendarIcon, current: false },
  { name: 'homepage.staf', href: 'staff', icon: UsersIcon, current: false },
  { name: 'listing.service', href: 'services', icon: FolderIcon, current: false },
  { name: 'homepage.customers', href: 'customers', icon: UserGroupIcon, current: false },
  { name: 'notifications.booking', href: 'bookings', icon: InboxIcon, current: false },
  { name: 'listing.setting', href: 'settings', icon: Cog8ToothIcon, current: false },
]
const userNavigation = [
  { name: 'homepafe.profile', href: 'settings' },
  { name: 'listing.setting', href: 'settings' },
  { name: 'form.signout', href: 'auth' },
]

const sidebarOpen = ref(false)
</script>

<template>
  <div>
    <TransitionRoot as="template" :show="sidebarOpen">
      <Dialog as="div" class="relative z-40 md:hidden" @close="sidebarOpen = false">
        <TransitionChild as="template" enter="transition-opacity ease-linear duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="transition-opacity ease-linear duration-300" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-gray-600 bg-opacity-75" />
        </TransitionChild>

        <div class="fixed inset-0 z-40 flex">
          <TransitionChild as="template" enter="transition ease-in-out duration-300 transform" enter-from="-translate-x-full" enter-to="translate-x-0" leave="transition ease-in-out duration-300 transform" leave-from="translate-x-0" leave-to="-translate-x-full">
            <DialogPanel class="relative flex flex-col flex-1 w-full max-w-xs pt-5 pb-4 bg-white">
              <TransitionChild as="template" enter="ease-in-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in-out duration-300" leave-from="opacity-100" leave-to="opacity-0">
                <div class="absolute top-0 pt-2 -me-12 end-0">
                  <button type="button" class="flex items-center justify-center w-10 h-10 rounded-full ms-1 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white" @click="sidebarOpen = false">
                    <span class="sr-only">Close sidebar</span>
                    <XMarkIcon class="w-6 h-6 text-white" aria-hidden="true" />
                  </button>
                </div>
              </TransitionChild>
              <div class="flex items-center flex-shrink-0 ps-4 pe-4">
                <img class="w-auto h-8" src="../assets/logo.svg" alt="Your Company">
              </div>
              <div class="flex-1 h-0 mt-5 overflow-y-auto">
                <nav class="space-y-1 ps-2 pe-2">
                  <router-link v-for="item in navigation" :key="item.name" :to="{ name: item.href }" class="flex items-center py-2 text-base font-medium rounded-md ps-2 pe-2 group" :class="[item.current ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900']">
                    <component :is="item.icon" class="flex-shrink-0 w-6 h-6 me-4" :class="[item.current ? 'text-gray-500' : 'text-gray-400 group-hover:text-gray-500']" aria-hidden="true" />
                    {{ $t(item.name) }}
                  </router-link>
                </nav>
              </div>
            </DialogPanel>
          </TransitionChild>
          <div class="flex-shrink-0 w-14" aria-hidden="true">
            <!-- Dummy element to force sidebar to shrink to fit close icon -->
          </div>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Static sidebar for desktop -->
    <div class="hidden md:fixed md:inset-y-0 md:flex md:w-64 md:flex-col">
      <!-- Sidebar component, swap this element with another sidebar if you like -->
      <div class="flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r border-gray-200">
        <div class="flex items-center flex-shrink-0 ps-4 pe-4">
          <img class="w-auto h-8" src="../assets/logo.svg" alt="Your Company">
        </div>
        <div class="flex flex-col flex-grow mt-5">
          <nav class="flex-1 pb-4 space-y-1 ps-2 pe-2">
            <router-link v-for="item in navigation" :key="item.name" :to="{ name: item.href }" class="flex items-center justift-center py-2 text-sm font-medium rounded-md ps-2 pe-2 group" :class="[item.current ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900']">
              <component :is="item.icon" class="flex-shrink-0 w-6 h-6 me-3" :class="[item.current ? 'text-gray-500' : 'text-gray-400 group-hover:text-gray-500']" aria-hidden="true" />
              {{ $t(item.name) }}
            </router-link>
          </nav>
        </div>
      </div>
    </div>
    <div class="flex flex-col flex-1 md:ps-64">
      <div class="sticky top-0 z-10 flex flex-shrink-0 h-16 bg-white shadow">
        <button type="button" class="text-gray-500 border-r border-gray-200 ps-4 pe-4 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden" @click="sidebarOpen = true">
          <span class="sr-only">Open sidebar</span>
          <Bars3BottomLeftIcon class="w-6 h-6" aria-hidden="true" />
        </button>
        <div class="flex justify-between flex-1 ps-4 pe-4">
          <div class="flex items-center ms-4 md:ms-6">
            <LanguageSwitcher class="pt-1" />
            <AppNotifications class="pt-2" />

            <!-- Profile dropdown -->
            <Menu as="div" class="relative ms-3">
              <div>
                <MenuButton class="flex items-center max-w-xs text-sm bg-black rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                  <span class="sr-only">Open user menu</span>
                  <span class="inline-block h-8 w-8 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </span>
                </MenuButton>
              </div>
              <transition enter-active-class="transition duration-100 ease-out" enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100" leave-active-class="transition duration-75 ease-in" leave-from-class="transform scale-100 opacity-100" leave-to-class="transform scale-95 opacity-0">
                <MenuItems class="absolute z-10 w-48 py-1 mt-2 origin-top-right bg-white rounded-md shadow-lg end-0 ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <MenuItem v-for="item in userNavigation" :key="item.name" v-slot="{ active }">
                    <router-link :to="{ name: item.href }" class="block py-2 text-sm text-gray-700 ps-4 pe-4" :class="[active ? 'bg-gray-100' : '']">
                      {{ $t(item.name) }}
                    </router-link>
                  </MenuItem>
                </MenuItems>
              </transition>
            </Menu>
          </div>
        </div>
      </div>

      <main class="flex-1">
        <div class=" ">
          <div class="ps-4 pe-4 ms-auto me-auto max-w-12xl sm:ps-6 sm:pe-6 md:ps-8 md:pe-8">
            <h1 class="text-2xl font-semibold text-gray-900">
              {{ $t($route?.name?.toUpperCase() || 'Dashboard') }}
            </h1>
          </div>
          <div class="ps-4 pe-4 ms-auto me-auto max-w-12xl sm:ps-6 sm:pe-6 md:ps-8 md:pe-8">
            <Suspense>
              <RouterView />
            </Suspense>
          </div>
        </div>
        <WizardDialog />
      </main>
    </div>
  </div>
</template>
