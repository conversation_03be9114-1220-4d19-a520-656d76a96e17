<script setup lang="ts">
import {
  add,
  endOfDay,
  endOfMonth,
  endOfWeek,
  format, parse,
  startOfDay,
  startOfMonth,
  startOfToday,
  startOfWeek,
} from 'date-fns'

import {
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ClockIcon,
  EllipsisHorizontalIcon,
} from '@heroicons/vue/20/solid'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'

// First-party components
import OverlayLoader from '@/components/Common/OverlayLoader.vue'
import MonthView from '@/components/Calendar/MonthView.vue'
import WeekView from '@/components/Calendar/WeekView.vue'
import DayView from '@/components/Calendar/DayView.vue'

// import composers
import useStaff from '@/composables/useStaff'
import useBooking from '@/composables/useBooking'

// import composers functions
const { tableData, fetchStaffPage } = useStaff()
const { fetchEventpage } = useBooking()

const processing = ref(false)
const calendarViews = [
  {
    title: 'calendar.month_view',
    type: 'month',
    skipType: 'months',
    component: MonthView,
  },
  {
    title: 'calendar.week_view',
    type: 'week',
    skipType: 'weeks',
    component: WeekView,
  },
  {
    title: 'calendar.day_view',
    type: 'day',
    skipType: 'days',
    component: DayView,
  },
]
const calendarView = ref(calendarViews[2])

const events = ref([])

const today = startOfToday()
const currentDate = ref(today)
const selectedStaff = ref({})
const dateFilter = computed(() => {
  switch (calendarView.value.type) {
    case 'month':
      return {
        from: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
        to: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
      }
    case 'week':
      return {
        from: format(startOfWeek(currentDate.value), 'yyyy-MM-dd'),
        to: format(endOfWeek(currentDate.value), 'yyyy-MM-dd'),
      }
    case 'day':
      return {
        from: format(startOfDay(currentDate.value), 'yyyy-MM-dd'),
        to: format(endOfDay(currentDate.value), 'yyyy-MM-dd'),
      }
    default:
      return {
        from: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
        to: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
      }
  }
})

const fetchEvent = async () => {
  processing.value = true
  events.value = []
  const params = {
    from: dateFilter.value.from,
    to: dateFilter.value.to,
    staff: selectedStaff.value.uuid ? selectedStaff.value.uuid : '',
  }

  const res = await fetchEventpage(1, params)

  events.value = res.data.map(event => ({
    uuid: event.uuid,
    name: `${event.customer.first_name} ${event.customer.last_name}`,
    start_time: format(parse(`${event.date} ${event.start}`, 'yyyy/MM/dd HH:mm', new Date()), 'yyyy-MM-dd HH:mm'),
    end_time: format(parse(`${event.date} ${event.end}`, 'yyyy/MM/dd HH:mm', new Date()), 'yyyy-MM-dd HH:mm'),
    date: event.date,
    start: event.start,
    end: event.end,
    href: '#',
    routeName: 'booking',
  }))

  processing.value = false
}
const previous = (scope) => {
  currentDate.value = add(currentDate.value, { [scope]: -1 })
  fetchEvent()
}

const next = (scope) => {
  currentDate.value = add(currentDate.value, { [scope]: 1 })
  fetchEvent()
}

const setCurrentDate = (date) => {
  currentDate.value = date
  fetchEvent()
}
const setToday = () => {
  currentDate.value = today
  fetchEvent()
}

const selectStaff = (staff) => {
  selectedStaff.value = staff
  fetchEvent()
}
const setCalendarView = (view) => {
  calendarView.value = view
  fetchEvent()
}

onMounted(() => {
  fetchStaffPage()
  fetchEvent()
})
</script>

<template>
  <div>
    <OverlayLoader v-if="processing" />
    <div class="w-full overflow-hidden rounded-lg ring-1 ring-slate-900/10">
      <div class="bg-gray-50 lg:h-0 lg:min-h-[768px]">
        <div class="lg:flex lg:h-full lg:flex-col">
          <header class="flex items-center justify-between border-b border-gray-200 px-6 py-4 lg:flex-none">
            <h1 class="text-base md:mx-2 font-semibold leading-6 text-gray-900">
              <time :datetime="calendarView.type !== 'day' ? format(currentDate, 'yyyy-MM') : format(currentDate, 'yyyy-MM-dd')" class="sm:hidden">{{ calendarView.type !== 'day' ? format(currentDate, 'MMM yyyy') : format(currentDate, 'dd-MM-yyyy') }}</time>
              <time :datetime="calendarView.type !== 'day' ? format(currentDate, 'yyyy-MMMM') : format(currentDate, 'yyyy-MMMM-dd')" class="hidden sm:inline">{{ calendarView.type !== 'day' ? format(currentDate, 'MMMM yyyy') : format(currentDate, 'do MMMM yyyy') }}</time>
            </h1>
            <div class="flex items-center">
              <div class="hidden md:mx-2 md:flex md:items-center">
                <Menu as="div" class="relative">
                  <MenuButton type="button" class="flex items-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                    {{ selectedStaff.name || $t("calendar.all_staff") }}
                    <ChevronDownIcon class="-mr-1 h-5 w-5 text-gray-400" aria-hidden="true" />
                  </MenuButton>

                  <transition enter-active-class="transition ease-out duration-100" enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100" leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95">
                    <MenuItems class="absolute right-0 z-10 mt-3 w-36 origin-top-right overflow-hidden rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                      <div class="py-1">
                        <MenuItem v-slot="{ active }" @click="selectStaff({})">
                          <a href="#" class="block px-4 py-2 text-sm" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700']">{{ $t("calendar.all_staff") }}</a>
                        </MenuItem>
                        <MenuItem v-for="staff in tableData.staffList" :key="staff.uuid" v-slot="{ active }" @click="selectStaff(staff)">
                          <a href="#" class="block px-4 py-2 text-sm" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700']">{{ staff.name }}</a>
                        </MenuItem>
                      </div>
                    </MenuItems>
                  </transition>
                </Menu>
              </div>
              <div class="relative md:mx-2 flex items-center rounded-md bg-white shadow-sm md:items-stretch">
                <div class="pointer-events-none absolute inset-0 rounded-md ring-1 ring-inset ring-gray-300" aria-hidden="true" />
                <button
                  type="button"
                  class="
                      flex items-center justify-center rounded-l-md py-2 pl-3 pr-4 text-gray-400
                      hover:text-gray-500 focus:relative md:w-9 md:px-2 md:hover:bg-gray-50"
                  @click="previous(calendarView.skipType)"
                >
                  <span class="sr-only">Previous month</span>
                  <ChevronLeftIcon class="h-5 w-5" aria-hidden="true" />
                </button>
                <button type="button" class="hidden px-3.5 text-sm font-semibold text-gray-900 hover:bg-gray-50 focus:relative md:block" @click="setToday">
                  {{ $t('calendar.Today') }}
                </button>
                <span class="relative -mx-px h-5 w-px bg-gray-300 md:hidden" />
                <button
                  type="button"
                  class="flex items-center justify-center rounded-r-md py-2 pl-4 pr-3 text-gray-400
                      hover:text-gray-500 focus:relative md:w-9 md:px-2 md:hover:bg-gray-50"
                  @click="next(calendarView.skipType)"
                >
                  <span class="sr-only">Next month</span>
                  <ChevronRightIcon class="h-5 w-5" aria-hidden="true" />
                </button>
              </div>
              <div class="hidden md:ml-4 md:flex md:items-center">
                <Menu as="div" class="relative">
                  <MenuButton type="button" class="flex items-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                    {{ $t(calendarView.title) }}
                    <ChevronDownIcon class="-mr-1 h-5 w-5 text-gray-400" aria-hidden="true" />
                  </MenuButton>

                  <transition enter-active-class="transition ease-out duration-100" enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100" leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95">
                    <MenuItems class="absolute right-0 z-10 mt-3 w-36 origin-top-right overflow-hidden rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                      <div class="py-1">
                        <MenuItem v-for="view in calendarViews" :key="view.type" v-slot="{ active }" @click="setCalendarView(view)">
                          <a href="#" class="block px-4 py-2 text-sm" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700']">{{ $t(view.title) }}</a>
                        </MenuItem>
                      </div>
                    </MenuItems>
                  </transition>
                </Menu>
              </div>
              <Menu as="div" class="relative ml-6 md:hidden">
                <MenuButton class="-mx-2 flex items-center rounded-full border border-transparent p-2 text-gray-400 hover:text-gray-500">
                  <span class="sr-only">Open menu</span>
                  <EllipsisHorizontalIcon class="h-5 w-5" aria-hidden="true" />
                </MenuButton>

                <transition enter-active-class="transition ease-out duration-100" enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100" leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95">
                  <MenuItems class="absolute right-0 z-10 mt-3 w-36 origin-top-right divide-y divide-gray-100 overflow-hidden rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <div class="py-1">
                      <MenuItem v-slot="{ active }">
                        <a href="#" class="block px-4 py-2 text-sm" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700']">Create event</a>
                      </MenuItem>
                    </div>
                    <div class="py-1">
                      <MenuItem v-slot="{ active }">
                        <a href="#" class="block px-4 py-2 text-sm" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700']">Go to today</a>
                      </MenuItem>
                    </div>
                    <div class="py-1">
                      <MenuItem v-slot="{ active }">
                        <a href="#" class="block px-4 py-2 text-sm" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700']">{{ $t("calendar.day_view") }}</a>
                      </MenuItem>
                      <MenuItem v-slot="{ active }">
                        <a href="#" class="block px-4 py-2 text-sm" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700']">Week view</a>
                      </MenuItem>
                      <MenuItem v-slot="{ active }">
                        <a href="#" class="block px-4 py-2 text-sm" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700']">Month view</a>
                      </MenuItem>
                      <MenuItem v-slot="{ active }">
                        <a href="#" class="block px-4 py-2 text-sm" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700']">Year view</a>
                      </MenuItem>
                    </div>
                  </MenuItems>
                </transition>
              </Menu>
            </div>
          </header>
          <component :is="calendarView.component" :events="events" :current-date="currentDate" @custom-prev="previous($event)" @custom-next="next($event)" @current-date-set="setCurrentDate($event)" />
        </div>
      </div>
    </div>
  </div>
</template>
