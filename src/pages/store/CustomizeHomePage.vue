<script lang="ts" setup>
import { CursorArrowRippleIcon, PlusIcon } from '@heroicons/vue/20/solid'
import { VueDraggableNext } from 'vue-draggable-next'
const elements = ref([])
const showMenu = ref(false)
const elementOptions = ref([
  'Banner',
  'Services',
  'Branches',
  'Header',
  'Footer',
])
const addElement = (option) => {
  elements.value.push({
    uuid: Math.random().toString(36).substring(7),
    name: option,
  })
  showMenu.value = false
}
const dragOptions = computed(() => {
  return {
    animation: 0,
    group: 'description',
    disabled: false,
    ghostClass: 'ghost',
  }
})
const isDragging = ref(false)
</script>

<template>
  <div>
    <h1 class="text-3xl font-semibold text-gray-900" />
    <!-- gird one col - last col +  -->
    <div class="flex flex-col p-3 border-4 border-primary rounded-lg">
      <div class="rounded-lg grid grid-cols-1">
        <VueDraggableNext
          class="list-group"
          tag="ul"
          :list="elements"
          v-bind="dragOptions"
          @start="isDragging = true"
          @end="isDragging = false"
        >
          <transition-group type="transition" name="flip-list">
            <div
              v-for="element in elements"
              :key="element.uuid"
              class="border-2 border-gray-600 p-4 mb-2 rounded-lg relative list-group"
            >
              <CursorArrowRippleIcon
                class="w-10 h-10 text-primary absolute end-0 top-1/2 -translate-y-1/2 cursor-move"
                aria-hidden="true"
              />
              <div class="flex justify-center">
                <h5 class="font-semibold tracking-tight text-gray-900 text-xl">
                  {{ element.name }}
                </h5>
              </div>
            </div>
          </transition-group>
        </VueDraggableNext>
      </div>
      <div
        class="border-dashed border-2 border-gray-600 flex justify-center align-center relative"
      >
        <button
          class="flex h-full w-full p-4 flex justify-center align-center"
          @click="showMenu = !showMenu"
        >
          <PlusIcon class="w-16 h-16 text-primary" aria-hidden="true" />
        </button>
        <div
          v-if="showMenu"
          ref="elementMenu"
          class="absolute start-0 z-10 w-full rounded-md bg-white shadow-lg ring-1 ring-gray-900/5 focus:outline-none top-full"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="user-menu-button"
          tabindex="-1"
        >
          <button
            v-for="option in elementOptions"
            :id="`element-${option}`"
            class="flex w-full px-3 text-lg leading-6 text-gray-900 cursor-pointer bg-gray-100 hover:bg-primary-100 py-5"
            role="menuitem"
            tabindex="-1"
            @click="addElement(option)"
          >
            {{ $t(option) }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.flip-list-move {
  transition: transform 0.5s;
}
.no-move {
  transition: transform 0s;
}
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
.list-group {
  min-height: 20px;
}
.list-group-item {
  cursor: move;
}
.list-group-item i {
  cursor: pointer;
}

.btn {
  @apply font-bold py-2 px-4 rounded;
}
.btn-blue {
  @apply bg-blue-500 text-white;
}
.btn-blue:hover {
  @apply bg-blue-700;
}
</style>
