<script setup lang="ts">
import {
  Cog8ToothIcon,
  ComputerDesktopIcon,
  PhoneIcon,
  PhotoIcon,
  ShieldExclamationIcon,
} from '@heroicons/vue/20/solid'

const items = [
  {
    name: 'store-general',
    icon: Cog8ToothIcon,
    to: '/store/general',
  },
  {
    name: 'appearance',
    icon: ComputerDesktopIcon,
    to: '/store/appearance',
  },
  {
    name: 'gallery',
    icon: PhotoIcon,
    to: '/store/gallery',
  },
  {
    name: 'contacts',
    icon: PhoneIcon,
    to: '/store/contacts',
  },
  {
    name: 'website-management',
    icon: ShieldExclamationIcon,
    to: '/store/website-management',
  },
  // {
  //   name: "customize-homepage",
  //   icon: RectangleGroupIcon,
  //   to: "/store/customize-homepage",
  // },
  {
    name: 'store-settings',
    icon: Cog8ToothIcon,
    to: '/store/store-settings',
  },
]
</script>

<template>
  <div>
    <div class="my-4">
      <div
        class="grid gap-2 xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-2 sm:grid-cols-1"
      >
        <router-link
          v-for="(item, index) in items"
          :key="index"
          class="bg-white border border-gray-200 shadow-sm px-8 py-6 rounded-lg text-black rounded min-h-[100px] flex items-center justify-center flex-col gap-2 cursor-pointer hover:bg-gray-100 active:scale-95 active:duration-150 active:ease-in-out transition duration-150 ease-in-out"
          :to="{ path: item.to }"
        >
          <component
            :is="item.icon"
            class="w-10 h-10 mb-2 text-primary-900"
            aria-hidden="true"
          />
          <p class="font-semibold text-md text-primary-900">
            {{ $t(item.name) }}
          </p>
        </router-link>
      </div>
    </div>
  </div>
</template>
