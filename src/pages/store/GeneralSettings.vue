<script lang="ts" setup>
import useVuelidate from '@vuelidate/core'
import {
  CheckIcon,
  ClipboardIcon,
  GlobeAltIcon,
} from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import { minLength, required } from '@/utils/i18n-validators'
interface BasicInfoForm {
  title: string
  description: string
  page: string
  custom_domain: number
  subdomain: string
}
const { fetchSettings, checkDomain, updateDomain } = useAccountSettingStore()
const settingData = useAccountSettingStore()

const formData: BasicInfoForm = reactive({
  title: '',
  description: '',
  page: 'general',
  custom_domain: 0,
})
const subDominForm: BasicInfoForm = reactive({
  subdomain: '',
})

const rules = {
  title: {
    required,
    minLength: minLength(3),
  },
}

const subDominRule = {
  subdomain: {
    required,
    minLength: minLength(3),
  },
}
const v1$ = useVuelidate(subDominRule, subDominForm)
const v$ = useVuelidate(rules, formData)
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())

const subDomain = debouncedRef(toRef(subDominForm, 'subdomain'), 1000)
const localProcessing = ref(false)
const fullDomain = ref('')
const siteFullName = ref('')
const validDomain = ref(false)
const isSubDominEditable = ref(false)

watch(subDomain, (val) => {
  validDomain.value = false
  if (v1$.value.$invalid || val == settingData.accountSetting.subdomain)
    return
  checkDomain({ subdomain: val })
    .then((res) => {
      validDomain.value = true
    })
    .catch((err) => {
      validDomain.value = false
    })
})

const savingDomain = ref(false)

const saveSubDomin = () => {
  if (v1$.value.$invalid || !validDomain)
    return
  savingDomain.value = true
  updateDomain({ subdomain: subDominForm.subdomain }).then((res) => {
    settingData.accountSetting.subdomain = subDominForm.subdomain
    isSubDominEditable.value = false
    validDomain.value = false
    fullDomain.value = `https://${subDominForm.subdomain}.mahjoz.io`
  }).finally(() => {
    savingDomain.value = false
  })
}

onMounted(async () => {
  localProcessing.value = true
  await fetchSettings('booking-page-settings').then(() => {
    localProcessing.value = false
  })
  formData.title = settingData.accountSetting.sitename || ''
  formData.description = settingData.accountSetting.description || ''
  formData.custom_domain = settingData.accountSetting.custom_domain
  subDominForm.subdomain = settingData.accountSetting.subdomain

  fullDomain.value = `${settingData.accountSetting.full_domain}` || ''
  siteFullName.value
    = `${settingData.accountSetting.full_domain}/sitemap.xml` || ''
})

const sendBasicInfo = () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return
  settingData.processing = true
  settingData
    .updateAccountSetting('booking-page-settings', formData)
    .finally(() => {
      settingData.processing = false
    })
}

const checkIcon = ref(false)
function copyFulldomain() {
  navigator.clipboard.writeText(siteFullName.value)
  checkIcon.value = true
}

const qrCode = computed(() => {
  return `https://api.qrserver.com/v1/create-qr-code/?size=550x550&data=${fullDomain.value}`
})

const embded_script = computed(() => {
  return `<iframe src="${fullDomain.value}/?embeded=1" style="border:0px #ffffff hidden;" name="mahjozBookingPage" scrolling="yes" frameborder="1" marginheight="0px" marginwidth="0px" height="600px" width="600px" allowfullscreen></iframe>`
})
</script>

<template>
  <form
    class="grid relative grid-cols-1 gap-y-4 py-8"
    @submit.prevent="sendBasicInfo()"
  >
    <OverlayLoader v-if="localProcessing" :full-screen="false" />
    <div class="flex">
      <div class="basis-3/4">
        <div class="mb-3">
          <div class="flex mt-1 rounded-md  ">
            <form-group :validation="v$" name="title">
              <template #default="{ attrs }">
                <TextInput
                  v-bind="attrs"
                  id="site-name"
                  v-model="formData.title"
                  :label="$t('settings.bookingpage.title')"
                  :placeholder="$t('settings.bookingpage.title')"
                  custom-classes="w-full"
                />
              </template>
            </form-group>
          </div>
        </div>
        <div>
          <div class="flex justify-start items-center">
            <LabelInput for="subdomain">
              {{
                $t("settings.bookingpage.subdomain")
              }}
            </LabelInput>
            <button
              v-if="!isSubDominEditable"
              class="px-2 text-red-500"
              @click="isSubDominEditable = !isSubDominEditable"
            >
              {{ $t("edit") }}
            </button>
          </div>
          <form-group :validation="v1$" name="subdomain">
            <template #default="{ attrs }">
              <div
                class="flex mt-1 rounded-md shadow-sm ltr:flex-row-reverse rtl:flex-row"
              >
                <div
                  v-if="!formData.custom_domain"
                  class="flex justify-center items-center px-2 bg-gray-200 border border-gray-300 shadow-sm center-items"
                >
                  <span v-if="getLocale(locale)?.id === 'ar'">
                    mahjoz.net.
                  </span>
                  <span v-else> .mahjoz.net </span>
                </div>

                <TextInput
                  v-bind="attrs"
                  id="subdomain"
                  v-model="subDominForm.subdomain"
                  custom-classes="w-full !border-s-0  disabled:bg-gray-200 rounded-none border-e"
                  :disabled="!isSubDominEditable"
                />
                <div
                  v-if="isSubDominEditable"
                  class="text-white border cursor-pointer bg-primary-800 min-w-fit"
                >
                  <button
                    type="button"
                    :disabled="savingDomain"
                    class="px-4 h-full w-fit"
                    :class="{ 'cursor-not-allowed': !validDomain }"
                    @click="saveSubDomin"
                  >
                    {{ savingDomain ? $t('saving') : $t("save_changes") }}
                  </button>
                </div>

                <a
                  class="inline-flex items-center px-3 text-xs text-white rounded-l-md border sm:text-base bg-primary-600 sm:text-sm"
                  target="_blank"
                  :href="fullDomain"
                >
                  <GlobeAltIcon class="w-5 h-5" />
                </a>
              </div>
              <span
                v-if="validDomain"
                class="mt-1 text-black text-green-600 text-md"
              >{{ $t('available') }}</span>
              <CheckLoad :showloader="settingData.processing" />
            </template>
          </form-group>
          <!-- Hint -->
          <span v-if="isSubDominEditable" class="text-xs text-red-500">
            {{
              $t("warning_visitors_will_not_be_able_to_access_your_website")
            }}</span>
        </div>
      </div>
      <div class="basis-2/4">
        <div
          class="flex flex-col gap-2 justify-center items-center text-center basis-1/2"
        >
          <img class="w-40 h-40 border" :src="qrCode">
          <a target="_blank" class="" :href="qrCode">
            {{ $t("qrCode") }}
          </a>
        </div>
      </div>
    </div>
    <div class="sm:col-span-6">
      <div class="mt-1">
        <form-group error-name="description">
          <template #default="{ attrs }">
            <TextareaInput
              id="description"
              v-bind="attrs"
              v-model="formData.description"
              :label="$t('settings.bookingpage.description')"
              rows="4"
              :placeholder="$t('settings.bookingpage.desc')"
            />
          </template>
        </form-group>
      </div>
      <p class="mt-1 text-sm text-blue-gray-500">
        {{ $t("settings.bookingpage.description2") }}
      </p>
    </div>
    <div class="sm:col-span-6">
      <div class="flex mt-1 rounded-md  ">
        <TextInput
          v-bind="attrs"
          id="siteFullName"
          v-model="siteFullName"
          :placeholder="$t('settings.bookingpage.sitemap')"
          :label="$t('settings.bookingpage.sitemap')"
          disabled
        />
        <button
          class="inline-flex items-center px-3 text-white border bg-primary-600 sm:text-sm"
          title="copy Full Domin"
          type="button"
          @click="copyFulldomain"
        >
          <ClipboardIcon v-if="!checkIcon" class="w-5 h-5" />
          <div v-else class="flex items-center">
            <CheckIcon class="w-5 h-5" />
            <span class="text-xs">{{ $t("coiped") }}</span>
          </div>
        </button>
      </div>
    </div>
    <div class="pt-6 sm:col-span-4">
      <BaseButton
        type="submit"
        :processing="settingData.processing"
        class="w-60 hover:bg-gray-800"
        custome-bg="bg-gray-700"
        show-icon
      >
        {{ $t("form.update") }}
      </BaseButton>
    </div>
  </form>
</template>
