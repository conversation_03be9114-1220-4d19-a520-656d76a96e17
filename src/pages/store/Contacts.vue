<script lang="ts" setup>
import { storeToRefs } from 'pinia'
const formData = reactive({
  facebook: '',
  twitter: '',
  youtube: '',
  snapchat: '',
  linkedin: '',
  email: '',
  instagram: '',
  phone_country: '',
  contactno: '',
  tiktok: '',
  whatsapp: '',
  whatsno: '',
  page: 'contact',
})

const setPhoneNumber = (phoneNumber: string, phoneObject: { countryCode: string }) => {
  formData.contactno = phoneNumber
  formData.phone_country = phoneObject.countryCode
}
const setWhatsNumber = (phoneNumber: string, phoneObject: { countryCode: string }) => {
  formData.whatsno = phoneNumber
  formData.whatsapp = phoneNumber
}
const { accountSetting, fetchSettings, updateAccountSetting } = useAccountSettingStore()
const { getAccountSettings } = storeToRefs(useAccountSettingStore())
const sendContactInfo = async () => {
  accountSetting.processing = true
  updateAccountSetting('booking-page-settings', formData).finally(() => {
    accountSetting.processing = false
  })
}
onMounted(async () => {
  accountSetting.processing = true
  await fetchSettings('booking-page-settings')
  accountSetting.processing = false
  if (!getAccountSettings.value.socials)
    return
  formData.facebook = getAccountSettings.value.socials.facebook || ''
  formData.tiktok = getAccountSettings.value.socials.tiktok || ''
  formData.instagram = getAccountSettings.value.socials.instagram || ''
  formData.snapchat = getAccountSettings.value.socials.snapchat || ''
  formData.linkedin = getAccountSettings.value.socials.linkedin || ''
  formData.email = getAccountSettings.value.socials.email || ''
  formData.twitter = getAccountSettings.value.socials.twitter || ''
  formData.youtube = getAccountSettings.value.socials.youtube || ''
  formData.contactno = getAccountSettings.value.contact || ''
  formData.phone_country = getAccountSettings.value.phone_country || ''
  formData.whatsapp = getAccountSettings.value.socials.whatsapp || ''
  formData.whatsno = getAccountSettings.value.socials.whatsno || ''
})
</script>

<template>
  <div>
    <h1 class="text-3xl font-semibold text-gray-900">
      {{ $t("social_media") }}
    </h1>
    <form
      class="grid grid-cols-1 pt-8 gap-y-4 sm:grid-cols-6 sm:gap-x-6 relative"
      @submit.prevent="sendContactInfo()"
    >
      <OverlayLoader v-if="accountSetting.processing" :full-screen="false" />
      <div class="sm:col-span-3">
        <form-group error-name="email">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="email"
              v-model="formData.email"
              :label="$t('settings.bookingpage.email')"
              :placeholder="$t('settings.bookingpage.emailLink')"
              custom-classes="mt-1"
            />
          </template>
        </form-group>
      </div>
      <div class="sm:col-span-3">
        <form-group error-name="tiktok">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="facebook-account"
              v-model="formData.tiktok"
              :label="$t('settings.bookingpage.tiktok')"
              :placeholder="$t('settings.bookingpage.tiktokLink')"
              custom-classes="mt-1"
            />
          </template>
        </form-group>
      </div>
      <div class="sm:col-span-3">
        <form-group error-name="facebook">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="facebook-account"
              v-model="formData.facebook"
              :label="$t('settings.bookingpage.facebook')"
              :placeholder="$t('settings.bookingpage.facebookLink')"
              custom-classes="mt-1"
            />
          </template>
        </form-group>
      </div>
      <div class="sm:col-span-3">
        <form-group error-name="twitter">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="twitter"
              v-model="formData.twitter"
              :label="$t('settings.bookingpage.twitter')"
              :placeholder="$t('settings.bookingpage.twitterLink')"
              custom-classes="mt-1"
            />
          </template>
        </form-group>
      </div>

      <div class="sm:col-span-3">
        <form-group error-name="linkedin">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="twitter"
              v-model="formData.linkedin"
              :label="$t('settings.bookingpage.linkedin')"
              :placeholder="$t('settings.bookingpage.linkedinLink')"
              custom-classes="mt-1"
            />
          </template>
        </form-group>
      </div>
      <div class="sm:col-span-3">
        <form-group error-name="snapchat">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="snapchat"
              v-model="formData.snapchat"
              :label="$t('settings.bookingpage.snapchat')"
              :placeholder="$t('settings.bookingpage.snapchatLink')"
              custom-classes="mt-1"
            />
          </template>
        </form-group>
      </div>

      <div class="sm:col-span-3">
        <form-group error-name="youtube">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="youtube"
              v-model="formData.youtube"
              :label="$t('settings.bookingpage.youtube')"
              :placeholder="$t('settings.bookingpage.youtubeLink')"
              custom-classes="mt-1"
            />
          </template>
        </form-group>
      </div>

      <div class="sm:col-span-3">
        <form-group error-name="instagram">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="instagram"
              v-model="formData.instagram"
              :label="$t('settings.bookingpage.instagram')"
              :placeholder="$t('settings.bookingpage.instagramLink')"
              custom-classes="mt-1"
            />
          </template>
        </form-group>
      </div>
      <div class="sm:col-span-3">
        <form-group error-name="whatsno">
          <template #default="attrs">
            <PhoneInput
              v-bind="attrs"
              :model-value="formData.whatsno"
              class=""
              :show-label="false"
              mode="international"
              label="settings.bookingpage.whatsapp"
              @update:model-value="setWhatsNumber"
            />
          </template>
        </form-group>
      </div>
      <div class="sm:col-span-3">
        <form-group error-name="contactno">
          <template #default="attrs">
            <PhoneInput
              v-bind="attrs"
              :model-value="formData.contactno"
              class=""
              :show-label="false"
              mode="international"
              label="form.callCenter"
              @update:model-value="setPhoneNumber"
            />
          </template>
        </form-group>
      </div>
      <div class="sm:col-span-4 pt-6">
        <BaseButton
          type="submit"
          :processing="accountSetting.processing"
          class="w-60 hover:bg-gray-800"
          custome-bg="bg-gray-700"
          show-icon
        >
          {{ $t("form.update") }}
        </BaseButton>
      </div>
    </form>
  </div>
</template>
