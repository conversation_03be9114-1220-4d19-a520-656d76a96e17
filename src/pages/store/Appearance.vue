<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { reactive, ref } from 'vue'
import useVuelidate from '@vuelidate/core'
import { required } from '@/utils/i18n-validators'
const { getAccountSettings } = storeToRefs(useAccountSettingStore())
const { fetchSettings, updateAccountSetting } = useAccountSettingStore()

const rules = {
  primary: { required },
  secondary: { required },
  background: { required },
}

const logoReq = reactive({
  logo: null as null | string | File,
  page: 'appearance',
})

const bannerReq = reactive({
  banner: null as null | string | File,
  page: 'appearance',
})

const iconReq = reactive({
  favicon: null as string | null | File,
  page: 'appearance',
})

const styles = reactive({
  primary: '',
  secondary: '',
  background: '',
  page: 'appearance',
})

const favIconUrl = ref('')
const bannerUrl = ref('')
const logoUrl = ref('')

const activateLogoBtn = ref(false)
const activateFavBtn = ref(false)
const activateBannerBtn = ref(false)

const processingLogo = ref(false)
const processingBanner = ref(false)
const processingIcon = ref(false)
const processingColor = ref(false)

const processing = ref(false)
const activeTab = ref('branding')

const showBannerClearBtn = ref(false)

const v$ = useVuelidate(rules, styles)
const handleFreshData = () => {
  styles.primary = getAccountSettings.value.styles?.primary || '#3B71FE'
  styles.secondary = getAccountSettings.value.styles?.secondary || '#6DC8F9'
  styles.background = getAccountSettings.value.styles?.background || '#FFFFFF'
  favIconUrl.value = getAccountSettings.value.favicon || ''
  bannerUrl.value = getAccountSettings.value.banner || ''
  logoUrl.value = getAccountSettings.value.logo || ''
}

onMounted(async () => {
  processing.value = true
  await fetchSettings('booking-page-settings')
  processing.value = false
  handleFreshData()
})

// updateColor control
const updateColor = async () => {
  processingColor.value = true

  v$.value.$touch()
  if (v$.value.$invalid)
    return
  await updateAccountSetting('booking-page-settings', styles)
    .then(() => {
      return true
    })
    .finally(() => {
      processingColor.value = false
    })
}

// update banner control
function onBannerChange(event) {
  if (event.target.files.length === 1) {
    bannerReq.banner = event.target.files[0]
    bannerUrl.value = URL.createObjectURL(event.target.files[0])
    activateBannerBtn.value = true
    showBannerClearBtn.value = true
  }
}

function startUpload(file: File): void {
  bannerReq.banner = file
  bannerUrl.value = URL.createObjectURL(file)
  activateBannerBtn.value = true
  showBannerClearBtn.value = true
}

function clearBanner(): void {
  bannerReq.banner = null
  bannerUrl.value = ''
  activateBannerBtn.value = true
  showBannerClearBtn.value = false
}

const updateBanner = async () => {
  processingBanner.value = true
  processing.value = true
  updateAccountSetting('booking-page-settings', bannerReq)
    .then(() => {
      activateBannerBtn.value = false
    })
    .finally(() => {
      processingBanner.value = false
      processing.value = false
    })
}

// update favicon control
function updateFavIcon(link: string): void {
  favIconUrl.value = link
  activateFavBtn.value = true
}

const updateBrowserIcon = async () => {
  processingIcon.value = true
  processing.value = true
  updateAccountSetting('booking-page-settings', iconReq)
    .then(() => {
      activateFavBtn.value = false
    })
    .finally(() => {
      processingIcon.value = false
      processing.value = false
    })
}

// update logo control
function updateLogoIcon(link: string): void {
  logoUrl.value = link
  activateLogoBtn.value = true
}

const updateLogo = async () => {
  processingLogo.value = true
  processing.value = true
  updateAccountSetting('booking-page-settings', logoReq)
    .then(() => {
      activateLogoBtn.value = false
    })
    .finally(() => {
      processingLogo.value = false
      processing.value = false
    })
}
</script>

<template>
  <div class="mx-auto max-w-4xl appearance-page">
    <h1 class="mb-6 text-2xl font-bold text-gray-900">
      {{ $t("settings.bookingpage.appearance") }}
    </h1>

    <div class="relative">
      <OverlayLoader v-if="processing" :full-screen="false" />

      <!-- Tab Navigation -->
      <div class="flex mb-8 border-b border-gray-200">
        <button
          class="py-3 px-6 font-medium text-sm focus:outline-none" :class="[
            activeTab === 'branding'
              ? 'border-b-2 border-indigo-600 text-indigo-600'
              : 'text-gray-500 hover:text-gray-700',
          ]"
          @click="activeTab = 'branding'"
        >
          {{ $t("settings.bookingpage.branding") }}
        </button>
        <button
          class="py-3 px-6 font-medium text-sm focus:outline-none" :class="[
            activeTab === 'colors'
              ? 'border-b-2 border-indigo-600 text-indigo-600'
              : 'text-gray-500 hover:text-gray-700',
          ]"
          @click="activeTab = 'colors'"
        >
          {{ $t("settings.bookingpage.colors") }}
        </button>
      </div>

      <!-- Branding Tab Content -->
      <div v-if="activeTab === 'branding'" class="space-y-8">
        <!-- Logo Section -->
        <div class="p-6 bg-white rounded-lg shadow-sm">
          <h2 class="mb-4 text-lg font-medium text-gray-900">
            {{ $t("settings.bookingpage.logo") }}
          </h2>
          <form class="space-y-4" @submit.prevent="updateLogo()">
            <div class="flex items-center">
              <div class="flex overflow-hidden relative justify-center items-center w-20 h-20 bg-gray-100 rounded-lg border border-gray-200">
                <img
                  v-if="logoUrl"
                  class="object-cover w-full h-full"
                  :src="logoUrl"
                  alt="Company logo"
                >
                <svg
                  v-else
                  class="w-12 h-12 text-gray-300"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              </div>

              <div class="ml-5 space-y-2">
                <SmallUploadInput v-model="logoReq.logo" class="flex" @image-upload="updateLogoIcon">
                  <template #upload-button>
                    <BaseButton
                      type="button"
                      class="text-sm text-gray-700 border border-gray-300"
                    >
                      {{ $t("settings.bookingpage.change") }}
                    </BaseButton>
                  </template>
                </SmallUploadInput>

                <p class="text-xs text-gray-500">
                  {{ $t("settings.bookingpage.recommended_size") }}: 200x200px
                </p>
              </div>
            </div>

            <BaseButton
              v-if="activateLogoBtn"
              type="submit"
              :processing="processingLogo"
              custome-bg="bg-indigo-600"
              class="mt-2 hover:bg-indigo-700"
            >
              {{ $t("settings.bookingpage.save") }}
            </BaseButton>
          </form>
        </div>

        <!-- Banner Section -->
        <div class="p-6 bg-white rounded-lg shadow-sm">
          <h2 class="mb-4 text-lg font-medium text-gray-900">
            {{ $t("settings.bookingpage.banner") }}
          </h2>
          <form class="space-y-4" @submit.prevent="updateBanner()">
            <!-- Banner Container with Preview -->
            <div class="relative w-full">
              <!-- Banner Preview Area with Improved Styling -->
              <div
                class="flex overflow-hidden relative justify-center items-center w-full h-52 bg-gray-50 rounded-lg border border-gray-200 group"
                :style="bannerUrl ? {
                  backgroundImage: `url(${bannerUrl})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                } : {}"
              >
                <!-- Empty State with Better Visual -->
                <div
                  v-if="!bannerUrl"
                  class="p-6 text-center bg-gray-50 rounded-lg cursor-pointer"
                  @click="$refs.uploadedPhoto.click()"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto w-12 h-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p class="mt-3 text-sm font-medium text-gray-500">
                    {{ $t("settings.bookingpage.banner_desc") }}
                  </p>
                  <p class="mt-2 text-xs text-gray-400">
                    {{ $t("settings.bookingpage.recommended_size") }}: 900x300px
                  </p>
                  <p class="mt-4 text-sm font-medium text-indigo-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="inline-block mr-1 w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                    </svg>
                    {{ $t("Click anywhere here to upload your banner") }}
                  </p>
                </div>

                <!-- Overlay with Actions for when banner exists -->
                <div
                  v-if="bannerUrl"
                  class="flex absolute inset-0 justify-center items-center bg-black bg-opacity-10"
                >
                  <div class="flex gap-3 space-x-2">
                    <BaseButton
                      type="button"
                      class="text-sm border border-gray-100 shadow-md hover:bg-gray-50"
                      @click="$refs.uploadedPhoto.click()"
                    >
                      {{ $t("settings.bookingpage.change") }}
                    </BaseButton>

                    <BaseButton
                      v-if="showBannerClearBtn || bannerUrl"
                      type="button"
                      custome-bg="bg-red-600"
                      class="text-sm text-white border border-gray-100 shadow-md hover:bg-red-700"
                      @click="clearBanner"
                    >
                      {{ $t("settings.bookingpage.clear") }}
                    </BaseButton>
                  </div>
                </div>

                <!-- Drag & Drop Upload Area -->
                <DragDropUploadInput
                  v-if="!bannerUrl"
                  class="absolute inset-0 cursor-pointer"
                  @upload="startUpload"
                  @click.stop
                />
              </div>

              <!-- Hidden File Input -->
              <input
                id="uploaded-photo"
                ref="uploadedPhoto"
                accept="image/*"
                type="file"
                class="hidden"
                @change="onBannerChange"
              >

              <!-- Image Requirements & Dimensions -->
              <div class="flex flex-col mt-3 space-y-1">
                <p class="flex items-center text-xs text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 w-4 h-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {{ $t("settings.bookingpage.recommended_size") }}: 900x300px
                </p>
                <p class="flex items-center text-xs text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 w-4 h-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {{ $t("settings.logo_validations.format") }}
                </p>
                <p class="flex items-center text-xs text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 w-4 h-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {{ $t("settings.logo_validations.size") }}
                </p>
              </div>
            </div>

            <!-- Save Button -->
            <div v-if="activateBannerBtn" class="flex justify-end">
              <BaseButton
                type="submit"
                :processing="processingBanner"
                custome-bg="bg-indigo-600"
                class="mt-2 hover:bg-indigo-700"
              >
                {{ $t("settings.bookingpage.save") }}
              </BaseButton>
            </div>
          </form>
        </div>

        <!-- Favicon Section -->
        <div class="p-6 bg-white rounded-lg shadow-sm">
          <h2 class="mb-4 text-lg font-medium text-gray-900">
            {{ $t("settings.bookingpage.favicon") }}
          </h2>
          <form class="space-y-4" @submit.prevent="updateBrowserIcon()">
            <div class="flex items-center">
              <div class="flex overflow-hidden relative justify-center items-center w-16 h-16 bg-gray-100 rounded-lg border border-gray-200">
                <img
                  v-if="favIconUrl"
                  class="object-cover w-full h-full"
                  :src="favIconUrl"
                  alt="Favicon"
                >
                <svg
                  v-else
                  class="w-10 h-10 text-gray-300"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              </div>

              <div class="ml-5 space-y-2">
                <SmallUploadInput
                  v-model="iconReq.favicon"
                  accept=".ico"
                  class="flex"
                  @image-upload="updateFavIcon"
                >
                  <template #upload-button>
                    <BaseButton
                      type="button"
                      class="text-sm text-gray-700 border border-gray-300 shadow-sm hover:bg-gray-50"
                    >
                      {{ $t("settings.bookingpage.change") }}
                    </BaseButton>
                  </template>
                </SmallUploadInput>

                <p class="text-xs text-gray-500">
                  {{ $t("settings.bookingpage.favicon_desc") }} (.ico format)
                </p>
              </div>
            </div>

            <BaseButton
              v-if="activateFavBtn"
              type="submit"
              :processing="processingIcon"
              custome-bg="bg-indigo-600"
              class="mt-2 hover:bg-indigo-700"
            >
              {{ $t("settings.bookingpage.save") }}
            </BaseButton>
          </form>
        </div>
      </div>

      <!-- Colors Tab Content -->
      <div v-if="activeTab === 'colors'" class="space-y-8">
        <div class="p-6 bg-white rounded-lg shadow-sm">
          <h2 class="mb-2 text-lg font-medium text-gray-900">
            {{ $t("settings.bookingpage.styles") }}
          </h2>
          <p class="mb-6 text-sm text-gray-500">
            {{ $t("settings.bookingpage.styles2") }}
          </p>

          <form class="space-y-6" @submit.prevent="updateColor()">
            <!-- Primary Color -->
            <div>
              <form-group :validation="v$" name="primary">
                <template #default="{ attrs }">
                  <div class="space-y-2">
                    <div class="flex justify-between items-center">
                      <LabelInput for="primary-color">
                        {{ $t('settings.bookingpage.primary') }}
                      </LabelInput>
                    </div>
                    <div class="relative">
                      <TextInput
                        v-bind="attrs"
                        id="primary-color"
                        v-model="styles.primary"
                        placeholder="#3B71FE"
                        class="pr-10"
                      />
                      <input
                        v-model="styles.primary"
                        type="color"
                        class="absolute top-1/2 w-7 h-7 bg-transparent rounded-full border-none transform -translate-y-1/2 appearance-none cursor-pointer end-2"
                      >
                    </div>
                    <!-- Color Suggestions -->
                    <div class="mt-2">
                      <p class="mb-1 text-xs text-gray-500">
                        {{ $t('settings.bookingpage.suggestions') || 'Suggested colors:' }}
                      </p>
                      <div class="flex flex-wrap gap-2">
                        <button
                          v-for="color in ['#3B71FE', '#4F46E5', '#6366F1', '#8B5CF6', '#EC4899', '#F43F5E', '#F59E0B']"
                          :key="color"
                          type="button"
                          class="w-7 h-7 rounded-full border border-gray-200 shadow-sm transition-transform hover:scale-110"
                          :style="{ backgroundColor: color }"
                          @click="styles.primary = color"
                        />
                      </div>
                    </div>
                  </div>
                </template>
              </form-group>
            </div>

            <!-- Secondary Color -->
            <div>
              <form-group :validation="v$" name="secondary">
                <template #default="{ attrs }">
                  <div class="space-y-2">
                    <div class="flex justify-between items-center">
                      <LabelInput for="secondary-color">
                        {{ $t('settings.bookingpage.secondary') }}
                      </LabelInput>
                    </div>
                    <div class="relative">
                      <TextInput
                        v-bind="attrs"
                        id="secondary-color"
                        v-model="styles.secondary"
                        placeholder="#6DC8F9"
                        class="pr-10"
                      />
                      <input
                        v-model="styles.secondary"
                        type="color"
                        class="absolute top-1/2 w-7 h-7 bg-transparent rounded-full border-none transform -translate-y-1/2 appearance-none cursor-pointer end-2"
                      >
                    </div>
                    <!-- Color Suggestions -->
                    <div class="mt-2">
                      <p class="mb-1 text-xs text-gray-500">
                        {{ $t('settings.bookingpage.suggestions') || 'Suggested colors:' }}
                      </p>
                      <div class="flex flex-wrap gap-2">
                        <button
                          v-for="color in ['#6DC8F9', '#60A5FA', '#38BDF8', '#22D3EE', '#2DD4BF', '#4ADE80', '#A3E635']"
                          :key="color"
                          type="button"
                          class="w-7 h-7 rounded-full border border-gray-200 shadow-sm transition-transform hover:scale-110"
                          :style="{ backgroundColor: color }"
                          @click="styles.secondary = color"
                        />
                      </div>
                    </div>
                  </div>
                </template>
              </form-group>
            </div>

            <!-- Background Color -->
            <div>
              <form-group :validation="v$" name="background">
                <template #default="{ attrs }">
                  <div class="space-y-2">
                    <div class="flex justify-between items-center">
                      <LabelInput for="background-color">
                        {{ $t('settings.bookingpage.background') }}
                      </LabelInput>
                      <div
                        class="overflow-hidden w-8 h-8 rounded-full border border-gray-200 shadow-sm"
                        :style="{ backgroundColor: styles.background }"
                      />
                    </div>
                    <div class="relative">
                      <TextInput
                        v-bind="attrs"
                        id="background-color"
                        v-model="styles.background"
                        placeholder="#FFFFFF"
                        class="pr-10"
                      />
                      <input
                        v-model="styles.background"
                        type="color"
                        class="absolute top-1/2 w-7 h-7 bg-transparent rounded-full border-none transform -translate-y-1/2 appearance-none cursor-pointer end-2"
                      >
                    </div>
                    <!-- Color Suggestions -->
                    <div class="mt-2">
                      <p class="mb-1 text-xs text-gray-500">
                        {{ $t('settings.bookingpage.suggestions') || 'Suggested colors:' }}
                      </p>
                      <div class="flex flex-wrap gap-2">
                        <button
                          v-for="color in ['#FFFFFF', '#F9FAFB', '#F3F4F6', '#F8FAFC', '#EFF6FF', '#F0F9FF', '#F0FDF4']"
                          :key="color"
                          type="button"
                          class="w-7 h-7 rounded-full border border-gray-200 shadow-sm transition-transform hover:scale-110"
                          :style="{ backgroundColor: color }"
                          @click="styles.background = color"
                        />
                      </div>
                    </div>
                  </div>
                </template>
              </form-group>
            </div>

            <!-- Preview Section (Optional) -->
            <div class="p-4 mt-6 rounded-md border">
              <h3 class="mb-3 text-sm font-medium text-gray-900">
                {{ $t('settings.bookingpage.preview') }}
              </h3>
              <div class="flex space-x-3">
                <div class="flex-1 p-4 rounded-md" :style="{ backgroundColor: styles.background }">
                  <div class="mb-2 w-full h-10 rounded-md" :style="{ backgroundColor: styles.primary }" />
                  <div class="w-full h-6 rounded-md" :style="{ backgroundColor: styles.secondary }" />
                </div>
              </div>
            </div>

            <BaseButton
              type="submit"
              :processing="processingColor"
              custome-bg="bg-indigo-600"
              class="mt-4 hover:bg-indigo-700"
              show-icon
            >
              {{ $t("form.update") }}
            </BaseButton>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>
