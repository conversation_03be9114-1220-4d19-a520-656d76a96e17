<script lang="ts" setup>
import useVuelidate from '@vuelidate/core'
import { storeToRefs } from 'pinia'
import { minLength, required } from '@/utils/i18n-validators'
const status = ['active', 'maintenance', 'suspended']
const processing = ref(false)
const process = ref(false)
const formData = reactive({
  status: '',
  maintenance_title: '',
  maintenance_body: '',
  page: 'status',
})

const rules = {
  maintenance_title: {
    required,
    minLength: minLength(5),
  },
  maintenance_body: {
    required,
    minLength: minLength(20),
  },
}
const v$ = useVuelidate(rules, formData)
const { updateAccountSetting, fetchSettings } = useAccountSettingStore()
const { getAccountSettings } = storeToRefs(useAccountSettingStore())
onMounted(async () => {
  processing.value = true
  await fetchSettings('booking-page-settings')
  processing.value = false
  formData.maintenance_title = getAccountSettings.value.maintenance_title || ''
  formData.maintenance_body = getAccountSettings.value.maintenance_body || ''
  formData.status = getAccountSettings.value.status || 'active'
})

const sendMainMode = () => {
  if (formData.status === 'maintenance') {
    v$.value.$touch()
    if (v$.value.$invalid)
      return
  }
  else if (formData.status !== 'maintenance') {
    delete formData.maintenance_title
    delete formData.maintenance_body
  }
  processing.value = true
  updateAccountSetting('booking-page-settings', formData).finally(() => {
    processing.value = false
  })
}
</script>

<template>
  <div>
    <h1 class="text-3xl font-semibold text-gray-900">
      {{ $t("bookingPage.websiteManagement") }}
    </h1>
    <form class="flex relative flex-col gap-y-4 mt-8" @submit.prevent="sendMainMode()">
      <OverlayLoader v-if="processing" :full-screen="false" />
      <div class="flex flex-wrap gap-2">
        <div class="flex-col w-full max-w-md">
          <SelectInput
            id="status"
            v-model="formData.status" :label="$t('maintenance.status')" custom-classes="mt-2"
          >
            <option v-for="(stat, index) in status" :key="index" :value="stat">
              {{ $t(`maintenance.${stat}`) }}
            </option>
          </SelectInput>
        </div>

        <div v-if="formData.status === 'maintenance'" class="flex-col w-full max-w-md">
          <form-group :validation="v$" name="maintenance_title">
            <template #default="{ attrs }">
              <TextInput
                v-bind="attrs"
                id="facebook-account"
                v-model="formData.maintenance_title"
                :label="$t('maintenance.maintenance_title')"
                required
                custom-classes="mt-2"
                :placeholder="$t('formPlaceHolder.title')"
              />
            </template>
          </form-group>
        </div>
      </div>
      <div v-if="formData.status === 'maintenance'" class="flex-col">
        <form-group :validation="v$" name="maintenance_body">
          <template #default="{ attrs }">
            <TextareaInput
              v-bind="attrs"
              id="facebook-account"
              v-model="formData.maintenance_body"
              :label="$t('maintenance.maintenance_body')"
              :placeholder="$t('formPlaceHolder.body')"
              custom-classes="mt-2"
            />
          </template>
        </form-group>
      </div>
      <div class="pt-2 sm:col-span-4">
        <BaseButton
          type="submit"
          :processing="process"
          class="w-fit hover:bg-gray-800"
          custome-bg="bg-gray-700"
          show-icon
        >
          {{ $t("form.update") }}
        </BaseButton>
      </div>
    </form>
  </div>
</template>

<style></style>
