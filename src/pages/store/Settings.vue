<script lang="ts" setup>
const { generalCustomeSetting, updateGeneralCustomeSettings } = useAccountSettingStore()
const processing = ref(false)
const inputs = ref([])
onMounted(() => {
  processing.value = true
  generalCustomeSetting('booking_page').then((res) => {
    inputs.value = res
  }).finally(() => {
    processing.value = false
  })
})
const errors = ref([])
const submit = (formData) => {
  const payload = {
    setting: {
      booking_page: {
        ...formData,
      },
    },
  }
  processing.value = true
  updateGeneralCustomeSettings(payload, 'booking_page').catch((err) => {
    errors.value = err.errors
  }).finally(() => {
    processing.value = false
  })
}
</script>

<template>
  <div class="relative">
    <OverlayLoader v-if="processing" :full-screen="false" />
    <h1 class="text-3xl font-semibold text-gray-900 mb-4">
      {{ $t('bookingPage.setting') }}
    </h1>
    <div class="flex flex-col w-9/12">
      <customForm :inputs="inputs" :err="errors" error-object-name="setting.booking_page" @submit="submit" />
    </div>
  </div>
</template>
