<script lang="ts" setup>
import { storeToRefs } from 'pinia'
const processingImageUpload = ref(false)
const { getImagesGallery, postImageGallery, deleteImageGallery } = useAccountSettingStore()
const images = ref({})
const processing = ref(false)
onMounted(async () => {
  processing.value = true
  const img = await getImagesGallery()
  images.value = img.data
  processing.value = false
})

function removeImage(imageIndex, image, confirmCallBack) {
  processingImageUpload.value = true
  deleteImageGallery(image.id)
    .then((res) => {
      confirmCallBack()
    })
    .finally(() => {
      processingImageUpload.value = false
    })
}
function uploadImage(imageFormData: FormData, _, _1, imageUploaded) {
  processingImageUpload.value = true
  postImageGallery(imageFormData)
    .then((res) => {
      imageUploaded(res.data.id)
    })
    .finally(() => {
      processingImageUpload.value = false
    })
}
</script>

<template>
  <div class="w-full my-4">
    <h2 class="text-2xl mb-4 font-semibold">
      {{ $t(`images`) }}
    </h2>
    <overlay-loader v-if="processing" :full-screen="false" />
    <images-uploader
      :data-images="images.data"
      class="flex w-full"
      small-images-container-classes="mt-0 !max-w-full gap-2"
      small-images-classes="h-full !w-[190px] !h-[180px]"
      :show-controls-for-single-image="true"
      add-button-classes="!w-[190px] !h-[180px] relative rounded-md border-2 border-gray-300 flex relative text-center border border-gray-300 rounded-md items-center justify-center"
      :show-edit="false"
      :loading="processingImageUpload"
      :drag-text="$t('drag_service_image')"
      browse-text=""
      :drop-text="$t('drop_service_image')"
      @before-remove="removeImage"
      @before-upload="uploadImage"
    />
  </div>
</template>
