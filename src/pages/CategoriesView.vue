<script lang="ts" setup>
import { ArrowDownIcon, PlusIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import type { header } from '@/types'
import { minLength, required, requiredIf } from '@/utils/i18n-validators'
import Noimg from '@/assets/no-image.jpg'

const { fetchCategories, createCategory, removeCategory, updateCategory } = useCategoryStore()
const { getCategories } = storeToRefs(useCategoryStore())
const { locale, t } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { getUserInfo } = storeToRefs(useAuthStore())
const categoryData = reactive({
  name: '',
  slug: '',
  sort_order: 0,
  base_link: '',
  description: '',
  type: 'service',
  display_on_booking_page: false,
  uuid: '',
  team_id: '',
  image: '',
})
const cateValidation = {
  name: {
    required,
  },
  slug: {
    required: requiredIf((val) => {
      return editMode.value
    }),
    minLength: minLength(3),
  },
  team_id: {
    required,
  },
}
const v1$ = useVuelidate(cateValidation, categoryData)
const showModal = ref(false)
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.name'),
    },
    {
      title: t('fields.team_id'),
    },
    {
      title: t('form.sort_order'),
    },
  ]
})

const processingTable = ref(false)
onBeforeMount(() => {
  // updated when page open
  processingTable.value = true
  fetchCategories().finally(() => {
    processingTable.value = false
  })
})

const router = useRouter()
const redirectToService = (item) => {
  router.push({ name: 'category', params: { id: item.uuid } })
}
function toggleModel() {
  showModal.value = !showModal.value
}
const processingModal = ref(false)
const getTeam = computed(() => {
  return getUserInfo.value.teams
})
</script>

<template>
  <div>
    <div>
      <CategoryModal
        :show="showModal"
        :category-data="categoryData"
        :teams="getTeam"
        @close="toggleModel"
        @closed="showModal = false"
      />
    </div>
    <div class="flex justify-between ms-auto me-auto max-w-12xl">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t(`homepage.${$route?.name}` || "homepage.title") }}
      </h1>
      <div class="mt-4 sm:mt-0 sm:flex-none">
        <BaseButton
          class="inline-flex w-auto hover:bg-green-700"
          custome-bg="bg-green-600"
          @click="toggleModel()"
        >
          {{ $t("form.create") }}
          <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </div>
    <!-- <service-filteration :tags="[]"  /> -->

    <div class="flex flex-col mt-8">
      <generic-table
        :is-loading="processingTable"
        :data="getCategories"
        :headers="headers"
        tr-class="cursor-pointer"
        item-key="uuid"
        :on-row-click="redirectToService"
      >
        <template #row="{ item }">
          <grid-td
            :default-style="false"
            class="flex gap-2 items-center px-2 py-2 text-sm whitespace-nowrap"
          >
            <div>
              <img
                id="image"
                class="w-10 h-10 rounded-full"
                :src="item?.imageLink ?? Noimg"
                :link="null"
              >
            </div>
            <span>
              {{ item?.name }}
            </span>
          </grid-td>
          <grid-td>
            <div class="flex items-center text-base">
              {{ item.team?.name ?? '-' }}
            </div>
          </grid-td>
          <grid-td>
            <div class="flex items">
              {{ item.sort_order }}
            </div>
          </grid-td>
          <!--  -->
        </template>
      </generic-table>
    </div>
  </div>
</template>
