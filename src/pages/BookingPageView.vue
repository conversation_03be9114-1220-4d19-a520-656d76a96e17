<script setup lang="ts">
import {
  CameraIcon,
  CogIcon,
  DocumentPlusIcon,
  PaintBrushIcon,
  PhoneIcon,
  UserIcon,
  WrenchScrewdriverIcon,
} from '@heroicons/vue/20/solid'

import checkRoleFun from '@/composables/checkRole'
const appearanceVue = defineAsyncComponent(
  () => import('@/components/BookingPageSettingTabs/Appearance.vue'),
)
const BasicInformationVue = defineAsyncComponent(
  () => import('@/components/BookingPageSettingTabs/BasicInformation.vue'),
)
const GalleryVue = defineAsyncComponent(
  () => import('@/components/BookingPageSettingTabs/Gallery.vue'),
)
const ContactVue = defineAsyncComponent(
  () => import('@/components/BookingPageSettingTabs/Contact.vue'),
)
const Management = defineAsyncComponent(
  () => import('@/components/BookingPageSettingTabs/Management.vue'),
)
const websiteSeo = defineAsyncComponent(
  () => import('@/components/BookingPageSettingTabs/websiteSeo.vue'),
)
const setting = defineAsyncComponent(
  () => import('@/components/BookingPageSettingTabs/setting.vue'),
)

const router = useRouter()

const subNavigation = [
  {
    query: 'GeneralSetting',
    name: 'bookingPage.GeneralSetting',
    description: 'settings.navigation.GeneralSetting_desc',
    icon: UserIcon,
    component: BasicInformationVue,
  },
  {
    query: 'Appearance',
    name: 'bookingPage.Appearance',
    description: 'settings.Appearance_desc',
    icon: PaintBrushIcon,
    component: appearanceVue,
  },
  {
    query: 'Gallery',
    name: 'bookingPage.Gallery',
    description: 'settings.Gallery_desc',
    icon: CameraIcon,
    component: GalleryVue,
  },
  {
    query: 'websiteSeo',
    name: 'bookingPage.Contact',
    description: 'settings.Contact_desc',

    icon: PhoneIcon,
    component: ContactVue,
  },
  {
    query: 'websiteManagement',
    name: 'bookingPage.websiteManagement',
    description: 'settings.websiteManagement_desc',

    icon: CogIcon,
    component: Management,
  },

  {
    query: 'setting',
    name: 'bookingPage.setting',
    description: 'bookingPage.setting_desc',
    icon: WrenchScrewdriverIcon,
    component: setting,
  },
]
const mobileMenuOpen = ref(false)
const activeTabIndex = ref(
  subNavigation.findIndex(tab => tab.query === router.currentRoute.value.query.page)
    || 0,
)
const activeTab = computed(() => {
  const activeTab = router.currentRoute.value.query.page
  return subNavigation.find(tab => tab.query === activeTab) || subNavigation[0]
})
const activeIndex = (index: number) => {
  activeTabIndex.value = index
  mobileMenuOpen.value = false
  router.push({ query: { page: subNavigation[index].query } })
}

onMounted(async () => {
  const { userInfo } = useAuthStore()
  const validQuery = subNavigation.findIndex(tab => tab.query === router.currentRoute.value.query.page) !== -1
  if (validQuery)
    return
  router.push({ query: { page: subNavigation[0].query } })
  activeTabIndex.value = 0
  if (userInfo.role?.length !== 0) {
    if (userInfo.role?.indexOf('Admin') === -1)
      router.push({ name: 'dashboard' })
  }
  else {
    checkRoleFun().then((res) => {
      if (res.includes('Admin'))
        return true
      else if (!res.includes('Admin'))
        router.push({ name: 'dashboard' })
    })
  }
})
</script>

<template>
  <div class="">
    <main class="flex flex-1 overflow-hidden">
      <div class="flex-1">
        <div class="px-2 py-6 sm:px-6 relative">
          <div class="mx-auto">
            <h1 class="text-2xl font-semibold text-gray-900">
              {{ $t("booking_page") }}
            </h1>
          </div>
          <div class="my-4">
            <div class="lg:hidden">
              <SelectInput
                id="tabs"
                :label="$t('form.select')"
                name="tabs"
                class="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                @change="activeIndex($event.target.value)"
              >
                <option
                  v-for="(item, index) in subNavigation"
                  :key="index"
                  :selected="activeTabIndex === index"
                  :value="index"
                >
                  {{ $t(item.name) }}
                </option>
              </SelectInput>
            </div>
            <div class="hidden lg:block">
              <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 gap-3" aria-label="Tabs">
                  <a
                    v-for="(item, index) in subNavigation"
                    :key="index"
                    class="text-gray-500 whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium cursor-pointer"
                    :class="
                      activeTabIndex === index
                        ? 'border-indigo-500 text-indigo-600'
                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                    "
                    :area-current="activeTabIndex === index ? 'page' : undefined"
                    @click="activeIndex(index)"
                  >
                    <div class="flex items-center justify-center gap-1">
                      <component
                        :is="item.icon"
                        class="-mt-0.5 h-6 w-6 flex-shrink-0 text-blue-gray-400"
                        aria-hidden="true"
                      />
                      {{ $t(item.name) }}

                    </div>
                  </a>
                </nav>
              </div>
            </div>
          </div>
          <keep-alive>
            <Suspense>
              <component :is="activeTab.component" />
            </Suspense>
          </keep-alive>
        </div>
      </div>
    </main>
  </div>
</template>
