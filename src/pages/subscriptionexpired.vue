<script lang="ts" setup>
import {
  AUTH_TOKEN,
} from '@/constants'
const router = useRouter()
const goPage = () => {
  router.push({ name: 'plans' })
}
</script>

<template>
  <div class="flex items-center justify-center flex-col mt-24">
    <h1 class="text-4xl mb-6">
      {{ $t('subscriptionExpired') }}
    </h1>
    <base-button @click="goPage">
      {{ $t('showPackages') }}
    </base-button>
  </div>
</template>

<style>

</style>
