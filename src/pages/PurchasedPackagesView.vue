<script setup lang="ts">
import dayjs from 'dayjs'
import type { PaginationLinks, PaginationMeta } from '@/types'

const { locale, t } = useI18n()

const { fetchSoldPackages } = useServicesStore()
const tableData = reactive({
  packagesList: [] as [],
  paginationMeta: {
    current_page: 1,
    from: 1,
    last_page: 1,
    links: [],
    path: '',
    per_page: 15,
    to: 15,
    total: 1,
  } as PaginationMeta,
  paginationLinks: {
    first: '',
    last: '',
    prev: null,
    next: null,
  } as PaginationLinks,
  processing: false,
  filters: {
    page: 1,
    per_page: 10,
  } as { [key: string]: any },
})

const fetchPackges = async (filters: { [key: string]: any }) => {
  tableData.processing = true
  try {
    const data = await fetchSoldPackages(filters)
    tableData.packagesList = data.data
    tableData.paginationLinks = data.links
    tableData.paginationMeta = data.meta
  }
  finally {
    tableData.processing = false
  }
}

onMounted(async () => {
  await fetchPackges(tableData.filters)
})

const router = useRouter()
const navigateToPage = ({ id }) => {
  router.push({ name: 'purchased-package', params: { id } })
}

const headers = computed(() => {
  return [
    {
      title: t('number'),
    },
    {
      title: t('modalPlacholder.packageName'),
    },
    {
      title: t('fields.customer_id'),
    },
    {
      title: t('fields.team_id'),
    },
    {
      title: t('altNav.status'),
    },
    {
      title: t('fields.expiry_date'),
    },
  ]
})

const painationChange = async (page: number) => {
  tableData.filters.page = page
  await fetchPackges(tableData.filters)
}
const filterPackages = async (filters: {}) => {
  tableData.filters = filters
  await fetchPackges(tableData.filters)
}
</script>

<template>
  <div>
    <div class="justify-between items-center sm:flex">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("homepage.purchased-packages") }}
      </h1>
    </div>
    <package-filteration @changed="filterPackages" @clear="fetchPackges" />
    <div class="flex flex-col mt-8">
      <div class="">
        <generic-table
          :is-loading="tableData.processing"
          :data="tableData.packagesList"
          :headers="headers"
          tr-class="cursor-pointer hover:bg-gray-50"
          :on-row-click="navigateToPage"
          item-key="id"
        >
          <template #row="{ item }">
            <grid-td> # {{ item.packageNum }} </grid-td>
            <grid-td>
              {{ item.name }}
            </grid-td>
            <grid-td>
              {{ item.customer ?? $t("not_selected") }}
            </grid-td>
            <grid-td>
              {{ item.team }}
            </grid-td>
            <grid-td>
              {{ item.active ? $t("active") : $t("inactive") }}
            </grid-td>
            <grid-td>
              {{
                dayjs(item.expired_at).format(
                  "dddd, DD MMMM YYYY | hh:mm A",
                )
              }}
            </grid-td>
          </template>
        </generic-table>

        <Pagination
          v-if="tableData.packagesList.length"
          :pagination-meta="tableData.paginationMeta"
          :pagination-links="tableData.paginationLinks"
          @change="painationChange"
        />
      </div>
    </div>
  </div>
</template>
