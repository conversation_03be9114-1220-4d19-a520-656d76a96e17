<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { ChevronDownIcon } from '@heroicons/vue/20/solid'
import { useTransactionState } from '@/stores/transaction'
import type { Transaction } from '@/types/transaction'

const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())

const route = useRoute()
const { fetchTransactionById, updateTrans } = useTransactionState()
const transaction = ref<Transaction>({
  amount: '',
  date: '',
  currency: '',
  customer: '',
  note: '',
  id: '',
  recipt_file: null,
  status: '',
  transaction_no: 0,
})
const processing = ref(false)
const crumbs = ref([
  {
    name: 'homepage.transaction',
    path: '/sales/transactions',
  },
  {
    name: '',
    path: '',
  },
])
const fetchTransaction = (routeId: string) => {
  processing.value = true
  return fetchTransactionById(routeId).then((data) => {
    transaction.value = data.data
    crumbs.value[crumbs.value.length - 1].name = data.data?.transaction_no
  }).finally(() => {
    processing.value = false
  })
}
onMounted(() => {
  fetchTransaction(route.params.id)
})
const updateTransStatus = (status: string) => {
  processing.value = true
  updateTrans(route.params.id, status).then(() => {
    fetchTransaction(route.params.id)
  }).finally(() => {
    processing.value = false
  })
}
</script>

<template>
  <div class="flex flex-col relative">
    <bread-crumb :crumbs="crumbs" class="mt-5" />
    <overlay-loader v-if="processing" :full-screen="false" />
    <div class="flex  sm:flex-row flex-col items-center my-6 justify-between mb-4 gap-4 sm:gap-0 ">
      <div class="flex sm:gap-3 gap-1 items-center ">
        <h1 class="sm:text-3xl text-base font-semibold">
          <!-- {{ customer?.first_name }} {{ customer?.last_name }} -->
          {{ $t('homepage.transaction') }}<span class="cursor-pointer">#{{ transaction?.transaction_no }}</span>
        </h1>
        <div class="text-gray-900">
          <booking-status :book-status="transaction.status" />
        </div>
      </div>
      <div v-if="transaction.status === 'pending'" class="inline-flex  rounded-md shadow-sm w-full sm:w-auto">
        <button type="button" class="relative inline-flex items-center w-3/4 sm:w-auto rounded-l-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10  " :class="[getLocale(locale)?.direction === 'rtl' ? 'order-2' : 'order-1']" @click="updateTransStatus('confirmed')">
          {{ $t("form.approveTrans") }}
        </button>
        <Menu as="div" class="relative w-1/4 sm:w-auto -ml-px block" :class="getLocale(locale)?.direction === 'rtl' ? 'order-1' : 'order-2' ">
          <MenuButton class="relative inline-flex w-full items-center justify-center rounded-r-md bg-white px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10">
            <span class="sr-only">Open options</span>
            <ChevronDownIcon class="h-5 w-5" aria-hidden="true" />
          </MenuButton>
          <transition enter-active-class="transition ease-out duration-100" enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100" leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95">
            <MenuItems class="absolute  z-10 mt-2 -mr-1 w-48 origin-top-right rounded-md  bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" :class="[getLocale(locale)?.direction === 'rtl' ? '-right-20' : '-left-40']">
              <div class="py-1">
                <MenuItem v-if="transaction.recipt_file !== null">
                  <a :href="transaction.recipt_file" class="flex items-center gap-1 w-full text-start p-2 text-sm">
                    {{ $t('form.downloadReceipt') }}
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4" viewBox="0 0 16 16"><path fill="#888888" fill-rule="evenodd" d="M8 0a5.53 5.53 0 0 0-3.594 1.342c-.766.66-1.321 1.52-1.464 2.383C1.266 4.095 0 5.555 0 7.318C0 9.366 1.708 11 3.781 11H7.5V5.5a.5.5 0 0 1 1 0V11h4.188C14.502 11 16 9.57 16 7.773c0-1.636-1.242-2.969-2.834-3.194C12.923 1.999 10.69 0 8 0zm-.354 15.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 14.293V11h-1v3.293l-2.146-2.147a.5.5 0 0 0-.708.708l3 3z" /></svg>

                  </a>
                </MenuItem>
                <MenuItem>
                  <button type="button" class="flex items-center gap-1 w-full text-start p-2 text-sm text-red-600" @click="updateTransStatus('refused')">
                    {{ $t("form.rejectTrans") }}
                  </button>
                </MenuItem>
              </div>
            </MenuItems>
          </transition>
        </Menu>
      </div>
    </div>
    <div class="pb-8">
      <div class="grid sm:grid-cols-2 grid-cols-1">
        <div class="border-b border-stone-100 flex items-center gap-2 py-3">
          <label for="customer" class="block text-sm font-medium text-neutral-500  w-15">
            {{ $t("form.customerName") }}
          </label>
          <div class="flex mt-1  font-semibold">
            <div class="relative flex items-stretch flex-grow focus-within:z-10">
              {{ transaction.customer }}
            </div>
          </div>
        </div>
        <div class="border-b border-stone-100 flex items-center gap-2 py-3">
          <label for="staff" class="block text-sm font-medium text-neutral-500  w-15">{{ $t("Date") }}
          </label>
          <div class="mt-1 font-semibold">
            <span v-if="transaction.date?.length > 0">
              {{ transaction.date }}
            </span>
            <span v-else class="text-neutral-400">-</span>
          </div>
        </div>

        <div class="border-b border-stone-100 flex items-center gap-2 py-3">
          <label for="booking-start" class="block text-sm font-medium text-neutral-500  w-15">{{ $t("dashboard.booking.amount") }} </label>
          <div v-if="transaction.amount?.length > 0" class="mt-1 font-semibold">
            <price-format :form-data="{ price: transaction?.amount, currency: transaction?.currency }" />
          </div>
          <span v-else class="text-neutral-400">-</span>
        </div>

        <div class="border-b border-stone-100 flex items-center gap-2 py-3">
          <label for="booking-start" class="block text-sm font-medium text-neutral-500  w-15">{{ $t("transaction.payment_method") }} </label>
          <div v-if="transaction.payment_method" class="mt-1 font-semibold">
            {{ transaction.payment_method.name }}
          </div>
          <span v-else class="text-neutral-400">-</span>
        </div>

        <div class="border-b border-stone-100 flex items-center gap-2 py-3">
          <label for="booking-start" class="block text-sm font-medium text-neutral-500  w-15">{{ $t("booking.status") }} </label>
          <div v-if="transaction.status?.length > 0" class="mt-1 font-semibold">
            <booking-status :book-status="transaction.status" />
          </div>
          <span v-else class="text-neutral-400">-</span>
        </div>

        <div class="border-b border-stone-100 flex items-center gap-2 py-3">
          <label for="booking-start" class="block text-sm font-medium text-neutral-500  w-15">{{ $t("note") }} </label>
          <div v-if="transaction.note" class="mt-1 font-semibold">
            {{ transaction.note }}
          </div>
          <span v-else class="text-neutral-400">-</span>
        </div>

        <div class="border-b border-stone-100 flex items-center gap-2 py-3">
          <label for="payment-gateway" class="block text-sm font-medium text-neutral-500 w-15">{{ $t("transaction.payment_gateway_id") }}</label>
          <div v-if="transaction.payment_gateway_id" class="mt-1 font-semibold">
            <a
              v-if="transaction.payment_gateway_link"
              :href="transaction.payment_gateway_link"
              target="_blank"
              class="text-primary-600 hover:text-primary-800 flex items-center gap-2"
            >
              {{ transaction.payment_gateway_id }}
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
              </svg>
            </a>
            <span v-else>{{ transaction.payment_gateway_id }}</span>
          </div>
          <span v-else class="text-neutral-400">-</span>
        </div>

        <div class="border-b border-stone-100 flex items-center gap-2 py-3">
          <label for="order-number" class="block text-sm font-medium text-neutral-500  w-15">{{ $t("transaction.order_number") }}</label>
          <div v-if="transaction.order" class="mt-1 font-semibold">
            <router-link
              :to="{ name: 'orders', query: { order_id: transaction.order.id } }"
              class="text-primary-600 hover:text-primary-800 flex items-center gap-2"
            >
              {{ transaction.order.orderNum }}
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
              </svg>
            </router-link>
          </div>
          <span v-else class="text-neutral-400">-</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style>

</style>
