<script setup>
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import rocketImage from '@/assets/onboarding/rocket.svg'
import Icons from '@/components/Icons/Icons.vue'

const router = useRouter()
const authStore = useAuthStore()
const tenantType = computed(() => authStore.onboardingData.systemType || 'store')
const storeSlug = computed(() => authStore.userInfo?.tenant?.booking_page?.full_domain || 'yourstore')
const copied = ref(false)

const goToDashboard = async () => {
  // Mark profile as completed
  // refetch profile
  // Navigate to dashboard
  await authStore.getProfile()
  window.location.href = '/dashboard'
}

// Function to copy the store link
const copyLink = () => {
  navigator.clipboard.writeText(storeSlug.value).then(() => {
    copied.value = true
    setTimeout(() => { copied.value = false }, 2000) // Reset after 2 seconds
  }).catch((err) => {
    console.error('Failed to copy: ', err)
    // Handle error (e.g., show an error message)
  })
}
</script>

<template>
  <div class="fixed inset-0 w-full h-full bg-gradient-to-l from-sky-400 to-green-300 overflow-hidden z-[9999]">
    <!-- Centered Main Content -->
    <div class="flex absolute inset-0 flex-col justify-center items-center p-3 sm:p-6 md:p-8">
      <div class="flex flex-col items-center gap-4 sm:gap-6 md:gap-8 w-[95%] max-w-xs sm:max-w-md md:max-w-lg lg:max-w-2xl text-center ">
        <img
          :src="rocketImage"
          alt="Success"
          class="w-[110px] h-[110px] sm:w-[140px] sm:h-[140px] md:w-[200px] md:h-[200px] lg:w-[240px] lg:h-[240px]"
        >
        <!-- Store Version -->
        <template v-if="tenantType === 'store'">
          <h1 class="text-xl font-extrabold leading-tight text-white sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl">
            {{ $t('launch_profile.store.success_title') }}
          </h1>

          <div class="flex flex-col gap-2 items-center w-full md:gap-3">
            <p class="text-sm font-medium leading-relaxed text-white sm:text-base md:text-lg">
              {{ $t('launch_profile.store.share_link') }}
            </p>
            <div
              class="flex relative gap-2 justify-center items-center px-3 py-2 mt-2 bg-indigo-50 rounded-full cursor-pointer w-fit md:justify-start md:mt-0"
              @click="copyLink"
            >
              <span class="text-xs font-normal break-all text-neutral-800 sm:text-sm md:text-base">
                {{ storeSlug }}
              </span>
              <Icons
                name="copy"
                class="w-4 h-4 transition-colors text-neutral-600 hover:text-neutral-800"
                color="currentColor"
              />
              <!-- Copied feedback -->
              <span v-if="copied" class="absolute right-2 top-1/2 px-2 py-0.5 text-xs text-white rounded -translate-y-1/2 bg-primary">
                Copied!
              </span>
            </div>
          </div>

          <button
            class="px-3 py-2 mt-3 w-full h-10 rounded-xl transition-colors sm:w-4/5 md:w-3/4 lg:max-w-md xl:max-w-lg sm:mt-4 sm:h-12 sm:px-6 sm:py-3 bg-sky-950 hover:bg-sky-900"
            @click="goToDashboard"
          >
            <span class="text-sm font-medium text-white sm:text-base md:text-lg">
              {{ $t('launch_profile.store.start_managing') }}
            </span>
          </button>
        </template>
        <!-- System Version -->
        <template v-else>
          <h1 class="text-xl font-extrabold leading-tight text-white sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl">
            {{ $t('launch_profile.system.success_title') }}
          </h1>

          <p class="px-2 text-xs font-medium leading-relaxed text-white sm:text-sm md:text-base lg:text-lg">
            {{ $t('launch_profile.system.start_exploring') }}
          </p>

          <button
            class="px-3 py-2 mt-3 w-full h-10 rounded-xl transition-colors sm:w-4/5 md:w-3/4 lg:max-w-md xl:max-w-lg sm:mt-4 sm:h-12 sm:px-6 sm:py-3 bg-sky-950 hover:bg-sky-900"
            @click="goToDashboard"
          >
            <span class="text-sm font-medium text-white sm:text-base md:text-lg">
              {{ $t('launch_profile.system.start_using') }}
            </span>
          </button>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
@media screen and (max-height: 600px) {
  img {
    width: 90px !important;
    height: 90px !important;
  }

  h1 {
    font-size: 1.25rem !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  button {
    margin-top: 0.5rem !important;
    height: 2.5rem !important;
  }

  .gap-4 {
    gap: 0.5rem !important;
  }

  .py-6 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
}
</style>
