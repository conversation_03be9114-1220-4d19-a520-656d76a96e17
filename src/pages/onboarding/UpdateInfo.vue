<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import useVuelidate from '@vuelidate/core'
import { useAuthStore } from '@/stores/auth' // Assuming you use Pinia
import useNotifications from '@/composables/useNotifications'
import { required } from '@/utils/i18n-validators'

const { showNotification } = useNotifications()
const { t } = useI18n()

// Store feature icons paths
const storeFeatureIcons = {
  bills: new URL('@/assets/icons/store_features/bills.svg', import.meta.url).href,
  calendar: new URL('@/assets/icons/store_features/calendar.svg', import.meta.url).href,
  discounts: new URL('@/assets/icons/store_features/discounts.svg', import.meta.url).href,
  marketing: new URL('@/assets/icons/store_features/marketing.svg', import.meta.url).href,
  notifications: new URL('@/assets/icons/store_features/notifications.svg', import.meta.url).href,
  phone: new URL('@/assets/icons/store_features/phone.svg', import.meta.url).href,
  pos: new URL('@/assets/icons/store_features/pos.svg', import.meta.url).href,
  reports: new URL('@/assets/icons/store_features/reports.svg', import.meta.url).href,
  whatsapp: new URL('@/assets/icons/store_features/whatsapp.svg', import.meta.url).href,
  manage: new URL('@/assets/icons/store_features/manage.svg', import.meta.url).href,
}

// Internal feature icons paths
const internalFeatureIcons = {
  apps: new URL('@/assets/icons/internal_features/apps.svg', import.meta.url).href,
  employees: new URL('@/assets/icons/internal_features/emplyees.svg', import.meta.url).href,
  location: new URL('@/assets/icons/internal_features/location.svg', import.meta.url).href,
  orders: new URL('@/assets/icons/internal_features/orders.svg', import.meta.url).href,
  packages: new URL('@/assets/icons/internal_features/packages.svg', import.meta.url).href,
  users: new URL('@/assets/icons/internal_features/users.svg', import.meta.url).href,
}

const router = useRouter()
const authStore = useAuthStore()
const formData = ref({
  systemType: authStore.onboardingData?.systemType || 'store',
  selectedFeatures: authStore.onboardingData.selectedFeatures || [],
  selectedEmployees: authStore.onboardingData.selectedEmployees,
  selectedRole: authStore.onboardingData.selectedRole,
  businessName: authStore.onboardingData.businessName,
})

const rules = {
  systemType: { required },
  businessName: { required },
  selectedEmployees: { required },
}
const $v = useVuelidate(rules, formData)

// Set tenant type when system is selected
const selectSystem = (type: 'store' | 'internal') => {
  formData.value.systemType = type
  formData.value.selectedFeatures = []
  authStore.setOnboardingData({ systemType: type, selectedFeatures: [] })
}

// Define features for each system type with icons
const systemFeatures = {
  store: [
    { name: 'store_features.mobile_app', icon: storeFeatureIcons.phone },
    { name: 'store_features.booking_management', icon: storeFeatureIcons.calendar },
    { name: 'store_features.customer_service', icon: storeFeatureIcons.manage },
    { name: 'store_features.auto_notifications', icon: storeFeatureIcons.notifications },
    { name: 'store_features.marketing_campaigns', icon: storeFeatureIcons.marketing },
    { name: 'store_features.discount_codes', icon: storeFeatureIcons.discounts },
    { name: 'store_features.payment_billing', icon: storeFeatureIcons.bills },
    { name: 'store_features.whatsapp_integration', icon: storeFeatureIcons.whatsapp },
    { name: 'store_features.reports_reviews', icon: storeFeatureIcons.reports },
    { name: 'store_features.payment_link', icon: storeFeatureIcons.pos },
  ],
  internal: [
    { name: 'internal_features.employee_management', icon: internalFeatureIcons.employees },
    { name: 'internal_features.app_integration', icon: internalFeatureIcons.apps },
    { name: 'internal_features.customer_management', icon: internalFeatureIcons.users },
    { name: 'internal_features.order_management', icon: internalFeatureIcons.orders },
    { name: 'internal_features.geographic_management', icon: internalFeatureIcons.location },
    { name: 'internal_features.subscription_management', icon: internalFeatureIcons.packages },
  ],
}

// Employee count options
const employeeOptions = [{
  label: 'employee_count.1_5',
  value: '1_5',
}, {
  label: 'employee_count.6_10',
  value: '6_10',
}, {
  label: 'employee_count.11_20',
  value: '11_20',
}, {
  label: 'employee_count.21_50',
  value: '21_50',
}, {
  label: 'employee_count.50_plus',
  value: '50_plus',
}]
// Role options
const roleOptions = [{
  label: 'roles.owner',
  value: 'owner',
}, {
  label: 'roles.ceo',
  value: 'ceo',
}, {
  label: 'roles.marketing',
  value: 'marketing',
}, {
  label: 'roles.customer_service',
  value: 'customer_service',
}]
const toggleFeature = (featureName: string) => {
  // Translate the feature name for storage
  const index = formData.value.selectedFeatures.indexOf(featureName)
  if (index === -1)
    formData.value.selectedFeatures.push(featureName)
  else
    formData.value.selectedFeatures.splice(index, 1)

  authStore.setOnboardingData({ selectedFeatures: formData.value.selectedFeatures })
}

// Watch for changes in selected employees and role
watch(() => formData.value.selectedEmployees, (newValue) => {
  authStore.setOnboardingData({ selectedEmployees: newValue })
})

watch(() => formData.value.selectedRole, (newValue) => {
  authStore.setOnboardingData({ selectedRole: newValue })
})

// Watch for changes in business name
watch(() => formData.value.businessName, (newValue) => {
  authStore.setOnboardingData({ businessName: newValue })
})

// Update the isFormValid computed property

const proceedToNextStep = () => {
  // This will trigger the computed property and update formErrors
  // TODO : touch validation
  $v.value.$touch()
  if ($v.value.$invalid)
    return false
  // Prepare payload for the API call
  const payload = {
    tenant_type: formData.value.systemType,
    tenant_creator_role: formData.value.selectedRole,
    number_of_employees: formData.value.selectedEmployees,
    tenant_benefits: formData.value.selectedFeatures,
    name: formData.value.businessName,
  }

  // Call the API through the auth store
  authStore.updateTenantInfo(payload)
    .then(() => {
      router.push('/onboarding/update-profile')
    })
    .catch((error) => {
      if (error.response?.data?.message) {
        showNotification({
          title: t('Error'),
          type: 'error',
          message: error.response.data.message,
        })
      }
      else {
        showNotification({
          title: t('Error'),
          type: 'error',
          message: t('update_info.errors.save_error'),
        })
      }
    })
}
</script>

<template>
  <div class="flex flex-col space-y-12">
    <div class="space-y-2">
      <h1 class="font-bold text-h1 text-secondary">
        {{ $t('update_info.title') }}
      </h1>
      <p class="text-base text-[#7C7C7C]">
        {{ $t('update_info.description') }}
      </p>
    </div>
    <!-- System Selection -->
    <div>
      <div class="space-y-1">
        <h1 class="text-lg font-medium">
          {{ $t('update_info.system_selection.title') }} <span class="text-red-500">*</span>
        </h1>
        <p class="text-sm text-[#7C7C7C]">
          {{ $t('update_info.system_selection.description') }}
        </p>
      </div>
      <form-group :validation="$v" name="systemType">
        <template #default="{ attrs }">
          <div class="flex flex-col gap-4 mt-4 md:flex-row">
            <div
              class="flex flex-col justify-center items-center space-y-2 text-center outline outline-1 rounded-lg p-4 hover:bg-[#EDF0FF] cursor-pointer transition-colors"
              :class="{
                'bg-primary-50 outline-2 outline-secondary': formData.systemType === 'store',
              }"
              @click="selectSystem('store')"
            >
              <img src="@/assets/onboarding/service_type/store.svg" alt="Store System" class="w-16 h-16">
              <h1 class="text-lg">
                {{ $t('update_info.system_selection.store.title') }}
              </h1>
              <p class="text-sm text-[#7C7C7C]">
                {{ $t('update_info.system_selection.store.description') }}
              </p>
            </div>

            <div
              class="flex flex-col justify-center items-center space-y-2 text-center outline outline-1 rounded-lg p-4 hover:bg-[#EDF0FF] cursor-pointer transition-colors"
              :class="{
                'bg-primary-50 outline-2 outline-secondary': formData.systemType === 'internal',
              }"
              @click="selectSystem('internal')"
            >
              <img src="@/assets/onboarding/service_type/system.svg" alt="Internal System" class="w-16 h-16">
              <h1 class="text-lg">
                {{ $t('update_info.system_selection.internal.title') }}
              </h1>
              <p class="text-sm text-[#7C7C7C]">
                {{ $t('update_info.system_selection.internal.description') }}
              </p>
            </div>
          </div>
        </template>
      </form-group>
    </div>

    <!-- Business Name -->
    <div class="space-y-2">
      <h1 class="text-lg font-medium">
        {{ formData.systemType === 'store'
          ? $t('update_profile.store_name')
          : $t('update_profile.activity_name')
        }} <span class="text-red-500">*</span>
      </h1>
      <form-group :validation="$v" name="businessName">
        <template #default="{ attrs }">
          <TextInput
            v-bind="attrs"
            v-model="formData.businessName"
            :placeholder="formData.systemType === 'store'
              ? $t('update_profile.store_name_placeholder')
              : $t('update_profile.activity_name_placeholder')"
          />
        </template>
      </form-group>
    </div>

    <!-- Features Section -->
    <div v-if="formData.systemType">
      <h1 class="text-lg font-medium">
        {{ $t('update_info.features.title') }}
      </h1>
      <div class="flex flex-wrap gap-3 mt-4">
        <CardSelector
          v-for="feature in systemFeatures[formData.systemType]"
          :key="feature.name"
          :option="$t(feature.name)"
          :active="formData.selectedFeatures.includes(feature.name)"
          @click="toggleFeature(feature.name)"
        >
          <template #icon>
            <img :src="feature.icon" :alt="feature.name" class="w-6 h-6">
          </template>
        </CardSelector>
      </div>
    </div>

    <!-- Employee Count -->
    <div class="space-y-2">
      <form-group :validation="$v" name="selectedEmployees">
        <template #default="{ attrs }" name="selectedEmployees">
          <h1 class="text-lg font-medium">
            {{ $t('update_info.employee_count.title') }} <span class="text-red-500">*</span>
          </h1>
          <div class="flex flex-wrap gap-3 pt-2">
            <CardSelector
              v-for="(option, index) in employeeOptions"
              :key="index"
              :option="$t(option.label)"
              :active="formData.selectedEmployees === option.value"
              @click="formData.selectedEmployees = option.value"
            />
          </div>
        </template>
      </form-group>
    </div>

    <!-- User Role -->
    <div class="space-y-2">
      <h1 class="text-lg font-medium">
        {{ $t('update_info.roles.title') }} <span class="text-red-500">*</span>
      </h1>
      <div class="flex flex-wrap gap-3 pt-2">
        <CardSelector
          v-for="(role, index) in roleOptions"
          :key="index"
          :option="$t(role.label)"
          :active="formData.selectedRole === role.value"
          @click="formData.selectedRole = role.value"
        />
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex flex-col gap-4 justify-between items-center self-stretch py-4 sm:flex-row sm:gap-0 text-secondary">
      <button
        class="flex order-2 gap-2 justify-center items-center px-6 py-4 w-full h-12 rounded-xl cursor-pointer sm:w-64 sm:order-1 disabled:cursor-not-allowed"
        :class="[
          $v.$invalid
            ? 'bg-gray-300'
            : 'bg-gradient-to-l from-sky-400 to-green-300',
        ]"
        :disabled="$v.$invalid"
        @click="proceedToNextStep"
      >
        <span class="text-lg font-bold text-white">{{ $t('update_profile.next') }}</span>
      </button>
    </div>
  </div>
</template>
