<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import useVuelidate from '@vuelidate/core'
import { useAuthStore } from '@/stores/auth'
import AuthService from '@/services/AuthService'
import SettingsService from '@/services/SettingsService'
import useNotifications from '@/composables/useNotifications'
import { required, requiredIf } from '@/utils/i18n-validators'

const { showNotification } = useNotifications()
const { t } = useI18n()

// Service type icons paths
const serviceTypeIcons = {
  home: new URL('@/assets/icons/service_type/home.svg', import.meta.url).href,
  eStore: new URL('@/assets/icons/service_type/e-store.svg', import.meta.url).href,
  store: new URL('@/assets/icons/service_type/store.svg', import.meta.url).href,
}

// Industry icons mapping
const industryIcons: Record<string, string> = {
  'spa-massage': new URL('@/assets/icons/industries/spa-massage.svg', import.meta.url).href,
  'sports': new URL('@/assets/icons/industries/sports.svg', import.meta.url).href,
  'training': new URL('@/assets/icons/industries/training.svg', import.meta.url).href,
  'medical': new URL('@/assets/icons/industries/medical.svg', import.meta.url).href,
  'rent': new URL('@/assets/icons/industries/rent.svg', import.meta.url).href,
  'gover': new URL('@/assets/icons/industries/gover.svg', import.meta.url).href,
  'hotels': new URL('@/assets/icons/industries/hotels.svg', import.meta.url).href,
  'maintance-service': new URL('@/assets/icons/industries/maintance-service.svg', import.meta.url).href,
  'camers': new URL('@/assets/icons/industries/camers.svg', import.meta.url).href,
  'car': new URL('@/assets/icons/industries/car.svg', import.meta.url).href,
  'food': new URL('@/assets/icons/industries/food.svg', import.meta.url).href,
  'business-consulting': new URL('@/assets/icons/industries/business-consulting.svg', import.meta.url).href,
  'cafe': new URL('@/assets/icons/industries/cafe.svg', import.meta.url).href,
  'home': new URL('@/assets/icons/industries/Home.svg', import.meta.url).href,
  'aid': new URL('@/assets/icons/industries/aid.svg', import.meta.url).href,
  'animal-care': new URL('@/assets/icons/industries/animal-care.svg', import.meta.url).href,
  // PNG files
  'workspace': new URL('@/assets/icons/industries/workspace.png', import.meta.url).href,
  'others': new URL('@/assets/icons/industries/others.png', import.meta.url).href,
  'beauty-care': new URL('@/assets/icons/industries/beauty-care.png', import.meta.url).href,
}

const router = useRouter()
const authStore = useAuthStore()

const showUrlField = computed(() => {
  return authStore.onboardingData.systemType === 'store'
})
// Replace multiple refs with single formData object
const formData = ref({
  storeUrl: authStore.onboardingData.storeUrl,
  industryId: authStore.onboardingData.industryId || '',
  serviceType: authStore.onboardingData.serviceType || '',
})
const rules = {
  storeUrl: { required },
  industryId: { required },
  serviceType: { required },
}

const $v = useVuelidate(rules, formData)

// Keep these refs as they are UI state, not form data
const isUrlAvailable = ref(false)
const isLoading = ref(false)
const urlError = ref('')
const isCheckingUrl = ref(false)
const suggestedDomains = ref<string[]>([])
const initialSuggestionsLoaded = ref(false)

// Determine if we should show URL field based on tenant type

// Format the full store URL with domain
const fullStoreUrl = computed(() => {
  return formData.value.storeUrl ? `${formData.value.storeUrl}.mahjoz.net` : ''
})

// Watch for changes in form fields
watch(() => formData.value.storeUrl, (newValue) => {
  authStore.setOnboardingData({ storeUrl: newValue })
})

watch(() => formData.value.industryId, (newValue) => {
  authStore.setOnboardingData({ industryId: newValue })
})

watch(() => formData.value.serviceType, (newValue) => {
  authStore.setOnboardingData({ serviceType: newValue })
})

// Update URL watcher with debounce
let urlCheckTimeout: NodeJS.Timeout
watch(() => formData.value.storeUrl, (newValue) => {
  if (urlCheckTimeout)
    clearTimeout(urlCheckTimeout)

  if (newValue) {
    const sanitizedValue = newValue.toLowerCase().replace(/[^a-z0-9-]/g, '')
    if (sanitizedValue !== newValue)
      formData.value.storeUrl = sanitizedValue

    authStore.setOnboardingData({ storeUrl: sanitizedValue })

    isCheckingUrl.value = true
    urlCheckTimeout = setTimeout(() => {
      checkUrlAvailability()
    }, 500)
  }
  else {
    isUrlAvailable.value = false
    urlError.value = ''
    isCheckingUrl.value = false
    suggestedDomains.value = []
    authStore.setOnboardingData({ storeUrl: '' })
  }
})

// Updated industry model to include UUID and icon
interface Industry {
  uuid: string
  name: string
  slug?: string
  icon?: string
}

// Domain interface for suggested domains
interface Domain {
  name: string
}

// Fetch industries from the API
const industries = ref<Industry[]>([])
const domains = ref<Domain[]>([])

const fetchIndustries = async () => {
  try {
    isLoading.value = true

    // 1. Make API call
    const response = await AuthService.fetchIndustries()

    // 2. Type-safe data extraction
    const responseData = response.data as {
      data: Array<{
        uuid: string
        name: string
        slug?: string
      }>
    }

    // 3. Process data with null checks and add icons
    industries.value = (responseData.data || []).map(item => ({
      uuid: item.uuid || '',
      name: item.name || 'Unnamed Industry',
      slug: item.slug || '',
      icon: item.slug && industryIcons[item.slug] ? industryIcons[item.slug] : undefined,
    }))
  }
  catch (error) {
    console.error('API Error:', error)
    industries.value = [{
      uuid: 'error-fallback',
      name: 'فشل تحميل الأصناف - يرجى المحاولة لاحقاً',
      slug: 'error',
    }]
  }
  finally {
    isLoading.value = false
  }
}

// Service type options
const serviceTypes = [
  { name: 'service_types.home_service', icon: serviceTypeIcons.home, value: 'home-service' },
  { name: 'service_types.online', icon: serviceTypeIcons.eStore, value: 'online' },
  { name: 'service_types.on_site', icon: serviceTypeIcons.store, value: 'on-site' },
]

const toggleIndustry = (industry: Industry) => {
  formData.value.industryId = industry.uuid
  authStore.setOnboardingData({
    industryId: industry.uuid,
  })
}

const selectServiceType = (type: any) => {
  formData.value.serviceType = type.value
  authStore.setOnboardingData({
    serviceType: type.name,
  })
}

const submitForm = async () => {
  $v.value.$touch()
  if ($v.value.$invalid)
    return

  try {
    isLoading.value = true

    // Get domain from API suggestions or use first suggested domain as fallback
    let domain = formData.value.storeUrl
    if (!domain) {
      await fetchSuggestedDomains()
      domain = suggestedDomains.value[0] || ''
    }

    const payload = {
      name: authStore.onboardingData.businessName,
      industry_id: formData.value.industryId,
      location: formData.value.serviceType || 'online',
      domain,
    }

    await authStore.updateTenantProfile(payload)
    await authStore.getProfile()

    if (authStore.onboardingData.systemType === 'store') {
      router.push('/onboarding/complete-profile')
    }
    else {
      await authStore.completeProfile({
        staffs: [],
        services: [],
      })
      router.push('/launch-profile')
    }
  }
  catch (error: any) {
    if (error.response?.data?.message) {
      showNotification({
        title: t('Error'),
        type: 'error',
        message: error.response.data.message,
      })
    }
    else {
      showNotification({
        title: t('Error'),
        type: 'error',
        message: t('update_info.errors.save_error'),
      })
    }
  }
  finally {
    isLoading.value = false
  }
}

// Update checkUrlAvailability function
const checkUrlAvailability = async () => {
  if (!formData.value.storeUrl) {
    isUrlAvailable.value = false
    urlError.value = ''
    suggestedDomains.value = []
    return
  }

  try {
    isCheckingUrl.value = true

    const response = await SettingsService.checkDomain({
      subdomain: formData.value.storeUrl.toLowerCase(),
    })

    if (response.status === 202) {
      isUrlAvailable.value = true
      urlError.value = ''
    }
    else {
      isUrlAvailable.value = false
      urlError.value = t('update_profile.url_unavailable')
    }
  }
  catch (error: any) {
    console.error('Error checking URL availability:', error)
    isUrlAvailable.value = false

    if (error.response?.status === 403)
      urlError.value = t('update_profile.url_check_forbidden')
    else if (error.response?.data?.message)
      urlError.value = error.response.data.message
    else
      urlError.value = t('update_profile.url_check_error')
  }
  finally {
    isCheckingUrl.value = false
  }
}

// Fetch suggested domains from API
const fetchSuggestedDomains = async () => {
  try {
    const response = await AuthService.getSuggestedDomains({
      name: authStore.onboardingData.businessName || '',
    })

    // Type-safe data extraction
    const responseData = response.data as {
      suggestion_domains: Array<string>
    }

    // Process data with null checks
    domains.value = (responseData.suggestion_domains || []).map(name => ({
      name: name || '',
    }))

    // Update suggestedDomains for the template
    suggestedDomains.value = domains.value.map(domain => domain.name)
  }
  catch (error) {
    console.error('Error fetching suggested domains:', error)
    domains.value = [{
      name: 'فشل تحميل النطاقات - يرجى المحاولة لاحقاً',
    }]
    suggestedDomains.value = domains.value.map(domain => domain.name)
  }
}

// Add this function after other function declarations
const loadInitialSuggestions = async () => {
  if (authStore.onboardingData.businessName) {
    try {
      const response = await AuthService.getSuggestedDomains({
        name: authStore.onboardingData.businessName,
      })

      const responseData = response.data as {
        suggestion_domains: Array<string>
      }

      domains.value = (responseData.suggestion_domains || []).map(name => ({
        name: name || '',
      }))

      suggestedDomains.value = domains.value.map(domain => domain.name)
      initialSuggestionsLoaded.value = true
    }
    catch (error) {
      console.error('Error fetching initial domain suggestions:', error)
    }
  }
}

// Add onMounted hook before the template
onMounted(() => {
  fetchIndustries()
  loadInitialSuggestions()
})
</script>

<template>
  <div class="flex flex-col">
    <!-- Header Section -->
    <div class="mb-12">
      <div class="space-y-2">
        <h1 class="font-bold text-h1 text-secondary">
          {{ authStore.onboardingData.systemType === 'store' ? $t('update_profile.store_title') : $t('update_profile.activity_title') }}
        </h1>
        <p class="text-base text-[#7C7C7C]">
          {{ authStore.onboardingData.systemType === 'store' ? $t('update_profile.store_description') : $t('update_profile.activity_description') }}
        </p>
      </div>
    </div>

    <!-- URL Section - Only for store type -->
    <div class="mb-12 w-full">
      <h1 class="mb-2 text-lg font-medium">
        {{ $t('update_profile.store_url') }} <span class="text-red-500">*</span>
      </h1>
      <form-group :validation="$v" name="storeUrl">
        <template #default="{ attrs }">
          <UrlInput
            v-bind="attrs"
            v-model="formData.storeUrl"
            :placeholder="$t('update_profile.store_url_placeholder')"
            required
            class="w-full font-medium"
            :error-message="urlError"
            :loading="isCheckingUrl"
          >
            <template #suffix>
              <span class="text-gray-500">.mahjoz.net</span>
            </template>
          </UrlInput>
        </template>
      </form-group>

      <p v-if="formData.storeUrl && isUrlAvailable" class="mt-1 text-sm text-green-600">
        {{ $t('update_profile.url_available') }}
      </p>

      <!-- Show suggestions section -->
      <div v-if="(!formData.storeUrl || !isUrlAvailable) && !isCheckingUrl && suggestedDomains.length > 0" class="flex gap-2 items-center mt-4">
        <p class="text-sm font-medium text-secondary">
          {{ $t('update_profile.url_suggestions') }}
        </p>
        <div class="flex flex-wrap gap-2">
          <button
            v-for="domain in suggestedDomains"
            :key="domain"
            class="px-3 py-1.5 text-sm bg-gray-50 rounded-full border border-gray-200 transition-colors hover:bg-gray-100"
            @click="formData.storeUrl = domain; checkUrlAvailability()"
          >
            {{ domain }}
          </button>
        </div>
      </div>
    </div>

    <!-- Industry Selection -->
    <div class="mb-12 space-y-2">
      <h1 class="text-lg font-medium">
        {{ $t('update_profile.industry_title') }} <span class="text-red-500">*</span>
      </h1>
      <p class="text-sm text-[#7C7C7C]">
        {{ $t('update_profile.industry_description') }}
      </p>
      <form-group :validation="$v" name="industryId">
        <template #default="{ attrs }">
          <div v-if="isLoading" class="flex justify-center py-4">
            <span>{{ $t('update_profile.loading_industries') }}</span>
          </div>
          <div v-else class="flex flex-wrap gap-3 pt-2">
            <CardSelector
              v-for="industry in industries"
              :key="industry.uuid"
              :option="industry.name"
              :active="formData.industryId === industry.uuid"
              :icon="industry.icon"
              @click="toggleIndustry(industry)"
            />
          </div>
        </template>
      </form-group>
    </div>

    <!-- Service Type -->
    <div class="mb-12 space-y-2">
      <h1 class="text-lg font-medium">
        {{ $t('update_profile.service_type_title') }} <span class="text-red-500">*</span>
      </h1>
      <p class="text-sm text-[#7C7C7C]">
        {{ $t('update_profile.service_type_description') }}
      </p>
      <form-group :validation="$v" name="serviceType">
        <template #default="{ attrs }">
          <div class="flex flex-wrap gap-3 pt-2">
            <CardSelector
              v-for="(type, index) in serviceTypes"
              :key="index"
              :option="type.name"
              :icon="type.icon"
              :active="formData.serviceType === type.value"
              @click="selectServiceType(type)"
            />
          </div>
        </template>
      </form-group>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex flex-col gap-4 justify-between items-center self-stretch py-4 sm:flex-row sm:gap-0 text-secondary">
      <button class="flex gap-2 justify-start items-center py-2.5 rounded-xl" @click="router.push('/onboarding/update-info')">
        <img src="@/assets/icons/arrows/back.svg" alt="Back" class="w-6 h-6">
        <span class="justify-start text-lg font-medium leading-loose cursor-pointer">{{ $t('update_profile.back') }}</span>
      </button>

      <div class="flex gap-6 justify-center items-center">
        <button
          class="flex gap-2 justify-center items-center px-6 py-4 w-64 h-12 rounded-xl cursor-pointer disabled:cursor-not-allowed"
          :class="[
            $v.$invalid || isLoading
              ? 'bg-gray-300'
              : 'bg-gradient-to-l from-sky-400 to-green-300',
          ]"
          :disabled="$v.$invalid || isLoading"
          @click="submitForm"
        >
          <span class="justify-start text-lg font-bold text-white">{{ $t('update_profile.next') }}</span>
        </button>
      </div>
    </div>
  </div>
</template>
