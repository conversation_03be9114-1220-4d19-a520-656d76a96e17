<script setup>
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import useVuelidate from '@vuelidate/core'
import { useAuthStore } from '@/stores/auth'
import GenericSchedule from '@/components/Generic/GenericSchedule.vue'
import useNotifications from '@/composables/useNotifications'

const { showNotification } = useNotifications()
const { t } = useI18n()
const router = useRouter()
const authStore = useAuthStore()
const isLoading = ref(false)

// Add tenant type computed property
const tenantType = computed(() => authStore.onboardingData?.systemType || 'store')

// Replace individual refs with formData
const formData = ref({
  service: { name: '', price: '' },
  provider: { name: '' },
})

// Add vuelidate without required fields
const rules = {}
const $v = useVuelidate(rules, formData)

const completeProfile = async () => {
  try {
    isLoading.value = true
    const payload = {
      services: formData.value.service.name
        ? [{
            name: formData.value.service.name,
            price: Number(formData.value.service.price),
          }]
        : [],
      staffs: formData.value.provider.name
        ? [{
            name: formData.value.provider.name,
          }]
        : [],
    }

    await authStore.completeProfile(payload)
    await authStore.getProfile()
    router.push('/launch-profile')
  }
  catch (error) {
    showNotification({
      title: t('Error'),
      type: 'error',
      message: t('update_info.errors.save_error'),
    })
  }
  finally {
    isLoading.value = false
  }
}

const skipStep = async () => {
  try {
    isLoading.value = true
    await authStore.completeProfile({ services: [], staffs: [] })
    await authStore.getProfile()
    router.push('/launch-profile')
  }
  catch (error) {
    showNotification({
      title: t('Error'),
      type: 'error',
      message: t('update_info.errors.save_error'),
    })
  }
  finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div class="flex flex-col space-y-12">
    <!-- Service Setup Header -->
    <div class="space-y-2">
      <h1 class="font-bold text-h1 text-secondary">
        {{ $t('complete_profile.title') }}
      </h1>
      <p class="text-base text-[#7C7C7C]">
        {{ $t('complete_profile.description') }}
      </p>
    </div>

    <!-- Working Hours Section -->
    <div class="self-stretch px-6 py-8 bg-white rounded-2xl shadow-[1px_3px_15px_0px_rgba(0,0,0,0.08)] flex flex-col gap-8 overflow-hidden">
      <div class="flex flex-col gap-2 self-stretch">
        <h3 class="text-2xl font-medium text-black text-secondary">
          {{ $t('complete_profile.working_hours_title') }}
        </h3>
        <p class="self-stretch text-[#7C7C7C] text-base tracking-wide">
          {{ $t('complete_profile.working_hours_description') }}
        </p>
      </div>

      <div class="overflow-hidden relative ring-1 ring-black ring-opacity-5 shadow md:rounded-lg">
        <GenericSchedule
          :item-id="authStore.userInfo.teams?.[0]?.uuid"
          model="team"
        />
      </div>
    </div>

    <!-- Services Section -->
    <div class="self-stretch px-6 py-8 bg-white rounded-2xl shadow-[1px_3px_15px_0px_rgba(0,0,0,0.08)] flex flex-col gap-8 overflow-hidden">
      <div class="flex flex-col gap-2 self-stretch">
        <h3 class="text-2xl font-medium text-black text-secondary">
          {{ $t('complete_profile.add_services_title') }}
        </h3>
        <p class="self-stretch text-[#7C7C7C] text-base tracking-wide">
          {{ $t('complete_profile.add_services_description') }}
        </p>
      </div>
      <div class="flex flex-col gap-2 self-stretch">
        <div class="inline-flex gap-4 justify-end items-start self-stretch">
          <div class="inline-flex flex-col flex-1 justify-start items-end">
            <TextInput
              v-model="formData.service.name"
              :placeholder="$t('complete_profile.service_example')"
              :label="$t('complete_profile.service_name')"
            />
          </div>
          <div class="inline-flex flex-col flex-1 justify-start items-end">
            <NumberInput
              v-model="formData.service.price"
              :placeholder="$t('complete_profile.price_example')"
              :label="$t('complete_profile.price')"
              class="w-full"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Service Providers Section -->
    <div class="self-stretch px-6 py-8 bg-white rounded-2xl shadow-[1px_3px_15px_0px_rgba(0,0,0,0.08)] flex flex-col gap-8 overflow-hidden">
      <div class="flex flex-col gap-2 self-stretch">
        <h3 class="text-2xl font-medium text-black text-secondary">
          {{ $t('complete_profile.add_providers_title') }}
        </h3>
        <p class="self-stretch text-[#7C7C7C] text-base tracking-wide">
          {{ $t('complete_profile.add_providers_description') }}
        </p>
      </div>
      <div class="flex flex-col gap-4 self-stretch">
        <TextInput
          v-model="formData.provider.name"
          :placeholder="$t('complete_profile.staff_example')"
          :label="$t('complete_profile.provider_name')"
        />
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex flex-col gap-4 justify-between items-center self-stretch py-4 sm:flex-row sm:gap-0 text-secondary">
      <button class="flex order-1 gap-2 justify-center items-center py-2.5 w-full rounded-xl sm:w-auto sm:justify-start sm:order-1" @click="router.push('/onboarding/update-profile')">
        <img src="@/assets/icons/arrows/back.svg" alt="Back" class="w-6 h-6">
        <span class="text-lg font-medium leading-loose cursor-pointer">{{ $t('complete_profile.back') }}</span>
      </button>

      <div class="flex flex-col order-2 gap-4 justify-center items-center w-full sm:flex-row sm:gap-6 sm:w-auto sm:order-2">
        <button
          class="flex order-1 gap-2 justify-center items-center py-2.5 w-full rounded-xl sm:w-auto sm:order-1"
          :disabled="isLoading"
          @click="skipStep"
        >
          <span class="text-base text-gray-500">
            {{ $t('complete_profile.skip') }}
          </span>
        </button>
        <button
          class="flex order-2 gap-2 justify-center items-center px-6 py-4 w-full h-12 rounded-xl cursor-pointer sm:w-64 disabled:cursor-not-allowed sm:order-2"
          :class="[
            isLoading
              ? 'bg-gray-300'
              : 'bg-gradient-to-l from-sky-400 to-green-300',
          ]"
          :disabled="isLoading"
          @click="completeProfile"
        >
          <span v-if="isLoading" class="text-white">{{ $t('complete_profile.saving') }}</span>
          <span v-else class="text-lg font-bold text-white">
            {{ $t('complete_profile.confirm') }}
          </span>
        </button>
      </div>
    </div>
  </div>
</template>

