<script setup lang="ts">
const route = useRoute()

const { order_id, total_cost, payment_type } = toRefs(route.query)
const isBankTransfer = computed(() => payment_type.value == 'bank_transfer')
onMounted(() => {
  PostAffTracker.setAccountId('ab65b0d7')
  const sale = PostAffTracker.createSale()
  sale.setTotalCost(total_cost.value)
  sale.setOrderID(order_id.value)
  PostAffTracker.register()
})
</script>

<template>
  <div class="flex flex-col justify-center items-center mt-10">
    <div class="p-8 text-center bg-white rounded-lg payment-success">
      <h1 class="mb-4 text-3xl font-bold text-green-600">
        {{ payment_type ? $t("payment_success_bank_transfer_message") : $t("payment_success") }}!
      </h1>
      <p v-if="!payment_type" class="mb-6 text-gray-700">
        {{ $t("payment_success_message") }}
      </p>
      <router-link
        :to="{ name: 'dashboard' }"
        class="inline-block px-6 py-2 text-white bg-blue-600 rounded transition duration-300 hover:bg-blue-700"
      >
        {{ $t("payment_success_go_to_home") }}
      </router-link>
    </div>
  </div>
</template>
