<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { ChevronDownIcon, NoSymbolIcon } from '@heroicons/vue/20/solid'
import { useTagStore } from '@/stores/tags'
import type { Booking, Customer } from '@/types'
const tagStore = useTagStore()
const { tags } = storeToRefs(tagStore)
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const {
  removeCustomer,
  blockCustomer,
  fetchCustomerBookings,
  fetchCustomerById,
  fetchCustomerInvoices,
} = useCustomerStore()

const route = useRoute()
const router = useRouter()
const crumbs = ref([
  {
    name: 'homepage.customers',
    path: '/management/customers',
  },
  {
    name: '',
    path: '',
  },
])
const customer = ref<Customer>({
  uuid: '',
  first_name: '',
  last_name: '',
  email: '',
  phone: '',
  address: '',
  phone_country: '',
  note: '',
  tags: [],
})
const customerBooking = ref<Booking[]>([])
const loadingInvoices = ref(false)
const bookingsLoading = ref(false)
const isOpenOrderModal = ref(false)
const orderId = ref('')
const openOrderModal = (order_id: string) => {
  orderId.value = order_id
  isOpenOrderModal.value = true
}

const closeOrderModal = () => {
  isOpenOrderModal.value = false
  orderId.value = ''
}
const processing = ref(false)
const customerInvoices = reactive<any>({ list: [] }) // any for now later would be invoice array type

const fetchCustomerDetails = () => {
  loadingInvoices.value = true
  bookingsLoading.value = true
  Promise.all([
    fetchCustomerById(route.params.id as string).then((res) => {
      crumbs.value[crumbs.value.length - 1].name = `${res.first_name} ${
        res.last_name ?? ''
      }`
      customer.value = res
    }),
    fetchCustomerBookings(route.params.id as string).then((res) => {
      customerBooking.value = res.data
      bookingsLoading.value = false
    }),
    fetchCustomerInvoices(route.params.id as string).then((res) => {
      customerInvoices.list = res.data
      loadingInvoices.value = false
    }),
    tagStore.fetchTags('customers'),
  ]).catch(() => {
    router.push({ path: '/management/customers' })
  })
}

onMounted(() => {
  fetchCustomerDetails()
})
const showModal = ref(false)
const toggleModel = () => {
  showModal.value = false
}
const updated = (resUpdated: Customer) => {
  customer.value = resUpdated
}
const showConfModal = ref(false)
const showConfBlockModal = ref(false)

const removed = () => {
  showConfModal.value = true
}
const toggleBlock = async () => {
  processing.value = true
  await blockCustomer(customer.value.uuid).then((res) => {
    customer.value.block = !customer.value.block
  })
  processing.value = false
}
const editRecored = () => {
  showModal.value = true
}
</script>

<template>
  <div>
    <overlay-loader v-if="processing" />
    <AddCustomerModel
      v-if="showModal"
      :customer="customer"
      :tags="tags.data"
      :show-modal="showModal"
      @updated="updated"
      @closed="toggleModel"
    />
    <confirmation-modal
      :dir="getLocale(locale)?.direction"
      :is-open="showConfModal"
      redirect-url="/management/customers"
      :api-call="removeCustomer"
      :record-id="customer.uuid"
      @closed="showConfModal = false"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.msg") }}
      </p>
    </confirmation-modal>

    <order-details-modal
      v-if="isOpenOrderModal"
      :is-open="isOpenOrderModal"
      :order-id="orderId"
      @close="closeOrderModal"
      @refresh="fetchCustomerDetails()"
    />
    <div class="flex flex-col">
      <bread-crumb :crumbs="crumbs" class="mt-5" />

      <div
        class="flex flex-col gap-4 justify-between items-center my-6 mb-4 sm:flex-row sm:gap-0"
      >
        <div class="flex gap-1 items-center sm:gap-3 group">
          <h1
            class="flex gap-2 items-center text-base font-semibold sm:text-3xl"
          >
            {{ customer?.first_name }} {{ customer?.last_name }}
            <span v-if="customer.block" class="px-2 py-1">
              <NoSymbolIcon
                class="w-7 h-7 text-red-500 cursor-pointer"
                aria-hidden="true"
              /></span>
          </h1>
          <span
            v-if="customer.block"
            class="hidden ml-2 text-red-500 group-hover:inline-block"
          >{{ $t("block_msg") }}</span>
        </div>
        <div class="inline-flex relative rounded-md shadow-sm">
          <button
            type="button"
            class="inline-flex relative order-1 items-center px-3 py-2 text-sm font-semibold text-gray-900 bg-white rounded-l-md ring-1 ring-inset ring-gray-300 max-w-48 w-fit sm:w-auto hover:bg-gray-50 focus:z-10 rtl:order-2"
            @click="editRecored()"
          >
            {{ $t("form.edit") }}
          </button>
          <Menu as="div" class="block order-2 -ml-px sm:w-auto rtl:order-1">
            <MenuButton
              class="inline-flex justify-center items-center px-2 py-2 w-full text-gray-400 bg-white rounded-r-md ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10"
            >
              <span class="sr-only">Open options</span>
              <ChevronDownIcon class="w-5 h-5" aria-hidden="true" />
            </MenuButton>
            <transition
              enter-active-class="transition duration-100 ease-out"
              enter-from-class="opacity-0 transform scale-95"
              enter-to-class="opacity-100 transform scale-100"
              leave-active-class="transition duration-75 ease-in"
              leave-from-class="opacity-100 transform scale-100"
              leave-to-class="opacity-0 transform scale-95"
            >
              <MenuItems
                class="absolute -left-full z-10 mt-2 w-48 bg-white rounded-md ring-1 ring-black ring-opacity-5 shadow-lg origin-top-right max-w-64 focus:outline-none rtl:left-auto rtl:-right-full"
              >
                <div class="py-1">
                  <MenuItem>
                    <button
                      type="button"
                      class="block p-2 px-4 w-full text-sm hover:bg-gray-200 text-start"
                      @click="toggleBlock()"
                    >
                      {{
                        customer.block ? $t("form.unblock") : $t("form.block")
                      }}
                    </button>
                  </MenuItem>
                  <MenuItem>
                    <button
                      type="button"
                      class="block p-2 px-4 w-full text-sm hover:bg-gray-200 text-start"
                      @click="removed()"
                    >
                      {{ $t("form.deleteCustomer") }}
                    </button>
                  </MenuItem>
                </div>
              </MenuItems>
            </transition>
          </Menu>
        </div>
      </div>
      <TagsComp :tags="customer?.tags" />
      <div class="pb-8">
        <div class="grid grid-cols-1 sm:grid-cols-2">
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="customer"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("booking.customer") }}
            </label>
            <div class="flex mt-1 font-semibold">
              <div
                class="flex relative flex-grow items-stretch focus-within:z-10"
              >
                {{ customer?.first_name }} {{ customer?.last_name }}
                <span v-if="customer.block" />
              </div>
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="staff"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("form.phone") }}
            </label>
            <div class="flex gap-1 items-center mt-1 font-semibold">
              <span v-if="customer?.phone" class="">
                {{ customer?.phone }}
              </span>
              <span v-else class="text-neutral-400">-</span>
              <a
                v-if="customer?.phone"
                :href="`https://wa.me/${customer?.phone}`"
                target="_blank"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-5 h-5"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="#25d366"
                    d="M19.05 4.91A9.816 9.816 0 0 0 12.04 2c-5.46 0-9.91 4.45-9.91 9.91c0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21c5.46 0 9.91-4.45 9.91-9.91c0-2.65-1.03-5.14-2.9-7.01zm-7.01 15.24c-1.48 0-2.93-.4-4.2-1.15l-.3-.18l-3.12.82l.83-3.04l-.2-.31a8.264 8.264 0 0 1-1.26-4.38c0-4.54 3.7-8.24 8.24-8.24c2.2 0 4.27.86 5.82 2.42a8.183 8.183 0 0 1 2.41 5.83c.02 4.54-3.68 8.23-8.22 8.23zm4.52-6.16c-.25-.12-1.47-.72-1.69-.81c-.23-.08-.39-.12-.56.12c-.17.25-.64.81-.78.97c-.14.17-.29.19-.54.06c-.25-.12-1.05-.39-1.99-1.23c-.74-.66-1.23-1.47-1.38-1.72c-.14-.25-.02-.38.11-.51c.11-.11.25-.29.37-.43s.17-.25.25-.41c.08-.17.04-.31-.02-.43s-.56-1.34-.76-1.84c-.2-.48-.41-.42-.56-.43h-.48c-.17 0-.43.06-.66.31c-.22.25-.86.85-.86 2.07c0 1.22.89 2.4 1.01 2.56c.12.17 1.75 2.67 4.23 3.74c.59.26 1.05.41 1.41.52c.59.19 1.13.16 1.56.1c.48-.07 1.47-.6 1.67-1.18c.21-.58.21-1.07.14-1.18s-.22-.16-.47-.28z"
                  />
                </svg>
              </a>
              <!-- <option v-for="service of lookups.services" :key="service.uuid" :value="service.uuid">
                    {{ service.name }} ({{
                      `${service.duration} ${$t("booking.minutes")}`
                    }})
                  </option> -->
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="staff"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("form.email") }}
            </label>
            <div class="mt-1 font-semibold">
              <span v-if="customer?.email">
                {{ customer?.email }}
              </span>
              <span v-else class="text-neutral-400">-</span>
            </div>
          </div>

          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("form.address") }}
            </label>
            <div v-if="customer?.address" class="mt-1 font-semibold">
              {{ customer?.address }}
            </div>
            <span v-else class="text-neutral-400">-</span>
          </div>
        </div>
      </div>
      <CustomerInvoices
        :list="customerInvoices.list"
        :loading-invoices="loadingInvoices"
        @open-order-modal="openOrderModal"
      />
      <CustomerBookings
        :bookings="customerBooking"
        :customer="customer"
        :bookings-loading="bookingsLoading"
        @open-order-modal="openOrderModal"
      />

      <customer-attachment />
      <customer-notes />
    </div>
  </div>
</template>

<style></style>
