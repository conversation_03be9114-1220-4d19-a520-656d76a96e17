<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { ArrowDownIcon, PlusIcon } from '@heroicons/vue/24/outline'
import excelExport from '@/composables/useExcelExport'

const { tableData, fetchSalePage } = usePos()
const { sales } = storeToRefs(usePosStore())
const { clearPos } = usePosStore()
// const { processingExport, getExcel } = excelExport('Sale')

const search = ref('')
const filtersRef = reactive({
  id: '',
  range: '',
  status: 'draft',
})
const loadingSales = ref(false)
const filterSales = (filters) => {
  const { id, range, status } = filters
  filtersRef.id = id
  filtersRef.range = range
  filtersRef.status = status
  const currentPage = tableData.paginationMeta.current_page
  const params = `${id ? `id=${id}` : ''}&${range || ''}&status=${status || 'draft'}`
  search.value = params
  loadingSales.value = true
  fetchSalePage(currentPage, params).finally(() => {
    loadingSales.value = false
  })
}

const getFilteredExcel = () => {
  const { getExcel } = excelExport('Sale', { id: filtersRef.id, range: filtersRef.range, status: filtersRef.range })
  getExcel()
}

const router = useRouter()
function goToCheckout() {
  router.push({ path: '/point-of-sale' })
}

const changePage = (page) => {
  loadingSales.value = true
  fetchSalePage(page, search.value).finally(() => {
    loadingSales.value = false
  })
}
onMounted(() => {
  loadingSales.value = true
  fetchSalePage(tableData.paginationMeta?.current_page, '').finally(() => {
    loadingSales.value = false
  })
})

const currentTab = ref('all')

onUnmounted(() => {
  clearPos()
})
</script>

<template>
  <div>
    <div class="flex justify-between items-center  ms-auto me-auto max-w-12xl  ">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t(`homepage.${$route?.name}` || 'homepage.title') }}
      </h1>
      <div class="mt-4 sm:mt-0  sm:flex-none">
        <BaseButton
          type="button" :disabled="processingExport" class="w-4 inline-flex bg-green-600 disabled:bg-green-300 me-2"
          @click="getFilteredExcel()"
        >
          {{ $t("table.export_excel") }}
          <ArrowDownIcon class="ms-2 -me-0.5 h-4 w-4" aria-hidden="true" />
        </BaseButton>

        <BaseButton class="inline-flex w-auto hover:bg-green-700" custome-bg="bg-green-600" @click="goToCheckout()">
          {{ $t("form.new_invoice") }}
          <PlusIcon class="ms-2 -me-0.5 h-4 w-4" aria-hidden="true" />
        </BaseButton>
      </div>
    </div>

    <sale-filteration @changed="filterSales" />
    <sale-grid
      :list="sales" class="mt-5" :loading-sales="loadingSales"
      @open-booking-modal="openEvent"
    />
    <Pagination
      v-if="sales.length" :pagination-meta="tableData.paginationMeta"
      :pagination-links="tableData.paginationLinks" class="px-4" @change="changePage"
    />
  </div>
</template>

