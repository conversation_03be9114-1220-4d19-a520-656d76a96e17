<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { Menu, MenuButton, MenuItem, MenuItems, Switch } from '@headlessui/vue'
import { ChevronDownIcon, HomeIcon } from '@heroicons/vue/20/solid'
import { GoogleMap, Polygon } from 'vue3-google-map'
import type { ComputedRef } from 'vue'
import type { header } from '@/types'
import { useBoundaries } from '@/stores/boundaries'
const API_KEY = import.meta.env.VITE_GOOGLE_MAP_KEY
const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()

const route = useRoute()
const router = useRouter()
const { fetchSingleBoundary, deleteBoundary, toggleStaffBoundaries }
  = useBoundaries()
const { coords, calculateCenter } = useMap()

const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('serviceProvider.tabs.zones.toggle'),
    },
    {
      title: t('services.staffs'),
    },
    // {
    //   title: t("modalPlacholder.branch"),
    // }
  ]
})
// prepare Boundary data
const boundaryData = ref({})
const processing = ref(false)
const center = ref()
const bermudaTriangle = reactive({
  strokeColor: '#FF0000',
  strokeOpacity: 0.8,
  strokeWeight: 2,
  fillColor: '#FF0000',
  fillOpacity: 0.35,
  editable: false,
  draggable: false,
  geodesic: true,
  paths: [],
})

// breadcrumbs
const crumbs = ref([
  {
    name: 'homepage.boundaries',
    path: '/management/boundaries',
  },
  {
    name: '',
    path: '',
  },
])
const proccessing = ref(false)
const googleMapPolygon = ref(null)
const mapPaths = ref([])
const showConfirmModal = ref(false)
const showModal = ref(false)
const boundaryId = ref<string | null>(null)
const team_id = ref<string | null>(null)
onBeforeMount(async () => {
  processing.value = true
  try {
    await loadBoundary()
    crumbs.value[1].name = boundaryData.value.name
  }
  finally {
    processing.value = false
  }
})
const loadBoundary = async () => {
  const data = await fetchSingleBoundary(route.params.id as string)
  boundaryData.value = data
  const paths = data.boundary.coordinates.flat().map(([lng, lat]) => {
    return { lat, lng }
  })
  center.value = calculateCenter(paths)
  mapPaths.value = paths
}

watch(
  () => [googleMapPolygon.value?.polygon, mapPaths.value.length],
  (value) => {
    if (googleMapPolygon.value?.polygon && mapPaths.value.length)
      googleMapPolygon.value.polygon.setPath(mapPaths.value)
  },
  { immediate: true },
)

const openEditModel = (item: any) => {
  showModal.value = true
  team_id.value = item.team.id
  boundaryId.value = item.id
}
const openConfirmModal = (item: any) => {
  boundaryId.value = item.id
  showConfirmModal.value = true
}
async function closed() {
  showModal.value = false
  loadBoundary()
}
const deleteRecord = async () => {
  try {
    proccessing.value = true
    await deleteBoundary(boundaryId.value as string)
    boundaryId.value = null
    team_id.value = null
    showConfirmModal.value = false
    router.push({ name: 'boundaries' })
  }
  finally {
    proccessing.value = false
  }
}

const current_staff = ref('')
const toggleStaffBounday = async (staffId: string, boundaryId: string) => {
  try {
    current_staff.value = staffId
    proccessing.value = true
    await toggleStaffBoundaries(staffId, boundaryId)
    loadBoundary()
  }
  finally {
    proccessing.value = false
    current_staff.value = ''
  }
}
</script>

<template>
  <div>
    <confirmation-modal
      v-if="showConfirmModal"
      :is-open="showConfirmModal"
      :dir="getLocale(locale)?.direction"
      class="z-50"
      @closed="showConfirmModal = false"
      @removed="deleteRecord"
    />

    <generic-boundary-modal
      v-if="showModal"
      v-model:show-modal="showModal"
      model="team"
      :item-id="team_id"
      :boundary-id="boundaryId"
      @updated="closed"
      @close="closed"
    />

    <div class="flex relative flex-col">
      <overlay-loader v-if="processing" :full-screen="false" />
      <bread-crumb :crumbs="crumbs" class="mt-5" />
      <div
        class="flex flex-col gap-4 justify-between items-center mt-6 mb-4 sm:flex-row sm:gap-0"
      >
        <div class="flex gap-1 items-center sm:gap-3">
          <h1 class="text-base font-semibold sm:text-3xl">
            {{ boundaryData?.name }}
          </h1>
        </div>
        <div class="flex gap-1 items-center sm:gap-3">
          <span class="text-gray-800">{{ $t("form.branch") }}</span>
          <h1 class="text-base font-semibold sm:text-md">
            : {{ boundaryData?.team?.name }}
          </h1>
        </div>

        <div class="inline-flex w-full rounded-md shadow-sm sm:w-auto">
          <button
            type="button"
            class="inline-flex relative items-center px-3 py-2 w-3/4 text-sm font-semibold text-gray-900 bg-white rounded-l-md ring-1 ring-inset ring-gray-300 sm:w-auto hover:bg-gray-50 focus:z-10"
            :class="[
              getLocale(locale)?.direction === 'rtl' ? 'order-2' : 'order-1',
            ]"
            @click="openEditModel(boundaryData)"
          >
            {{ $t("form.edit") }}
          </button>
          <Menu
            as="div"
            class="block relative -ml-px w-1/4 sm:w-auto"
            :class="
              getLocale(locale)?.direction === 'rtl' ? 'order-1' : 'order-2'
            "
          >
            <MenuButton
              class="inline-flex relative justify-center items-center px-2 py-2 w-full text-gray-400 bg-white rounded-r-md ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10"
            >
              <span class="sr-only">Open options</span>
              <ChevronDownIcon class="w-5 h-5" aria-hidden="true" />
            </MenuButton>
            <transition
              enter-active-class="transition duration-100 ease-out"
              enter-from-class="opacity-0 transform scale-95"
              enter-to-class="opacity-100 transform scale-100"
              leave-active-class="transition duration-75 ease-in"
              leave-from-class="opacity-100 transform scale-100"
              leave-to-class="opacity-0 transform scale-95"
            >
              <MenuItems
                class="absolute z-10 mt-2 -mr-1 w-48 bg-white rounded-md ring-1 ring-black ring-opacity-5 shadow-lg origin-top-right focus:outline-none"
                :class="[
                  getLocale(locale)?.direction === 'rtl'
                    ? '-right-20'
                    : '-left-40',
                ]"
              >
                <div class="py-1">
                  <MenuItem>
                    <button
                      type="button"
                      class="block p-2 w-full text-sm text-red-600 text-start"
                      @click="openConfirmModal(boundaryData)"
                    >
                      {{ $t("form.delete") }}
                    </button>
                  </MenuItem>
                </div>
              </MenuItems>
            </transition>
          </Menu>
        </div>
      </div>

      <GoogleMap
        ref="map"
        :api-key="API_KEY"
        class="w-full h-96"
        :center="center"
        :zoom="13"
        :draggable="false"
      >
        <Polygon ref="googleMapPolygon" :options="bermudaTriangle" />
      </GoogleMap>

      <h2 class="my-2 text-2xl font-semibold">
        {{ $t(`services.staffs`) }}
      </h2>
      <div class="flex flex-col mt-8">
        <div class="">
          <generic-table
            :data="boundaryData.staff"
            item-key="id"
            :headers="headers"
          >
            <template #row="{ item }">
              <grid-td
                class="py-4 pr-3 pl-4 text-sm whitespace-nowrap sm:pl-6"
                :default-style="false"
              >
                <div class="flex items-center">
                  <the-loader v-if="current_staff === item.id" />
                  <Switch
                    v-else
                    v-model="item.selected"
                    class="inline-flex relative flex-shrink-0 w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                    :class="
                      item.selected ? 'bg-primary-600' : 'bg-gray-200'
                    "
                    @update:model-value="
                      () => toggleStaffBounday(item.id, boundaryData.id)
                    "
                  >
                    <span class="sr-only">Service Status</span>
                    <span
                      aria-hidden="true"
                      class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
                      :class="
                        item.selected
                          ? 'translate-x-0'
                          : 'translate-x-5 rtl:-translate-x-5'
                      "
                    />
                  </Switch>
                </div>
              </grid-td>
              <grid-td>
                {{ item.name }}
              </grid-td>
            </template>
          </generic-table>
        </div>
      </div>
    </div>
  </div>
</template>
