<script setup lang="ts">
import { storeToRefs } from 'pinia'
import router from '@/router'

const { sendVerificationLink, verifyEmailAddress, logout } = useAuthStore()
const { locale } = useI18n()
const route = useRoute()
const { t } = useI18n()
const verifying = ref(false)
const sendingLink = ref(false)
const { userInfo } = storeToRefs(useAuthStore())
const { showNotification } = useNotifications()
const resendLink = () => {
  sendingLink.value = true
  sendVerificationLink().finally(() => {
    sendingLink.value = false
  })
}

onBeforeMount(async () => {
  if (route.query.link) {
    const url = window.location.href
      .split('?link=')[1]
      .toString()
      .replace('/api/v1/', '')
    verifying.value = true
    try {
      await verifyEmailAddress(url as string)
      router.push({ name: 'dashboard' })
    }
    catch (error) {
      window.location.reload()
    }
    finally {
      verifying.value = false
    }
  }
  else if (route.query.reload) {
    window.location.href = window.location.href.replace('?reload=1', '')
  }
  else if (userInfo.value.email_verified) {
    router.push({ name: 'dashboard' })
  }
})
const processing = ref(false)
const logOut = async () => {
  processing.value = true
  logout().then(() => {
    localStorage.clear()
    router.push({ name: 'auth', query: { section: 'sign-in' } })
  }).finally(() => {
    processing.value = false
  })
}
</script>

<template>
  <div class="flex items-center justify-center flex-col mt-24">
    <h1 class="text-xl text-center mb-6 px-12 ">
      {{ $t("unverified_email.alert") }}
    </h1>
    <div class="flex items-center justify-center gap-2 mb-6">
      <!-- logOut button -->
      <base-button
        class="flex items-center justify-center gap-2 bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        :processing="processing" @click="logOut"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 cursor-pointer" viewBox="0 0 24 24" title="LogOut">
          <path
            fill="#fff"
            d="M13 3h-2v10h2V3zm4.83 2.17l-1.42 1.42A6.92 6.92 0 0 1 19 12c0 3.87-3.13 7-7 7A6.995 6.995 0 0 1 7.58 6.58L6.17 5.17A8.932 8.932 0 0 0 3 12a9 9 0 0 0 18 0c0-2.74-1.23-5.18-3.17-6.83z"
          />
        </svg>
        <span>{{ $t("logout") }}</span>
      </base-button>

      <base-button
        class="flex items-center justify-center gap-2 bg-primary-800 hover:bg-primary-600 text-white font-semibold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        :processing="sendingLink"
        @click="resendLink"
      >
        <span>{{ $t("unverified_email.resend") }}</span>
      </base-button>

      <a
        href="/dashboard"
        class="flex items-center justify-center gap-2 bg-primary-800 hover:bg-primary-600 text-white font-semibold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
      >
        <span>{{ $t("home_page") }}</span>
      </a>
    </div>
  </div>
</template>
