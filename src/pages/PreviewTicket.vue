<script lang="ts" setup>
import { PrinterIcon } from '@heroicons/vue/20/solid'
const { getInvoiceHtml } = usePosStore()
const route = useRoute()
const router = useRouter()
const html = ref('')
const processing = ref(false)
onMounted(async () => {
  try {
    const id = route.params?.id as string
    processing.value = true
    if (id) {
      const invioceHTML = await getInvoiceHtml(id)
      // html.value = invioceHTML.data;
      document.write(invioceHTML.data)
    }
  }
  catch (e) {
    router.push({ name: 'invoices' })
  }
  finally {
    processing.value = false
  }
})
async function printInvoice() {
  const prtHtml = (document.getElementById('invoice') as HTMLElement)
    .innerHTML
    // Get all stylesheets HTML
  let stylesHtml = ''
  const stylesheets = document.querySelectorAll(
    'link[rel="stylesheet"], style',
  )
  stylesheets.forEach((node) => {
    stylesHtml += node.outerHTML
  })
  // Open the print window
  const WinPrint = window.open(
    '',
    '',
    'left=0,top=0,width=800,height=900,toolbar=0,scrollbars=0,status=0',
  ) as Window & { document: Document }

  WinPrint.document.write(`<!DOCTYPE html>
  <html dir="rtl">
    <head>
      ${stylesHtml}
    </head>
    <body>
      ${prtHtml}
    </body>
  </html>`)

  WinPrint.document.close()
  WinPrint.focus()
  WinPrint.addEventListener('load', async () => {
    await WinPrint.print()
  })
}
</script>

<template>
  <main id="invoice-preview">
    <div class="relative flex flex-col w-full">
      <overlay-loader v-if="processing" :full-screen="false" />
      <!-- show invioce -->
      <div
        class="flex items-start justify-center px-4 py-6 border rounded-md border-primaty-200 rtl:flex-row-reverse"
      >
        <button
          class="flex items-center gap-2 px-3 py-2 text-sm font-semibold text-white bg-indigo-600 rounded-md shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          @click="printInvoice"
        >
          <PrinterIcon class="w-5 h-5" />
          {{ $t("pos.print") }}
        </button>
        <div class="w-full">
          <div v-html="html" />
        </div>
      </div>
    </div>
  </main>
</template>
