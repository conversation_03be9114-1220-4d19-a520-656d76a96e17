<script setup lang="ts">
import { PlusIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import { useBoundaries } from '@/stores/boundaries'

const emits = defineEmits(['close'])
const i18n = useI18n()
const { fetchBoundaries } = useBoundaries()
const { getBoundaries, getBoundariesLinks, getBoundariesMeta } = storeToRefs(
  useBoundaries(),
)
const router = useRouter()
const headers = computed(() => {
  return [
    {
      title: i18n.t('form.name'),
    },
    {
      title: i18n.t('modalPlacholder.branch'),
    },
    {
      title: i18n.t('form.boundary_fees'),
    },
  ]
})

onMounted(async () => {
  await Promise.all([fetchBoundaries()])
})

const proccessing = ref(false)
const showConfirmModal = ref(false)
const showModal = ref(false)
const isOpen = ref(false)
const boundaryId = ref<string | null>(null)
const team_id = ref<string | null>(null)
const team = ref('')
const boundaryName = ref(' ')

const openCreateModel = () => {
  boundaryId.value = null
  team_id.value = null
  showModal.value = true
}

watch([team, boundaryName], () => {
  if (!boundaryName.value && !team.value)
    fetchBoundaries()
})
const changeData = (page: number) => {
  fetchBoundaries(page)
}

const redirectToBoundary = (item: any) => {
  router.push({ name: 'boundary', params: { id: item.id } })
}
</script>

<template>
  <div>
    <generic-boundary-modal
      v-model:show-modal="showModal"
      model="team"
      :item-id="null"
      :boundary-id="null"
      @updated="fetchBoundaries()"
      @created="fetchBoundaries()"
      @close="(boundaryId = null), (team_id = null)"
    />
    <div
      class="flex flex-col justify-between items-center sm:flex-row ms-auto me-auto max-w-12xl"
    >
      <overlay-loader v-if="proccessing" :full-screen="false" />
      <h1 class="mb-4 text-2xl font-semibold text-gray-900 sm:mb-0">
        {{ $t(`homepage.${String($route?.name)}` || "homepage.title") }}
      </h1>
      <div class="flex flex-col gap-2 w-full sm:flex-row sm:w-auto">
        <BaseButton
          class="inline-flex w-full sm:w-auto hover:bg-green-700"
          custome-bg="bg-green-600"
          @click="openCreateModel()"
        >
          {{ $t("new_zone") }}
          <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
        <BaseButton
          class="inline-flex w-full sm:w-auto hover:bg-primary-700"
          custome-bg="bg-primary-600"
          @click="router.push({ name: 'staffsBoundaries' })"
        >
          {{ $t("show_staff_boundaries_on_map") }}
          <MapIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </div>
    <div class="grid grid-cols-1 gap-4 my-2 sm:grid-cols-2 md:grid-cols-2">
      <div class="col-span-1">
        <form-group error-name="name">
          <template #default="{ attrs }">
            <TextInput
              id="team"
              v-bind="attrs"
              v-model="boundaryName"
              :label="$t('form.name')"
              @keypress="fetchBoundaries(1, `name=${$event.target.value}`)"
            />
          </template>
        </form-group>
      </div>

      <div class="col-span-1 px-2">
        <form-group error-name="team_id">
          <template #default="{ attrs }">
            <filter-with-team
              v-bind="attrs"
              v-model="team"
              :default="0"
              @change="fetchBoundaries(1, `team=${$event.target.value}`)"
            />
          </template>
        </form-group>
      </div>
    </div>

    <div class="flex flex-col mt-8">
      <div class="">
        <generic-table
          :headers="headers"
          :data="getBoundaries"
          tr-class="cursor-pointer"
          :on-row-click="redirectToBoundary"
          :is-loading="proccessing"
        >
          <template #row="{ item }">
            <grid-td
              class="px-4 py-4 text-sm whitespace-nowrap sm:pl-6"
              :default-style="false"
            >
              {{ item.name }}
            </grid-td>
            <grid-td
              class="px-4 py-4 text-sm whitespace-nowrap sm:pl-6"
              :default-style="false"
            >
              {{ item.team.name }}
            </grid-td>
            <grid-td
              class="px-4 py-4 text-sm whitespace-nowrap sm:pl-6"
              :default-style="false"
            >
              {{ item.fees }}
            </grid-td>
          </template>
        </generic-table>

        <Pagination
          v-if="getBoundaries.length"
          :pagination-meta="getBoundariesMeta"
          :pagination-links="getBoundariesLinks"
          class="px-4"
          @change="changeData"
        />
      </div>
    </div>
  </div>
</template>

<style>
.cust-box {
  min-height: 70px;
}
</style>
