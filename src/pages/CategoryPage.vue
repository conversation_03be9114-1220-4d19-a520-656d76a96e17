<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { ChevronDownIcon, HomeIcon } from '@heroicons/vue/20/solid'

const { fetchCategoryById, removeCategory } = useCategoryStore()
const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const { getUserInfo } = storeToRefs(useAuthStore())
const route = useRoute()
const router = useRouter()
// prepare data
const categoryData = ref({})
const processing = ref(false)
onBeforeMount(async () => {
  processing.value = true
  try {
    const data = await fetchCategoryById(route.params.id as string)
    categoryData.value = data
  }
  finally {
    processing.value = false
  }
})
async function closed() {
  const data = await fetchCategoryById(route.params.id as string)
  categoryData.value = data
  showModal.value = false
}
// breadcrumbs
const crumbs = ref([
  {
    name: 'homepage.categories',
    path: '/management/categories',
  },
  {
    name: '',
    path: '',
  },
])
const showModal = ref(false)
function editRecored() {
  showModal.value = true
}

async function deleted() {
  await removeCategory(categoryData.value.uuid)
  router.push({ name: 'categories' })
}
const getTeam = computed(() => {
  return getUserInfo.value.teams
})
</script>

<template>
  <div>
    <CategoryModal
      v-if="showModal"
      :show="showModal"
      :category-data="categoryData"
      :teams="getTeam"
      :edit-mode="true"
      @close="showModal = false"
      @closed="closed"
    />
    <div class="flex relative flex-col">
      <overlay-loader v-if="processing" :full-screen="false" />
      <bread-crumb :crumbs="crumbs" class="mt-5" />
      <div class="flex flex-col gap-4 justify-between items-center mt-6 mb-4 sm:flex-row sm:gap-0">
        <div class="flex gap-1 items-center sm:gap-3">
          <h1 class="text-base font-semibold sm:text-3xl">
            {{ categoryData?.name }}
          </h1>
        </div>
        <div class="inline-flex w-full rounded-md shadow-sm sm:w-auto">
          <button
            type="button"
            class="inline-flex relative items-center px-3 py-2 w-3/4 text-sm font-semibold text-gray-900 bg-white rounded-l-md ring-1 ring-inset ring-gray-300 sm:w-auto hover:bg-gray-50 focus:z-10"
            :class="[
              getLocale(locale)?.direction === 'rtl' ? 'order-2' : 'order-1',
            ]" @click="editRecored()"
          >
            {{ $t("form.edit") }}
          </button>
          <Menu
            as="div" class="block relative -ml-px w-1/4 sm:w-auto" :class="getLocale(locale)?.direction === 'rtl' ? 'order-1' : 'order-2'
            "
          >
            <MenuButton
              class="inline-flex relative justify-center items-center px-2 py-2 w-full text-gray-400 bg-white rounded-r-md ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10"
            >
              <span class="sr-only">Open options</span>
              <ChevronDownIcon class="w-5 h-5" aria-hidden="true" />
            </MenuButton>
            <transition
              enter-active-class="transition duration-100 ease-out"
              enter-from-class="opacity-0 transform scale-95" enter-to-class="opacity-100 transform scale-100"
              leave-active-class="transition duration-75 ease-in" leave-from-class="opacity-100 transform scale-100"
              leave-to-class="opacity-0 transform scale-95"
            >
              <MenuItems
                class="absolute z-10 mt-2 -mr-1 w-48 bg-white rounded-md ring-1 ring-black ring-opacity-5 shadow-lg origin-top-right focus:outline-none"
                :class="[
                  getLocale(locale)?.direction === 'rtl'
                    ? '-right-20'
                    : '-left-40',
                ]"
              >
                <div class="py-1">
                  <MenuItem>
                    <button type="button" class="block p-2 w-full text-sm text-red-600 text-start" @click="deleted()">
                      {{ $t("form.delete") }}
                    </button>
                  </MenuItem>
                </div>
              </MenuItems>
            </transition>
          </Menu>
        </div>
      </div>
      <div v-if="!processing" class="pb-8">
        <img
          v-if="categoryData?.imageLink" id="image" class="block mx-auto mb-3 w-36 h-36 rounded-full"
          :src="categoryData.imageLink" :link="null"
        >
        <svg
          v-else
          class="block mx-auto mb-3 w-36 h-36 text-gray-300 rounded-full"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
        <div class="grid grid-cols-1 sm:grid-cols-2">
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label for="customer" class="block text-sm font-medium text-neutral-500 w-15">{{ $t("fields.name") }}
            </label>
            <div class="flex mt-1 font-semibold">
              <div class="flex relative flex-grow items-stretch focus-within:z-10">
                {{ categoryData?.name || categoryData?.name_localized[locale] || '-' }}
              </div>
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label for="booking-start" class="block text-sm font-medium text-neutral-500 w-15">{{
              $t("displayOnBookingPage") }}
            </label>
            <div class="mt-1 font-semibold">
              {{ categoryData?.display_on_booking_page ? $t("yes") : $t("no") }}
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label for="booking-start" class="block text-sm font-medium text-neutral-500 w-15">{{ $t("description") }}
            </label>
            <div v-if="categoryData?.description?.length > 0" class="mt-1 font-semibold">
              <p v-html="categoryData?.description || categoryData?.description_localized[locale]" />
            </div>
            <span v-else class="text-neutral-400">-</span>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label for="booking-start" class="block text-sm font-medium text-neutral-500 w-15">
              {{ $t("modalPlacholder.branch") }} :</label>
            <div v-if="categoryData?.team?.uuid" class="mt-1 font-semibold">
              {{ categoryData?.team?.name }}
            </div>
            <span v-else>-</span>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label for="booking-start" class="block text-sm font-medium text-neutral-500 w-15">
              {{ $t("form.sort_order") }} :</label>
            <div v-if="categoryData?.sort_order" class="mt-1 font-semibold">
              {{ categoryData?.sort_order }}
            </div>
            <span v-else>-</span>
          </div>
        </div>
      </div>
      <div class="my-4 w-full">
        <share-box :link="categoryData.base_link" :with-text="false" />
      </div>

      <!-- <ServicesTabs v-if="service?.uuid" :service="service" /> -->
    </div>
  </div>
</template>
