<script setup lang="ts">
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
  Listbox,
  ListboxButton,
  ListboxLabel,
  ListboxOption,
  ListboxOptions,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
} from '@headlessui/vue'
import {
  ArrowLongLeftIcon,
  ArrowLongRightIcon,
  BriefcaseIcon,
  CalendarIcon,
  CheckCircleIcon,
  CheckIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  CurrencyDollarIcon,
  EnvelopeIcon,
  LinkIcon,
  LockClosedIcon,
  MagnifyingGlassIcon,
  MapPinIcon,
  PencilIcon,
} from '@heroicons/vue/20/solid'
import { Bars3Icon, BellIcon, XMarkIcon } from '@heroicons/vue/24/outline'
import type { PaginationLinks, PaginationMeta } from '@/types'
import Noimg from '@/assets/no-image.jpg'

const tabs = ref([
  { value: null, name: 'pending', href: '#', count: 0, current: true },
  { value: 0, name: 'un_published', href: '#', count: 0, current: false },
  { value: 1, name: 'published', href: '#', count: 0, current: false },
])

const setSelectedTab = (tabName: string): void => {
  tabs.value.map((tab) => {
    tab.current = tab.name === tabName
    return tab
  })
}
const reply = ref({
  content: '',
  replied_by: null,
})
const tableData = reactive({
  commentList: [],
  paginationMeta: {
    current_page: 1,
    from: 1,
    last_page: 1,
    links: [],
    path: '',
    per_page: 15,
    to: 15,
    total: 1,
  } as PaginationMeta,
  paginationLinks: {
    first: '',
    last: '',
    prev: null,
    next: null,
  } as PaginationLinks,
  processing: false,
  selectedStaff: null,
  selectedIndex: null,
})

const { fetchComments, changeStatus, updateReply } = useCommentsStore()
const updateTabCounts = (data): void => {
  tabs.value.map((tab) => {
    const count = 0
    const status = data.find(item => item.published === tab.value)
    if (status)
      tab.count = status.status_count
    return tab
  })
}
const fetchCommentsPage = (
  page = 1, status = null,
): void => {
  tableData.processing = true
  fetchComments(page)
    .then(({ data, links, meta, counts }) => {
      tableData.commentList = data.filter(item => item.published === status)
      tableData.paginationMeta = meta
      tableData.paginationLinks = links
      updateTabCounts(counts)
    })
    .finally(() => {
      tableData.processing = false
    })
}

const currentTab = computed(() => tabs.value.filter(t => t.current)[0])

watch(currentTab, (current) => {
  const statusName = current.value
  fetchCommentsPage(tableData.paginationMeta.current_page, statusName)
})

const showModal = ref(false)
const showReply = ref(false)

const selectedComment = ref(null)

const showReplyModal = (comment: any): void => {
  selectedComment.value = comment
  if (comment.replies.length > 0) {
    reply.value.content = comment.replies[0].content
    reply.value.replied_by = comment.replies[0]
  }
  showReply.value = true
}
const hideReplyModal = () => {
  selectedComment.value = null
  reply.value.content = ''
  reply.value.replied_by = null
  showReply.value = false
}

const showComment = (comment: any): void => {
  selectedComment.value = comment
  showModal.value = true
}
const processing = ref(false)
const hideModal = () => {
  selectedComment.value = null
  showModal.value = false
}
const submitReply = async (comment: any, reply: string) => {
  if (!comment.id)
    return
  processing.value = true
  await updateReply(comment.id, reply).then(() => {
    hideReplyModal()
    fetchCommentsPage(tableData.paginationMeta.current_page, comment.published)
  }).finally(() => {
    processing.value = false
  })
}
const makeAction = async (action: string) => {
  if (!selectedComment.value?.id)
    return

  processing.value = true
  const updateStatus = action === 'published' ? 1 : 0

  await changeStatus(selectedComment.value.id, updateStatus).then(() => {
    hideModal()
    fetchCommentsPage(tableData.paginationMeta.current_page, updateStatus)
    setSelectedTab(action)
  }).finally(() => {
    processing.value = false
  })
}

onMounted(() => {
  fetchCommentsPage()
})
</script>

<template>
  <div class="min-h-full">
    <Modal :open="showReply" @close="hideReplyModal">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900">
          {{ $t('comments.details.reply') }}
        </h3>

        <div v-if="selectedComment" class="mt-5">
          <div class="px-6 py-5 rounded-md bg-gray-50">
            <p
              class="inline-flex items-center px-3 py-1 text-sm font-medium leading-4 text-white bg-blue-500 rounded-full"
            >
              {{ $t(`homepage.${selectedComment?.model}`) }}
            </p>
            <vue3-star-ratings
              v-model="selectedComment.rating" :show-control="false" :disable-click="true"
              star-color="#38bdf8" inactive-color="#c5e6f5" star-size="20"
            />

            <p class="flex items-center justify-center mt-2 text-sm text-gray-500 ">
              <CheckCircleIcon
                v-if="selectedComment.published == 1" class="mr-1.5 h-5 w-5 flex-shrink-0 text-green-600"
                aria-hidden="true"
              />
              <LockClosedIcon
                v-if="selectedComment.published == 0" class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400"
                aria-hidden="true"
              />
              {{ selectedComment.published == 1 ? $t('comments.published') : $t('comments.un_published') }}
            </p>
            <div class="mt-1 sm:mt-1">
              {{ $t('comments.details.by') }}
              <p class="text-gray-400">
                {{ `${selectedComment?.user?.first_name}` }} {{ `${selectedComment?.user?.last_name}` }}
              </p>
              <time :datetime="selectedComment.created_at">{{ selectedComment.created_at }}</time>
            </div>

            <p
              dir="auto" class="flex-auto px-5 py-5 my-1 text-center border rounded-md border-spacing-4"
              style="background-color: #F3F4F6; color: #111827;"
            >
              {{ selectedComment.content }}
            </p>

            <div class="pt-5">
              <span v-if="reply.replied_by" class="text-gray-600"> {{ $t('comments.replied') }} <br>
                {{ reply.replied_by.reply_by.email }} | {{ reply.replied_by.reply_by.name }} - {{ reply.replied_by.updated_at ?? reply.replied_by.created_at }} </span>

              <div class="flex items-center justify-center gap-3 my-3">
                <TextareaInput
                  v-model="reply.content"
                  custom-classes="w-full px-3 py-2 border border-gray-300 rounded-md"
                />

                <button
                  type="button"
                  class="inline-flex items-center px-4 py-2 mx-1 font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:text-sm"
                  @click="hideReplyModal"
                >
                  {{ $t('form.cancel') }}
                </button>

                <BaseButton
                  v-if="selectedComment.replies.length > 0" :disabled="processing" show-icon
                  class="" @click="submitReply(selectedComment, reply)"
                >
                  {{ $t('comments.update') }}
                </BaseButton>

                <BaseButton
                  v-if="selectedComment.replies.length == 0" :disabled="processing" show-icon
                  class="" @click="submitReply(selectedComment, reply)"
                >
                  {{ $t('comments.reply') }}
                </BaseButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>

    <Modal :open="showModal" @close="hideModal">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900">
          {{ $t('comments.details.heading') }}
        </h3>

        <div v-if="selectedComment" class="mt-5">
          <div class="px-6 py-5 rounded-md bg-gray-50">
            <p
              class="inline-flex items-center px-3 py-1 text-sm font-medium leading-4 text-white bg-blue-500 rounded-full"
            >
              {{ $t(`homepage.${selectedComment?.model}`) }}
            </p>
            <vue3-star-ratings
              v-model="selectedComment.rating" :show-control="false" :disable-click="true"
              star-color="#38bdf8" inactive-color="#c5e6f5" star-size="20"
            />

            <p class="flex items-center justify-center mt-2 text-sm text-gray-500 ">
              <CheckCircleIcon
                v-if="selectedComment.published == 1" class="mr-1.5 h-5 w-5 flex-shrink-0 text-green-600"
                aria-hidden="true"
              />
              <LockClosedIcon
                v-if="selectedComment.published == 0" class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400"
                aria-hidden="true"
              />
              {{ selectedComment.published == 1 ? $t('comments.published') : $t('comments.un_published') }}
            </p>

            <div class="mt-1 sm:mt-1">
              {{ $t('comments.details.by') }}
              <p class="text-gray-400">
                {{ `${selectedComment?.user?.first_name}` }} {{ `${selectedComment?.user?.last_name}` }}
              </p>
              <time :datetime="selectedComment.created_at">{{ selectedComment.created_at }}</time>
            </div>
            <p
              dir="auto" class="flex-auto px-5 py-5 my-1 text-center border rounded-md border-spacing-4"
              style="background-color: #F3F4F6; color: #111827;"
            >
              {{ selectedComment.content }}
            </p>

            <div class="pt-5">
              <div class="flex items-center justify-center">
                <button
                  type="button"
                  class="inline-flex items-center justify-center w-1/5 px-4 py-2 mx-1 font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:text-sm"
                  @click="hideModal"
                >
                  {{ $t('form.cancel') }}
                </button>
                <button
                  v-if="selectedComment" type="button"
                  class="inline-flex items-center justify-center w-1/5 px-4 py-2 mx-1 font-medium text-gray-100 bg-red-600 border border-gray-300 rounded-md shadow-sm hover:bg-red-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:text-sm disabled:bg-primary-300 disabled:cursor-not-allowed"
                  :disabled="processing" @click="makeAction('un_published')"
                >
                  {{ $t('comments.reject') }}
                </button>
                <button
                  v-if="selectedComment.published !== 1" type="button"
                  class="inline-flex items-center justify-center w-1/5 px-4 py-2 mx-1 font-medium text-center text-gray-100 border border-gray-300 rounded-md shadow-sm bg-primary-600 hover:bg-red-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:text-sm disabled:bg-primary-300 disabled:cursor-not-allowed"
                  :disabled="processing" @click="makeAction('published')"
                >
                  {{ $t('comments.approve') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>

    <main class="pb-16">
      <div class="mx-auto">
        <div class="px-4 sm:px-0">
          <h1 class="text-lg font-medium text-gray-900">
            {{ $t('comments.heading') }}
          </h1>
          <div class="sm:hidden">
            <SelectInput
              id="tabs"
              :label="$t('form.select')"
              name="tabs"
              class="block w-full py-2 pl-3 pr-10 mt-4 text-base border-gray-300 rounded-md focus:border-purple-500 focus:outline-none focus:ring-purple-500 sm:text-sm"
              @change="($e: Event) => setSelectedTab($e.target?.value)"
            >
              <option v-for="tab in tabs" :key="tab.name" :selected="tab.current">
                {{ $t(`comments.${tab.name}`) }}
              </option>
            </SelectInput>
          </div>
          <div class="hidden sm:block">
            <div class="border-b border-gray-200">
              <nav class="flex mt-2 -mb-px" aria-label="Tabs">
                <a
                  v-for="tab in tabs" :key="tab.name" :href="tab.href"
                  class="px-4 py-4 text-sm font-medium border-b-2 whitespace-nowrap" :class="[
                    tab.current
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-200',
                  ]" @click.prevent="setSelectedTab(tab.name)"
                >
                  {{ $t(`comments.${tab.name}`) }}
                  <span
                    v-if="tab.count" class="hidden py-0.5 px-2.5 rounded-full text-xs font-medium md:inline-block"
                    :class="[
                      tab.current
                        ? 'bg-purple-100 text-purple-600'
                        : 'bg-gray-100 text-gray-900',
                    ]"
                  >{{ tab.count }}</span>
                </a>
              </nav>
            </div>
          </div>
        </div>

        <ul role="list" class="relative mt-5 border-t border-gray-200 divide-y divide-gray-200 sm:mt-0 sm:border-t-0">
          <OverlayLoader v-if="tableData.processing" :full-screen="false" />
          <li v-for="comment in tableData.commentList" :key="comment.id">
            <div v-if="comment.reply_by == null" class="flex items-center px-4 py-5 sm:py-6 sm:px-0">
              <div class="flex items-center flex-1 min-w-0">
                <div class="flex-shrink-0">
                  <img
                    v-if="comment?.user?.imageUrl" class="w-12 h-12 rounded-full group-hover:opacity-75"
                    :src="comment.user.imageUrl" alt=""
                  >
                  <img
                    v-else
                    class="w-12 h-12 rounded-full group-hover:opacity-75"
                    src="../assets/no-image.jpg"
                    alt="Your Store"
                  >
                </div>
                <div class="flex-1 min-w-0 px-4 md:grid md:grid-cols-2 md:gap-4">
                  <div>
                    <p class="text-sm font-medium text-purple-600 truncate">
                      {{ `${comment?.user?.first_name}` }} {{ `${comment?.user?.last_name}` }}
                    </p>
                    <p class="flex items-center mt-2 text-sm text-gray-500">
                      <EnvelopeIcon class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" aria-hidden="true" />
                      <span class="truncate">{{ comment?.user?.email || comment?.user?.phone }}</span>
                    </p>

                    <p class="px-4 py-4">
                      <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 mx-1 font-medium text-white bg-green-500 border border-gray-300 rounded-md shadow-sm hover:bg-yellow-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:text-sm"
                        @click="showReplyModal(comment)"
                      >
                        {{ $t('Reply') }}
                      </button>

                      <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 mx-1 font-medium text-white border border-gray-300 rounded-md shadow-sm bg-sky-500 hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:text-sm"
                        @click="showComment(comment)"
                      >
                        {{ $t('Change Status') }}
                      </button>
                    </p>
                  </div>
                  <div class="hidden md:block">
                    <div>
                      <p class="text-sm text-gray-900">
                        {{ $t('comments.on') }}
                        <time :datetime="comment.created_at">{{
                          comment.created_at
                        }}</time>
                      </p><p
                        class="inline-flex items-center px-3 py-1 mx-3 text-sm font-medium leading-4 text-white bg-gray-500 rounded-full"
                      >
                        #{{ $t(`homepage.${comment?.model}`) }}
                      </p>

                      </p>
                      <p class="flex items-center mt-2 text-sm text-gray-500">
                        <vue3-star-ratings
                          v-model="comment.rating" :show-control="false" :disable-click="true"
                          star-color="#38bdf8" inactive-color="#c5e6f5" star-size="20"
                        />
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <ChevronRightIcon class="w-5 h-5 text-gray-400 group-hover:text-gray-700" aria-hidden="true" />
              </div>
            </div>
          </li>
          <li v-if="!tableData.commentList.length">
            <a href="#" class="block group">
              <div class="flex items-center px-4 py-5 sm:py-6 sm:px-0">
                <span>{{ $t("pagination.empty") }}</span>
              </div>
            </a>
          </li>
        </ul>
      </div>
    </main>
    <Pagination
      v-if="tableData.commentList.length" :pagination-meta="tableData.paginationMeta"
      :pagination-links="tableData.paginationLinks" @change="fetchCommentsPage"
    />
  </div>
</template>

<style scoped>
.vue3-star-ratings__wrapper {
  display: unset;
  margin: 0;
  text-align: unset;
  padding: 0;
}
</style>
