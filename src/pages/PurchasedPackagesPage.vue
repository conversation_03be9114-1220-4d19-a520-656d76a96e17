<script setup lang="ts">
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import { ArrowRightCircleIcon, CalendarDaysIcon } from '@heroicons/vue/20/solid'
import i18n from '@/i18n'
const { locale, t } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const processing = ref(false)
const { fetchSoldPackage } = useServicesStore()
const route = useRoute()
const router = useRouter()
const { userInfo } = useAuthStore()
const transactionLoading = ref(false)
const packages = ref({})

const crumbs = ref([
  {
    name: 'homepage.purchased-packages',
    path: '/purchased-packages',
  },
  {
    name: '',
    path: '',
  },
])
onBeforeMount(async () => {
  callPackage()
})
const to_order = (id: string) => {
  router.push({ name: 'orders', query: { order_id: id } })
}

const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('fields.serviceId'),
    },
    {
      title: t('quantity'),
    },
    {
      title: t(''),
    },

  ]
})
const bookingHeaders = computed(() => {
  return [
    {
      title: t('booking.booking_number'),
    },
    {
      title: t('dashboard.booking.customer'),
    },
    {
      title: t('dashboard.booking.staff'),
    },
    {
      title: t('dashboard.booking.service'),
    },
    {
      title: t('dashboard.booking.date'),
    },
    {
      title: t('dashboard.booking.start'),
    },
    {
      title: t('dashboard.booking.end'),
    },
    {
      title: t('booking.status'),
    },
  ]
})
const showBookingDetailsModal = ref(false)
const showSetAppointmentModal = ref(false)
const showAlert = ref(false)
const showUsageAlert = ref(false)
const eventId = ref('')
const selectedItem = ref('')

const openBookingDetails = (item: any) => {
  showBookingDetailsModal.value = true
  eventId.value = item.uuid
}
const closeBookingDetails = () => {
  showBookingDetailsModal.value = false
  eventId.value = ''
}
const openSetAppointment = (item: any) => {
  if (!packages.value.customer) {
    showAlert.value = true
    return
  }
  if (item.quantity - item.usage <= 0) {
    showUsageAlert.value = true
    return
  }
  showSetAppointmentModal.value = true
  selectedItem.value = {
    ...item,
    quantity: 1,
  }
}

const closeSetAppointment = () => {
  showSetAppointmentModal.value = false
  selectedItem.value = {}
  callPackage()
}
const callPackage = async () => {
  transactionLoading.value = true
  await fetchSoldPackage(route.params.id as string).then((data) => {
    packages.value = data.data
    crumbs.value[crumbs.value.length - 1].name = `${data.data.packageNum}`
    transactionLoading.value = false
  })
}
</script>

<template>
  <div>
    <AlertModal v-if="showAlert" :is-open="showAlert" :message="$t('you_cant_set_appointment_for_order')" @close="showAlert = false" />
    <AlertModal v-if="showUsageAlert" :is-open="showUsageAlert" :message="$t('you_cant_set_appointment_for_order_limit_usage')" @close="showUsageAlert = false" />
    <modal-set-appointment
      v-if="showSetAppointmentModal"
      :is-open="showSetAppointmentModal"
      :selected-item="selectedItem"
      :order-id="packages.orderId"
      @close="closeSetAppointment"
    />
    <main-booking-modal
      v-if="showBookingDetailsModal"
      hide-price
      :is-open="showBookingDetailsModal"
      :item-id="eventId"
      @closed="closeBookingDetails"
      @start-loading="() => {}"
      @end-loading="() => {}"
      @refresh-events="callPackage"
    />
    <div class="flex relative flex-col">
      <overlay-loader v-if="transactionLoading" :full-screen="false" />
      <bread-crumb :crumbs="crumbs" class="mt-5" />
      <div class="flex justify-between items-center mt-5">
        <h1 class="text-2xl font-semibold text-gray-900">
          {{ $t("homepage.purchased-package-details") }}
        </h1>
      </div>
      <div class="pb-8">
        <div class="grid grid-cols-1 sm:grid-cols-2">
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <span class="block text-sm font-medium text-neutral-500 w-15">{{
              $t("fields.customer_id")
            }} </span>
            <div class="flex mt-1 font-semibold">
              <div class="flex relative flex-grow items-stretch focus-within:z-10">
                {{ packages.customer }}
              </div>
            </div>
          </div>

          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <span class="block text-sm font-medium text-neutral-500 w-15">{{ $t("fields.expiry_date") }} </span>
            <div class="mt-1 font-semibold">
              {{ dayjs(packages.expired_at).format("dddd, DD MMMM YYYY | hh:mm A") }}
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <span class="block text-sm font-medium text-neutral-500 w-15">{{
              $t("order_number")
            }} </span>
            <div class="flex mt-1 font-semibold">
              <div class="flex relative flex-grow items-stretch cursor-pointer hover:text-sky-500 hover:underline focus-within:z-10" @click="to_order(packages.orderId)">
                {{ packages?.orderNum }} <ArrowRightCircleIcon class="inline-block mx-2 w-6 h-6" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- services table  -->
      <div class="flex justify-between items-center my-5">
        <h1 class="text-2xl font-semibold text-gray-900">
          {{ $t("homepage.services") }}
        </h1>
      </div>
      <div class="overflow-hidden ring-1 ring-black ring-opacity-5 shadow md:rounded-lg">
        <div class="inline-block min-w-full align-middle">
          <generic-table
            :headers="headers"
            :data="packages.items"
            item-key="uuid"
            :custom-classes-per-tr="(item) => (item.booking ? 'cursor-pointer' : '')"
          >
            <template #row="{ item }">
              <grid-td
                class="flex gap-1 items-center py-2 pr-2 pl-2 text-sm whitespace-nowrap sm:pl-6"
                :default-style="false"
              >
                <div class="flex gap-2 justify-start items-center">
                  <img
                    v-if="item.photo"
                    id="image"
                    class="w-10 h-10 rounded-full"
                    :src="item.photo"
                  >
                  <svg
                    v-else
                    xmlns="http://www.w3.org/2000/svg"
                    class="w-10"
                    viewBox="0 0 24 24"
                  >
                    <path
                      fill="#e1e1e1"
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z"
                    />
                  </svg>
                  <div class="flex flex-col gap-2 justify-center items-start">
                    <span>
                      {{ item?.name }}

                    </span>
                  </div>
                </div>
              </grid-td>
              <!-- <grid-td>
                <div class="text-gray-900">
                  <span v-if="item?.service_type">
                    {{ $t(`pos.${item?.service_type}`) }}
                  </span>
                  <span v-else class="text-zinc-400"> - </span>
                </div>
              </grid-td> -->
              <grid-td>
                <div class="mx-5 text-gray-900">
                  <span v-if="item?.quantity">

                    {{ item?.usage }} / <span class="font-bold">{{ item?.quantity }}</span>
                  </span>
                  <span v-else class="text-zinc-400"> 0 </span>
                </div>
              </grid-td>

              <grid-td>
                <div class="text-gray-900">
                  <button class="text-gray-500 rounded-sm duration-150 ont-semibold rtl:ml-1" @click="openSetAppointment(item)">
                    <CalendarDaysIcon class="inline-block w-7 h-7" />
                  </button>
                </div>
              </grid-td>
            </template>
          </generic-table>
        </div>
      </div>
      <!-- {{ packages.bookings }} -->
      <div class="flex justify-between items-center my-5">
        <h1 class="text-2xl font-semibold text-gray-900">
          {{ $t("homepage.time-table-package") }}
        </h1>
      </div>
      <div class="flex flex-col">
        <div class="">
          <generic-table
            :headers="bookingHeaders"
            :data="packages.bookings"
            tr-class="cursor-pointer"
            :on-row-click="openBookingDetails"
            :is-loading="transactionLoading"
          >
            <template #row="{ item }">
              <grid-td>
                <p class="flex gap-1 items-center py-2">
                  <ArrowPathIcon v-if="item.is_recurring" class="block w-5 h-5 text-black" />
                  {{ item?.booking_number ?? item?.booking_no }}
                </p>
              </grid-td>
              <grid-td
                v-if="bookCustomer !== 'bookCustomer'"
                :default-style="false"
                class="py-2 text-sm text-black whitespace-nowrap"
              >
                <div class="flex items-center">
                  <div class="">
                    {{ item?.customer?.first_name }}
                    {{ item.customer?.last_name }}
                  </div>
                </div>
              </grid-td>
              <grid-td
                :default-style="false"
                class="flex gap-1 items-center px-0 py-2 text-sm whitespace-nowrap"
              >
                <div>
                  <svg
                    v-if="item?.staff?.imageLink === null"
                    xmlns="http://www.w3.org/2000/svg"
                    class="w-10"
                    viewBox="0 0 24 24"
                  >
                    <path
                      fill="#e1e1e1"
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z"
                    />
                  </svg>
                  <img
                    v-else
                    id="image"
                    class="w-10 h-10 rounded-full"
                    :src="item?.staff?.imageLink"
                    :link="null"
                  >
                </div>
                <span>
                  {{ item.staff?.name }}
                </span>
              </grid-td>

              <grid-td>
                {{ item.services?.map((service) => service.name).join(", ") }}
              </grid-td>
              <grid-td>
                <div class="text-gray-900">
                  {{ item.date ?? item.start.split("T")[0] }}
                </div>
              </grid-td>
              <grid-td>
                {{ formatTime(item?.start) }}
              </grid-td>
              <grid-td>
                <div class="text-gray-900">
                  {{ formatTime(item?.end) }}
                </div>
              </grid-td>
              <grid-td>
                <booking-status :book-status="item?.status" />
              </grid-td>
            </template>
            <template v-if="hasActions" #actions="{ item }">
              <slot name="actions" :item="item" />
            </template>
          </generic-table>
        </div>
      </div>
    </div>
  </div>
</template>
