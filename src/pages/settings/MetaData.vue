<script lang="ts" setup>
import Vue3TagsInput from 'vue3-tags-input'
import { storeToRefs } from 'pinia'
import type { ComputedRef } from 'vue'
import useVuelidate from '@vuelidate/core'
import type { MetaData } from '@/types/metaData'
import type { header } from '@/types'
import { useMetaDataStore } from '@/stores/metaData'
import { minLength, required, requiredIf } from '@/utils/i18n-validators'
const ENTER_KEY = 13
const fieldsStore = useMetaDataStore()
const { fetchTeams } = useTeamStore()
const { getTeams } = storeToRefs(useTeamStore())
const { fetchMetaData, deleteMetaData, upsertMetaData } = fieldsStore
const { locale, t } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const showModal = ref(false)
const { getMetaData, processing } = storeToRefs(fieldsStore)
const htmlTags: Array<string> = [
  'text',
  'number',
  'textarea',
  'select',
  'date',
  'file',
]
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.fieldLabel'),
    },
    {
      title: t('fields.team_id'),
    },
  ]
})
const formProcessing = ref<boolean>(false)

const modalTitle = ref('')

const metaDataForm = reactive<MetaData>({
  id: '',
  label: '',
  target: '',
  htmlTag: '',
  values: [],
  team_id: '',
  is_required: false,
  model: '',
})
const rules = {
  label: {
    required,
  },
  htmlTag: {
    required,
  },
  values: {
    required: requiredIf(() => metaDataForm.htmlTag === 'select'),
  },
}
const v$ = useVuelidate(rules, metaDataForm)

const openCreateModal = (model: string) => {
  showModal.value = true
  modalTitle.value = 'createMetaData'
  metaDataForm.model = model
}

const handleChangeInput = (words: Object) => {
  metaDataForm.values = Object.values(words)
}

const editField = (field: MetaData): void => {
  modalTitle.value = 'editMetaData'
  const { id, label, htmlTag, values, is_required, model, target, team_id } = field
  metaDataForm.id = id
  metaDataForm.label = label
  metaDataForm.team_id = team_id

  metaDataForm.htmlTag = htmlTag
  metaDataForm.values = values
  metaDataForm.target = target || ''
  metaDataForm.is_required = is_required
  metaDataForm.model = model

  showModal.value = true
}

onBeforeMount(async () => {
  // withoutScope
  try {
    processing.value = true
    await fetchTeams()
    await fetchMetaData(false)
  }
  catch (error) {
    console.error(error)
  }
  finally {
    processing.value = false
  }
})

function resetForm() {
  metaDataForm.id = ''
  metaDataForm.label = ''
  metaDataForm.htmlTag = ''
  metaDataForm.target = ''
  metaDataForm.team_id = ''
  metaDataForm.values = []
  metaDataForm.is_required = false
  v$.value.$reset()
}

const closeModal = () => {
  resetForm()
  showModal.value = false
}

const deleteRecord = () => {
  formProcessing.value = true
  deleteMetaData(metaDataForm.id)
    .then(() => {
      return fetchMetaData(false)
    })
    .then(() => {
      resetForm()
      showModal.value = false
    })
    .finally(() => {
      formProcessing.value = false
    })
}
const saveRecord = () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return false
  formProcessing.value = true
  upsertMetaData(metaDataForm)
    .then(() => {
      // in edit mode
      if (metaDataForm.id)
        showModal.value = false

      resetForm()
    })
    .finally(() => {
      formProcessing.value = false
    })
}
</script>

<template>
  <div class="">
    <Modal
      v-if="showModal" :open="showModal" :title="modalTitle" :dir="getLocale(locale)?.direction"
      @close="closeModal"
    >
      <form class="relative text-start" @submit.prevent="saveRecord">
        <overlay-loader v-if="formProcessing" :full-screen="false" />
        <div class="grid gap-x-2 gap-y-6 sm:grid-cols-2">
          <div class="sm:col-span-1">
            <form-group :validation="v$" name="label">
              <template #default="{ attrs }">
                <TextInput
                  v-bind="attrs"
                  id="field-label"
                  v-model="metaDataForm.label"
                  :label="$t('form.fieldLabel')"
                  :placeholder="$t('form.fieldLabel')"
                />
              </template>
            </form-group>
          </div>
          <div class="sm:col-span-1">
            <form-group :validation="v$" name="team_id">
              <template #default="{ attrs }">
                <SelectInput
                  id="team"
                  v-model="metaDataForm.team_id"
                  :placeholder="$t('fields.team_id')"
                  :label="$t('fields.team_id')"
                  v-bind="attrs"
                >
                  <option value="">
                    {{ $t("form.select") }}
                  </option>
                  <option v-for="team in getTeams" :key="team.uuid" :value="team.uuid">
                    {{ team.name }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
        </div>

        <div class="sm:col-span-3">
          <form-group :validation="v$" name="target">
            <template #default="{ attrs }">
              <SelectInput
                id="field-target"
                v-model="metaDataForm.target"
                :placeholder="$t('form.target')"
                :label="$t('form.target')"
                not-need-default-option
                v-bind="attrs"
              >
                <option value="">
                  {{ $t("form.metadata.both") }}
                </option>
                <option value="web">
                  {{ $t(`form.metadata.web_only`) }}
                </option>
                <option value="booking-page">
                  {{ $t(`form.metadata.bookingpage_only`) }}
                </option>
              </SelectInput>
            </template>
          </form-group>
        </div>

        <div class="grid gap-x-2 gap-y-6 mt-3">
          <div class="sm:col-span-3">
            <form-group :validation="v$" name="htmlTag">
              <template #default="{ attrs }">
                <SelectInput
                  v-bind="attrs"
                  id="field-tag"
                  v-model="metaDataForm.htmlTag"
                  :placeholder="$t('form.fieldHtmlTaq')"
                  :label="$t('form.fieldHtmlTaq')"
                >
                  <option value="">
                    {{ $t("form.select") }}
                  </option>
                  <option v-for="tag of htmlTags" :key="tag" :value="tag">
                    {{ $t(`form.htmlTags.${tag}`) }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
          <div v-if="metaDataForm.htmlTag === 'select'" class="sm:col-span-3">
            <label class="block mb-2 text-sm font-bold tracking-wide text-gray-700" for="field-values">
              {{ $t("form.fieldOptions") }}
            </label>
            <form-group :validation="v$" name="values">
              <template #default="{ attrs }">
                <Vue3TagsInput
                  v-bind="attrs" id="field-values" :tags="metaDataForm.values"
                  :placeholder="$t('htmlTags.enter_options')"
                  :add-tag-on-keys="[ENTER_KEY]" @on-blur="attrs.onblur"
                  @on-tags-changed="handleChangeInput"
                />
              </template>
            </form-group>
          </div>
        </div>
        <div class="flex items-center mt-5">
          <label class="block text-sm font-bold tracking-wide text-gray-700 me-3" for="is_required">
            {{ $t("form.fieldRequired") }}
          </label>
          <CheckInput
            id="is_required"
            v-model="metaDataForm.is_required"
            size="md"
          />
        </div>
        <div class="mt-5 sm:mt-6">
          <div class="mt-8">
            <div class="flex justify-end items-center">
              <div class="flex gap-2 justify-between">
                <BaseButton
                  v-if="metaDataForm.id" class="mx-auto w-1/2 hover:bg-red-700" custome-bg="bg-red-600"
                  type="button" @click="deleteRecord()"
                >
                  {{ $t("form.delete") }}
                </BaseButton>

                <BaseButton
                  show-icon class="mx-auto w-1/2 hover:bg-primary-700" custome-bg="bg-primary-700"
                  type="submit"
                >
                  {{
                    metaDataForm.id ? $t("form.update") : $t("form.create")
                  }}
                </BaseButton>
              </div>
            </div>
          </div>
        </div>
      </form>
    </Modal>
    <div class="mb-8">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-semibold text-gray-900">
            {{ $t(`metadata`) }}
          </h1>
          <p class="mt-2 text-sm text-gray-700">
            {{ $t(`manage_metadata`) }}
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:flex-none">
          <BaseButton
            class="inline-flex w-auto hover:bg-green-700" custome-bg="bg-green-600"
            @click="openCreateModal('orders')"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </div>
      <div class="flex flex-col mt-8">
        <generic-table
          :is-loading="processing" :data="getMetaData.orders" :headers="headers"
          tr-class="cursor-pointer" itemkey="id" :on-row-click="editField"
        >
          <template #row="{ item }">
            <grid-td>
              <div class="flex items-center text-base">
                {{ item.label }}
              </div>
            </grid-td>
            <grid-td>
              <div class="flex items-center text-base">
                {{ item.team_name }}
              </div>
            </grid-td>
          </template>
        </generic-table>
      </div>
    </div>
  </div>
</template>
