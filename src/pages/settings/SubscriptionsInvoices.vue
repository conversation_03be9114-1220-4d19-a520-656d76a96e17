<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { ArrowDownTrayIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid'
import { useVuelidate } from '@vuelidate/core'
import { useI18n } from 'vue-i18n'
import i18n from '@/i18n'
import SubscriptionService from '@/services/SubscriptionService'
import type { BillingAddress, Invoice } from '@/services/SubscriptionService'
import { countries } from '@/countries'
import type { header } from '@/types/table'

import { required } from '@/utils/i18n-validators'
import useNotifications from '@/composables/useNotifications'

const { showNotification } = useNotifications()
const { t } = useI18n()
const invoices = ref<Invoice[]>([])
const billingForm = ref({
  billing_name: '',
  billing_country: '',
  billing_address: '',
  vat_id: '',
})
const processing = ref(false)
const totalPages = ref(1)
const perPage = 20

const headers = computed<header[]>(() => [
  {
    title: t('invoice.number'),
    className: 'text-right',
  },
  {
    title: t('invoice.created_at'),
    className: 'text-right',
  },
  {
    title: '',
    className: 'text-right',
  },
])

const rules = computed(() => ({
  billing_name: { required },
  billing_country: { required },
  billing_address: { required },
  vat_id: {
    required,
    format: {
      $validator: (value: string) => {
        if (!value)
          return true // Let required handle empty values
        const regex = /^3\d{13}3$/
        return regex.test(value)
      },
      $message: t('invoice.billing.vat_id_format'),
    },
  },
}))

const v$ = useVuelidate(rules, billingForm)

const fetchBillingAddress = async () => {
  const response = await SubscriptionService.getBillingAddress()
  billingForm.value = response.data
}

const saveBillingAddress = async () => {
  try {
    const isValid = await v$.value.$validate()
    if (!isValid || processing.value)
      return
    processing.value = true
    await SubscriptionService.updateBillingAddress(billingForm.value)
    showNotification({
      title: t('Success'),
      type: 'success',
      message: t('invoice_billing_save_success'),
    })
  }
  finally {
    processing.value = false
  }
}

const fetchInvoices = async (page = 1) => {
  try {
    const response = await SubscriptionService.fetchInvoices(page, perPage)
    invoices.value = response.data
    // For now, we'll disable pagination since the API doesn't return total count
    totalPages.value = 1
  }
  catch (error) {
    console.error('Error fetching invoices:', error)
  }
}

const downloadInvoice = async (id: number) => {
  try {
    const response = await SubscriptionService.downloadInvoice(id)
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `invoice-${id}.pdf`)
    document.body.appendChild(link)
    link.click()
    link.remove()
  }
  catch (error) {
    console.error('Error downloading invoice:', error)
  }
}

onMounted(() => {
  fetchInvoices()
  fetchBillingAddress()
})
</script>

<template>
  <div class="px-4 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-gray-900">
          {{ t('invoice.heading') }}
        </h1>
      </div>
    </div>

    <!-- Billing Information Form -->
    <div class="mt-8">
      <div class="mt-5 md:mt-0">
        <div class="bg-white shadow sm:rounded-lg">
          <div class="px-4 py-5">
            <h3 class="text-lg font-medium leading-6 text-gray-900">
              {{ t('invoice.billing.title') }}
            </h3>
            <form class="grid grid-cols-1 gap-y-6 mt-5 sm:grid-cols-6 sm:gap-x-3" @submit.prevent="saveBillingAddress">
              <div class="sm:col-span-6">
                <form-group :validation="v$" name="billing_name">
                  <template #default="{ attrs }">
                    <TextInput
                      v-bind="attrs"
                      id="billing_name"
                      v-model="billingForm.billing_name"
                      :label="t('invoice.billing.billing_name')"
                      required
                    />
                  </template>
                </form-group>
              </div>
              dsad
              <div class="sm:col-span-3">
                <form-group :validation="v$" name="billing_country">
                  <template #default="{ attrs }">
                    <SelectInput
                      v-bind="attrs"
                      id="billing_country"
                      v-model="billingForm.billing_country" :label="t('invoice.billing.billing_country')" required
                    >
                      <option hidden selected value="">
                        {{ t('form.select') }}
                      </option>
                      <option v-for="(country, code) in countries" :key="code" :value="code">
                        {{ t(`countries.${code}`) }}
                      </option>
                    </SelectInput>
                  </template>
                </form-group>
              </div>

              <div class="sm:col-span-3">
                <form-group :validation="v$" name="vat_id">
                  <template #default="{ attrs }">
                    <TextInput
                      v-bind="attrs"
                      id="vat_id"
                      v-model="billingForm.vat_id"
                      :label="t('invoice.billing.vat_id')"
                      required
                    />
                  </template>
                </form-group>
              </div>

              <div class="sm:col-span-6">
                <form-group :validation="v$" name="billing_address">
                  <template #default="{ attrs }">
                    <TextInput
                      v-bind="attrs"
                      id="billing_address"
                      v-model="billingForm.billing_address"
                      :label="t('invoice.billing.billing_address')"
                      required
                    />
                  </template>
                </form-group>
              </div>

              <div class="flex justify-end items-center space-x-3 sm:col-span-6">
                <base-button
                  type="submit"
                  :disabled="processing"
                  :processing="processing"
                  class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white rounded-md border border-transparent shadow-sm bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50"
                >
                  {{ t('invoice.billing.save') }}
                </base-button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Invoices Table -->
    <div class="flow-root mt-8">
      <div class="">
        <generic-table
          v-if="invoices && invoices.length > 0"
          :headers="headers"
          :data="invoices"
          item-key="id"
        >
          <template #row="{ item }">
            <grid-td class="py-4 pr-3 pl-4 text-sm text-right text-gray-900 whitespace-nowrap sm:pl-6">
              {{ t('invoice.number_template', { id: item.id }) }}
            </grid-td>
            <grid-td class="px-3 py-4 text-sm text-right text-gray-500 whitespace-nowrap">
              {{ new Date(item.created_at).toLocaleDateString() }}
            </grid-td>
            <grid-td class="relative py-4 pr-4 pl-3 text-sm font-medium text-right whitespace-nowrap sm:pr-6">
              <a
                :href="item.invoice_url"
                target="_blank"
                class="text-indigo-600 cursor-pointer hover:text-indigo-900"
              >
                <ArrowDownTrayIcon class="w-5 h-5" />
              </a>
            </grid-td>
          </template>
          <template #no-data>
            <grid-td colspan="3">
              <div class="py-12 text-center">
                <p class="text-gray-500">
                  {{ t('invoice.no_invoices') }}
                </p>
              </div>
            </grid-td>
          </template>
        </generic-table>
        <div v-else class="py-12 text-center">
          <p class="text-gray-500">
            {{ t('invoice.no_invoices') }}
          </p>
        </div>
      </div>
    </div>
    <!-- Pagination disabled until API supports it -->
    <!-- <div class="flex justify-between items-center px-4 py-3 bg-white border-t border-gray-200 sm:px-6" v-if="totalPages > 1">
      <div class="flex flex-1 justify-between sm:hidden">
        <button
          @click="currentPage > 1 && fetchInvoices(currentPage - 1)"
          :disabled="currentPage === 1"
          class="inline-flex relative items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 hover:bg-gray-50"
        >
          {{ t('invoice.previous') }}
        </button>
        <button
          @click="currentPage < totalPages && fetchInvoices(currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="inline-flex relative items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 hover:bg-gray-50"
        >
          {{ t('invoice.next') }}
        </button>
      </div>
    </div> -->
  </div>
</template>
