<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
// import randomBg from '../../composables/randomBg'
import { required } from '@/utils/i18n-validators'
import { useTagStore } from '@/stores/tags'
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const showModal = ref(false)
const hideModal = () => {
  showModal.value = false
}

const tagStore = useTagStore()

const { fetchTags, createTag, deleteTags } = tagStore

const { tags } = storeToRefs(tagStore)

const tagData = reactive({
  name: '',
  id: '',
  model: '',
  color: '',
  processing: false,
})

const showCreateForm = ref(false)

const tagValidation = reactive({
  name: {
    required,
  },
  color: {
    required,
  },
})

const v1$ = useVuelidate(tagValidation, tagData)
const errHandle = reactive({
  name: [],
  model: [],
  color: [],
})
const resetFun = () => {
  tagData.name = ''
  tagData.id = ''
  tagData.model = ''
  tagData.color = ''
  tagData.processing = false
  showCreateForm.value = false
  v1$.value.$reset()
}
const createNewTag = async () => {
  v1$.value.$touch()
  if (tagData.processing || v1$.value.$invalid)
    return false

  tagData.processing = true

  createTag({ name: tagData.name, id: tagData.id, model: tagData.model, color: tagData.color }).then(() => {
    resetFun()
  }).catch((err) => {
    for (const prop in err.errors)
      errHandle[prop] = err.errors[prop]
  })
}
const state = reactive({
  processing: false,
})

const processing = ref(false)
const randomBackround = ref('')
function randomBg() {
  const hexArr = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 'A', 'B', 'C', 'D', 'E', 'F']
  const bgCode = []
  for (let i = 0; i < 6; i++)
    bgCode.push(hexArr[Math.floor(Math.random() * hexArr.length)])
  tagData.color = `#${bgCode.join('')}`
}
onBeforeMount(() => {
  processing.value = true
  fetchTags().then(() => {
    processing.value = false
  })
  randomBg()
})

tagStore.$subscribe((_, state) => tags.value = state.tags)

interface Tag {
  id: string
  name: string
  model: string
  color: string
}

const editTag = (tagToEdit: Tag) => {
  const { id, name, model, color } = tagToEdit
  tagData.id = id
  tagData.name = name
  tagData.model = model
  tagData.color = color
  showCreateForm.value = true
}

const createModal = (model: string): void => {
  tagData.model = model
  showCreateForm.value = true
  randomBg()
}

// delete tag
const deleteTag = () => {
  deleteTags(tagData.id).then((res) => {
    resetFun()
  }).finally(() => {
    showCreateForm.value = false
  })
}
const closeFun = () => {
  resetFun()
  showCreateForm.value = false
}
</script>

<template>
  <div>
    <modal
      :open="showCreateForm" :title="tagData.model" :dir="getLocale(locale)?.direction"
      @close="closeFun"
    >
      <form
        :class="[
          getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
        ]" @submit.prevent="createNewTag"
      >
        <div class="grid grid-cols-6 gap-6">
          <div class="col-span-3">
            <TextInput
              id="tag-name"
              v-model="tagData.name"
              :label="$t('form.name')" :placeholder="$t('form.name')"
              class="block w-full py-3 mb-3 leading-tight text-gray-700 rounded appearance-none ps-4 pe-4 focus:outline-none focus:bg-white"
            />
            <p v-for="error of v1$.name.$errors" :key="error.$uid" class="error-message">
              {{ $t(error.$message) }}
            </p>
            <err-msg v-for="err in errHandle.name" :key="err" :msg="err" />
          </div>
          <div class="col-span-3">
            <label class="block mb-2 text-sm font-bold tracking-wide text-gray-700  " for="tag-color">
              {{ $t("color") }}
            </label>
            <div class="relative">
              <input
                v-model="tagData.color" type="text" placeholder="#000000" class="
                block
                w-full
                rounded-md

                py-3
                px-4
                border-blue-gray-300
                text-blue-gray-900
                focus:border-blue-500 focus:ring-blue-500
                sm:text-sm
              "
              >
              <input
                v-model="tagData.color" type="color" class="
                absolute
                transform
                -translate-y-1/2
                bg-transparent
                border-none
                rounded-full
                appearance-none
                cursor-pointer
                top-1/2
                w-7
                h-7
                end-2
                "
              >
            </div>
            <p v-for="error of v1$.color.$errors" :key="error.$uid" class="error-message">
              {{ $t(error.$message) }}
            </p>
            <err-msg v-for="err in errHandle.color" :key="err" :msg="err" />
          </div>
        </div>
        <err-msg v-for="err in errHandle.model" :key="err" :msg="err" />

        <div class="mt-8">
          <div class="flex items-center justify-end">
            <div
              v-if="tagData?.id"
              class="flex gap-2 justify-between"
            >
              <BaseButton
                type="button"
                class="w-1/2 mx-auto py-3  hover:bg-red-700"
                custome-bg="bg-red-600"
                show-icon
                :processing="processing"
                @click="deleteTag()"
              >
                {{ $t("form.delete") }}
              </BaseButton>

              <BaseButton
                type="submit"
                class="w-1/2 mx-auto py-3  hover:bg-primary-800"
                custome-bg="bg-primary-700"
                show-icon
                :processing="processing"
              >
                {{ $t("form.update") }}
              </BaseButton>
            </div>

            <BaseButton
              v-else
              type="submit"
              class=" py-3  hover:bg-green-700"
              custome-bg="bg-green-600"
              show-icon
              :processing="processing"
            >
              {{ $t("form.create") }}
            </BaseButton>
          </div>
        </div>
      </form>
    </modal>
    <div class="relative">
      <OverlayLoader v-if="processing" :full-screen="false" />
      <div v-for="key of Object.keys(tags)" :key="key" class="mb-8">
        <div class="sm:flex sm:items-center">
          <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">
              {{ $t(`settings.tags.${key}`) }}
            </h1>
            <p class="mt-2 text-sm text-gray-700">
              {{ $t(`settings.tags.${key}_desc`) }}
            </p>
          </div>
          <div class="mt-4 sm:mt-0 sm:flex-none">
            <BaseButton
              class="inline-flex w-auto hover:bg-green-700" custome-bg="bg-green-600"
              @click="createModal(key)"
            >
              {{ $t("form.create") }}
            </BaseButton>
          </div>
        </div>
        <div class="flex flex-col mt-8">
          <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
              <div
                class="
                                overflow-hidden
                                shadow
                                ring-1 ring-black ring-opacity-5
                                md:rounded-lg
                                p-5
                              "
              >
                <div v-for="tag in tags[key]" :key="tag.id" class="text-xs mx-1 cursor-pointer inline-flex items-center font-bold leading-sm uppercase px-3 bg-white py-1  text-blue-700 rounded-full" :style="[{ color: tag.color }, { border: `1px solid ${tag.color}` }]" @click="editTag(tag)">
                  {{ tag.name }}
                </div>
                <div v-if="!tags[key].length">
                  {{ $t('pagination.empty') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
