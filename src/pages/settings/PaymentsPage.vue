<!-- eslint-disable no-trailing-spaces -->
<script lang="ts" setup>
import { Switch } from '@headlessui/vue'
import { storeToRefs } from 'pinia'
import { XCircleIcon } from '@heroicons/vue/20/solid'
import PaymentMethods from './PaymentMethods.vue'
import AppsPage from './AppsPage.vue'
import type { bankAccount } from '@/types/bankAccount'
import { useBank } from '@/stores/bank'
import useNotifications from '@/composables/useNotifications'
import i18n from '@/i18n'
// const { getApps, getCategory } = storeToRefs(useAppsStore())
const { showNotification } = useNotifications()
const {} = useBank()
const store = useAppsStore()
const paymenet = useBank()
const { getAllAccounts, getBankTransfer, getInstallmentProgram, getOnArrival, getOnlinePayment, getSwitches, switches } = storeToRefs(useBank())
const processing = ref(false)
const paymentData = reactive({
  bankTransfer: {
    apps: [],
    id: '',
  },
  onArrival: {
    status: true,
  },
})
const formData = reactive({
  electronic_payement: {
    val: false,
    paymentId: '',
  },
  paymentInstallments: {
    val: false,
    paymentId: '',
  },
  bankTransfers: {
    val: false,
    paymentId: '',
  },
  paymentaArrival: {
    val: false,
    paymentId: '',
  }, 
})
const bankListsAccounts = ref<bankAccount[]>([])
const currentPay_on_arrive = ref(true)
onBeforeMount(() => {
  processing.value = true
  paymenet.getPayOnArrivalStatus().then((res) => {
    currentPay_on_arrive.value = res.pay_on_arrival == 1
  })
  paymenet.getBankList().then((res) => {
  }).finally(() => {
    processing.value = false
  })
})

const showModal = ref(false)
const showBankModal = ref(false)
const bankModalTitle = ref('addBankAccount')
const bank = ref<bankAccount>({ name: '', iban: '', bank: { name: '', id: '' }, id: '', account_no: '' })
const modalType = ref('')
const toggleModel = () => {
  showModal.value = false
}
const toggleBankModal = () => {
  showBankModal.value = false
  bank.value = ''
}
const showBankAccount = (item: bankAccount) => {
  bankModalTitle.value = 'updateBankAccount'
  bank.value = item
  modalType.value = 'edit'
  showBankModal.value = true
}
const showAddAccountModal = () => {
  modalType.value = 'add'
  showBankModal.value = true
}
const chenagetoggle = () => {
  processing.value = true
  paymenet.togglePaymentMethodOnArrival().then((res) => {
    if (res)
      currentPay_on_arrive.value = res.value == 1
  }).finally(() => {
    processing.value = false
  })
  return currentPay_on_arrive.value
}
const showAlert = (paymentType: boolean) => {
  if (!paymentType) {
    showNotification({
      title: i18n.global.t('invalid'),
      type: 'error',
      message: i18n.global.t('settings.payementLabels.paymenetError'),
    })
  }
}
// const appsStore = useAppsStore();
</script>

<template>
  <div class="relative">
    <overlay-loader v-if="processing" :full-screen="false" />
    <Modal :open="showModal" @close="toggleModel">
      <h1>welcome</h1>
    </Modal>
    <bank-account-modal :bank="bank" :open="showBankModal" :title="bankModalTitle" :modal-type="modalType" @close="toggleBankModal" />
    <div>
      <h1 class="text-2xl font-semibold">
        {{ $t("settings.apps.payment_app") }}
      </h1>
      <p class="px-2 pt-2">
        {{ $t('online_payment_description') }}
      </p>
      <AppsPage ShowApps="payments" />

      <PaymentMethods />
      <div class="mt-14 border border-gray-300 rounded-md">
        <div class="flex justify-between items-center py-4 px-5 border-b border-gray-300">
          <h3 class="text-1xl font-semibold">
            {{ $t('settings.payementLabels.bankTransfers') }}
          </h3>
          <div class="flex gap-4 items-center">
            <div class="flex flex-col  items-center" @click="showAlert(switches.bankTransfers.val)">
              <div :class="[!getSwitches.bankTransfers?.val ? 'pointer-events-none' : 'pointer-event-auto']" />
            </div>
            <BaseButton class="w-42 h-9 py-2 px-2 text-sm hover:bg-green-700" custome-bg="bg-green-600" @click="showAddAccountModal()">
              {{ $t('settings.addBank') }}
            </BaseButton>
          </div>
        </div>

        <div v-if="getAllAccounts.length !== 0" class="mt-2 py-8 px-10">
          <div class="grid sm:grid-cols-2 grid-cols-1 gap-5">
            <div
              v-for="item in getAllAccounts" :key="item"
              class="bg-white hover:shadow-md cursor-pointer duration-150 py-4 px-5 border border-gray-400 flex text-start flex-col gap-3 rounded-md"
              @click="showBankAccount(item)"
            >
              <h3 class="text-1xl flex sm:flex-row flex-col items-center">
                <span class="text-sm w-24">{{ $t('settings.payementLabels.bankUser') }}</span>
                <span class="bg-gray-700 text-white ps-3  py-1 text-sm flex-1">{{ item.name }} </span>
              </h3>
              <h3 class="text-1xl flex sm:flex-row flex-col items-center">
                <span class="text-sm w-24">{{ $t('settings.payementLabels.bankName') }}</span>
                <span class="bg-gray-700 text-white ps-3  py-1 text-sm flex-1"> {{ item.bank.name }}</span>
              </h3>
              <h3 class="text-1xl flex sm:flex-row flex-col items-center ">
                <span class="text-sm w-24">{{ $t('settings.payementLabels.iban') }}</span>
                <span class="bg-gray-700 flex-1 text-white ps-3  py-1 text-sm  inline-block ">{{ item.iban }}</span>
              </h3>
              <h3 class="text-1xl flex sm:flex-row flex-col items-center">
                <span class="text-sm w-24">{{ $t('settings.payementLabels.bankNo') }}</span>
                <span class="bg-gray-700 flex-1 text-white ps-3  py-1 text-sm  inline-block ">{{ item.account_no }}</span>
              </h3>
            </div>
          </div>
        </div>
      </div>
      <div class="mt-5 border border-gray-300 rounded-md">
        <div class="flex justify-between items-center py-4 px-5 border-b border-gray-300">
          <h3 class="text-1xl font-semibold">
            {{ $t('settings.payementLabels.paymentaArrival') }}
          </h3>
          <div class="flex flex-col">
            <div class="pointer-event-auto">
              <Switch
                id="onOff" v-model="currentPay_on_arrive" class="
                      relative
                      inline-flex
                      flex-shrink-0
                      h-6
                      transition-colors
                      duration-200
                      ease-in-out
                      border-2 border-transparent
                      rounded-full
                      cursor-pointer
                      w-11
                      focus:outline-none
                      focus:ring-2
                      focus:ring-primary-500
                      focus:ring-offset-2
                      mt-1
                    " :class="[currentPay_on_arrive ? 'bg-primary-600' : 'bg-gray-200']"
                @click="chenagetoggle()"
              >
                <span
                  aria-hidden="true" class="
                      inline-block
                      w-5
                      h-5
                      transition
                      duration-200
                      ease-in-out
                      transform
                      bg-white
                      rounded-full
                      shadow
                      pointer-events-none
                      ring-0
              " :class="[
                currentPay_on_arrive
                      ? 'translate-x-5 rtl:-translate-x-5'
                      : 'translate-x-0',
              ]"
                />
              </Switch>
            </div>
          </div>
        </div>
        <div class="flex items-center justify-center p-8">
          <h3 class="text-1xl">
            {{ $t('settings.pay_on_arrive_description') }}
          </h3>
        </div>
      </div>
    </div>
  </div>
</template>
