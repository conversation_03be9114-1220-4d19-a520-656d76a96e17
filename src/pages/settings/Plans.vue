<script setup lang="ts">
import { CheckIcon, MinusIcon, XMarkIcon } from '@heroicons/vue/20/solid'
// getPlans
import { storeToRefs } from 'pinia'
import { CURRENCIES } from '@/constants'
import CopyInput from '@/components/FormControls/Inputs/CopyInput.vue'
const { getPlans, subscriptionPlan, getBankForSubscription }
  = useAccountSettingStore()
const { getLocale } = storeToRefs(useLocalesStore())
const router = useRouter()
const { locale } = useI18n()
const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)
const tiers = ref([])
const plans = ref([])
const processing = ref(false)

const billingType = ref('monthly')
onMounted(async () => {
  try {
    processing.value = true
    const getPricing = await getPlans()
    if (getPricing.billing_cycle.includes('monthly'))
      billingType.value = 'monthly'

    else
      billingType.value = 'yearly'

    await getBankForSubscription()
    plans.value = getPricing
  }
  finally {
    processing.value = false
  }
})
const selectedPlan = ref(null)
const showModal = ref(false)
const subscribe = (plan: any) => {
  selectedPlan.value = plan
  showModal.value = true
}
</script>

<template>
  <div class="relative py-5 bg-white sm:py-5">
    <div class="px-6 mx-auto max-w-full md:w-7xl lg:px-8">
      <div class="mx-auto max-w-4xl min-h-full text-center">
        <OverlayLoader v-if="processing" :full-screen="false" />
        <h2 class="text-base font-semibold leading-7 text-indigo-600">
          {{ $t("plans_and_Pricing") }}
        </h2>
        <p
          class="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl"
        >
          {{ $t("plans_and_Pricing_brief") }}
        </p>
      </div>
      <p
        class="mx-auto mt-6 max-w-2xl text-lg leading-8 text-center text-gray-600"
      >
        {{ $t("plans_and_Pricing_description") }}
      </p>
      <div v-if="plans.plans?.length" class="flex justify-center mt-16">
        <fieldset aria-label="Payment frequency">
          <div
            class="grid gap-x-1 p-1 text-xs font-semibold leading-5 text-center rounded-full"
            :class="[plans.billing_cycle.length == 1 ? '' : 'ring-1 ring-inset ring-gray-200 grid-cols-2']"
          >
            <label
              v-for="billing in plans.billing_cycle"
              :key="billing"
              class="px-5 py-1 text-lg rounded-full cursor-pointer"
              :class="[
                billingType === billing
                  ? 'bg-primary-600 text-white'
                  : 'text-gray-500',
              ]"
            >
              <input
                v-model="billingType"
                type="radio"
                name="frequency"
                :value="billing"
                class="sr-only"
              >
              <span>{{
                $t(billing)
              }}</span>
            </label>
          </div>
        </fieldset>
      </div>
      <div class="mx-auto mt-12 space-y-8 max-w-md sm:mt-16 lg:hidden">
        <section
          v-for="(tier, pl_id) in plans.plans?.filter(p => p.prices.find(p => p.billing_cycle == billingType))"
          :key="tier.id"
          class="py-6 text-center xl:px-8 xl:pt-8" :class="[
            pl_id == 1 ? [
              'border-x border-t rounded-t border-gray-900/10 bg-gray-400/5 shadow-md',
              'relative z-10 scale-105 transform',
              'before:absolute before:inset-0 before:-z-10 before:rounded-t before:border-b-0 before:border-2 before:border-primary-600',
            ] : '',
          ]"
        >
          <h3
            :id="tier.id"
            class="font-semibold leading-6 text-center text-gray-900"
          >
            {{ $i18n.locale === "ar" ? tier.name.ar : tier.name.en }}
          </h3>
          <br>
          <p
            v-if="tier.price_yearly != 0"
            class="flex gap-x-1 justify-center items-baseline mt-2 text-gray-900"
          >
            <span class="text-4xl font-bold">
              {{
                billingType == "yearly" ? parseInt(tier.prices.find(p => p.billing_cycle == 'yearly')?.price)
                : parseInt(tier.prices.find(p => p.billing_cycle == 'monthly')?.price)
              }}

            </span>
            <span class="text-sm font-semibold leading-6">
              {{
                $i18n.locale === "ar"
                  ? CURRENCIES.SAR.symbol_native
                  : CURRENCIES.SAR.code
              }}
              / {{
                billingType == "yearly" ? $t("yearly") : $t("monthly")
              }}
            </span>
          </p>
          <a
            :aria-describedby="tier.id"
            class="justify-center mx-auto w-1/2 ring-1 ring-inset text-primary-600 ring-primary-200 hover:ring-primary-300', 'mt-8 block rounded-md py-2 px-3 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 my-4 hover:cursor-pointer "
            @click="subscribe(tier)"
          >{{ $t("subscribe") }}</a>
          <ul
            role="list"
            class="m-5 mt-5 space-y-4 text-sm leading-6 text-gray-900"
          >
            <li v-for="(section, group) in plans.features" :key="section">
              <ul v-if="section.length > 1" role="list" class="space-y-4">
                <span class="text-lg font-bold text-primary-600">{{
                  $t(`featureGroups.${group}`)
                }}</span>
                <template
                  v-for="(feature, inde) in section"
                  :key="feature.name"
                >
                  <li
                    v-if="feature.plans.find(p => p.id == tier.id)"
                    class="grid grid-cols-12 py-2"
                  >
                    <span class="col-span-11 text-base font-semibold text-start">
                      {{ feature.name }}
                    </span>

                    <span
                      v-if="feature.plans.find(p => p.id == tier.id).value > 0"
                      class="text-start"
                    >
                      {{
                        feature.plans.find(p => p.id == tier.id).value

                      }}</span>
                    <CheckIcon
                      v-else-if="feature.plans.find(p => p.id == tier.id).value == 'True'"
                      class="w-5 h-5 text-indigo-600 text-end"
                      aria-hidden="true"
                    />
                    <XMarkIcon
                      v-else-if="feature.plans.find(p => p.id == tier.id).value == 'False'"
                      class="w-5 h-5 text-indigo-600 text-end"
                      aria-hidden="true"
                    />
                    <MinusIcon
                      v-else
                      class="w-5 h-5 text-indigo-600 text-end"
                      aria-hidden="true"
                    />
                  </li>
                </template>
              </ul>
            </li>
          </ul>
        </section>
      </div>

      <!-- lg+ -->
      <div class="hidden isolate mt-20 w-full lg:block">
        <div class="relative -mx-8">
          <div class="flex absolute inset-y-0 inset-x-4 -z-10">
            <div class="flex px-4 w-1/4" aria-hidden="true">
              <div class="w-full" />
            </div>
          </div>
          <table
            v-if="plans.plans?.length"
            class="w-full text-left border-separate table-fixed border-spacing-x-8"
          >
            <caption class="sr-only" />
            <colgroup>
              <col class="w-1/4">
            </colgroup>
            <thead>
              <tr>
                <td />
                <th
                  v-for="(tier, index) in plans.plans?.filter(p => p.prices.find(p => p.billing_cycle == billingType))"
                  :key="tier.id"
                  scope="col"
                  class="py-6 text-center xl:px-8 xl:pt-8"
                  :class="[
                    index == 1 ? [
                      'border-x border-t rounded-t border-gray-900/10 bg-gray-400/5 shadow-md',
                      'relative z-10 scale-105 transform',
                      'before:absolute before:inset-0 before:-z-10 before:rounded-t before:border-b-0 before:border-2 before:border-primary-600',
                    ] : '',
                  ]"
                >
                  <div class="text-sm font-semibold leading-7 text-gray-900">
                    {{ $i18n.locale === "ar" ? tier.name.ar : tier.name.en }}
                  </div>
                  <div
                    v-if="tier.price_yearly != 0"
                    class="flex gap-x-1 justify-center items-baseline text-gray-900"
                  >
                    <span class="text-4xl font-bold">
                      {{
                        billingType == "yearly" ? parseInt(tier.prices.find(p => p.billing_cycle == 'yearly')?.price)
                        : parseInt(tier.prices.find(p => p.billing_cycle == 'monthly')?.price)
                      }}
                    </span>
                    <span class="text-sm font-semibold leading-6">
                      {{
                        $i18n.locale === "ar"
                          ? CURRENCIES.SAR.symbol_native
                          : CURRENCIES.SAR.code
                      }}
                      /
                      {{ billingType == "yearly"
                        ? $t("yearly")
                        : $t("monthly") }}
                    </span>
                  </div>
                  <button
                    class="justify-center w-2/3 mx-auto text-base font-medium mt-6 block rounded-md py-2 px-4 text-center leading-6 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-600 hover:shadow-md" :class="[
                      index == 1
                        ? 'bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800'
                        : 'bg-white text-primary-600 border border-primary-600 hover:bg-primary-50 active:bg-primary-100',
                    ]"
                    @click="subscribe(tier)"
                  >
                    {{ $t("subscribe") }}
                  </button>
                </th>
              </tr>
            </thead>
            <tbody>
              <template
                v-for="(section, group, index) in plans.features"
                :key="section"
              >
                <tr v-if="section.length > 1">
                  <th
                    scope="colgroup"
                    colspan="1"
                    class="text-start pb-4 text-sm font-semibold leading-6 text-gray-900"
                    :class="[
                      index === 0 ? 'pt-8' : 'pt-16',
                    ]"
                  >
                    <span class="px-2 text-lg font-semibold text-primary-600">{{ $t(`featureGroups.${group}`) }}</span>
                    <div class="absolute inset-x-8 mt-4 h-px bg-gray-900/10" />
                  </th>
                  <th v-for="(tier, p_i) in plans.plans?.filter(p => p.prices.find(p => p.billing_cycle == billingType))" />
                </tr>
                <tr v-for="(feature, i) in section" :key="feature.name">
                  <th
                    scope="row"
                    class="py-6 text-base font-semibold leading-6 text-gray-900 text-start"
                  >
                    {{ feature.name }}
                    <div class="absolute inset-x-8 mt-6 h-px bg-gray-900/5" />
                  </th>
                  <td
                    v-for="(tier, p_i) in plans.plans?.filter(p => p.prices.find(p => p.billing_cycle == billingType))"
                    :key="tier"
                    class="px-6 xl:px-8"
                    :class="[
                      p_i == 1
                        ? 'rounded border-x  border-gray-900/10 bg-gray-400/5'
                        : '',
                    ]"
                  >
                    <div
                      v-if="feature.plans.find(p => p.id == tier.id)"
                      class="text-center"
                    >
                      <span v-if="feature.plans.find(p => p.id == tier.id).value > 0">
                        {{ feature.plans.find(p => p.id == tier.id).value }}</span>
                      <CheckIcon
                        v-else-if="feature.plans.find(p => p.id == tier.id).value == 'True'"
                        class="mx-auto w-5 h-5 text-indigo-600 mohamed"
                        aria-hidden="true"
                      />
                      <XMarkIcon
                        v-else-if="feature.plans.find(p => p.id == tier.id).value == 'False'"
                        class="mx-auto w-5 h-5 text-indigo-600 mosab"
                        aria-hidden="true"
                      />
                      <XMarkIcon
                        v-else
                        class="mx-auto w-5 h-5 text-indigo-600"
                        aria-hidden="true"
                      />
                    </div>
                    <XMarkIcon
                      v-else
                      class="mx-auto w-5 h-5 text-indigo-600"
                      aria-hidden="true"
                    />
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
          <SubscriptionBanksModal
            v-if="showModal"
            :is-open="showModal"
            :selected-plan="selectedPlan"
            :billing-cycle="billingType"
            @close="showModal = false"
          />
        </div>
      </div>
    </div>
  </div>
</template>
