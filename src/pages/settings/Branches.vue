<script setup lang="ts">
import { PencilSquareIcon, PlusIcon } from '@heroicons/vue/24/outline'
import { ArrowTopRightOnSquareIcon } from '@heroicons/vue/20/solid'
import { storeToRefs } from 'pinia'
import type { ComputedRef } from 'vue'
import type { header } from '@/types'
import BranchDetailsModal from '@/components/Settings/Branch/BranchDetailsModal.vue'
import { ref } from 'vue'
const { t } = useI18n()
const { fetchTeams } = useTeamStore()
const { getTeams, processing } = storeToRefs(useTeamStore())
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('welcome.form.branch_name'),
    },
  ]
})

onMounted(async () => {
  await fetchTeams()
})

const router = useRouter()
const goToTeam = (team: any) => {
  router.push({
    name: 'branche',
    params: { id: team.uuid },
    query: { pageName: team.name },
  })
}
const seletedTeam = ref(null)
const showModal = ref(false)
const openEditModal = (team) => {
  showModal.value = true
  seletedTeam.value = team
}
const openCreateModal = () => {
  showModal.value = true
  seletedTeam.value = null
}

const showBranchModal = ref(false)
const selectedBranchUuid = ref(null)

function openBranchDetails(branch) {
  selectedBranchUuid.value = branch.uuid
  showBranchModal.value = true
}
</script>

<template>
  <div>
    <branch-modal
      v-if="showModal"
      :show-modal="showModal"
      :branch="seletedTeam"
      @refresh="fetchTeams"
      @close="showModal = false"
    />
    <BranchDetailsModal
      :show-modal="showBranchModal"
      :branch-uuid="selectedBranchUuid"
      @close="showBranchModal = false"
      @refresh="fetchTeams()"
    />
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-2xl font-semibold text-gray-900">
          {{ $t("teams.title") }}
        </h1>
        <p class="mt-2 text-sm text-gray-700">
          {{ $t("teams.desc") }}
        </p>
      </div>
      <div class="mt-4 sm:mt-0 sm:flex-none">
        <BaseButton
          class="inline-flex w-auto hover:bg-green-700"
          custome-bg="bg-green-600"
          @click="openCreateModal"
        >
          {{ $t("form.create") }}
          <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </div>
    <div class="flex flex-col mt-4">
      <generic-table
        :is-loading="processing"
        :data="getTeams"
        :headers="headers"
        item-key="uuid"
        tr-class="cursor-pointer"
        :on-row-click="openBranchDetails"
      >
        <template #row="{ item }">
          <grid-td>
            <div class="flex items-center">
              {{ item.name }}
            </div>
          </grid-td>
        </template>
      </generic-table>
    </div>
  </div>
</template>

<style scoped>
form > div:not(:last-child) {
  flex: 1 0 48%;
}
form > div.full {
  flex: 1 0 100%;
}
@media (max-width: 768px) {
  form > div:not(:last-child) {
    flex: 1 0 100% !important;
  }
}
</style>
