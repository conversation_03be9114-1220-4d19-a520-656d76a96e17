<script setup lang="ts">
import type { Ref } from 'vue'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import { ClockIcon } from '@heroicons/vue/20/solid'
const { locale } = useI18n()
interface CurrentPlan {
  plan: string
  next_billing: string
  status: string
}
const router = useRouter()
const {
  getCurrentSubscription,
  cancelSubscription,
  resumeCanceledSubscription,
} = useAccountSettingStore()
const processing = ref(false)
const currentPlan = ref(null) as Ref<CurrentPlan | null>
const showModal = ref(false)
const route = useRoute()
const { renewNow } = toRefs(route.query)
const { getSubscriptionStatus } = storeToRefs(useAuthStore())
onMounted(async () => {
  processing.value = true
  try {
    currentPlan.value = await getCurrentSubscription()
    if (renewNow?.value || getSubscriptionStatus.value == 'past_due')
      renew()
  }
  finally {
    processing.value = false
  }
})
const renew = async () => {
  showModal.value = true
}

const hasPlan = computed(() => {
  return Boolean(currentPlan.value?.plan)
})
const billingType = ref('monthly')
const changePlanStatus = async () => {
  processing.value = true
  try {
    // await changeSubscriptionStatus();
    if (currentPlan.value?.status === 'active')
      await cancelSubscription()
    else
      await resumeCanceledSubscription()

    await getCurrentSubscription()
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <div>
    <SubscriptionBanksModal
      v-if="showModal"
      :is-open="showModal"
      :selected-plan="currentPlan?.selected_plan"
      :billing-cycle="currentPlan?.billing_type"
      :renew="true"
      @close="showModal = false"
    />
    <div class="py-8 sm:py-10">
      <div class="relative px-4 mx-auto max-w-full md:w-7xl sm:px-6 lg:px-8">
        <overlay-loader v-if="processing" :full-screen="false" />
        <div
          v-if="hasPlan"
          class="overflow-hidden relative bg-white bg-gradient-to-r rounded-xl shadow-lg from-primary-50 to-primary-200"
        >
          <span
            class="absolute top-2 px-3 py-1 text-lg font-medium rounded-full end-3"
            :class="{
              'bg-green-100 text-green-800': currentPlan.status === 'active',
              'bg-red-100 text-red-800': currentPlan.status === 'past_due',
            }"
          >
            {{ $t(currentPlan.status) }}
          </span>
          <div class="p-6 md:p-8">
            <h3 class="flex gap-4 items-center text-2xl font-bold text-gray-900">
              {{ $t("your_current_plan") }}:
              <span class="text-primary-600">{{ currentPlan?.plan }}</span>
            </h3>

            <div
              class="flex flex-col justify-between items-start space-y-4 text-gray-600 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4"
            >
              <div class="flex gap-2 items-center">
                <ClockIcon class="w-5 h-5 text-gray-500" />
                <span class="text-sm font-medium">
                  {{ $t("your_next_billing_date") }}:
                  <span class="font-semibold text-red-800" dir="auto">
                    {{ formatDateAndTime(currentPlan?.next_billing) }}</span>
                </span>
              </div>
              <base-button
                custome-bg="bg-primary-600 hover:bg-primary-700 transition duration-150 ease-in-out"
                @click="renew()"
              >
                {{ $t("renew_plan") }}
              </base-button>
            </div>
          </div>
        </div>
        <div
          v-else
          class="overflow-hidden relative bg-white rounded-xl shadow-lg"
        >
          <div class="p-6 md:p-8">
            <h3 class="mb-4 text-2xl font-bold text-gray-900">
              {{ $t("no_active_plan") }}
            </h3>
            <p class="mb-6 text-gray-600">
              {{ $t("choose_the_right_plan_for_your_business") }}
            </p>
            <base-button
              custome-bg="bg-primary-600 hover:bg-primary-700 transition duration-150 ease-in-out"
              @click="router.push('/settings/subscriptions-panel/plans')"
            >
              {{ $t("choose_plan") }}
            </base-button>
          </div>
        </div>
      </div>

      <div v-if="currentPlan" class="relative mt-4 bg-white rounded-lg shadow">
        <h2 class="py-2 text-xl font-bold text-center text-gray-900">
          {{ $t("current_plan_features") }}
        </h2>
        <div
          class="grid grid-cols-1 gap-4 p-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4"
        >
          <div
            v-for="feature in currentPlan.features"
            :key="feature"
            class="px-2 py-3 bg-gray-50 rounded-lg border border-gray-200 shadow-sm transition-shadow duration-300 hover:shadow-md"
          >
            <div class="flex justify-between items-center h-full">
              <span class="font-medium text-gray-800">{{ feature.name }}</span>
              <div class="flex items-center">
                <svg
                  v-if="feature.value === true"
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-6 h-6 text-green-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clip-rule="evenodd"
                  />
                </svg>
                <div
                  v-else-if="feature.value"
                  class="flex justify-center items-center w-6 h-6 text-sm font-medium text-white rounded-full bg-primary-500"
                >
                  {{ feature.value }}
                </div>
                <svg
                  v-else
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-6 h-6 text-red-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
