<script setup lang="ts">
interface Tab {
  value: 'TrashOrders' | 'TrashTransactions'
  current: boolean
  component: any
}
const currentTab = ref<Tab>({} as Tab)
const router = useRouter()

const TrashTransaction = defineAsyncComponent(
  () => import('@/components/Trash/TrashTransaction.vue'),
)
const TrashOrder = defineAsyncComponent(
  () => import('@/components/Trash/TrashOrder.vue'),
)
const tabs = reactive<Tab[]>([
  { component: TrashOrder, value: 'TrashOrders', current: false },
  {
    component: TrashTransaction,
    value: 'TrashTransactions',
    current: false,
  },
])

const selectTab = (tabSelect: {
  value: 'TrashOrders' | 'TrashTransactions'
}) => {
  tabs.forEach((tab) => {
    if (tab.value === tabSelect.value)
      tab.current = true
    else
      tab.current = false
  })
  currentTab.value = tabs.find(tab => tab.value === tabSelect.value)!
}

watch(
  () => router.currentRoute.value.query.tab,
  (tab) => {
    if (tab)
      selectTab({ value: tab as 'TrashOrders' | 'TrashTransactions' })
    else
      router.push({ query: { tab: 'TrashOrders' } })
  },
  { immediate: true },
)
</script>

<template>
  <div>
    <div class="mx-auto">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("trash") }}
      </h1>
    </div>
    <div class="relative mt-2">
      <div>
        <div class="sm:block">
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex w-full" aria-label="Tabs">
              <a
                v-for="tab in tabs"
                :key="tab.value"
                class="min-w-min border-b-2 py-4 px-4 text-center font-medium cursor-pointer border-gray-400" :class="[
                  tab.current
                    ? 'border-primary-500 text-primary-600 !font-bold'
                    : 'text-gray-500 hover:border-gray-300 hover:text-gray-700',
                ]"
                @click="selectTab(tab)"
              >{{ $t(tab.value) }}
              </a>
            </nav>
          </div>
          <Component :is="currentTab.component" />
        </div>
      </div>
    </div>
  </div>
</template>
