<script setup lang="ts">
import {
  BuildingLibraryIcon,
  CubeTransparentIcon,
  DocumentTextIcon,
} from '@heroicons/vue/20/solid'
import { storeToRefs } from 'pinia'
const { getSubscriptionStatus } = storeToRefs(useAuthStore())
const items = [
  ...(!getSubscriptionStatus.value
    ? [
        {
          name: 'plans',
          icon: CubeTransparentIcon,
          to: '/settings/subscriptions-panel/plans',
        },
      ]
    : []),
  {
    name: 'current-plan',
    icon: BuildingLibraryIcon,
    to: '/settings/subscriptions-panel/current-plan',
  },
  {
    name: 'invoices',
    icon: DocumentTextIcon,
    to: '/settings/subscriptions-panel/invoices',
  },
]
</script>

<template>
  <div>
    <div class="my-4">
      <div
        class="grid gap-2 xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-2 sm:grid-cols-1"
      >
        <router-link
          v-for="(item, index) in items"
          :key="index"
          class="bg-white border border-gray-200 shadow-sm px-8 py-6 rounded-lg text-black rounded min-h-[100px] flex items-center justify-center flex-col gap-2 cursor-pointer hover:bg-gray-100 active:scale-95 active:duration-150 active:ease-in-out transition duration-150 ease-in-out"
          :to="{ path: item.to }"
        >
          <component
            :is="item.icon"
            class="mb-2 w-10 h-10 text-primary-900"
            aria-hidden="true"
          />
          <p class="font-semibold text-md text-primary-900">
            {{ $t(item.name) }}
          </p>
        </router-link>
      </div>
    </div>
  </div>
</template>
