<script setup lang="ts">
import { GoogleMap, Marker, MarkerCluster } from 'vue3-google-map'
import { ArrowTopRightOnSquareIcon } from '@heroicons/vue/20/solid'
import { storeToRefs } from 'pinia'
import type { Branch } from '@/types'

const API_KEY = import.meta.env.VITE_GOOGLE_MAP_KEY
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { Team } = storeToRefs(useTeamStore())
const { fetchTeamById, removeTeam } = useTeamStore()
const { params } = useRoute()
const processing = ref(false)
const router = useRouter()

const openModal = ref(false)
const showConfModal = ref(false)
const selectedUuid = ref('')
onBeforeMount(async () => {
  await fetchTeam()
})
const fetchTeam = async () => {
  try {
    processing.value = true
    await fetchTeamById(params?.id as string)
  }
  finally {
    processing.value = false
  }
}

const deleteTeam = async () => {
  showConfModal.value = true
  selectedUuid.value = params?.id as string
}
</script>

<template>
  <div class="flex flex-col">
    <branch-modal
      v-if="openModal" :show-modal="openModal" :branch="Team" @close="openModal = false"
      @refresh="fetchTeam()"
    />
    <overlay-loader v-if="processing" :full-screen="false" />
    <div v-else class="relative px-4 my-2 overflow-x-auto">
      <div>
        <div class="flex flex-col items-center justify-end gap-3 mt-6 mb-4 sm:flex-row sm:gap-0">
          <div class="inline-flex w-full rounded-md shadow-sm md:w-auto">
            <button
              type="button"
              class="relative inline-flex items-center w-3/4 px-3 py-2 text-sm font-semibold text-gray-900 bg-white rounded-md sm:w-auto ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10"
              :class="[
                getLocale(locale)?.direction === 'rtl' ? 'order-2' : 'order-1',
              ]" @click="openModal = true"
            >
              {{ $t("form.edit") }}
            </button>
          </div>
          <div class="inline-flex w-full rounded-md shadow-sm mx-2 md:w-auto">
            <button
              type="button"
              class="relative inline-flex items-center w-3/4 px-3 py-2 text-sm font-semibold text-white bg-red-500 rounded-md sm:w-auto ring-1 ring-inset ring-gray-300 hover:bg-red-600 hover:text-gray-900 focus:z-10"
              :class="[
                getLocale(locale)?.direction === 'rtl' ? 'order-1' : 'order-2',
              ]" @click="deleteTeam"
            >
              {{ $t("form.delete") }}
            </button>
          </div>
        </div>
        <confirmation-modal
          v-if="showConfModal" :dir="getLocale(locale)?.direction" :is-open="showConfModal"
          :api-call="removeTeam" :record-id="selectedUuid" @removed="router.push({ name: 'branches' })"
          @closed="showConfModal = false"
        >
          <p class="leading-7 text-start">
            {{ $t("confirmModal.msg") }}
          </p>
        </confirmation-modal>
        <div class="relative pb-8">
          <img
            v-if="Team?.imageLink" id="image" class="block mx-auto mb-3 rounded-full w-36 h-36" :src="Team.imageLink"
            :link="null"
          >
          <svg
            v-else class="block mx-auto mb-3 text-gray-300 rounded-full w-36 h-36" fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>
          <h1 class="flex items-center justify-center text-base py-2 font-semibold gap-1 sm:gap-4 sm:text-3xl">
            {{ Team?.name }}
          </h1>
          <div class="max-w-7xl mx-auto">
            <GoogleMap
              :api-key="API_KEY" style="width: 100%; height: 400px" :center="{
                lat: Number(Team.latitude),
                lng: Number(Team.longitude),
              }" :draggable="true" :editable="true" :zoom="13"
            >
              <MarkerCluster>
                <Marker
                  :draggable="false" :options="{
                    position: {
                      lat: Number(Team.latitude),
                      lng: Number(Team.longitude),
                    },
                  }"
                />
              </MarkerCluster>
            </GoogleMap>
          </div>
          <div class="grid grid-cols-1 mt-3 sm:grid-cols-2">
            <div class="flex items-center gap-2 py-3 border-b border-stone-100">
              <label for="address" class="block text-sm font-medium text-neutral-500 w-15">{{ $t("address") }} :</label>
              <div class="flex mt-1 font-semibold">
                <div class="relative flex items-stretch flex-grow focus-within:z-10">
                  {{ Team?.address }}
                </div>
              </div>
            </div>
            <div class="flex items-center gap-2 py-3 border-b border-stone-100">
              <label for="status" class="block text-sm font-medium text-neutral-500 w-15">{{ $t("status") }} :</label>
              <div class="flex mt-1 font-semibold">
                <div class="relative flex items-stretch flex-grow focus-within:z-10">
                  {{ Team?.enabled ? $t("active") : $t("inactive") }}
                </div>
              </div>
            </div>
            <div class="flex items-center gap-2 py-3 border-b border-stone-100">
              <label for="phone" class="block text-sm font-medium text-neutral-500 w-15">{{ $t("form.phone") }} :
              </label>
              <div class="flex items-center gap-1 mt-1 font-semibold">
                <span v-if="Team?.phone" class="">
                  {{ Team?.phone }}
                </span>
                <span v-else>-</span>
                <a v-if="Team?.phone" :href="`https://wa.me/${Team?.phone}`" target="_blank">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24">
                    <path
                      fill="#25d366"
                      d="M19.05 4.91A9.816 9.816 0 0 0 12.04 2c-5.46 0-9.91 4.45-9.91 9.91c0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21c5.46 0 9.91-4.45 9.91-9.91c0-2.65-1.03-5.14-2.9-7.01zm-7.01 15.24c-1.48 0-2.93-.4-4.2-1.15l-.3-.18l-3.12.82l.83-3.04l-.2-.31a8.264 8.264 0 0 1-1.26-4.38c0-4.54 3.7-8.24 8.24-8.24c2.2 0 4.27.86 5.82 2.42a8.183 8.183 0 0 1 2.41 5.83c.02 4.54-3.68 8.23-8.22 8.23zm4.52-6.16c-.25-.12-1.47-.72-1.69-.81c-.23-.08-.39-.12-.56.12c-.17.25-.64.81-.78.97c-.14.17-.29.19-.54.06c-.25-.12-1.05-.39-1.99-1.23c-.74-.66-1.23-1.47-1.38-1.72c-.14-.25-.02-.38.11-.51c.11-.11.25-.29.37-.43s.17-.25.25-.41c.08-.17.04-.31-.02-.43s-.56-1.34-.76-1.84c-.2-.48-.41-.42-.56-.43h-.48c-.17 0-.43.06-.66.31c-.22.25-.86.85-.86 2.07c0 1.22.89 2.4 1.01 2.56c.12.17 1.75 2.67 4.23 3.74c.59.26 1.05.41 1.41.52c.59.19 1.13.16 1.56.1c.48-.07 1.47-.6 1.67-1.18c.21-.58.21-1.07.14-1.18s-.22-.16-.47-.28z"
                    />
                  </svg>
                </a>
              </div>
            </div>
            <div class="flex items-center gap-2 py-3 border-b border-stone-100">
              <label for="staff" class="block text-sm font-medium text-neutral-500 w-15">{{ $t("preview") }} :
              </label>
              <div class="mt-1 font-semibold">
                <a
                  v-if="Team.base_link" :href="`${Team.base_link}${Team.slug}`" target="_blank"
                  class="flex items-center gap-2"
                >
                  <ArrowTopRightOnSquareIcon class="w-5 h-5 cursor-pointer" />
                  {{ Team.base_link }}{{ Team.slug }}
                </a>
                <span v-else>-</span>
              </div>
            </div>
            <div class="flex items-center gap-2 py-3 border-b border-stone-100">
              <label for="branch_type" class="block text-sm font-medium text-neutral-500 w-15">{{ $t("branch_type") }} :
              </label>
              <div class="mt-1 font-semibold">
                <p v-if="Team.location" class="flex items-center gap-2">
                  {{ $t(Team.location) }}
                </p>
                <span v-else>-</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <BranchTabs v-if="Team.uuid && !processing" :team="Team" />
  </div>
</template>
