<script setup lang="ts">
import { ref } from 'vue'
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid'
import {
  Listbox,
  ListboxButton,
  ListboxLabel,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/vue'
import useVuelidate from '@vuelidate/core'
import { CURRENCIES, daysMapping } from '@/constants'
import { required } from '@/utils/i18n-validators'
import useSettings from '@/composables/useSettings'
import IndustryService from '@/services/IndustryService'
import { countries } from '@/countries'

const languages = [
  { name: 'ar', label: 'العربية' },
  { name: 'en', label: 'English' },
]

const timeFormat = [
  {
    name: 'hour12',
  },
  {
    name: 'hour24',
  },
]

const dateFormats = [
  {
    name: 'd/m/Y',
    example: '31/12/2021',
  },
  {
    name: 'Y/m/d',
    example: '2021/12/31',
  },
  {
    name: 'd-m-Y',
    example: '31-12-2021',
  },
  {
    name: 'Y-m-d',
    example: '2021-12-31',
  },
  {
    name: 'd.m.Y',
    example: '31.12.2021',
  },
]

const { processing, updateSettings } = useSettings(
  'general-settings',
  handleFreshData,
)

const formData = reactive({
  name: '',
  industry_id: '',
  lang: '',
  currency: '',
  country: '',
  start_of_week: 0,
  time_format: '',
  date_format: '',
  logo: '',
})
const isLoading = ref(false)
const industries = ref<{ [key: string]: string }[]>([])

const rules = {
  name: {
    required,
  },
  industry_id: {
    required,
  },
  lang: {
    required,
  },
  currency: {
    required,
  },
  country: {
    required,
  },
  start_of_week: {
    required,
  },
  time_format: {
    required,
  },
  date_format: {
    required,
  },
}
const v$ = useVuelidate(rules, formData)
const link = ref('')
function handleFreshData(freshData: any) {
  formData.name = freshData.name || ''
  formData.industry_id = freshData.industry.uuid || ''
  formData.lang = freshData.lang || ''
  formData.currency = freshData.currency || ''
  formData.country = freshData.country || 'SA'
  formData.start_of_week = freshData.start_of_week || ''
  formData.time_format = freshData.time_format || ''
  formData.date_format = freshData.date_format || ''
  formData.logo = freshData.logo || ''
  link.value = freshData.logo
}

onMounted(() => {
  IndustryService.fetchIndustries().then(({ data }) => {
    industries.value = data.data
  })
})

const updateGeneralSetting = () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return
  updateSettings(formData)
}
</script>

<template>
  <div>
    <div class="py-4 mx-auto lg:py-2 relative">
      <OverlayLoader v-if="processing" :full-screen="false" />
      <div class="flex">
        <h1 class="text-3xl font-bold tracking-tight text-blue-gray-900 text-start">
          {{ $t("settings.general.heading") }}
        </h1>
        <!-- <img v-if="formData.logo" class="h-12 w-12 rounded-xl text-end mx-5 object-contain border " :src="formData.logo" alt="Your Store">  -->
      </div>
      <form
        class="mt-8 space-y-8 divide-y divide-y-blue-gray-200"
        @submit.prevent="updateGeneralSetting"
      >
        <div class="grid sm:grid-cols-2 grid-cols-1 gap-2">
          <div class="">
            <form-group :validation="v$" name="name">
              <template #default="{ attrs }">
                <TextInput id="tenant-name" v-model="formData.name" :label="$t('settings.general.name')" v-bind="attrs" />
              </template>
            </form-group>
          </div>
          <div>
            <form-group :validation="v$" name="industry_id">
              <template #default="{ attrs }">
                <SelectInput
                  id="sector"
                  v-model="formData.industry_id"
                  :label="$t('settings.general.sector')"
                  v-bind="attrs"
                >
                  <option
                    v-for="inds of industries"
                    :key="inds.uuid"
                    :value="inds.uuid"
                  >
                    {{ inds.name }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>

          <div>
            <form-group :validation="v$" name="lang">
              <template #default="{ attrs }">
                <SelectInput v-model="formData.lang" :label="$t('profile.language')" v-bind="attrs">
                  <option
                    v-for="lang in languages"
                    :key="lang.name"
                    :value="lang.name"
                  >
                    {{ lang.label }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>

          <div>
            <form-group :validation="v$" name="currency">
              <template #default="{ attrs }">
                <Listbox v-model="formData.currency" as="div">
                  <ListboxLabel class="  mb-2 w-full flex items-center text-start text-[#261E27] text-base    ">
                    {{ $t("transaction.currency") }}
                  </ListboxLabel>

                  <div class="relative mt-1">
                    <ListboxButton
                      class="relative cursor-default border border-gray-300 bg-white pl-3 pr-10 text-left focus:ring-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm py-3"
                      v-bind="attrs"
                    >
                      <span class="inline-flex w-full truncate">
                        <template v-for="curr of CURRENCIES">
                          <span
                            v-if="formData.currency === curr.code"
                            :key="curr.name"
                            class="truncate"
                          >
                            {{ curr.name }}
                          </span>
                        </template>
                      </span>
                      <span
                        class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                      >
                        <ChevronUpDownIcon
                          class="h-5 w-5 text-gray-400"
                          aria-hidden="true"
                        />
                      </span>
                    </ListboxButton>

                    <transition
                      leave-active-class="transition ease-in duration-100"
                      leave-from-class="opacity-100"
                      leave-to-class="opacity-0"
                    >
                      <ListboxOptions
                        class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                      >
                        <ListboxOption
                          v-for="(curr, code) in CURRENCIES"
                          :key="code"
                          v-slot="{ active, selected }"
                          as="template"
                          :value="code"
                        >
                          <li
                            class="relative cursor-default select-none py-2 pl-3 pr-9 text-white"
                            :class="[
                              active || selected ? ' bg-indigo-600' : '',
                            ]"
                          >
                            <div class="flex gap-1">
                              <span
                                class="truncate"
                                :class="[
                                  selected || active
                                    ? 'text-white-900'
                                    : 'text-primary',
                                ]"
                              >{{ curr.name }}</span>
                            </div>
                            <span
                              v-if="selected"
                              class="absolute inset-y-0 right-0 flex items-center pr-4"
                              :class="[
                                selected ? 'text-white' : 'text-primay-600',
                              ]"
                            >
                              <CheckIcon class="h-5 w-5" aria-hidden="true" />
                            </span>
                          </li>
                        </ListboxOption>
                      </ListboxOptions>
                    </transition>
                  </div>
                </Listbox>
              </template>
            </form-group>
          </div>

          <div>
            <Listbox v-model="formData.country" as="div">
              <ListboxLabel class="  mb-2 w-full flex items-center text-start text-[#261E27] text-base    ">
                {{ $t("country") }}
              </ListboxLabel>
              <div class="relative mt-1">
                <ListboxButton
                  class="relative cursor-default border border-gray-300 bg-white pl-3 pr-10 text-left focus:ring-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm py-3"
                >
                  <span class="inline-flex w-full truncate">
                    <template v-for="(country, code) in countries">
                      <span
                        v-if="formData.country === code"
                        :key="code"
                        class="truncate"
                      >
                        {{ $t(`countries.${code}`) }}
                      </span>
                    </template>
                  </span>
                  <span
                    class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                  >
                    <ChevronUpDownIcon
                      class="h-5 w-5 text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </ListboxButton>

                <transition
                  leave-active-class="transition ease-in duration-100"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                  >
                    <ListboxOption
                      v-for="(country, code) in countries"
                      :key="code"
                      v-slot="{ active, selected }"
                      as="template"
                      :value="code"
                    >
                      <li
                        class="relative cursor-default select-none py-2 pl-3 pr-9 text-white"
                        :class="[active || selected ? ' bg-indigo-600' : '']"
                      >
                        <div class="flex gap-1">
                          <span
                            class="truncate"
                            :class="[
                              selected || active
                                ? 'text-white-900'
                                : 'text-primary',
                            ]"
                          >{{ $t(`countries.${code}`) }}</span>
                        </div>
                        <span
                          v-if="selected"
                          class="absolute inset-y-0 right-0 flex items-center pr-4"
                          :class="[selected ? 'text-white' : 'text-primay-600']"
                        >
                          <CheckIcon class="h-5 w-5" aria-hidden="true" />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
          </div>

          <div>
            <form-group :validation="v$" name="start_of_week">
              <template #default="{ attrs }">
                <SelectInput
                  id="start_of_week"
                  v-model="formData.start_of_week"
                  :label="$t('start_of_week')"
                  v-bind="attrs"
                >
                  <option
                    v-for="(day, index) in daysMapping"
                    :key="day"
                    :value="index"
                  >
                    {{ $t(`welcome.days.${day}`) }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
          <div>
            <form-group :validation="v$" name="time_format">
              <template #default="{ attrs }">
                <SelectInput
                  v-bind="attrs"
                  id="time_format" v-model="formData.time_format" :label="$t('settings.timeFormat')"
                >
                  <option
                    v-for="(format, index) in timeFormat"
                    :key="index"
                    :value="format.name"
                  >
                    {{ $t(`settings.${format.name}`) }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
          <div>
            <form-group :validation="v$" name="date_format">
              <template #default="{ attrs }">
                <SelectInput
                  v-bind="attrs"
                  id="date_format" v-model="formData.date_format" :label="$t('settings.dateFormat')"
                >
                  <option
                    v-for="(format, index) in dateFormats"
                    :key="index"
                    :value="format.name"
                  >
                    {{ format.example }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>

          <div>
            <LabelInput for="logo">
              {{
                $t("settings.logo")
              }}
            </LabelInput>
            <ImageInput id="logo" v-model="formData.logo" :validation="v$" name="logo" :link="link" @update:link="link = $event" />
            <span class="text-sm text-gray-500">
              {{ $t("settings.logo_validations.size") }}  <br>
              {{ $t("settings.logo_validations.dimensions") }} <br>
              {{ $t("settings.logo_validations.format") }}<br>
            </span>
          </div>
          <div />
        </div>

        <BaseButton
          type="submit"
          :processing="processing"
          class="w-fit inline-flex hover:bg-gray-800 ms-auto mt-3"
          custome-bg="bg-gray-700"
          show-icon
        >
          {{ $t("form.update") }}
        </BaseButton>
      </form>
    </div>
  </div>
</template>
