<script setup lang="ts">
import {
  Switch,
  SwitchDescription,
  SwitchGroup,
  SwitchLabel,
} from '@headlessui/vue'
import useSettings from '@/composables/useSettings'

const formData = reactive({
  channels: [],
  customers: [],
  staff: [],
})

const handleFreshData = ({ customers, channels, staff }) => {
  formData.channels = channels
  formData.customers = customers
  formData.staff = staff
}

const { processing, updateNotificationSetting } = useSettings(
  'notifications',
  handleFreshData,
)
const { generalCustomeSetting, updateGeneralCustomeSettings } = useAccountSettingStore()
const processingActionChannels = ref([])

const toggleChannel = (channel: object, action: object, eventType: string) => {
  const processing = `${channel.id}-${action.id}`
  processingActionChannels.value.push(processing)

  updateNotificationSetting({
    eventType,
    channelId: channel.id,
    actionId: action.id,
  }).finally(() => {
    processingActionChannels.value = processingActionChannels.value.filter(key => key !== processing)
  })
}
const inputs = ref([])
onMounted(() => {
  processing.value = true
  generalCustomeSetting('notifications').then((res) => {
    inputs.value = res
  }).finally(() => {
    processing.value = false
  })
})
const customError = ref([])
const submit = (formData) => {
  customError.value = []
  const payload = {
    setting: {
      notifications: {
        ...formData,
      },
    },
  }
  processing.value = true
  updateGeneralCustomeSettings(payload, 'notifications').catch((err) => {
    customError.value = err.errors
  }).finally(() => {
    processing.value = false
  })
}
</script>

<template>
  <div>
    <!-- <div class="mx-auto max-w-3xl py-4 px-4 sm:px-6 lg:py-2 lg:px-8"> -->
    <div class="mx-auto">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("settings.notifications.heading") }}
      </h1>
    </div>
    <div class="flex flex-col">
      <customForm :inputs="inputs" :err="customError" @submit="submit" />
    </div>

    <div class="relative">
      <overlay-loader v-if="processing" :full-screen="false" />

      <!-- <div class="mx-auto max-w-3xl py-4 px-4 sm:px-6 lg:py-2 lg:px-8"> -->
      <div class="mx-auto">
        <div class="text-xl font-bold text-gray-400">
          {{ $t("settings.notifications.customers") }}
        </div>
        <!-- Privacy section -->
        <div v-for="item of formData.customers" :key="item.id" class="divide-y divide-gray-200 pt-2">
          <div class="">
            <div>
              <h2 class="text-lg font-bold leading-6 text-gray-900">
                {{ item.name }}
              </h2>
            </div>
            <ul role="list" class="mb-4">
              <SwitchGroup v-for="channel of formData.channels" :key="channel.id" as="li" class="flex items-center justify-between py-1">
                <div class="flex flex-col">
                  <SwitchLabel
                    as="p"
                    class="text-sm font-medium text-gray-900"
                    passive
                  >
                    {{ $t(`settings.${channel.name}`) }}
                  </SwitchLabel>
                </div>
                <div class="flex flex-row">
                  <Switch
                    v-if="!processingActionChannels.includes(`${channel.id}-${item.id}`)"
                    class="relative ml-4 inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2" :class="[
                      item.notifications.includes(channel.id) ? 'bg-teal-500' : 'bg-gray-200',
                    ]"
                    @update:model-value="() => toggleChannel(channel, item, 'customer')"
                  >
                    <span
                      aria-hidden="true"
                      class="inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                    />
                  </Switch>
                  <the-loader v-else />
                </div>
              </SwitchGroup>
            </ul>
          </div>
        </div>
      </div>

      <!-- <div class="mx-auto max-w-3xl py-4 px-4 sm:px-6 lg:py-2 lg:px-8"> -->
      <div class="mx-auto ">
        <div class="text-xl font-bold text-gray-400">
          {{ $t("settings.notifications.staff") }}
        </div>
        <!-- Privacy section -->
        <div v-for="item of formData.staff" :key="item.id" class="divide-y divide-gray-200 pt-2">
          <div class="">
            <div>
              <h2 class="text-lg font-bold leading-6 text-gray-900">
                {{ item.name }}
              </h2>
            </div>
            <ul role="list" class="mb-4">
              <SwitchGroup v-for="channel of formData.channels" :key="channel.id" as="li" class="flex items-center justify-between py-1">
                <div class="flex flex-col">
                  <SwitchLabel
                    as="p"
                    class="text-sm font-medium text-gray-900"
                    passive
                  >
                    {{ $t(`settings.${channel.name}`) }}
                  </SwitchLabel>
                </div>
                <div class="flex flex-row">
                  <Switch
                    v-if="!processingActionChannels.includes(`${channel.id}-${item.id}`)"
                    class="relative ml-4 inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2" :class="[
                      item.notifications.includes(channel.id) ? 'bg-teal-500' : 'bg-gray-200',
                    ]"
                    @update:model-value="() => toggleChannel(channel, item, 'staff')"
                  >
                    <span
                      aria-hidden="true"
                      class="inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                    />
                  </Switch>
                  <the-loader v-else />
                </div>
              </SwitchGroup>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
