<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { minLength, required } from '@/utils/i18n-validators'
import Reasons from '@/services/Reasons'
type ReasonType = 'booking-cancel' | 'invoice-refund'
interface Reason {
  uuid: string
  name: string
  type: ReasonType
}
const { locale, t } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { showNotification } = useNotifications()
const loading = reactive<{ [key: string]: Boolean }>({
  'booking-cancel': false,
  'invoice-refund': false,
})
const reasonForm = reactive<Reason & { processing: Boolean }>({
  name: '',
  uuid: '',
  type: 'booking-cancel',
  processing: false,
})
const reasons = reactive<{ [key: string]: Reason[] }>({
  'booking-cancel': [],
  'invoice-refund': [],
})

const openCreateModal = (type: ReasonType): void => {
  openModal.value = true
  editMode.value = false
  reasonForm.type = type
}
const closeFun = () => {
  resetForm()
  openModal.value = false
}
const openModal = ref(false)
const reasonsRules = reactive({
  name: {
    required,
    minLength: minLength(3),
  },
})
const v1$ = useVuelidate(reasonsRules, reasonForm)

const resetForm = () => {
  reasonForm.name = ''
  reasonForm.uuid = ''
  reasonForm.processing = false
  editMode.value = false
  v1$.value.$reset()
}
const fetchRecords = () => {
  loading['booking-cancel'] = true
  loading['invoice-refund'] = true
  Reasons.getReasons()
    .then(({ data }) => {
      // reset reasons to avoid duplication
      reasons['booking-cancel'] = []
      reasons['invoice-refund'] = []
      data.data.forEach((item: Reason) => {
        if (!reasons[item.type]?.length) {
          // sparate reasons by type
          reasons[item.type] = data.data.filter(
            (reason: { type: ReasonType }) => reason.type === item.type,
          )
        }
      })
    })
    .finally(() => {
      loading['booking-cancel'] = false
      loading['invoice-refund'] = false
    })
}
const createRecord = async () => {
  return Reasons.createReason({
    name: reasonForm.name,
    type: reasonForm.type,
  }).then(({ data }) => {
    reasons[data.data.type].push(data.data)
    resetForm()
    showNotification({
      title: t('Success'),
      type: 'success',
      message: t('operations.created'),
    })
  })
}
const editRecord = async () => {
  return Reasons.updateReason({
    name: reasonForm.name,
    uuid: reasonForm.uuid,
    type: reasonForm.type,
  }).then(({ data }) => {
    openModal.value = false
    resetForm()
    fetchRecords()
    showNotification({
      title: t('Success'),
      type: 'success',
      message: t('operations.updated'),
    })
  })
}
const saveReason = async () => {
  v1$.value.$touch()
  if (reasonForm.processing || v1$.value.$invalid)
    return false
  reasonForm.processing = true
  try {
    if (editMode.value)
      await editRecord()
    else
      await createRecord()
  }
  finally {
    reasonForm.processing = false
  }
}
const deleteRecord = async () => {
  reasonForm.processing = true
  Reasons.deleteReason(reasonForm.uuid)
    .then(() => {
      openModal.value = false
      const recordIndex = reasons[reasonForm.type].findIndex(
        (item: Reason) => item.uuid === reasonForm.uuid,
      )
      reasons[reasonForm.type].splice(recordIndex, 1)
      resetForm()
      showNotification({
        title: t('Success'),
        type: 'success',
        message: t('operations.deleted'),
      })
    })
    .finally(() => {
      reasonForm.processing = false
    })
}
const editMode = ref(false)

const headers = computed(() => {
  return [
    {
      title: t('settings.canelationReasons.reason'),
    },
  ]
})

onMounted(() => {
  fetchRecords()
})

const editReason = (reason: Reason) => {
  const { uuid, name, type } = reason
  reasonForm.uuid = uuid
  reasonForm.name = name
  reasonForm.type = type
  editMode.value = true
  openModal.value = true
}
</script>

<template>
  <div class="flex flex-col gap-8">
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("settings.canelationReasons.heading") }}
      </h1>
    </div>
    <modal
      :open="openModal"
      :title="editMode ? 'edit_canelation_reason' : 'create_canelation_reason'"
      :dir="getLocale(locale)?.direction"
      @close="closeFun"
    >
      <form
        :class="[
          getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
        ]"
        @submit.prevent="saveReason"
      >
        <OverlayLoader v-if="reasonForm.processing" :full-screen="false" />
        <div class="col-span-12">
          <form-group :validation="v1$" name="name">
            <template #default="{ attrs }">
              <TextInput
                v-bind="attrs"
                id="reason-name"
                v-model="reasonForm.name"
                :label="$t('settings.canelationReasons.reason')"
                :placeholder="$t('settings.canelationReasons.reason')"
              />
            </template>
          </form-group>
        </div>
        <div class="flex justify-center items-center mt-5 sm:mt-6">
          <div class="flex gap-2 justify-between">
            <BaseButton
              type="submit"
              show-icon
              class="mx-auto w-1/2 hover:bg-green-700"
              custome-bg="bg-green-600"
            >
              {{ editMode ? $t("form.edit") : $t("form.create") }}
            </BaseButton>
            <BaseButton
              v-if="editMode"
              class="mx-auto w-1/2 bg-red-600 rounded-r-md hover:bg-red-700"
              @click="deleteRecord()"
            >
              {{ $t("form.delete") }}
            </BaseButton>
          </div>
        </div>
      </form>
    </modal>
    <div class="relative">
      <!-- <OverlayLoader v-if="processing" :fullScreen="false"/> -->
      <div v-for="type of Object.keys(reasons)" :key="type" class="mb-8">
        <div class="sm:flex sm:items-center">
          <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">
              {{ $t(`settings.canelationReasons.${type}`) }}
            </h1>
          </div>
          <div class="mt-4 sm:mt-0 sm:flex-none">
            <BaseButton
              class="inline-flex w-auto hover:bg-green-700"
              custome-bg="bg-green-600"
              @click="openCreateModal(type)"
            >
              {{ $t("form.create") }}
            </BaseButton>
          </div>
        </div>
        <div class="flex flex-col mt-8">
          <generic-table
            :headers="headers"
            :data="reasons[type]"
            :is-loading="loading[type]"
            tr-class="cursor-pointer"
            :on-row-click="editReason"
            item-key="uuid"
          >
            <template #row="{ item }">
              <grid-td
                class="flex gap-1 items-center py-3 pr-2 pl-2 text-sm whitespace-nowrap sm:pl-6"
                :default-style="false"
              >
                {{ item.name }}
              </grid-td>
            </template>
          </generic-table>
        </div>
      </div>
    </div>
  </div>
</template>
