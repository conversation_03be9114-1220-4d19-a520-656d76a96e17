<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/vue'
import { ClipboardDocumentListIcon } from '@heroicons/vue/20/solid'

const selectedTab = ref()
const tabs = ['order', 'calendar', 'pos_system', 'provider_app']
onMounted(() => {
  selectedTab.value = 'order'
})
</script>

<template>
  <div>
    <div>
      <h1 class="text-2xl font-semibold">
        {{ $t("settings-advanced") }}
      </h1>

      <div class="relative mt-2">
        <div>
          <div class="sm:block">
            <div class="border-b border-gray-200">
              <nav class="flex -mb-px w-full" aria-label="Tabs">
                <a
                  v-for="(tab, idx) in tabs"
                  :key="idx"
                  class="min-w-min border-b-2 py-4 px-4 text-center font-medium cursor-pointer border-gray-400" :class="[
                    selectedTab === tab
                      ? 'border-primary-500 text-primary-600'
                      : 'text-gray-500 hover:border-gray-300 hover:text-gray-700',
                  ]"
                  @click="selectedTab = tab"
                >
                  {{ $t(`homepage.${tab}`) }}
                </a>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
    <order-settings v-if="selectedTab === 'order'" />
    <calendar-settings v-if="selectedTab === 'calendar'" />
    <pos-system-settings v-if="selectedTab === 'pos_system'" />
    <provider-app-settings v-if="selectedTab === 'provider_app'" />
  </div>
</template>
