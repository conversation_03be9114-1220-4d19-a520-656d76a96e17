<script setup lang="ts">
import type { ComputedRef } from 'vue'
import { storeToRefs } from 'pinia'
import AppsPage from './AppsPage.vue'
import TheLoader from '@/components/TheLoader.vue'
import { alertError, alertSuccess } from '@/utils/alert'
import invoicesService from '@/services/invoicesService'
import type { header } from '@/types'
const { t } = useI18n()

const invoices = reactive({
  processing: true,
  data: [],
  links: {},
  meta: {},
})

const syncStatus = ref('synced')

const fetchInvoices = (page = 1) => {
  invoices.processing = true

  invoicesService.fetchSyncedInvoices(page, syncStatus.value).then((response) => {
    const { data, links, meta } = response.data

    invoices.data = data
    invoices.links = links
    invoices.meta = meta
  }).finally(() => {
    invoices.processing = false
  })
}

const sendingInvoice = ref<string | null>(null)

const sendInvoice = (item: { id: string }) => {
  sendingInvoice.value = item.id

  invoicesService.sendInvoiceToWafeq(item.id).then(({ data }) => {
    if (data.sent) {
      invoices.data = invoices.data.filter(inv => inv.id !== item.id)
      alertSuccess(t('settings.syncedInvoices.sentInvoiceSuccess'))
    }
    else {
      alertError(t('settings.syncedInvoices.sentInvoiceError'))
    }
  }).finally(() => {
    sendingInvoice.value = null
  })
}
const appsStore = useAppsStore()

const installedApps = ref(null)
watch(syncStatus, () => {
  fetchInvoices(1)
})

onBeforeMount(async () => {
  if (!await invoicesService.checkWafeqInstallationStatus()) {
    invoices.processing = false
    alertError(t('settings.syncedInvoices.prerequisite'))
  }

  const apps = await appsStore.fetchAppsByCategory('accounting')
  installedApps.value = apps.length
  fetchInvoices()
})

const headers: ComputedRef<header[]> = computed(() => {
  return [
    { title: t('settings.syncedInvoices.headers.invoice_number') },
    { title: t('settings.syncedInvoices.headers.amount') },
    { title: t('settings.syncedInvoices.headers.created_at') },
    { title: t('settings.syncedInvoices.headers.created_by') },
    { title: t('') },
  ]
})
</script>

<template>
  <div class="flex flex-col gap-4">
    <h1 class="text-2xl font-semibold text-gray-900">
      {{ $t('settings.syncedInvoices.title') }}
    </h1>
    <p class="px-2">
      {{ $t('accounting_apps') }}
    </p>
    <AppsPage ShowApps="accounting" />
    <div v-if="installedApps > 0" class="flex itmes-center justify-between">
      <div class="mt-1">
        <SelectInput v-model="syncStatus" custom-classes="mt-2">
          <option value="">
            {{ $t("form.select") }}
          </option>
          <option value="synced">
            {{ $t("settings.syncedInvoices.synced") }}
          </option>
          <option value="unsynced">
            {{ $t("settings.syncedInvoices.unsynced") }}
          </option>
        </SelectInput>
      </div>
    </div>
    <div v-if="installedApps > 0" class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
      <div class="inline-block min-w-full align-middle">
        <generic-table
          v-if="installedApps > 0"
          :is-loading="invoices.processing"
          :data="invoices.data"
          :headers="headers"
        >
          <template #row="{ item }">
            <grid-td>{{ item.invoice_number }}</grid-td>
            <grid-td>
              <price-format
                :form-data="{
                  price: item.amount,
                  currency: item.currency || '',
                }"
              />
            </grid-td>

            <grid-td>{{ item.created_at }}</grid-td>
            <grid-td>{{ item.created_by }}</grid-td>
            <grid-td>
              <button v-if="!item.wafeq_invoice_id" :disabled="sendingInvoice === item.id" :class="{ 'opacity-50': sendingInvoice === item.id }" class="flex items-center gap-2 px-3 py-2 text-white rounded-md shadow-sm bg-primary-800 hover:bg-primary-700" @click="sendInvoice(item)">
                {{ $t("settings.syncedInvoices.sent_to_wafeq") }} <TheLoader v-if="sendingInvoice === item.id" />
              </button>
            </grid-td>
          </template>
        </generic-table>
      </div>
    </div>

    <Pagination
      v-if="invoices.data.length && installedApps > 0"
      :pagination-meta="invoices.meta"
      :pagination-links="invoices.links"
      class="px-4"
      @change="fetchInvoices"
    />
  </div>
</template>
