<script setup lang="ts">
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import AppsPage from './AppsPage.vue'
import type { ActionType, NotificationTemplates, Template } from '@/types'
import { useNotificationTemplate } from '@/stores/notificationTemplate'
interface Tab {
  name: string
  value: 'tenant-staff' | 'tenant-customer' | 'tenant-gift-receiver' | 'sentNotifications' | 'setting' | 'failedNotifications'
  current: boolean
}
const currentTab = ref<Tab>({} as Tab)
const { fetchNotificationTemplates } = useNotificationTemplate()
const NotificationTemplateStore = useNotificationTemplate()
const { getNotificationTemplates } = storeToRefs(useNotificationTemplate())
const router = useRouter()
const tabs = reactive<Tab[]>([
  { name: 'customer_notifications', value: 'tenant-customer', current: false },
  {
    name: 'service_provider_notifications',
    value: 'tenant-staff',
    current: false,
  },
  {
    name: 'tenant_gift_recievers',
    value: 'tenant-gift-receiver',
    current: false,
  },
  { name: 'SentNotifications', value: 'sentNotifications', current: false },
  { name: 'FailedNotifications', value: 'failedNotifications', current: false },
  { name: 'setting', value: 'setting', current: false },
])

const selectTab = (tabSelect: { value: ActionType }) => {
  tabs.forEach((tab) => {
    if (tab.value === tabSelect.value)
      tab.current = true
    else
      tab.current = false
  })
  currentTab.value = tabs.find(tab => tab.value === tabSelect.value)!
  router.push({ query: { tab: currentTab.value.value } })
  const hasNotificationTemplate
    = getNotificationTemplates.value?.actions?.[currentTab.value.value]?.length
  if (
    ['tenant-staff', 'tenant-customer', 'tenant-gift-receiver'].includes(currentTab.value.value)
    && !hasNotificationTemplate
  )
    fetchNotificationTemplates()
}

onBeforeMount(async () => {
  if (!router.currentRoute.value.query.tab)
    selectTab({ value: 'tenant-customer' })
})

watch(
  () => router.currentRoute.value.query.tab,
  (tab) => {
    if (tab)
      selectTab({ value: tab as ActionType })
  },
  { immediate: true },
)
</script>

<template>
  <div>
    <h1 class="text-2xl font-semibold">
      {{ $t("settings.apps.notifications_apps") }}
    </h1>
    <p class="px-2 pt-2">
      {{ $t('settings.apps.desc') }}
    </p>
    <AppsPage ShowApps="notifications" />

    <div class="mx-auto">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("settings.notifications.heading") }}
      </h1>

      <generic-alert
        type="info"
        :title="$t('Dear_Partner')"
        :message="$t('notification_note')"
      />
    </div>
    <div class="relative mt-2">
      <div>
        <div class="sm:block">
          <div class="border-b border-gray-200">
            <nav class="flex -mb-px w-full" aria-label="Tabs">
              <a
                v-for="tab in tabs"
                :key="tab.name"
                class="min-w-min border-b-2 py-4 px-4 text-center font-medium cursor-pointer border-gray-400" :class="[
                  tab.current
                    ? 'border-primary-500 text-primary-600 '
                    : 'text-gray-500 hover:border-gray-300 hover:text-gray-700',
                ]"
                @click="selectTab(tab)"
              >{{ $t(tab.name) }}
              </a>
            </nav>
          </div>
          <div v-if="currentTab.value">
            <overlay-loader
              v-if="NotificationTemplateStore.processing"
              :full-screen="false"
            />
            <staff-and-customer-tab
              v-if="
                ['tenant-staff', 'tenant-customer', 'tenant-gift-receiver'].includes(currentTab.value)
              "
              :notification-template-list="getNotificationTemplates"
              :current-tab="currentTab"
            />
            <sent-notifications-tab
              v-else-if="['sentNotifications', 'failedNotifications'].includes(currentTab.value)"
              :current-tab="currentTab.value"
            />
            <notification-settings-tab v-else />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
