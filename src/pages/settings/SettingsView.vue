<script lang="ts" setup>
const route = useRoute()

const fromPage = computed(() => {
  return route.name !== 'settings-panel'
    ? [{ name: 'settings-panel', path: '/settings/panel' }]
    : []
})

const crumbs = computed(() => {
  return [
    ...fromPage.value,
    {
      name: route.query?.pageName || route.name,
      path: route.path,
    },
  ]
})
</script>

<template>
  <div class="">
    <main class="flex flex-1">
      <div class="flex-1">
        <div class="flex relative flex-col">
          <bread-crumb :crumbs="crumbs" class="mt-5 mb-7" />
        </div>
        <Suspense>
          <!-- children pages -->
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </Suspense>
      </div>
    </main>
  </div>
</template>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
