<script setup lang="ts">
import { } from '@heroicons/vue/20/solid'
import { storeToRefs } from 'pinia'

import { Switch } from '@headlessui/vue'
import type { ComputedRef } from 'vue'
import useVuelidate from '@vuelidate/core'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import type { header } from '@/types'
// import type { Booking, Customer } from "@/types";
import { minLength, required, requiredIf } from '@/utils/i18n-validators'
import { usePaymentMethodsStore } from '@/stores/paymentMethods'
import { useLocalesStore } from '@/stores/locales'
import { useAuthStore } from '@/stores/auth'

const { getPaymentMethods, getPaymentMethod, createPaymentMethods, updatePaymentMethods, deletePaymentMethods } = usePaymentMethodsStore()
const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const router = useRouter()
const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)
const tiers = ref([])
const paymentMethods = ref({})
const types = ref([
  { id: 1, name: { en: 'Cash', ar: 'نقدي' }, slug: 'cash' },
  { id: 2, name: { en: 'Card', ar: 'بطاقة' }, slug: 'card' },
  { id: 3, name: { en: 'Other', ar: 'أخرى' }, slug: 'other' },
])
const processing = ref(false)
const showModal = ref(false)
const modalTitle = ref('')

const paymentMethodsForm = reactive<any>({
  id: '',
  name: '',
  status: true,
  type: '',
})

const rules = {
  name: {
    required,
    minLength: minLength(3),
  },
  type: {
    required,
  },
}
const v$ = useVuelidate(rules, paymentMethodsForm)

const openCreateModal = () => {
  showModal.value = true
  modalTitle.value = 'createPaymentMethod'
  resetForm()
}

const editField = (field: any): void => {
  modalTitle.value = 'editPaymentMethod'
  const { id, name, status, type } = field
  paymentMethodsForm.id = id
  paymentMethodsForm.name = name
  paymentMethodsForm.status = status
  showModal.value = true
}
function resetForm() {
  paymentMethodsForm.id = '',
  paymentMethodsForm.name = '',
  paymentMethodsForm.status = true,
  paymentMethodsForm.type = '',
  v$.value.$reset()
}
const closeModal = () => {
  resetForm()
  showModal.value = false
}

const deleteRecord = () => {
  processing.value = true
  deletePaymentMethods(paymentMethodsForm.id)
    .then(async () => {
      showModal.value = false
      const getPayments = await getPaymentMethods()
      paymentMethods.value = getPayments.data
    })
    .then(() => {
      resetForm()
      showModal.value = false
    })
    .finally(() => {
      processing.value = false
    })
}
const saveRecord = () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return false
  processing.value = true
  if (paymentMethodsForm.id) {
    updatePaymentMethods(paymentMethodsForm)
      .then(async () => {
      // in edit mode
        const getPayments = await getPaymentMethods()
        paymentMethods.value = getPayments.data
        if (paymentMethodsForm.id)
          showModal.value = false
      })
      .finally(() => {
        processing.value = false
      })
  }
  else {
    createPaymentMethods(paymentMethodsForm)
      .then(async () => {
        const getPayments = await getPaymentMethods()
        paymentMethods.value = getPayments.data
        resetForm()
      })
      .finally(() => {
        processing.value = false
      })
  }
}

const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.paymentMethodId'),
    },
    {
      title: t('fields.type'),
    },
  ]
})
onMounted(async () => {
  processing.value = true
  const getPayments = await getPaymentMethods()
  paymentMethods.value = getPayments.data
  processing.value = false
})
</script>

<template>
  <div class="">
    <OverlayLoader v-if="processing" :full-screen="false" />
    <modal
      v-if="showModal"
      :open="showModal"
      :title="modalTitle"
      :dir="getLocale(locale)?.direction"
      @close="closeModal"
    >
      <form class="relative text-start" @submit.prevent="saveRecord">
        <div class="grid grid-cols-1 gap-2 md:grid-cols-2">
          <div class="col-span-1">
            <form-group :validation="v$" name="name">
              <template #default="{ attrs }">
                <TextInput
                  v-bind="attrs"
                  id="name"
                  v-model="paymentMethodsForm.name"
                  :label="$t('form.paymentMethodId')"
                  :placeholder="$t('form.paymentMethodId')"
                />
              </template>
            </form-group>
          </div>
          <div class="col-span-1">
            <form-group :validation="v$" name="type">
              <template #default="{ attrs }">
                <SelectInput
                  v-bind="attrs"
                  id="type"
                  v-model="paymentMethodsForm.type"
                  :label="$t('form.paymentMethodType')"
                  required
                >
                  <option value="">
                    {{ $t("form.select") }}
                  </option>
                  <option v-for="type of types" :key="type.id" :value="type.slug">
                    {{ $i18n.locale === 'ar' ? type.name.ar : type.name.en }}
                  </option>
                </SelectInput>
              </template>
            </form-group>
          </div>
        </div>

        <div class="flex flex-col mt-5">
          <label class="mb-2 w-full flex items-center text-start text-[#261E27] text-base">
            {{ $t("form.status") }}
          </label>
          <Switch
            v-model="paymentMethodsForm.status"
            class="inline-flex relative flex-shrink-0 items-center w-11 h-6 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            :class="[paymentMethodsForm.status ? 'bg-primary-600' : 'bg-gray-200']"
          >
            <span
              aria-hidden="true"
              class="inline-block w-5 h-5 bg-white rounded-full ring-0 shadow transition duration-200 ease-in-out transform pointer-events-none"
              :class="[
                paymentMethodsForm.status
                  ? 'translate-x-5 rtl:-translate-x-5'
                  : 'translate-x-0',
              ]"
            />
          </Switch>
        </div>
        <div class="mt-5 sm:mt-6">
          <div class="mt-8">
            <div class="flex justify-end items-center">
              <div class="flex gap-2 justify-between">
                <BaseButton
                  v-if="paymentMethodsForm.id"
                  class="mx-auto w-1/2 hover:bg-red-700"
                  custome-bg="bg-red-600"
                  type="button"
                  @click="deleteRecord()"
                >
                  {{ $t("form.delete") }}
                </BaseButton>

                <BaseButton
                  show-icon
                  class="mx-auto w-1/2 hover:bg-primary-700"
                  custome-bg="bg-primary-700"
                  type="submit"
                >
                  {{ paymentMethodsForm.id ? $t("form.update") : $t("form.create") }}
                </BaseButton>
              </div>
            </div>
          </div>
        </div>
      </form>
    </modal>
    <div class="mb-8">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="pt-4 text-2xl font-semibold text-gray-900">
            {{ $t(`altNav.PaymentMethods`) }}
          </h1>
          <p class="p-2">
            {{ $t(`payment_method_description`) }}
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:flex-none">
          <BaseButton
            class="inline-flex w-auto hover:bg-green-700"
            custome-bg="bg-green-600"
            @click="openCreateModal()"
          >
            {{ $t("form.create") }}
          </BaseButton>
        </div>
      </div>
      <div class="flex flex-col mt-8">
        <div class="">
          <generic-table
            :is-loading="processing"
            :data="paymentMethods"
            :headers="headers"
            tr-class="cursor-pointer"
            itemkey="id"
            :on-row-click="editField"
          >
            <template #row="{ item }">
              <grid-td>
                <div class="flex items-center text-base">
                  {{ item.name }}
                </div>
              </grid-td>
              <grid-td>
                <div class="flex items-center text-base">
                  {{ item.type?.name }}
                </div>
              </grid-td>
            </template>
          </generic-table>
        </div>
      </div>
    </div>
  </div>
</template>

