<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { ArrowDownIcon, PlusIcon } from '@heroicons/vue/24/outline'
import type { Labels } from '../../types/labels'
import { useLabels } from '@/stores/labels'

const { getAllLabels } = useLabels()
const { labels, getLabels } = storeToRefs(useLabels())
const processing = ref(false)
const showModal = ref(false)
const labelsModal = ref({ name: '', color: '' })
const showSliderEvent = (labels: Labels) => {
  showModal.value = true
  labelsModal.value = labels
}
const openModal = () => {
  showModal.value = true
  labelsModal.value = { name: '', color: '' }
}
const couponsList = ref<Labels[]>([])
onMounted(() => {
  processing.value = true
  getAllLabels().finally(() => {
    processing.value = false
  })
})
</script>

<template>
  <div class="flex flex-col gap-8">
    <LabelsModal :label="labelsModal" :show-modal="showModal" @close="showModal = false" />
    <div class="flex itmes-center justify-between">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t('settings.labels') }}
      </h1>
      <BaseButton class="inline-flex w-auto hover:bg-green-700" custome-bg="bg-green-600" @click="openModal">
        {{ $t("form.create") }}
        <PlusIcon class="ms-2 -me-0.5 h-4 w-4" aria-hidden="true" />
      </BaseButton>
    </div>
    <labels-grid :is-loading="processing" @open-modal="showSliderEvent" />
  </div>
</template>
