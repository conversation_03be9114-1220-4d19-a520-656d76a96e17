<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { ArrowDownIcon, PlusIcon } from '@heroicons/vue/24/outline'
import type { Roles } from '../../types/roles'
import { useRoles } from '@/stores/roles'

const { getAllRoles, getAllPermission } = useRoles()
const { roles, getRoles } = storeToRefs(useRoles())
const processing = ref(false)
const showModal = ref(false)
const rolesModal = ref({ name: '', permissions: [''], editable: '' })
const showSliderEvent = (roles: Roles) => {
  showModal.value = true
  rolesModal.value = roles
}
const openModal = () => {
  showModal.value = true
  rolesModal.value = { name: '', permissions: [''], editable: true }
}
const couponsList = ref<Roles[]>([])
onMounted(() => {
  processing.value = true
  getAllRoles().finally(() => {
    processing.value = false
  })
})
</script>

<template>
  <div class="flex flex-col gap-8">
    <!-- <roles-modal :role="rolesModal" :show-modal="showModal" @close="showModal = false" /> -->
    <RolesModal :role="rolesModal" :show-modal="showModal" @close="showModal = false" />
    <div class="flex itmes-center justify-between">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t('settings.role') }}
      </h1>
      <BaseButton class="inline-flex w-auto hover:bg-green-700" custome-bg="bg-green-600" @click="openModal">
        {{ $t("form.create") }}
        <PlusIcon class="ms-2 -me-0.5 h-4 w-4" aria-hidden="true" />
      </BaseButton>
    </div>
    <roles-grid :is-loading="processing" @open-modal="showSliderEvent" />
  </div>
</template>
