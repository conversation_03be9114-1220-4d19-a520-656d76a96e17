<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { ArrowDownIcon, PlusIcon } from '@heroicons/vue/24/outline'
import type { Taxes } from '../../types/taxes'
import { useTaxes } from '@/stores/taxes'

const { getAllTaxes, deleteTaxes } = useTaxes()
// const { taxes, getTaxes, de } = storeToRefs(useTaxes())
const processing = ref(false)
const showModal = ref(false)
const taxesModal = ref<Taxes>({ uuid: '', name: '', rate: 0, status: true })
const showSliderEvent = (tax: Taxes) => {
  showModal.value = true
  taxesModal.value = { uuid: tax.uuid, name: tax.name, rate: tax.rate, status: tax.status }
}
const closeModal = () => {
  showModal.value = false
}

const openModal = () => {
  showModal.value = true
  taxesModal.value = { name: '', rate: 0, status: true }
}

const deleteTax = (uuid: string) => {
  processing.value = true
  deleteTaxes(uuid).finally(() => {
    getAllTaxes().finally(() => {
      processing.value = false
    })
  })
}

const couponsList = ref<Taxes[]>([])
onMounted(() => {
  processing.value = true
  getAllTaxes().finally(() => {
    showModal.value = false
    processing.value = false
  })
})
</script>

<template>
  <div class="flex flex-col gap-8">
    <TaxesModal :role="taxesModal" :show-modal="showModal" @close="closeModal" @delete-tax="deleteTax" />
    <div class="flex itmes-center justify-between">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t('settings.taxes') }}
      </h1>
      <BaseButton class="inline-flex w-auto hover:bg-green-700" custome-bg="bg-green-600" @click="openModal">
        {{ $t("form.create") }}
        <PlusIcon class="ms-2 -me-0.5 h-4 w-4" aria-hidden="true" />
      </BaseButton>
    </div>
    <taxes-grid :is-loading="processing" @open-modal="showSliderEvent" />
  </div>
</template>
