<script setup lang="ts">
import { PlusIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import type { ComputedRef } from 'vue'
import { email, required } from '@/utils/i18n-validators'
import { makeArrayEmpty } from '@/utils/makeArrOfErrEmpty'
import TheLoaderVue from '@/components/TheLoader.vue'
import type { header } from '@/types'
import staff from '@/composables/useStaff'
const store = useTeamStore()
const { processing } = storeToRefs(useTeamStore())
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const {
  createUser,
  updateUser,
  deactiveUser,
  fetchTeams,
  changeUserToProvider,
  changeProviderToUser,
} = useTeamStore()
const formData = reactive({
  uuid: '',
  name: '',
  industry_id: '',
  personal_team: '',
  enabled: false,
  processing: false,
})

const teamUsers = ref([])

const userRoles = ref([])

const userStaff = ref([])
const userStaffOptions = computed(() => {
  return userStaff.value.map(staff => ({
    value: staff.uuid,
    label: staff.name,
  }))
})
const providers = ref([])

const rules = {
  name: {
    required,
  },
}
const v$ = useVuelidate(rules, formData)

const updateSettings = () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return
  formData.processing = true

  const payload = {
    teamId: formData.uuid,
    body: {
      name: formData.name,
      personal_team: formData.personal_team,
      industry_id: formData.industry_id,
      status: formData.enabled,
    },
  }

  store.update(payload).finally(() => {
    formData.processing = false
  })
}

const showModal = ref(false)
const showProviderModal = ref(false)
const processingUser = ref('')
const userToDeactivate = ref<{
  uuid: string
  name: string
  isActive: boolean
} | null>(null)

const invitation = reactive({
  name: '',
  email: '',
  role: '',
  branches: [],
  staff: [],
  userId: '',
  type: 'user',
  processing: false,
})
const invitationStaff = reactive({
  name: '',
  email: '',
  staff_id: '',
  userId: '',
  type: 'provider',
  processing: false,
})
const invitRules = {
  name: { required },
  email: { required, email },
  role: { required },
  branches: { required },
}
const invitProviderRules = {
  name: { required },
  email: { required, email },
  staff_id: { required },
}

const v1$ = useVuelidate(invitRules, invitation)
const v2$ = useVuelidate(invitProviderRules, invitationStaff)

const errHandle = reactive<{ [key: string]: string[] }>({})

const hideModal = () => {
  showModal.value = false
  invitation.email = ''
  invitation.name = ''
  invitation.role = ''
  invitation.branches = []
  invitation.staff = []
  invitation.userId = ''
  invitation.type = 'user'
  v1$.value.$reset()
  makeArrayEmpty(errHandle)
}
const hideProviderModal = () => {
  showProviderModal.value = false
  invitationStaff.email = ''
  invitationStaff.name = ''
  invitationStaff.staff_id = ''
  invitationStaff.userId = ''
  invitationStaff.type = 'provider'
  v2$.value.$reset()
  makeArrayEmpty(errHandle)
}

const getAllUsers = () => {
  store.fetchInfo().then(({ users, roles, staff }) => {
    providers.value = users.filter(user => user.type === 'provider')
    teamUsers.value = users.filter(user => user.type === 'user')
    userRoles.value = roles
    userStaff.value = staff
  })
}

const upsertStaff = async () => {
  v2$.value.$touch()
  if (v2$.value.$invalid)
    return
  makeArrayEmpty(errHandle)
  const { name, email, staff_id, userId, type } = invitationStaff
  invitationStaff.processing = true
  processing.value = true
  if (invitationStaff.userId) {
    updateUser(userId, {
      name,
      email,
      staff_id,
      type,
    })
      .then((res) => {
        getAllUsers()
        hideProviderModal()
      })
      .catch((err) => {
        for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        invitationStaff.processing = false
        processing.value = false
      })
  }
  else {
    createUser({
      name,
      email,
      staff_id,
      type,
    })
      .then((res) => {
        getAllUsers().then(() => {
          hideProviderModal()
        })
      })
      .catch((err) => {
        for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        invitationStaff.processing = false
        processing.value = false
      })
  }
}
const router = useRouter()
const deleteProvider = async (user) => {
  if (confirm(t('settings.teamusers.confirmDelete'))) {
    processingUser.value = user.uuid
    try {
      await deactiveUser(user.uuid)
      await fetchTeams()
      getAllUsers()
    }
    finally {
      processingUser.value = ''
    }
  }
}

const ShowStaff = (user) => {
  router.push({ name: 'staff', params: { id: user.provider.uuid } })
}
const upsertUser = async () => {
  v1$.value.$touch()
  if (v1$.value.$invalid)
    return
  makeArrayEmpty(errHandle)
  const { name, email, role, branches, userId, staff, type } = invitation
  invitation.processing = true
  processing.value = true
  if (invitation.userId) {
    updateUser(userId, {
      name,
      email,
      branches,
      role,
      staff,
      type,
    })
      .then((res) => {
        getAllUsers()
        hideModal()
      })
      .catch((err) => {
        for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        invitation.processing = false
        processing.value = false
      })
  }
  else {
    createUser({
      name,
      email,
      branches,
      role,
      staff,
      type,
    })
      .then((res) => {
        getAllUsers()
        hideModal()
      })
      .catch((err) => {
        for (const prop in err.errors) errHandle[prop] = err.errors[prop]
      })
      .finally(() => {
        invitation.processing = false
        processing.value = false
      })
  }
}

const openModalUpdateUser = (user) => {
  if (user.isSuperAdmin)
    return
  invitation.email = user.email
  invitation.name = user.name
  invitation.role = user.roles[0]?.uuid || ''
  invitation.branches = user.teams.map(team => team.uuid)
  invitation.staff = user.staff.map(staff => staff.uuid)
  invitation.userId = user.uuid
  showModal.value = true
}

const deactivateUser = (user: { uuid: string; name: string; isActive: boolean }) => {
  userToDeactivate.value = user
}

const confirmDeactivate = async () => {
  if (!userToDeactivate.value)
    return

  processingUser.value = userToDeactivate.value.uuid
  try {
    await deactiveUser(userToDeactivate.value.uuid)
    await getAllUsers()
  }
  catch (error) {
    console.error('Error deactivating user:', error)
  }
  finally {
    processingUser.value = ''
    userToDeactivate.value = null
  }
}

const { t } = useI18n()
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('settings.teamusers.table.name'),
    },
    {
      title: t('settings.teamusers.table.email'),
    },
    {
      title: t('settings.teamusers.table.role'),
    },
    {
      title: t('settings.teamusers.table.status'),
    },
  ]
})

const headerStaff: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('settings.teamusers.table.name'),
    },
    {
      title: t('settings.teamusers.table.email'),
    },
    {
      title: t('settings.teamusers.table.provider'),
    },
    {
      title: t('settings.teamusers.table.status'),
    },
  ]
})

onMounted(() => {
  getAllUsers()
  fetchTeams()
})
const renderRoleName = (item) => {
  if (item?.isSuperAdmin)
    return t('super_admin')
  return item?.roles.map(item => item.name || '-').join(' , ')
}
</script>

<template>
  <div>
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h2 class="mb-1 text-2xl font-semibold text-gray-900">
            {{ $t("settings.teamusers.users") }}
          </h2>
          <p class="text-sm text-gray-700">
            {{ $t("settings.teamusers.branch-users") }}
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:flex-none">
          <BaseButton
            class="inline-flex w-auto hover:bg-green-700"
            custome-bg="bg-green-600"
            type="button"
            @click="showModal = true"
          >
            {{ $t("form.create") }}
            <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
          </BaseButton>
        </div>
      </div>
      <div class="flex flex-col mt-4">
        <generic-table
          :is-loading="processing"
          :data="teamUsers"
          :headers="headers"
          item-key="uuid"
          :on-row-click="openModalUpdateUser"
          tr-class="cursor-pointer hover:bg-gray-100 text-center"
        >
          <template #row="{ item }">
            <GridTd>
              {{ item?.name }}
            </GridTd>
            <GridTd>
              {{ item?.email }}
            </GridTd>
            <GridTd>
              {{ renderRoleName(item) }}
            </GridTd>

            <GridTd>
              <TheLoaderVue v-if="processingUser === item.uuid" />
              <div v-else-if="!item.isSuperAdmin" class="flex gap-3">
                <BaseButton
                  v-if="item.isActive"
                  :class="{ 'bg-red-600': item.isActive }"
                  @click.stop="deleteProvider(item)"
                >
                  {{ item.isActive ? $t("deactivate") : $t("activate") }}
                </BaseButton>
              </div>
            </GridTd>
          </template>
        </generic-table>
      </div>
    </div>

    <Modal
      :open="showModal"
      :title="invitation.userId ? 'staff' : 'user'"
      :dir="getLocale(locale)?.direction"
      panel-classes="w-full max-w-3xl"
      @close="hideModal"
    >
      <form
        class="mt-6"
        :class="[
          getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
        ]"
        @submit.prevent="upsertUser"
      >
        <ErrValidations :err-handle="errHandle" />
        <div class="grid grid-cols-2 gap-2">
          <div>
            <TextInput
              id="staff"
              v-model="invitation.name"
              :label="$t('form.name')"
              :placeholder="$t('form.name')"
              :readonly="!!invitation.userId"
            />
            <p
              v-for="error in v1$.name.$errors"
              :key="error.$uid"
              class="error-message"
            >
              {{ $t(error.$message) }}
            </p>
          </div>
          <div>
            <TextInput
              id="staff"
              v-model="invitation.email"
              :label="$t('form.email')"
              :placeholder="$t('form.email')"
              :readonly="!!invitation.userId"
            />
            <p
              v-for="error in v1$.email.$errors"
              :key="error.$uid"
              class="error-message"
            >
              {{ $t(error.$message) }}
            </p>
          </div>
        </div>
        <div class="mt-4">
          <div>
            <SelectInput
              id="role"
              v-model="invitation.role"
              :label="$t('settings.role')"
              :placeholder="$t('settings.role')"
              class="block w-full text-gray-700 rounded-md border-gray-300 focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="">
                {{ $t("form.select") }}
              </option>
              <option
                v-for="role of userRoles"
                :key="role?.uuid"
                :value="role?.uuid"
              >
                {{ role?.name }}
              </option>
            </SelectInput>
          </div>
          <p
            v-for="error in v1$.role.$errors"
            :key="error.$uid"
            class="error-message"
          >
            {{ $t(error.$message) }}
          </p>
        </div>
        <div />
        <div class="mt-4">
          <label
            for="staff"
            class=" mb-2 w-full flex items-center text-start text-[#261E27] text-base "
          >{{ $t("form.branches") }}</label>
          <div class="flex flex-col">
            <label v-for="team in store.getTeams" :key="team.uuid">
              <input
                v-model="invitation.branches"
                type="checkbox"
                class="mx-1"
                :value="team.uuid"
              >
              {{ team.name }}
            </label>
          </div>
          <p
            v-for="error in v1$.branches.$errors"
            :key="error.$uid"
            class="error-message"
          >
            {{ $t(error.$message) }}
          </p>
        </div>

        <!--
          <div>
            <label
              for="staff"
              class=" mb-2 w-full flex items-center text-start text-[#261E27] text-base "
              >{{ $t("form.staff_users") }}</label
            >
            <div class="flex flex-col">
              <label v-for="user in userStaff" :key="user.uuid">
                <input
                  v-model="invitation.staff"
                  type="checkbox"
                  class="mx-1"
                  :value="user.uuid"
                />
                {{ user.name }}
              </label>
            </div>
          </div>
        </div>
-->
        <BaseButton
          type="submit"
          class="mx-auto mt-6 w-1/2"
          show-icon
          :processing="invitation.processing"
        >
          {{
            $t(
              !invitation.userId
                ? "settings.teamusers.invitation.invite"
                : "form.update",
            )
          }}
        </BaseButton>
      </form>
    </Modal>

    <div class="py-11">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <div class="flex items-center">
            <span class="mr-1">{{ $t('settings.teamusers.providers') }}</span>
          </div>
          <p class="mb-1 text-base font-semibold text-red-600">
            {{ $t("provider_accounts_description") }}
          </p>
        </div>
        <!-- <div class="mt-4 sm:mt-0 sm:flex-none">
          <BaseButton
            class="inline-flex w-auto hover:bg-green-700"
            custome-bg="bg-green-600"
            type="button"
            @click="showProviderModal = true"
          >
            {{ $t("form.create") }}
            <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
          </BaseButton>
        </div> -->
      </div>
      <div class="flex flex-col mt-4">
        <generic-table
          :is-loading="processing"
          :data="providers"
          :headers="headerStaff"
          item-key="uuid"
          tr-class="cursor-pointer hover:bg-gray-100"
        >
          <template #row="{ item }">
            <GridTd @click="ShowStaff(item)">
              {{ item.name }}
            </GridTd>
            <GridTd @click="ShowStaff(item)">
              {{ item?.email }}
            </GridTd>
            <GridTd @click="ShowStaff(item)">
              <span v-if="item?.provider?.uuid">{{
                item?.provider?.name
              }}</span>
              <div v-else class="font-medium text-red-500">
                {{ $t("settings.teamusers.no-provider") }}
              </div>
            </GridTd>
            <GridTd>
              <TheLoaderVue v-if="processingUser === item.uuid" />
              <div v-else class="flex gap-3">
                <BaseButton
                  :class="{ 'bg-red-600': item.isActive }"
                  @click.stop="deleteProvider(item)"
                >
                  {{ item.isActive ? $t("deactivate") : $t("activate") }}
                </BaseButton>
              </div>
            </GridTd>
          </template>
        </generic-table>
      </div>
    </div>

    <Modal
      :open="showProviderModal"
      :title="invitationStaff.userId ? 'staff' : 'user'"
      :dir="getLocale(locale)?.direction"
      @close="hideProviderModal"
    >
      <form
        class="mt-6"
        :class="[
          getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
        ]"
        @submit.prevent="upsertStaff"
      >
        <ErrValidations :err-handle="errHandle" />
        <div class="grid grid-cols-2 gap-2">
          <div>
            <TextInput
              id="staff"
              v-model="invitationStaff.name"
              :label="$t('form.name')"
              :readonly="!!invitationStaff.userId"
              :placeholder="$t('form.name')"
            />
            <p
              v-for="error in v2$.name.$errors"
              :key="error.$uid"
              class="error-message"
            >
              {{ $t(error.$message) }}
            </p>
          </div>
          <div>
            <TextInput
              id="staff"
              v-model="invitationStaff.email"
              :label="$t('form.email')"
              :readonly="!!invitationStaff.userId"
              :placeholder="$t('form.email')"
            />
            <p
              v-for="error in v2$.email.$errors"
              :key="error.$uid"
              class="error-message"
            >
              {{ $t(error.$message) }}
            </p>
          </div>

          <div>
            <div class="flex flex-col">
              <BaseComboBox
                v-model="invitationStaff.staff_id"
                place-holder="staff"
                arial-label="Search"
                class="flex-1"
                :options="userStaffOptions"
              >
                {{ $t("form.staff_users") }}
              </BaseComboBox>
              <p
                v-for="error in v2$.staff_id.$errors"
                :key="error.$uid"
                class="error-message"
              >
                {{ $t(error.$message) }}
              </p>
            </div>
          </div>
        </div>

        <BaseButton
          type="submit"
          class="mx-auto mt-6 w-1/2"
          show-icon
          :processing="invitationStaff.processing"
        >
          {{
            $t(
              !invitationStaff.userId
                ? "settings.teamusers.invitation.invite"
                : "form.update",
            )
          }}
        </BaseButton>
      </form>
    </Modal>

    <AlertModal
      v-if="userToDeactivate"
      :title="$t('alert')"
      :message="$t('settings.teamusers.confirmDeactivate')"
      @confirm="confirmDeactivate"
      @cancel="userToDeactivate = null"
    />
  </div>
</template>
