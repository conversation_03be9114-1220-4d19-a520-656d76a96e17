<script setup lang="ts">
import { CheckBadgeIcon } from '@heroicons/vue/24/solid'
import CustomForm from '@/components/customForm.vue'
import { makeArrayEmpty } from '@/utils/makeArrOfErrEmpty'

const {
  generalCustomeSetting,
  updateGeneralCustomeSettings,
  getInvoiceTemplates,
  setDefaultInvoiceTemplate,
} = useAccountSettingStore()

const customError = reactive<{ [key: string]: string[] }>({})
const isLoading = ref(false)
const inputs = ref([])
const processing = ref(false)
async function fetchInvoiceDetails() {
  return generalCustomeSetting('invoices').then((res) => {
    inputs.value = res
  })
}

const submit = (formData: any) => {
  makeArrayEmpty(customError)
  const payload = {
    setting: {
      invoices: formData,
    },
  }

  isLoading.value = true
  updateGeneralCustomeSettings(payload, 'invoices')
    .then(() => {
      fetchInvoiceDetails()
    })
    .finally(() => {
      isLoading.value = false
    })
}
const invoiceTemplates = ref({})
const selectedTemplate = ref('')
onMounted(async () => {
  processing.value = true
  try {
    await Promise.all([
      fetchInvoiceDetails(),
      getInvoiceTemplates().then((res) => {
        invoiceTemplates.value = res.invoice_templates
        selectedTemplate.value = res.selected_template
      }),
    ])
  }
  finally {
    processing.value = false
  }
})

const getImgUrl = (templateName: string) => {
  return new URL(`../../assets/templates/${templateName}.png`, import.meta.url)
    .href
}

const setInvoiceTemplate = (template: string) => {
  processing.value = true
  setDefaultInvoiceTemplate(template)
    .then(() => {
      selectedTemplate.value = template
    })
    .finally(() => {
      processing.value = false
    })
}
</script>

<template>
  <div class="py-4 mx-auto">
    <OverlayLoader v-if="processing" :full-screen="false" />

    <div class="flex flex-col relative">
      <!--  -->
      <h2 class="text-xl font-bold mb-2">
        {{ $t("invoice_templates") }}
      </h2>
      <div class="flex flex-col gap-5 sm:flex-row items-center text-black">
        <div
          v-for="(description, template) in invoiceTemplates"
          :key="template"
          class="flex flex-col"
        >
          <label
            :for="template"
            class="flex flex-col items-center relative group cursor-pointer h-64 rounded-lg shadow-md bg-white group"
          >
            <img
              :src="getImgUrl(template)"
              class="object-contain rounded-lg p-2 h-64"
            >
            <input
              :id="template"
              type="radio"
              :value="template"
              class="mt-2 appearance-none hidden"
            >
            <!-- over lay -->
            <span
              class="absolute top-0 left-0 w-full h-full bg-black bg-opacity-0 opacity-0 group-hover:opacity-100 group-hover:bg-opacity-50 rounded-lg transition-all duration-300 ease-in-out text-white"
            >
              <span
                class="text-center text-sm flex flex-col items-center justify-center h-full"
              >
                {{ description }}
                <button
                  v-if="selectedTemplate != template"
                  class="mt-2 text-white bg-primary py-1 rounded-md"
                  @click="setInvoiceTemplate(template)"
                >
                  <span class="text-xs px-2">
                    {{ $t("set_as_default") }}
                  </span>
                </button>
              </span>
            </span>
            <CheckBadgeIcon
              v-if="selectedTemplate === template"
              class="absolute top-2 right-2 w-6 h-6 text-yellow-500"
            />
          </label>
          <span class="text-center text-sm">{{
            $t(template)
          }}</span>
        </div>
      </div>
    </div>
    <!-- divder -->
    <div class="border-b my-8" />
    <h1 class="text-xl  font-semibold text-gray-900">
      {{ $t("teams.additional") }}
    </h1>
    <CustomForm
      :inputs="inputs"
      :is-loading="isLoading"
      error-object-name="setting.invoices"
      @submit="submit"
    />
  </div>
</template>
