<script setup lang="ts">
import {
  AdjustmentsHorizontalIcon,
  BanknotesIcon,
  BellAlertIcon,
  BuildingLibraryIcon,
  Cog8ToothIcon,
  CreditCardIcon,
  CubeTransparentIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  FolderArrowDownIcon,
  HashtagIcon,
  KeyIcon,
  PresentationChartLineIcon,
  RectangleStackIcon,
  ShieldCheckIcon,
  TrashIcon,
  UsersIcon,
  WrenchScrewdriverIcon,
} from '@heroicons/vue/20/solid'

const items = [
  {
    name: 'settings-general',
    icon: Cog8ToothIcon,
    to: '/settings/general',
  },
  {
    name: 'settings-advanced',
    icon: CubeTransparentIcon,
    to: '/settings/advanced',
    highlight: true,
  },
  {
    name: 'settings_plugins',
    icon: KeyIcon,
    to: '/settings/plugins',
  },
  {
    name: 'branches',
    icon: BuildingLibraryIcon,
    to: '/settings/branches',
  },
  {
    name: 'users',
    icon: UsersIcon,
    to: '/settings/users',
  },
  {
    name: 'settings_roles',
    icon: AdjustmentsHorizontalIcon,
    to: '/settings/roles',
  },
  {
    name: 'taxes',
    icon: BanknotesIcon,
    to: '/settings/taxes',
  },
  {
    name: 'invoicing',
    icon: DocumentTextIcon,
    to: '/settings/invoicing',
  },
  {
    name: 'syncedInvoices',
    icon: DocumentTextIcon,
    to: '/settings/syncedInvoices',
  },
  {
    name: 'notification',
    icon: BellAlertIcon,
    to: '/settings/notifications',
  },
  {
    name: 'payments',
    icon: CreditCardIcon,
    to: '/settings/payments',
  },
  // {
  //   name: "subscription",
  //   icon: PresentationChartLineIcon,
  //   to: "/settings/subscription",
  // },
  {
    name: 'tag',
    icon: HashtagIcon,
    to: '/settings/tags',
  },
  {
    name: 'metadata',
    icon: CubeTransparentIcon,
    to: '/settings/metadata',
  },
  {
    name: 'reasons',
    icon: RectangleStackIcon,
    to: '/settings/reasons',
  },
  {
    name: 'apps',
    icon: FolderArrowDownIcon,
    to: '/settings/apps',
  },
  {
    name: 'pos-terminals',
    icon: CreditCardIcon,
    to: '/settings/pos-terminals',
  },
  {
    name: 'subscriptions',
    icon: ShieldCheckIcon,
    to: '/settings/subscriptions-panel',
  },
  // {
  //   name : "trash",
  //   icon : TrashIcon,
  //   to : "/settings/trash"
  // }
]
</script>

<template>
  <div>
    <div class="my-8">
      <div
        class="grid gap-4 xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-2 sm:grid-cols-1"
      >
        <router-link
          v-for="(item, index) in items"
          :key="index"
          class="bg-white border border-gray-200 shadow-md hover:shadow-lg px-8 py-6 rounded-xl text-black min-h-[120px] flex items-center justify-center flex-col gap-3 cursor-pointer hover:bg-gray-50 hover:-translate-y-1 active:translate-y-0 active:scale-98 transition-all duration-200 ease-in-out"
          :to="{ path: item.to }"
          :class="{
            'bg-primary-50 shadow-primary-300 shadow-lg border-primary-200':
              item.highlight,

          }"
        >
          <component
            :is="item.icon"
            class="mb-3 w-12 h-12 transition-colors duration-200 text-primary-900"
            aria-hidden="true"
            :class="{
              '!text-primary-600': item.highlight,
            }"
          />
          <p
            class="text-lg font-semibold transition-colors duration-200 text-primary-900"
            :class="{
              '!text-primary-600 !font-bold': item.highlight,
            }"
          >
            {{ $t(item.name) }}
          </p>
        </router-link>
      </div>
    </div>
  </div>
</template>
