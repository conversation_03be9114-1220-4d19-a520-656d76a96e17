<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { PencilSquareIcon, PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'
import useVuelidate from '@vuelidate/core'
import { useAuthStore } from '@/stores/auth'
import { useLocalesStore } from '@/stores/locales'
import PosTerminalServices from '@/services/PosTerminalServices'
import useNotifications from '@/composables/useNotifications'
import { required } from '@/utils/i18n-validators'
import type { PaginationLinks, PaginationMeta } from '@/types'

const terminals = ref([])
const loading = ref(false)
const showAddModal = ref(false)
const processing = ref(false)
const editMode = ref(false)
const selectedTerminal = ref(null)
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { userInfo } = storeToRefs(useAuthStore())
const { showNotification } = useNotifications()
const currentFilters = ref({})
const showConfModal = ref(false)
const selectedUuid = ref('')

const paginationMeta = ref<PaginationMeta>({
  current_page: 1,
  from: 1,
  last_page: 1,
  links: [],
  path: '',
  per_page: 15,
  to: 15,
  total: 0,
})
const { t } = useI18n()
const paginationLinks = ref<PaginationLinks>({
  first: '',
  last: '',
  prev: null,
  next: null,
})

const tableHeaders = ref([
  { title: t('Name') },
  { title: t('Device') },
  { title: t('Status') },
  { title: t('Team') },
  { title: t('settings.actions') },
])

const formData = reactive({
  name: '',
  device: '',
  status: '',
  team_id: '',
  code: '',
  id: '',
})

// Validation rules
const rules = {
  name: { required },
  status: { required },
  team_id: { required },
}

const v$ = useVuelidate(rules, formData)

const fetchTerminals = async (page = 1, filters = {}) => {
  try {
    loading.value = true
    const response = await PosTerminalServices.fetchPosTerminals(page, filters)
    terminals.value = response.data.data
    paginationMeta.value = response.data.meta
    paginationLinks.value = response.data.links
  }
  catch (error) {
    console.error('Error fetching terminals:', error)
  }
  finally {
    loading.value = false
  }
}

const changePage = (page: number) => {
  fetchTerminals(page, currentFilters.value)
}

const openCreateModal = () => {
  editMode.value = false
  resetForm()
  v$.value.$reset()
  showAddModal.value = true
}

const openEditModal = (terminal) => {
  editMode.value = true
  console.log(terminal)
  formData.name = terminal.name
  formData.status = terminal.status
  formData.team_id = terminal.team_id
  formData.code = terminal.code
  formData.id = terminal.id
  v$.value.$reset()
  showAddModal.value = true
}

const resetForm = () => {
  formData.name = ''
  formData.status = ''
  formData.team_id = ''
  formData.code = ''
  formData.id = ''
}

const saveTerminal = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return

  try {
    processing.value = true
    if (editMode.value) {
      await PosTerminalServices.updatePosTerminal(formData.id, formData)
      showNotification({
        title: t('success'),
        type: 'success',
        message: t('operations.updated_success'),
      })
    }
    else {
      await PosTerminalServices.createPosTerminal(formData)
      showNotification({
        title: t('success'),
        type: 'success',
        message: t('operations.created_success'),
      })
    }
    closeAddModal()
    fetchTerminals(paginationMeta.value.current_page, currentFilters.value)
  }
  catch (error: any) {
    showNotification({
      title: 'Error',
      type: 'error',
      message: error.response?.data?.message || 'Error processing terminal',
    })
  }
  finally {
    processing.value = false
  }
}

const showConfirmationModal = (uuid: string) => {
  selectedUuid.value = uuid
  showConfModal.value = true
}

const deletePosTerminal = async (uuid: string) => {
  return PosTerminalServices.deletePosTerminal(uuid)
}

const terminalRemoved = () => {
  showConfModal.value = false
  showNotification({
    title: t('success'),
    type: 'success',
    message: t('operations.deleted_success'),
  })
  fetchTerminals(paginationMeta.value.current_page, currentFilters.value)
}

const closeAddModal = () => {
  showAddModal.value = false
  resetForm()
  v$.value.$reset()
  editMode.value = false
}

onMounted(() => {
  fetchTerminals()
})
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("pos-terminals") }}
      </h1>
      <BaseButton
        class="inline-flex w-auto hover:bg-green-700"
        custome-bg="bg-green-600"
        @click="openCreateModal"
      >
        {{ $t("add_terminal") }}
        <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </div>
    <div class="">
      <generic-table
        :headers="tableHeaders"
        :data="terminals"
        :is-loading="loading"
        item-key="code"
      >
        <template #row="{ item }">
          <grid-td>{{ item.name }}</grid-td>
          <grid-td>
            <a :href="`https://pos.mahjoz.io/?cashier_code=${item.code}`" target="_blank" class="underline">
              <i class="fas fa-link" /> {{ item.code }}
            </a>
          </grid-td>
          <grid-td>{{ item.status }}</grid-td>
          <grid-td>{{ item.team }}</grid-td>
          <grid-td class="flex gap-2 items-center">
            <BaseButton
              class="inline-flex hover:bg-gray-600 w-fit"
              custome-bg="bg-gray-700"
              @click="openEditModal(item)"
            >
              <PencilSquareIcon class="w-4 h-4" aria-hidden="true" />
            </BaseButton>
            <!-- <BaseButton
                  class="inline-flex w-auto hover:bg-red-700"
                  custome-bg="bg-red-600"
                  @click="showConfirmationModal(item.code)"
                >
                  <TrashIcon class="w-4 h-4" aria-hidden="true" />
                </BaseButton> -->
          </grid-td>
        </template>
      </generic-table>
      <Pagination
        v-if="terminals.length"
        :pagination-meta="paginationMeta"
        :pagination-links="paginationLinks"
        class="px-4 mt-4"
        @change="changePage"
      />
    </div>

    <!-- Confirmation Modal for Delete -->
    <confirmation-modal
      v-if="showConfModal"
      :dir="getLocale(locale)?.direction"
      :is-open="showConfModal"
      :api-call="deletePosTerminal"
      :record-id="selectedUuid"
      @removed="terminalRemoved"
      @closed="showConfModal = false"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.msg") }}
      </p>
    </confirmation-modal>

    <!-- Add/Edit Terminal Modal -->
    <Modal
      v-if="showAddModal"
      :dir="getLocale(locale)?.direction"
      :open="showAddModal"
      :title="editMode ? 'edit_terminal' : 'add_terminal'"
      panel-classes="w-full mx-20 lg:mx-0 !mx-4"
      @close="closeAddModal"
    >
      <overlay-loader v-if="processing" :full-screen="false" />
      <form class="grid grid-cols-2 gap-5 mb-4 w-full text-start" @submit.prevent="saveTerminal">
        <div class="grid col-span-2 gap-4">
          <form-group :validation="v$" name="name">
            <template #default="{ attrs }">
              <TextInput
                v-bind="attrs"
                v-model="formData.name"
                :label="$t('form.name')"
                required
              />
            </template>
          </form-group>
        </div>
        <div class="col-span-2">
          <form-group :validation="v$" name="team_id">
            <template #default="{ attrs }">
              <SelectInput
                v-bind="attrs"
                v-model="formData.team_id"
                :label="$t('Team')"
                required
              >
                <option
                  v-for="team in userInfo.teams"
                  :key="team.uuid"
                  :value="team.uuid"
                >
                  {{ team.name }}
                </option>
              </SelectInput>
            </template>
          </form-group>
        </div>

        <div class="grid col-span-2 gap-4">
          <form-group :validation="v$" name="status">
            <template #default="{ attrs }">
              <SelectInput
                v-bind="attrs"
                v-model="formData.status"
                :label="$t('form.status')"
                required
              >
                <option value="active">
                  {{ $t("active") }}
                </option>
                <option value="inactive">
                  {{ $t("inactive") }}
                </option>
              </SelectInput>
            </template>
          </form-group>
        </div>
        <div class="flex col-span-2 gap-3 justify-end mt-6">
          <BaseButton
            type="button"
            class="py-3 hover:bg-gray-900"
            custome-bg="bg-gray-600"
            @click="closeAddModal"
          >
            {{ $t("cancel") }}
          </BaseButton>
          <BaseButton
            type="submit"
            class="py-3 hover:bg-primary-700 bg-primary-600 hover:bg-green-700"
          >
            {{ editMode ? $t("form.update") : $t("form.save") }}
          </BaseButton>
        </div>
      </form>
    </Modal>
  </div>
</template>
