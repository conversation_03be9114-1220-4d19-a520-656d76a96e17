<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { ChevronDownIcon, HomeIcon } from '@heroicons/vue/20/solid'
import type { HoursMinutes, Service, Staff } from '@/types'
import { convertToHoursAndMinutes } from '@/utils/time'
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const {
  removeService,
  fetchServiceById,
  removeImageFromService,
  addImageToService,
  makeMainImage,
} = useServicesStore()
const { userInfo } = useAuthStore()
const { fetchAllStaff } = useStaffStore()

const processing = ref(false)
const showConfModal = ref(false)
const route = useRoute()
const router = useRouter()
const crumbs = ref([
  {
    name: 'homepage.services',
    path: '/management/services',
  },
  {
    name: '',
    path: '',
  },
])
const showModal = ref(false)
const default_duration: HoursMinutes = {
  hours: 0,
  minutes: 15,
}
const service = ref<Service>({
  uuid: '',
  name: '',
  slug: '',
  imageLink: '',
  image: '',
  description: '',
  color: '',
  duration: { ...default_duration },
  price: 0,
  category_id: '',
  display_on_booking_page: false,
  location: '',
  team_id: '',
})

const teamStaff = ref<Staff[]>([])
const processingImageUpload = ref(false)
const fetchAllBranchStaff = async () => {
  const { data } = await fetchAllStaff()
  teamStaff.value = data
}
const serviceUuid = ref('')
const serviceSlug = ref('')
const fetchSingleService = async (id: string) => {
  fetchServiceById(`${id}?withStaff=true` as string)
    .then((res) => {
      service.value = res.service
      serviceUuid.value = res.service.uuid
      serviceSlug.value = res.service.slug
      service.value.images = res?.service?.images
      crumbs.value[crumbs.value.length - 1].name = `${res.service.name}`
    })
    .catch((e) => {
      router.push({ path: '/management/customers' })
    })
}
onBeforeMount(async () => {
  processing.value = true
  try {
    await Promise.all([
      fetchAllBranchStaff(),
      fetchSingleService(route.params.id as string),
    ])
  }
  finally {
    processing.value = false
  }
})
const toggleModel = () => {
  showModal.value = false
}
const updated = (resUpdated: Service) => {
  service.value = resUpdated
}
const deleted = () => {
  showConfModal.value = true
}

const duplicate = () => {
  service.value.uuid = ''
  service.value.slug = ''
  showModal.value = true
}
const editRecored = () => {
  service.value.uuid = serviceUuid.value
  service.value.slug = serviceSlug.value
  showModal.value = true
}
const duplicated = computed(() => {
  return Boolean(!service.value.uuid)
})

function removeImage(imageIndex, image, confirmCallBack) {
  processingImageUpload.value = true
  removeImageFromService(service.value.uuid, image.id)
    .then((res) => {
      confirmCallBack()
    })
    .finally(() => {
      processingImageUpload.value = false
    })
}
function uploadImage(imageFormData: FormData, _, _1, imageUploaded) {
  processingImageUpload.value = true
  addImageToService(service.value.uuid, imageFormData)
    .then((res) => {
      imageUploaded(res.data.id)
    })
    .finally(() => {
      processingImageUpload.value = false
    })
}
function makeImageAsMain(imageIndex: number, image: [key: string, value: any]) {
  processingImageUpload.value = true
  makeMainImage(service.value.uuid, image.id)
    .then((_) => {
      fetchSingleService(route.params.id as string)
    })
    .finally(() => {
      processingImageUpload.value = false
    })
}
</script>

<template>
  <div>
    <add-services-modal
      v-if="showModal"
      :service="service"
      :team-staff="teamStaff"
      :title="duplicated ? 'duplicateService' : 'editService'"
      :show-modal="showModal"
      :show-slug="!duplicated"
      @updated="updated"
      @closed="toggleModel"
    />

    <!-- @removed="deleteRecord" -->
    <confirmation-modal
      v-if="showConfModal"
      :dir="getLocale(locale)?.direction"
      :is-open="showConfModal"
      redirect-url="/management/services"
      :api-call="removeService"
      :record-id="serviceUuid"
      @closed="showConfModal = false"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.msg") }}
      </p>
    </confirmation-modal>

    <div class="flex relative flex-col">
      <overlay-loader v-if="processing" :full-screen="false" />
      <bread-crumb :crumbs="crumbs" class="mt-5" />
      <div
        class="flex flex-col gap-4 justify-between items-center mt-6 mb-4 sm:flex-row sm:gap-0"
      >
        <div class="flex gap-1 items-center sm:gap-3">
          <h1 class="text-base font-semibold sm:text-3xl">
            {{ service?.name }}
          </h1>
        </div>
        <div class="inline-flex w-full rounded-md shadow-sm sm:w-auto">
          <button
            type="button"
            class="inline-flex relative items-center px-3 py-2 w-3/4 text-sm font-semibold text-gray-900 bg-white rounded-l-md ring-1 ring-inset ring-gray-300 sm:w-auto hover:bg-gray-50 focus:z-10"
            :class="[
              getLocale(locale)?.direction === 'rtl' ? 'order-2' : 'order-1',
            ]"
            @click="editRecored()"
          >
            {{ $t("form.edit") }}
          </button>
          <Menu
            as="div"
            class="block relative -ml-px w-1/4 sm:w-auto"
            :class="
              getLocale(locale)?.direction === 'rtl' ? 'order-1' : 'order-2'
            "
          >
            <MenuButton
              class="inline-flex relative justify-center items-center px-2 py-2 w-full text-gray-400 bg-white rounded-r-md ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10"
            >
              <span class="sr-only">Open options</span>
              <ChevronDownIcon class="w-5 h-5" aria-hidden="true" />
            </MenuButton>
            <transition
              enter-active-class="transition duration-100 ease-out"
              enter-from-class="opacity-0 transform scale-95"
              enter-to-class="opacity-100 transform scale-100"
              leave-active-class="transition duration-75 ease-in"
              leave-from-class="opacity-100 transform scale-100"
              leave-to-class="opacity-0 transform scale-95"
            >
              <MenuItems
                class="absolute z-10 mt-2 -mr-1 w-48 bg-white rounded-md ring-1 ring-black ring-opacity-5 shadow-lg origin-top-right focus:outline-none"
                :class="[
                  getLocale(locale)?.direction === 'rtl'
                    ? '-right-20'
                    : '-left-40',
                ]"
              >
                <div class="py-1">
                  <MenuItem>
                    <button
                      type="button"
                      class="block p-2 w-full text-sm text-gray-600 text-start"
                      @click="duplicate()"
                    >
                      {{ $t("form.duplicate") }}
                    </button>
                  </MenuItem>
                  <MenuItem>
                    <button
                      type="button"
                      class="block p-2 w-full text-sm text-red-600 text-start"
                      @click="deleted()"
                    >
                      {{ $t("form.deleteService") }}
                    </button>
                  </MenuItem>
                </div>
              </MenuItems>
            </transition>
          </Menu>
        </div>
      </div>
      <TagsComp :tags="service?.tags" />

      <div class="pb-8">
        <img
          v-if="service?.imageLink"
          id="image"
          class="block mx-auto mb-3 w-36 h-36 rounded-full"
          :src="service.imageLink"
          :link="null"
        >

        <div class="grid grid-cols-1 sm:grid-cols-2">
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="customer"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("booking.service") }}
            </label>
            <div class="flex mt-1 font-semibold">
              <div
                class="flex relative flex-grow items-stretch focus-within:z-10"
              >
                {{ service?.name }}
              </div>
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="staff"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("form.category") }}
            </label>
            <div class="flex gap-1 items-center mt-1 font-semibold">
              <span v-if="service?.category_id" class="">
                {{ service?.category_id?.name }}
              </span>

              <span v-else class="text-neutral-400">-</span>
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("price") }}
            </label>
            <div class="mt-1 font-semibold">
              {{ service?.price }}
              <span class="text-gray-500 sm:text-sm ms-2">{{
                $t(`currenices.${userInfo.tenant?.currency}`)
              }}</span>
            </div>
          </div>
          <div
            v-if="service.enable_deposit"
            class="flex gap-2 items-center py-3 border-b border-stone-100"
          >
            <label
              for="staff"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("deposit") }}
            </label>
            <div class="mt-1 font-semibold">
              {{ service?.deposit }}
              <span class="text-gray-500 sm:text-sm ms-2">{{
                $t(`currenices.${userInfo.tenant?.currency}`)
              }}</span>
            </div>
          </div>

          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("duration") }}
            </label>
            <div class="mt-1 font-semibold">
              {{ convertToHoursAndMinutes(service?.duration || 0, true) }}
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("displayOnBookingPage") }}
            </label>
            <div class="mt-1 font-semibold">
              {{ service?.display_on_booking_page ? $t("yes") : $t("no") }}
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("form.sort_order") }}
            </label>
            <div class="mt-1 font-semibold">
              {{ service?.sort_order }}
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("modalPlacholder.branch") }} :</label>
            <div v-if="service?.team?.uuid" class="mt-1 font-semibold">
              {{ service?.team?.name }}
            </div>
            <span v-else>-</span>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("color") }}
            </label>
            <div
              class="w-6 h-4 font-semibold"
              :style="{ backgroundColor: service?.color }"
            />
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("description") }}
            </label>
            <div
              v-if="service?.description?.length > 0"
              class="mt-1 font-semibold"
            >
              <p>{{ service?.description }}</p>
            </div>
            <span v-else class="text-neutral-400">-</span>
          </div>
        </div>
      </div>
      <div class="my-4 w-full">
        <ShareBox :link="service.full_link" />
      </div>

      <form-group error-name="image">
        <template #default="{ attrs }">
          <div class="my-4 w-full" v-bind="attrs">
            <h2 class="mb-4 text-2xl font-semibold">
              {{ $t(`images`) }}
            </h2>
            <images-uploader
              :data-images="service?.images"
              class="flex w-full"
              small-images-container-classes="mt-0 !max-w-full gap-2"
              small-images-classes="h-full !w-[190px] !h-[180px]"
              :show-controls-for-single-image="true"
              add-button-classes="!w-[190px] !h-[180px] relative rounded-md border-2 border-gray-300 flex relative text-center border border-gray-300 rounded-md items-center justify-center"
              :show-edit="false"
              :loading="processingImageUpload"
              :drag-text="$t('drag_service_image')"
              browse-text=""
              :drop-text="$t('drop_service_image')"
              id-upload="service"
              :show-custom-icons="true"
              @before-remove="removeImage"
              @before-upload="uploadImage"
              @custom-action="makeImageAsMain"
            >
              <template #custom-button="{ image }">
                <button
                  class="flex justify-center items-center p-2 w-full h-full text-sm font-medium text-blue-500 rounded-md"
                  :title="
                    image?.is_main_image
                      ? $t('main_image')
                      : $t('make_main_image')
                  "
                  :class="{
                    'opacity-50	event-none': Boolean(image?.is_main_image),
                  }"
                >
                  <HomeIcon class="w-10 h-10" aria-hidden="true" />
                </button>
              </template>
            </images-uploader>
          </div>
        </template>
      </form-group>
      <ServicesTabs v-if="service?.uuid" :service="service" />
    </div>
  </div>
</template>
