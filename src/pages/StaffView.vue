<script setup lang="ts">
import { ArrowDownIcon, PlusIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import useStaff from '@/composables/useStaff'
import excelExport from '@/composables/useExcelExport'
import { useTagStore } from '@/stores/tags'
import { useAppsStore } from '@/stores/apps'
import type { PaginationLinks, PaginationMeta, Service, Staff } from '@/types'
const { loader } = storeToRefs(useAppsStore())
const i18n = useI18n()
const tagStore = useTagStore()
const { tags } = storeToRefs(tagStore)
const { fetchTags } = useTagStore()
const { processingExport, getExcel } = excelExport('Staff')
const { fetchStaff } = useStaffStore()


const tableData = reactive({
  staffList: [] as Staff[],
  paginationMeta: {
    current_page: 1,
    from: 1,
    last_page: 1,
    links: [],
    path: '',
    per_page: 15,
    to: 15,
    total: 1,
  } as PaginationMeta,
  paginationLinks: {
    first: '',
    last: '',
    prev: null,
    next: null,
  } as PaginationLinks,
  processing: false,
  selectedStaff: null as Staff | null,
  selectedIndex: null as number | null,
  filters: {} as { [key: string]: string },
})

const getStaffs = async (currentPage: number, filters: { [key: string]: string }) => {
  tableData.processing = true
  await fetchStaff(currentPage, filters)
    .then((res) => {
      tableData.staffList = res.data
      tableData.paginationLinks = res.links
      tableData.paginationMeta = res.meta
    })
    .finally(() => {
      tableData.processing = false
    })
}

onMounted(() => {
  Promise.all([
    getStaffs(tableData.paginationMeta.current_page, tableData.filters),
    fetchTags("staff"),
  ]);
});
const updated = () => {
  
  Promise.all([
    getStaffs(tableData.paginationMeta.current_page, tableData.filters),
    fetchTags("staff"),
  ]);
};
const changeData = (page: number) => {
  getStaffs(page, tableData.filters)
}

const filterStaffs = async (filters: {}) => {
  const { customer, phone, tags, team } = filters
  const tagsIds: string = tags.map(tag => tag.id).join('')
  tableData.filters = {
    name: customer || '',
    phone: phone || '',
    tags: tagsIds || '',
    team: team || '',
  }
  await getStaffs(tableData.paginationMeta.current_page, tableData.filters)
}

const created = (staff: Staff) => {
  // update local data
  tableData.staffList.unshift(staff)
  tableData.paginationMeta.total += 1
  tableData.paginationMeta.to += 1
}

const showModal = ref(false);
const toggleShowModal = () => {
  showModal.value = !showModal.value;
  showStaff.value = null;
};
const addModal = ref(false);
const toggleAddModal = () => {
  addModal.value = !addModal.value;
};
const showStaff = ref<Staff | null>(null);
const showRecord = (staff: Staff) => {
  showStaff.value = staff;
  showModal.value = true;
};
const editModal = ref(false);
const editStaff = ref<Staff | null>(null);
const editRecord = (staff: Staff) => {
  editStaff.value = staff;
  editModal.value = true;
};
const toggleModelEdit = () => {
  editModal.value = !editModal.value;
  editStaff.value = null;
};
const getExcelFile = () => {
  const { showNotification } = useNotifications()

  getExcel().then(() => {
    showNotification({
      title: i18n.t('Success'),
      type: 'success',
      message: i18n.t('operations.emailSent'),
    })
  })
}
</script>

<template>
  <div>
    <AddStaffModal
      :show-modal="addModal"
      :tags="tags.data"
      @created="created"
      @closed="toggleAddModal"
      v-if="addModal"
    />

    <ShowStaffModal
      :show-modal="showModal"
      :staff="showStaff"
      @created="updated"  
      @closed="toggleShowModal"
      v-if="showModal"
    />

    <EditStaffModal 
      :show-modal="editModal"
      :staff="editStaff"
      @created="updated"
      @closed="toggleModelEdit"
      v-if="editModal"
      />
    <div class="flex justify-between items-center ms-auto me-auto max-w-12xl">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t(`homepage.${$route?.name}` || "homepage.title") }}
      </h1>
      <div class="flex gap-2 mt-4 sm:mt-0 sm:flex-none">
        <router-link
          to="/management/service-metrics"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md border border-transparent shadow-sm bg-primary hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        >
          {{ $t("service_metrics.assign_services") }}
        </router-link>
        <BaseButton
          type="button"
          :disabled="processingExport"
          class="inline-flex w-4 bg-green-600 disabled:bg-green-300 me-2"
          @click="getExcelFile()"
        >
          {{ $t("table.export_excel") }}
          <ArrowDownIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
        <BaseButton
          class="inline-flex w-auto hover:bg-green-700"
          custome-bg="bg-green-600"
          @click="toggleAddModal()"
        >
          {{ $t("form.create") }}
          <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </div>
    <customer-filteration :tags="tags.data" @changed="filterStaffs" />
    <div class="flex flex-col mt-8">
      <div class="">
  
            <staff-grid
              :staff-list="tableData.staffList"
              :isLoading="tableData.processing"
              @click="showRecord"
            >
              <!-- <template #actions="prop">
                <BaseButton
                  class="inline-flex" custome-bg="bg-gray-700" show-icon
                  @click="() => editRecord(prop.customer)"
                >
                  {{ $t('form.showStaff') }}
                </BaseButton>
              </template> -->
        </staff-grid>
        <Pagination
          v-if="tableData.staffList.length"
          :pagination-meta="tableData.paginationMeta"
          :pagination-links="tableData.paginationLinks"
          class="px-4"
          @change="changeData"
        />
      </div>
    </div>
  </div>
</template>

<style>
.cust-box {
  min-height: 70px;
}
</style>
