<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { PlusIcon } from '@heroicons/vue/20/solid'
import type { ComputedRef } from 'vue'
import { reactive, ref } from 'vue'
import useVuelidate from '@vuelidate/core'
import { PencilIcon, TrashIcon } from '@heroicons/vue/24/outline'
import { required } from '@/utils/i18n-validators'
import { useExpenses } from '@/stores/expenses'
import type { Expense, ExpenseType, PaginationLinks, PaginationMeta, header } from '@/types'
import { usePaymentMethodsStore } from '@/stores/paymentMethods'
import { useTeamStore } from '@/stores/teams'

const {
  fetchExpenses,
  fetchExpenseTypes,
  createExpense,
  updateExpense,
  deleteExpense,
} = useExpenses()

const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const route = useRoute()
const router = useRouter()
const processing = ref(false)
const showModal = ref(false)
const showConfModal = ref(false)
const selectedExpense = ref<Expense | null>(null)
const expenseToDelete = ref<Expense | null>(null)
const expenseTypes = ref<ExpenseType[]>([])
const expenses = ref<Expense[]>([])
const { getPaymentMethods } = usePaymentMethodsStore()
const teamStore = useTeamStore()
const paymentMethods = ref([])
const teams = ref([])

// Add pagination data
const pagination = reactive({
  meta: {
    current_page: 1,
    from: 1,
    last_page: 1,
    path: '',
    per_page: 10,
    to: 10,
    total: 0,
  } as PaginationMeta,
  links: {
    first: '',
    last: '',
    prev: null,
    next: null,
  } as PaginationLinks,
})

const crumbs = ref([
  {
    name: 'homepage.expenses',
    path: '/management/expenses',
  },
  {
    name: '',
    path: '',
  },
])

const formData = reactive({
  title: '',
  note: '',
  file: '',
  type_expenses_id: '',
  amount: 0,
  reference_no: '',
  taxable: false,
  payment_method_id: '',
  team_id: '',
})

const v$ = useVuelidate(
  {
    title: { required },
    type_expenses_id: { required },
    team_id: { required },
  },
  formData,
)
const editMode = ref(false)
const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('form.name'),
    },
    {
      title: t('form.note'),
    },
    {
      title: t('fields.type'),
    },
    {
      title: t('fields.amount'),
    },
    {
      title: t('fields.reference_no'),
    },
    {
      title: t('Team'),
    },
    {
      title: t('fields.payment_method'),
    },
    {
      title: '',
    },
  ]
})
const showTypeExpenseModal = ref(false)
const handleCreatedType = async (createdExpenseType) => {
  try {
    processing.value = true
    const newTitle = createdExpenseType.title
    showTypeExpenseModal.value = false
    expenseTypes.value = (await fetchExpenseTypes())
    formData.type_expenses_id = expenseTypes.value.find(type => type.title === newTitle)?.uuid || ''
  }
  finally {
    processing.value = false
  }
}
const loadExpenses = async (page = 1) => {
  processing.value = true
  try {
    const response = await fetchExpenses(page)
    expenses.value = response.data

    // Update pagination data if available in response
    if (response.meta && response.links) {
      pagination.meta = response.meta
      pagination.links = response.links
    }

    expenseTypes.value = (await fetchExpenseTypes())
    formData.type_expenses_id = expenseTypes.value?.[0]?.uuid || ''
    processing.value = false
  }
  finally {
    processing.value = false
  }
}

// Add pagination handler
const handlePageChange = (page: number) => {
  loadExpenses(page)
}

const handleCreate = () => {
  editMode.value = false
  selectedExpense.value = null
  showModal.value = true
}

const handleEdit = (expense: Expense) => {
  editMode.value = true
  selectedExpense.value = expense
  formData.title = expense.title
  formData.note = expense.note || ''
  formData.type_expenses_id = expense.type?.id || ''
  formData.team_id = expense.team?.id || ''
  formData.payment_method_id = expense.payment_method?.id || ''
  formData.amount = expense.amount
  formData.reference_no = expense.reference_no
  formData.taxable = expense.taxable
  formData.linkFile = expense.file
  // Reset team_id to empty - we'll need to select it again

  showModal.value = true
}

const handleDelete = (expense: Expense) => {
  expenseToDelete.value = expense
  showConfModal.value = true
}

const handleSubmit = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return false

  processing.value = true
  try {
    const payload = {
      title: formData.title,
      note: formData.note,
      type_expenses_id: formData.type_expenses_id,
      amount: formData.amount,
      reference_no: formData.reference_no,
      taxable: Boolean(formData.taxable),
      payment_method_id: formData.payment_method_id,
      team_id: formData.team_id,
      file: formData.file,
    }
    formData.taxable = !!formData.taxable
    if (selectedExpense.value) {
      payload._method = 'PUT'
      await updateExpense(selectedExpense.value.uuid, payload)
    }
    else {
      await createExpense(payload)
    }
    await loadExpenses()
    resetForm()
    showModal.value = false
    editMode.value = false
  }
  finally {
    processing.value = false
  }
}

const resetForm = () => {
  formData.title = ''
  formData.note = ''
  formData.file = ''
  formData.type_expenses_id = ''
  formData.amount = 0
  formData.reference_no = ''
  formData.taxable = false
  formData.payment_method_id = ''
  formData.team_id = ''
  selectedExpense.value = null
  formData.linkFile = ''
  v$.value.$reset()
}
const closeModal = () => {
  resetForm()
  showModal.value = false
  editMode.value = false
}
onMounted(async () => {
  await getPaymentMethods().then((response) => {
    paymentMethods.value = response.data
    formData.payment_method_id = paymentMethods.value?.[0]?.id || ''
  })
  await teamStore.fetchTeams().then((response) => {
    teams.value = response.data
    formData.team_id = teams.value?.[0]?.uuid || ''
  })
  await loadExpenses()
})
</script>

<template>
  <div class="flex relative flex-col m-4">
    <confirmation-modal
      v-if="showConfModal"
      :is-open="showConfModal"
      :api-call="deleteExpense"
      :record-id="expenseToDelete?.uuid"
      @closed="showConfModal = false"
      @removed="loadExpenses"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.msg") }}
      </p>
    </confirmation-modal>
    <bread-crumb :crumbs="crumbs" class="mt-5" />
  </div>
  <div class="flex justify-between ms-auto me-auto max-w-12xl">
    <h1 class="text-2xl font-semibold text-gray-900">
      {{ $t(`homepage.${String($route?.name)}` || "homepage.title") }}
    </h1>
    <div class="flex gap-2 mt-4 sm:mt-0 sm:flex-none">
      <BaseButton
        class="inline-flex w-auto hover:bg-blue-700"
        custome-bg="bg-blue-600"
        @click="router.push({ name: 'expense-types' })"
      >
        {{ $t("Expense_Types") }}
      </BaseButton>
      <BaseButton
        class="inline-flex w-auto hover:bg-green-700"
        custome-bg="bg-green-600"
        @click="handleCreate"
      >
        {{ $t("form.create") }}
        <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </div>
  </div>

  <div class="flex flex-col mt-8">
    <generic-table
      :headers="headers"
      :data="expenses"
      :is-loading="processing"
      item-key="uuid"
    >
      <template #row="{ item }">
        <grid-td
          class="py-4 pr-3 pl-4 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6"
        >
          {{ item.title }}
        </grid-td>
        <grid-td
          class="py-4 pr-3 pl-4 text-sm text-gray-500 whitespace-nowrap"
        >
          {{ item.note }}
        </grid-td>
        <grid-td
          class="py-4 pr-3 pl-4 text-sm text-gray-500 whitespace-nowrap"
        >
          {{ item.type.name }}
        </grid-td>
        <grid-td
          class="py-4 pr-3 pl-4 text-sm text-gray-500 whitespace-nowrap"
        >
          {{ item.amount }}
        </grid-td>
        <grid-td
          class="py-4 pr-3 pl-4 text-sm text-gray-500 whitespace-nowrap"
        >
          {{ item.reference_no }}
        </grid-td>
        <grid-td
          class="py-4 pr-3 pl-4 text-sm text-gray-500 whitespace-nowrap"
        >
          {{ item.team?.name }}
        </grid-td>
        <grid-td
          class="py-4 pr-3 pl-4 text-sm text-gray-500 whitespace-nowrap"
        >
          {{ item.payment_method?.name }}
        </grid-td>
        <grid-td class="flex relative gap-2 py-4 pr-4 pl-3 text-sm font-medium text-right whitespace-nowrap">
          <button class="px-4 py-2 text-white text-gray-500 rounded-md transition-colors bg-primary-500 hover:bg-primary-600" @click="handleEdit(item)">
            {{ $t("form.edit") }}
          </button>
          <button class="px-4 py-2 text-white bg-red-500 rounded-md transition-colors hover:bg-red-600" @click="handleDelete(item)">
            {{ $t("form.delete") }}
          </button>
        </grid-td>
      </template>
    </generic-table>
  </div>
  <Pagination
    :pagination-meta="pagination.meta"
    :pagination-links="pagination.links"
    @change="handlePageChange"
  />

  <Modal
    v-if="showModal"
    :dir="getLocale(locale)?.direction"
    :open="showModal"
    :title="editMode ? 'editExpense' : 'createExpense'"
    panel-classes="w-full mx-20 lg:mx-0  !mx-4"
    @close="closeModal"
  >
    <form
      class="grid grid-cols-2 gap-5 mb-4 w-full text-start"
      @submit.prevent="handleSubmit"
    >
      <overlay-loader v-if="processing" :full-screen="false" />
      <div class="col-span-2 w-full sm:col-span-1">
        <labelInput for="expenseTitle" class="mb-1">
          {{ $t("form.title_expense") }} <span class="text-red-600">*</span>
        </labelInput>
        <form-group :validation="v$" name="title">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="expenseTitle"
              v-model="formData.title"
            />
          </template>
        </form-group>
      </div>

      <div class="col-span-2 w-full sm:col-span-1">
        <labelInput for="expenseAmount" class="mb-1">
          {{ $t("invoice.amount") }} <span class="text-red-600">*</span>
        </labelInput>
        <form-group :validation="v$" name="amount">
          <template #default="{ attrs }">
            <NumberInput
              v-bind="attrs"
              id="expenseAmount"
              v-model="formData.amount"
            />
          </template>
        </form-group>
      </div>

      <div class="col-span-2 w-full sm:col-span-1">
        <labelInput for="expenseReferenceNo" class="mb-1">
          {{ $t("fields.reference_no") }}
        </labelInput>
        <form-group :validation="v$" name="reference_no">
          <template #default="{ attrs }">
            <TextInput
              v-bind="attrs"
              id="expenseReferenceNo"
              v-model="formData.reference_no"
            />
          </template>
        </form-group>
      </div>

      <div class="grid col-span-2 gap-5 w-full sm:col-span-1">
        <form-group :validation="v$" name="taxable">
          <template #default="{ attrs }">
            <selectInput
              v-bind="attrs"
              id="paymentMethod"
              v-model="formData.payment_method_id"
              :label="$t('fields.payment_method')"
              required
            >
              <option
                v-for="paymentMethod in paymentMethods"
                :key="paymentMethod.id"
                :value="paymentMethod.id"
              >
                {{ paymentMethod.name }}
              </option>
            </selectInput>
          </template>
        </form-group>
      </div>
      <div class="flex col-span-2 w-full sm:col-span-1">
        <form-group :validation="v$" name="type_expenses_id">
          <template #default="{ attrs }">
            <SelectInput
              v-bind="attrs"
              id="type_expenses_id"
              v-model="formData.type_expenses_id"
              required
              :label="$t('form.category')"
            >
              <option hidden selected value="">
                {{ $t("form.select") }}
              </option>
              <option
                v-for="expenseType in expenseTypes"
                :key="expenseType.uuid"
                :value="expenseType.uuid"
              >
                {{ expenseType.title }}
              </option>
            </SelectInput>
          </template>
        </form-group>
        <button
          type="button"
          class="inline-flex justify-center self-end px-4 py-3 text-sm font-medium text-blue-900 bg-blue-100 rounded-md border border-transparent hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:bg-gray-100 disabled:cursor-not-allowed"
          @click="showTypeExpenseModal = true"
        >
          <PlusIcon class="w-5 h-5 text-blue-900" aria-hidden="true" />
          <span>{{ $t("New") }}</span>
        </button>
      </div>

      <div class="col-span-2 w-full sm:col-span-1">
        <form-group :validation="v$" name="team_id">
          <template #default="{ attrs }">
            <SelectInput
              v-bind="attrs"
              id="team_id"
              v-model="formData.team_id"
              :label="$t('Team')"
            >
              <option hidden selected value="">
                {{ $t("form.select") }}
              </option>
              <option
                v-for="team in teams"
                :key="team.uuid"
                :value="team.uuid"
              >
                {{ team.name }}
              </option>
            </SelectInput>
          </template>
        </form-group>
      </div>

      <div class="w-full">
        <labelInput for="expenseTaxable" class="mb-1">
          {{ $t("fields.taxable") }} <span class="text-red-600">*</span>
        </labelInput>
        <form-group :validation="v$" name="taxable">
          <template #default="{ attrs }">
            <CheckInput
              v-bind="attrs"
              id="expenseTaxable"
              v-model="formData.taxable"
            />
          </template>
        </form-group>
      </div>
      <div class="w-full">
        <LabelInput for="note">
          {{ $t("form.note") }}
        </LabelInput>
        <textarea
          id="note"
          v-model="formData.note"
          :rows="7"
          class="block p-2.5 w-full text-sm text-gray-900 rounded-lg border border-gray-300"
          :placeholder="$t('form.message')"
        />
      </div>

      <div class="w-full">
        <LabelInput for="file" class="mb-1">
          {{ $t("form.file") }}
        </LabelInput>
        <ImageInput
          id="image" v-model="formData.file" :link="formData.linkFile" :show-clear-btn="true" @image-cleared="formData.linkFile = ''"
          @update:link="formData.linkFile = $event"
        />
      </div>

      <div class="mt-6">
        <BaseButton
          type="submit"
          class="mx-auto"
          :class="[
            selectedExpense?.uuid ? 'bg-gray-800' : 'hover:bg-green-700',
          ]"
          :custome-bg="[selectedExpense?.uuid ? 'bg-gray-700' : 'bg-green-600']"
          show-icon
          :processing="processing"
        >
          <span>{{
            selectedExpense?.uuid ? $t("form.update") : $t("form.create")
          }}</span>
        </BaseButton>
      </div>
    </form>
    <add-expense-type-modal
      v-if="showTypeExpenseModal"
      :show-modal="showTypeExpenseModal"
      :expense-type="null"
      @created="handleCreatedType"
      @closed="showTypeExpenseModal = false"
    />
  </Modal>
</template>
