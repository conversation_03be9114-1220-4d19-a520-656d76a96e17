<script setup lang="ts">
import { PlusIcon } from '@heroicons/vue/24/outline'

import CheckCircleIcon from '@heroicons/vue/24/outline/CheckCircleIcon'
import XCircleIcon from '@heroicons/vue/24/outline/XCircleIcon'
import ExclamationTriangleIcon from '@heroicons/vue/24/outline/ExclamationTriangleIcon'
import { storeToRefs } from 'pinia'
import {
  MapPinIcon,
  ReceiptRefundIcon,
} from '@heroicons/vue/24/solid'
import dayjs from 'dayjs'
import type {
  Order,
  OrderParams,
  PaginationLinks,
  PaginationMeta,
} from '@/types'
import POSIcon from '@/components/Icons/POSIcon.vue'
import StorePanelIcon from '@/components/Icons/StorePanelIcon.vue'
import CalendarIcon from '@/components/Icons/CalendarsIcon.vue'
import OrderService from '@/services/OrderService'
import TrashIcon from '@/components/Icons/TrashIcon.vue'
const orderData = reactive({
  ordersList: [] as Order[],
  paginationMeta: {
    current_page: 1,
    from: 1,
    last_page: 1,
    links: [],
    path: '',
    per_page: 15,
    to: 15,
    total: 1,
  } as PaginationMeta,
  paginationLinks: {
    first: '',
    last: '',
    prev: null,
    next: null,
  } as PaginationLinks,
  filters: {
    page: 1,
    status: '',
    from: '',
    to: '',
    team: '',
    source: '',
    payment_status: '',
  } as OrderParams,
  processing: false,
})
const router = useRouter()
const processing = ref(false)
onMounted(() => {
  getOrders(orderData.filters)
})
const getOrders = (filters: OrderParams) => {
  processing.value = true
  OrderService.fetchOrders(filters).then((res) => {
    orderData.ordersList = res.data.data
    orderData.paginationLinks = res.data.links
    orderData.paginationMeta = res.data.meta
    processing.value = false
  })
}
const { updateOrderStatus, deleteOrder } = useOrder()
const { orderStatuses } = storeToRefs(useOrder())
const pageChanged = (page: number) => {
  orderData.filters.page = page
  getOrders(orderData.filters)
}
function goToCheckout() {
  router.push({ path: '/point-of-sale' })
}
function filterByStatusChanged({ status }: { status: string }) {
  orderData.filters = {
    ...orderData.filters,
    status,
    page: 1,
  }
  getOrders(orderData.filters)
}

function filterChanged({ range, startRange, team, source, payment_status, customer, invoiced, orderNum, staffId }: { range: { from: string; to: string };startRange: { start_from: string; start_to: string }; team: string; status: string; source: string; payment_status: string; customer: string; invoiced: string; orderNum: string; staffId: string },

) {
  // passing old filters to keep the old filters if the user didn't change it
  orderData.filters = {
    from: range?.from,
    to: range?.to,
    start_from: startRange?.start_from,
    start_to: startRange?.start_to,
    team,
    source,
    payment_status,
    customer,
    invoiced,
    orderNum,
    staffId,
    page: 1,
    status: orderData.filters.status || '',
  }
  getOrders(orderData.filters)
}

const orderIdQuery = ref('')
const showOrderModal = ref(false)
const route = useRoute()
watch(() => route.query.order_id, (newVal) => {
  if (!route.query.order_id) {
    showOrderModal.value = false
    return
  }
  orderIdQuery.value = newVal as string
  showOrderModal.value = true
}, { immediate: true })
const closeOrderModal = () => {
  showOrderModal.value = false
  router.push({ query: {} })
}
const { t, locale } = useI18n()
const headers = computed(() => ([
  {
    title: '#',
  },
  {
    title: t('customer'),
  },
  {
    title: t('ord_details.source'),
  },
  {
    title: t('Team'),
  },

  {
    title: t('created_at'),
  },
  {
    title: t('total'),
  },
  {
    title: t('ord_details.payment_status'),
  },

  {
    title: t('actions'),
  },

]))
const removedStatus = [
  'failed',
  'waiting-for-payment',
  'waiting-for-bank-transfer-confirmation',
  'fully-refunded',
  'partial-refund',
  'canceled',
  'pending',
]

const getPaymentStatusIcon = (status: string) => {
  switch (status) {
    case 'paid': return CheckCircleIcon
    case 'unpaid': return XCircleIcon
    case 'partially-paid': return ExclamationTriangleIcon
    default: return null
  }
}
const getPaymentStatusColor = (status: string) => {
  switch (status) {
    case 'paid': return 'text-green-600'
    case 'unpaid': return 'text-red-600'
    case 'partially-paid': return 'text-yellow-600'
    default: return 'text-gray-600'
  }
}

const getPaymentStatusText = (status: string) => {
  switch (status) {
    case 'paid': return t('paid')
    case 'unpaid': return t('unpaid')
    case 'partially-paid': return t('partially_paid')
    default: return status
  }
}
const getSourceIcon = (source: string) => {
  switch (source) {
    case 'pos': return {
      icon: POSIcon,
      text: t('booking.sources.pos'),
    }
    case 'pos2': return {
      icon: POSIcon,
      text: t('booking.sources.pos2'),
    }
    case 'bookingPage': return {
      icon: StorePanelIcon,
      text: t('booking.sources.booking-page'),
    }
    case 'calendar': return {
      icon: CalendarIcon,
      text: t('booking.sources.calendar'),
    }
    default: return null
  }
}

const getStatusStyle = (status: OrderStatus) => {
  const bgColor = status.bg_color || '#000000'
  const textColor = status.text_color || '#ffffff'

  return {
    backgroundColor: bgColor,
    color: textColor,
    // Add a subtle opacity to the background for a softer look
    opacity: 0.9,
  }
}

const handleStatusChange = async (orderId: string, statusObject: any) => {
  const payload = {
    status: statusObject.status,
    id: orderId,
  }
  await updateOrderStatus(payload)
  // refresh the orders list // refetch same page
  await pageChanged(orderData.filters.page)
}
const showConfModal = ref(false)
const selectedItemId = ref('')
const showConfirmationModal = (itemId) => {
  selectedItemId.value = itemId
  showConfModal.value = true
}

const handleDelete = async (itemId) => {
  showConfirmationModal(itemId)
}
const itemRemoved = async () => {
  // refresh the orders list // refetch same page
  await pageChanged(orderData.filters.page)
}

const openOrderDetails = (item: Order) => {
  router.push({ name: 'orders', query: { order_id: item.id } })
}
const isOpenCreateOrderModal = ref(false)
const openOrderModal = () => {
  isOpenCreateOrderModal.value = true
}
</script>

<template>
  <div class="ms-auto me-auto max-w-12xl">
    <ModalOrder
      v-if="isOpenCreateOrderModal"
      :is-open="isOpenCreateOrderModal"
      @close="isOpenCreateOrderModal = false"
      @refresh="getOrders(orderData.filters)"
    />
    <!-- Confirmation Modal for Delete -->
    <confirmation-modal
      v-if="showConfModal"
      :is-open="showConfModal"
      :api-call="deleteOrder"
      :record-id="selectedItemId"
      @removed="itemRemoved"
      @closed="showConfModal = false"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.msg") }}
      </p>
    </confirmation-modal>
    <div class="flex justify-between w-full item-center">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t(`homepage.${$route?.name}` || "homepage.title") }}
      </h1>
      <BaseButton
        class="inline-flex w-auto hover:bg-green-700"
        custome-bg="bg-green-600"
        @click="openOrderModal"
      >
        {{ $t("create_new_order") }}
        <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </div>
    <div>
      <div class="mt-4">
        <OrdersFilters @filter-changed="filterChanged" />
      </div>
      <div class="mt-4 sm:mt-0">
        <StatusFilter @filter-changed="filterByStatusChanged" />
      </div>
      <div class="flex flex-col mt-8">
        <!-- <OrdersList
              :isLoading="processing"
              :list="orderData.ordersList"
            ></OrdersList> -->

        <generic-table
          :is-loading="processing"
          :data="orderData.ordersList"
          :headers="headers"
          item-key="id"
          :on-row-click="openOrderDetails"
          tr-class="cursor-pointer"
        >
          <template #row="{ item }">
            <grid-td class="">
              {{ item?.OrderNum }}
            </grid-td>
            <grid-td class="flex gap-2 items-center max-w-[200px]">
              <img
                v-if="item.customer?.photo"
                :src="item.customer?.photo"
                class="object-cover w-8 h-8 rounded-full"
                alt="Customer Photo"
              >
              <svg v-else xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-8 h-8 text-gray-400 bg-gray-200 rounded-full" viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z" />
              </svg>
              <h3 class="flex flex-col gap-1 text-gray-900 truncate text-md">
                <p>
                  {{ item.customer.first_name ? `${item.customer?.first_name} ${item.customer?.last_name || ''}` : $t('pos.guest') }}
                </p>
                <div v-if="item.address" class="flex gap-1 items-center w-full text-sm text-gray-600">
                  <MapPinIcon class="w-4 h-4 text-gray-400 truncate" />
                  <p class="truncate">
                    {{ item.address }}
                  </p>
                </div>
              </h3>
            </grid-td>

            <grid-td class="">
              <div class="flex gap-2 items-center">
                <p>{{ getSourceIcon(item.source)?.text }}</p>
                <component :is="getSourceIcon(item.source)?.icon" class="w-5 h-5 text-gray-400" />
              </div>
            </grid-td>

            <grid-td class="">
              <div class="flex gap-2 items-center">
                {{ item.branch?.name }}
              </div>
            </grid-td>

            <grid-td class="">
              <div class="text-sm text-gray-500">
                {{ dayjs(item.created_at).locale(locale).fromNow() }}
              </div>
            </grid-td>
            <grid-td class="">
              <div class="text-sm text-gray-500">
                <price-format :form-data="{ price: item.total, currency: item.currency || '' }" />
              </div>
            </grid-td>
            <grid-td class="">
              <div class="flex flex-col gap-2">
                <div class="flex gap-2 items-center">
                  <component
                    :is="getPaymentStatusIcon(item.payment_status)"
                    class="w-5 h-5"
                    :class="getPaymentStatusColor(item.payment_status)"
                  />
                  <span class="text-sm font-medium" :class="getPaymentStatusColor(item.payment_status)">
                    {{ getPaymentStatusText(item.payment_status) }}
                  </span>
                </div>
                <div class="flex gap-2 items-center">
                  <ReceiptRefundIcon v-if="item.type == 'refund'" class="w-6 h-6 text-red-500" />
                  <div v-if="item.has_invoice" class="flex gap-1 items-center px-2 py-1 text-green-800 bg-green-100 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span class="text-xs font-medium">{{ $t('form.invoiced') }}</span>
                  </div>
                </div>
              </div>
            </grid-td>

            <grid-td class="flex gap-2 items-center">
              <div class="flex relative gap-2 items-center">
                <DynamicMenu>
                  <template #trigger="{ isOpen }">
                    <button
                      class="flex gap-2 justify-center items-center px-3 py-1.5 text-sm font-medium rounded-lg border border-gray-200 shadow-sm transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500 min-w-[120px]"
                      :style="{
                        'background-color': item.status?.bg_color ?? '#f3f4f6',
                        'color': item.status?.text_color ?? '#374151',
                        'border-color': item.status?.bg_color ? 'transparent' : '#d1d5db',
                      }"
                    >
                      <span>{{ item.status?.label }}</span>
                      <svg
                        class="ml-1 w-4 h-4 transition-transform duration-200"
                        :class="{ 'rotate-180': isOpen }"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        viewBox="0 0 24 24"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  </template>

                  <template #content="{ close }">
                    <div class="py-2">
                      <div v-for="status in Object.values(orderStatuses).filter(s => !removedStatus.includes(s.status))" :key="status.status">
                        <button
                          class="flex gap-3 items-center px-4 py-3 w-full text-sm text-left transition-all duration-150 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none group"
                          @click="() => { handleStatusChange(item.id, status); close(); }"
                        >
                          <div
                            class="flex-shrink-0 w-3 h-3 rounded-full shadow-sm"
                            :style="{
                              'background-color': status.bg_color ?? '#6b7280',
                            }"
                          />
                          <span
                            class="font-medium transition-colors duration-150 group-hover:text-gray-900"
                          >
                            {{ status.label }}
                          </span>
                        </button>
                      </div>
                    </div>
                  </template>
                </DynamicMenu>
              </div>
              <div v-if="!item.has_invoice " class="flex gap-2 items-center">
                <button @click="handleDelete(item.id)">
                  <TrashIcon class="w-6 h-6 text-red-500" />
                </button>
              </div>
            </grid-td>
          </template>
        </generic-table>
        <div class="px-4">
          <Pagination
            v-if="orderData.ordersList.length"
            :pagination-meta="orderData.paginationMeta"
            :pagination-links="orderData.paginationLinks"
            @change="pageChanged"
          />
        </div>

        <OrderDetailsModal
          v-if="showOrderModal"
          :is-open="showOrderModal"
          :order-id="orderIdQuery"
          @close="closeOrderModal"
        />
      </div>
    </div>
  </div>
</template>
