<script lang="ts" setup>
import useVuelidate from '@vuelidate/core'
import { Switch } from '@headlessui/vue'
import { QuillEditor } from '@vueup/vue-quill'
import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/vue/24/outline'
import { required } from '@/utils/i18n-validators'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import { usePage } from '@/stores/pages'
interface Page {
  title: string
  content: string
  is_published: boolean | number
}
const { addPage, getPageById, updatePage } = usePage()
const route = useRoute()
const processing = ref(false)
const formData = reactive<Page>({
  title: '',
  // build start editor content to easily edit
  content: '',
  is_published: true,
})

const rules = {
  title: {
    required,
  },
  content: {
    required,
  },
}

const $v = useVuelidate(rules, formData)
const router = useRouter()
const createRecord = async () => {
  return addPage(formData).then((res) => {
    formData.title = ''
    formData.content = ''
    formData.is_published = true
    $v.value.$reset()
  })
}
const updateRecord = async () => {
  return updatePage(formData, route.params.id as string)
}
const submitForm = async () => {
  if ($v.value.$invalid)
    return
  formData.is_published = formData.is_published ? 1 : 0
  processing.value = true
  try {
    if (route.params.id)
      await updateRecord()
    else
      await createRecord()
  }
  finally {
    processing.value = false
  }
}
onMounted(() => {
  if (route.params.id) {
    processing.value = true
    getPageById(route.params?.id as string)
      .then((res) => {
        formData.title = res.data.data.title
        formData.content = res.data.data.content
        formData.is_published = res.data.data.is_published
      })
      .catch((err) => {
        router.push({ name: 'createCustomPage' })
      })
      .finally(() => {
        processing.value = false
      })
  }
})
</script>

<template>
  <div>
    <form class="flex flex-col relative" @submit.prevent="submitForm()">
      <overlay-loader v-if="processing" :full-screen="false" />
      <div class="flex flex-col w-2/3 gap-4 mx-auto">
        <div
          class="flex items-center gap-2 cursor-pointer w-fit"
          @click="$router.go(-1)"
        >
          <div class="px-3 py-1 bg-primary">
            <ArrowLeftIcon
              v-if="$i18n.locale === 'en'"
              class="w-6 h-6 text-white"
            />
            <ArrowRightIcon v-else class="w-6 h-6 text-white" />
          </div>
          <h2 class="text-base text-primary">
            {{ $t("backToPages") }}
          </h2>
        </div>
        <div class="flex flex-col">
          <LanelInput
            for="title"
            class=" mb-2 w-full flex items-center text-start text-[#261E27] text-base  sm:mt-px sm:pt-2"
          >
            {{ $t("pageTitle") }}
            <span class="text-red-600">*</span>
          </LanelInput>
          <form-group :validation="$v" name="title">
            <template #default="{ attrs }">
              <TextInput
                v-bind="attrs"
                id="title"
                v-model="formData.title"
                :placeholder="$t('form.address')"
              />
            </template>
          </form-group>
        </div>
        <div class="flex flex-col">
          <LabelInput>
            {{ $t("form.publish_page") }}
          </LabelInput>
          <Switch
            v-model="formData.is_published"
            class="relative inline-flex flex-shrink-0 h-6 transition-colors duration-200 ease-in-out border-2 border-transparent rounded-full cursor-pointer w-11 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            :class="[formData.is_published ? 'bg-primary-600' : 'bg-gray-200']"
          >
            <span
              aria-hidden="true"
              class="inline-block w-5 h-5 transition duration-200 ease-in-out transform bg-white rounded-full shadow pointer-events-none ring-0"
              :class="[
                formData.is_published
                  ? 'translate-x-5 rtl:-translate-x-5'
                  : 'translate-x-0',
              ]"
            />
          </Switch>
        </div>

        <div>
          <LabelInput for="address">
            {{ $t("pageDesc") }}
          </LabelInput>
          <form-group :validation="$v" name="content">
            <template #default="{ attrs }">
              <div class="h-96 mt-1" v-bind="attrs">
                <div
                  id="my-toolbar"
                  class="flex gap-2 bg-gray-100 rounded-t-md justify-center"
                >
                  <!-- heading -->
                  <!-- <button class="ql-header" value="1"></button>
                  <button class="ql-header" value="2"></button> -->
                  <select class="ql-header flex gap-2" dir="ltr" style="width: 60px; justify-content: space-between; align-items: center; padding: 0 10px;  color: #000; font-size: 14px; font-weight: 500; padding: 0 ;">
                    <option value="1">
                      H1
                    </option>
                    <option value="2">
                      H2
                    </option>
                    <option value="3">
                      H3
                    </option>
                    <option value="4">
                      H4
                    </option>
                    <option value="5">
                      H5
                    </option>
                  </select>

                  <!-- Add buttons as you would before -->
                  <button class="ql-bold" />
                  <button class="ql-italic" />
                  <button class="ql-underline" />
                  <button class="ql-strike" />
                  <button class="ql-link" />
                  <button class="ql-image" />
                  <button class="ql-video" />
                  <!-- button for alignmeny -->
                  <select class="ql-align">
                    <option value="right" />
                    <option value="" />
                    <option value="center" />
                  </select>

                  <!-- video -->
                </div>
                <QuillEditor
                  v-model:content="formData.content"
                  toolbar="#my-toolbar"
                  content-type="html"
                  class="h-full text-black mt-2 rounded-b-md"
                  theme="snow"
                />
              </div>
            </template>
          </form-group>
        </div>
        <div class="mt-10">
          <template v-if="formData.content">
            <h2 class="text-lg font-semibold text-primary">
              {{ $t("preview") }}
            </h2>
            <div
              class="ql-editor mt-4 p-4 bg-white rounded-md shadow-md"
              v-html="formData.content"
            />
          </template>
        </div>
        <BaseButton
          class="block mt-16 hover:bg-green-700 w-fit ms-auto"
          custome-bg="bg-green-600"
          type="submit"
        >
          <span>
            {{ route.params.id ? $t("form.update") : $t("form.create") }}
          </span>
        </BaseButton>
      </div>
    </form>
  </div>
</template>

<style>
.ql-editor .ql-align-right {
  direction: rtl;
}

[dir="rtl"] .ql-editor {
  direction: rtl;
}
.ql-editor h1 {
  all: revert;
}
.ql-editor h2 {
  all: revert;
}
.ql-editor h3 {
  all: revert;
}
.ql-editor h4 {
  all: revert;
}
.ql-editor h5 {
  all: revert;
}
.ql-editor h6 {
  all: revert;
}
</style>
