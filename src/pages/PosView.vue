<script setup lang="ts">
import type { ComputedRef } from 'vue'
import { uniqueId } from 'lodash'
import { storeToRefs } from 'pinia'
import { TagIcon } from '@heroicons/vue/24/outline'
import { usePosStore } from '@/stores/pos'
const { enter, exit, isFullscreen } = useFullscreen(document.documentElement)
const {
  checkoutItems,
  items,
  checkoutData,
  processing,
  formData,
  allItems,
  createdOrderDetails,
} = storeToRefs(usePosStore())
const { addProduct, createPos, checkoutCalculate, clearPos, getInvoiceHtml }
  = usePosStore()

const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)

const route = useRoute()
const { t } = useI18n()
const { showNotification } = useNotifications()
const appointmentData: ComputedRef<Record<string, any>> = computed(() => {
  if (!Object.keys(route.query).length)
    return {}
  const { staff_id, booking_id, branch_id, customer, services } = route?.query
  return {
    staff_id,
    booking_id,
    branch_id,
    customer: customer ? JSON.parse(customer) : null,
    services: services ? JSON.parse(services) : null,
  }
})
const openFullscreenModel = ref(false)

const openFullScreenMode = () => {
  enter()
  openFullscreenModel.value = false
}
const closeFullScreenMode = () => {
  exit()
  openFullscreenModel.value = false
}

onMounted(() => {
  enter().catch(() => {
    openFullscreenModel.value = true
  })
  const warningMessage = t('warning_message')
  showNotification({
    title: t('warning'),
    type: 'warning',
    message: warningMessage,
  })
  if (appointmentData.value.services && appointmentData.value.services.length) {
    checkoutItems.value = appointmentData.value.services.map((service) => {
      return {
        name: service.name,
        price: service.price,
        selectedStaff: appointmentData.value.staff_id,
        id: service.uuid,
        uuid: uniqueId(),
        subTotal: service.price,
        type: 'services',
        discount: 0,
        qty: 1,
      }
    })
  }
  if (appointmentData.value.customer)
    checkoutData.value.customer = appointmentData.value.customer

  if (appointmentData.value.booking_id)
    checkoutData.value.booking_id = appointmentData.value.booking_id
})
onUnmounted(() => {
  clearPos()
  exit()
})
const tabs = [
  { label: 'pos.services', value: 'services', href: '#' },
  { label: 'pos.products', value: 'products', href: '#' },
  { label: 'pos.packages', value: 'packages', href: '#' },
]
const posStore = usePosStore()
const availableCategories = computed(() => {
  if (selectedType?.value == 'packages')
    return
  return Object.keys(posStore[selectedType?.value]).filter(Boolean)
})

const router = useRouter()

const searcItemName = ref('')
const changeType = (tab: string) => {
  searcItemName.value = ''
  router.push({
    query: { ...route.query, selectedType: tab, selectedCategory: '' },
  })
}
const selectedType = computed(() => {
  return route.query.selectedType || 'services'
})

const changeCategory = (category: string) => {
  router.push({ query: { ...route.query, selectedCategory: category } })
}
const selectedCategory = computed(() => {
  return route.query.selectedCategory || null
})

const itemsResult = computed(() => {
  const resultSearch = allItems.value.filter(
    item =>
      item.name?.toLowerCase().includes(searcItemName.value.toLowerCase())
      && searcItemName.value !== '',
  )
  let resultCategory
  if (selectedType.value === 'packages') {
    resultCategory = posStore?.[selectedType.value]
  }
  else {
    resultCategory
      = posStore?.[selectedType.value]?.[selectedCategory.value] || []
  }

  return resultSearch.concat(resultCategory)
})
const showPaymentModal = ref(false)
const next = async ({ saveAsDraft = false }: { saveAsDraft: boolean }) => {
  processing.value = true
  await createPos({ ...formData.value, draft: saveAsDraft })
    .then((res) => {
      if (saveAsDraft) {
        showNotification({
          type: 'success',
          title: t('Success'),
          message: t('draft_saved'),
        })
        clearPos()
        return
      }
      showPaymentModal.value = true
      createdOrderDetails.value = res
      // router.push({ name: "order", params: { id: res.id } });
    })
    .catch((err) => {
      showNotification({
        type: 'error',
        title: t('Error'),
        message: err.message,
      })
    })
    .finally(() => {
      processing.value = false
    })
}

watch(items, async (value) => {
  if (!value.length)
    return
  processing.value = true
  const data = {
    branch_id: appointmentData.value.branch_id || checkoutData.value.team_id,
    customer_id: appointmentData.value.customer?.id,
    booking_id: appointmentData.value.booking_id,
    items: items.value.map(o => ({
      item_id: o.id,
      price: o.price,
      discount: o.discount,
      staff_id: o.selectedStaff,
      quantity: o.qty,
    })),
  }
  await checkoutCalculate(data)
    .then((res) => {
      checkoutData.value.subTotalBeforeTax = res.subTotalBeforeTax
      checkoutData.value.totalIncludeTaxAndBeforeDiscount
        = res.subTotalBeforeTax
      checkoutData.value.discount = res.discount
      checkoutData.value.totalTaxable = res.totalTaxable
      checkoutData.value.taxes = res.taxes
      checkoutData.value.total = res.total
    })
    .catch((err) => {
      showNotification({
        type: 'error',
        title: t('Error'),
        message: err.message,
      })
    })
    .finally(() => {
      processing.value = false
    })
})
const closePaymentModal = () => {
  showPaymentModal.value = false
  clearPos()
}

function popupWindow(url, windowName, win, w, h) {
  const y = win.top.outerHeight / 2 + win.top.screenY - h / 2
  const x = win.top.outerWidth / 2 + win.top.screenX - w / 2
  return win.open(
    url,
    windowName,
    `toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=${w}, height=${h}, top=${y}, left=${x}`,
  )
}
async function openInvoicePdf() {
  // window.open("/preview-invoices/" + createdOrderDetails.value.id, "_target");
  const htmlRes = await getInvoiceHtml(createdOrderDetails.value.id)
  const winPrint = popupWindow('', 'Print', window, 800, 900)
  winPrint.document.write(htmlRes.data)
  winPrint.document.close()
  winPrint.focus()
  winPrint.addEventListener('load', async () => {
    await winPrint.print()
  })
}
const printInvoice = () => {
  showPaymentModal.value = false
  openInvoicePdf()
  clearPos()
}
</script>

<template>
  <div
    class="block overflow-visible relative w-full h-full checkout md:flex md:overflow-hidden"
  >
    <confirm-modal
      :is-open="openFullscreenModel"
      :title="$t('requset_fullscreen')"
      action-classes="justify-center"
      @confirm="openFullScreenMode"
      @cancel="closeFullScreenMode"
    />
    <OverlayLoader v-if="processing" :full-screen="false" />
    <div
      id="checkout-items"
      class="overflow-y-scroll px-6 py-3 w-full h-full bg-gray-50 hide-scroll p-l-0"
    >
      <div class="px-6 py-4 w-full text-center">
        <div
          class="flex flex-col gap-1 justify-start items-start mb-2 max-w-2xl"
        >
          <div class="flex relative gap-5 items-center w-full">
            <TextInput
              id="search"
              v-model="searcItemName"
              :placeholder="$t('search_by_product_name_or_service_name')"
              :label="$t('search_by_product_name_or_service_name')"
            />

            <base-button @click="searcItemName = ''">
              {{ $t("clear_search") }}
            </base-button>
            <base-button
              custome-bg="bg-red-900"
              class="mx-"
              @click="closeFullScreenMode, $router.push({ name: 'dashboard' })"
            >
              {{ $t("exist_pos") }}
            </base-button>
          </div>
        </div>
        <div class="mt-4 w-full text-center">
          <div
            class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4"
            role="alert"
          >
            <p>{{ t('warning_message') }}</p><a target="_blank" href="https://pos.mahjoz.io">https://pos.mahjoz.io</a>
          </div>
          <div class="flex gap-4 justify-start">
            <button
              v-for="(tab, index) in tabs"
              :key="index"
              :class="`px-4 py-2 rounded-md text-black font-semibold bg-gray-200  duration-150 border border-gray-200
            ${
                selectedType === tab.value
                  ? 'bg-primary text-white border border-primary'
                  : ''
              }`"
              @click="changeType(tab.value)"
            >
              {{ $t(tab.label) }}
            </button>
          </div>
        </div>
        <div class="w-full">
          <div v-if="!itemsResult.length" class="flex flex-wrap gap-4 mt-4">
            <button
              v-for="category in availableCategories"
              :key="category"
              class="flex gap-2 justify-between items-center px-6 py-4 text-sm font-medium text-left bg-blue-50 rounded-lg text-dark-900 hover:bg-blue-200 focus:outline-none focus-visible:ring focus-visible:primary-500 focus-visible:primary-75"
              :class="{
                '!bg-blue-200':
                  selectedCategory === posStore[selectedType][category],
              }"
              @click="changeCategory(category)"
            >
              <TagIcon class="w-5 h-5 text-gray-500" />
              <div class="text-md">
                {{ category }}
              </div>
            </button>
          </div>
          <div
            class="grid gap-4 mt-6 sm:grid-cols-3 md:grid-cols-2 xl:grid-cols-4"
          >
            <div v-for="item in itemsResult" :key="item.id">
              <div
                v-if="item?.id"
                class="bg-white rounded-xl shadow-md duration-500 hover:scale-105 hover:shadow-xl"
              >
                <button
                  class="flex flex-col justify-between items-center w-full h-full"
                  @click="addProduct(item, selectedType)"
                >
                  <img
                    v-if="item.photo"
                    :src="item.photo"
                    alt="Product"
                    class="object-cover w-full h-40"
                  >
                  <svg
                    v-else
                    class="object-cover w-full h-40"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"
                    />
                  </svg>
                  <div class="px-4 py-3 w-full">
                    <p class="block text-base font-bold text-black">
                      {{ item.name }}
                    </p>
                    <div class="flex justify-center items-center">
                      <p class="py-3 text-lg font-semibold text-black">
                        <price-format
                          :form-data="{
                            price: item.price,
                            currency: userInfo?.tenant?.currency || '',
                          }"
                        />
                      </p>
                    </div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!--  -->
    </div>
    <form
      id="checkout-board"
      class="relative w-auto min-h-[400px] h-auto sm:w-half md:w-[500px] bg-white shadow-lg p-5 pb-16 md:pb-16"
      @submit.prevent="next"
    >
      <checkout-product-selection :team-id="appointmentData.branch_id" />
      <div class="absolute bottom-0 left-0 p-3 mt-5 w-full bg-white shadow-md">
        <div
          v-if="checkoutItems.length"
          class="flex justify-end items-start mt-4"
        >
          <div class="w-full">
            <div
              class="flex justify-between items-center px-3 py-1 border-b-2 border-gray-200"
            >
              <span>{{ $t("pos.subtotal") }}</span>
              <span>
                <price-format
                  :form-data="{
                    price: checkoutData.totalIncludeTaxAndBeforeDiscount || 0,
                    currency: userInfo?.tenant?.currency || '',
                  }"
                />
              </span>
            </div>

            <div
              class="flex justify-between items-center px-3 py-1 border-b-2 border-gray-200"
            >
              <span>{{ $t("pos.discount") }}</span>
              <span>
                <price-format
                  :form-data="{
                    price: checkoutData.discount || 0,
                    currency: userInfo?.tenant?.currency || '',
                  }"
                />
              </span>
            </div>

            <div
              class="flex justify-between items-center px-3 py-1 border-b-2 border-gray-200"
            >
              <span>{{ $t("pos.totalTaxable") }}</span>
              <span>
                <price-format
                  :form-data="{
                    price: checkoutData.totalTaxable || 0,
                    currency: userInfo?.tenant?.currency || '',
                  }"
                />
              </span>
            </div>

            <div
              class="flex justify-between items-center px-3 py-1 border-b-2 border-gray-200"
            >
              <span>{{ $t("pos.tax") }}</span>
              <span>
                <price-format
                  :form-data="{
                    price: checkoutData.taxes || 0,
                    currency: userInfo?.tenant?.currency || '',
                  }"
                />
              </span>
            </div>
            <div
              class="flex justify-between items-center px-3 py-1 border-b-2 border-gray-200"
            >
              <span>{{ $t("pos.total") }}</span>
              <span>
                <price-format
                  :form-data="{
                    price: checkoutData.total || 0,
                    currency: userInfo?.tenant?.currency || '',
                  }"
                />
              </span>
            </div>
          </div>
        </div>
        <div class="flex gap-4 mt-4">
          <button
            type="submit"
            :disabled="!checkoutItems.length"
            class="p-4 w-full font-semibold text-white bg-blue-500 rounded-md duration-150 disabled:opacity-50 hover:bg-blue-700"
          >
            {{ $t("checkout") }}
          </button>

          <button
            type="button"
            :disabled="!checkoutItems.length"
            class="p-4 w-full font-semibold text-white bg-blue-500 rounded-md duration-150 disabled:opacity-50 hover:bg-blue-700"
            @click="next({ saveAsDraft: true })"
          >
            {{ $t("save_as_draft") }}
          </button>
        </div>
      </div>
    </form>

    <PaymentModal
      :is-open="showPaymentModal"
      :invoice="createdOrderDetails"
      :is-from-booking="false"
      :un-paid-amount="checkoutData.total"
      form-btn="pay_and_got_to_invoice"
      @closed="closePaymentModal"
      @updated="showPaymentModal = false"
      @refresh="printInvoice"
    />
  </div>
</template>
