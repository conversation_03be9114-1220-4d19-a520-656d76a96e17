<script lang="ts" setup>
import { PlusIcon } from "@heroicons/vue/24/outline";
import type { Coupons } from "@/types/coupons";
const { getCouponsList } = useCoupons();

// Modal states
const showAddModal = ref(false);
const showDetailsModal = ref(false);
const selectedCouponId = ref<string | null>(null);

const openDetailsModal = (coupon: Coupons) => {
  selectedCouponId.value = coupon.uuid!;
  showDetailsModal.value = true;
};

const openCreateModal = () => {
  showAddModal.value = true;
};

const closeAddModal = async () => {
  showAddModal.value = false;
  await getCouponsList();
};

const closeDetailsModal = async () => {
  showDetailsModal.value = false;
  selectedCouponId.value = null;
  await getCouponsList();
};

onMounted(async () => {
  await getCouponsList()
})
</script>

<template>
  <div class="flex flex-col gap-8">
    <!-- Add Coupon Modal -->
    <AddCouponModal 
      v-if="showAddModal"
      :show-modal="showAddModal"
      @closed="closeAddModal"
    />

    <!-- Coupon Details Modal -->
    <CouponDetailsModal
      v-if="showDetailsModal && selectedCouponId"
      :is-open="showDetailsModal"
      :coupon-id="selectedCouponId"
      @close="closeDetailsModal"
    />

    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("coupons.title") }}
      </h1>
      <BaseButton
        class="inline-flex w-auto hover:bg-green-700"
        custome-bg="bg-green-600"
        @click="openCreateModal"
      >
        {{ $t("form.create") }}
        <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </div>

    <div class="flex flex-col mt-4">
      <div class="">
        <coupons-grid @openEditModal="openDetailsModal" />
      </div>
    </div>
  </div>
</template>
