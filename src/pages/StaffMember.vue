<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import {
  ChevronDownIcon,
  DevicePhoneMobileIcon,
  UserMinusIcon,
  UserPlusIcon,
} from '@heroicons/vue/20/solid'
import { useAppsStore } from '@/stores/apps'
import i18n from '@/i18n'
import { useTagStore } from '@/stores/tags'
import type { Staff } from '@/types'

const tagStore = useTagStore()
const { tags } = storeToRefs(tagStore)
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { removeStaff, fetchStaffById, createUserStaff } = useStaffStore()
const { hasCommission } = storeToRefs(usePluginsStore())
const processing = ref(true)
const route = useRoute()
const router = useRouter()
const { showNotification } = useNotifications()
const store = useTeamStore()
const { deactiveUser } = useTeamStore()

const crumbs = ref([
  {
    name: 'homepage.staff',
    path: '/management/staffs',
  },
  {
    name: '',
    path: '',
  },
])
const staff = ref<Staff>({
  uuid: '',
  name: '',
  email: '',
  phone: '',
  phone_country: '',
  imageLink: '',
  clients_in_same_time: 0,
  has_workingHour: false,
  has_timeoff: false,
  tags: [],
  team_id: '',
})
onMounted(async () => {
  await fetchStaff()
})
const showModal = ref(false)
const toggleModel = () => {
  showModal.value = false
}
const updated = (resUpdated: Staff) => {
  staff.value = resUpdated
  showModal.value = false
}
// const router = useRouter()
const showConfModal = ref(false)

const removed = () => {
  showConfModal.value = true
}
const editRecored = () => {
  showModal.value = true
}
onMounted(async () => {
  tagStore.fetchTags('staff')

  const status = route.query.status

  if (typeof status === 'undefined')
    return

  if (status === 'synced') {
    showNotification({
      title: i18n.global.t('Success'),
      type: 'success',
      message: i18n.global.t('googleCalendar.synced'),
    })
  }
  else if (status === 'error') {
    showNotification({
      title: i18n.global.t('Error'),
      type: 'error',
      message: i18n.global.t('googleCalendar.syncedError'),
    })
  }
  fetchStaff()
})

const openCustomizeZoneModal = ref(false)

const deactivateUser = async (staff) => {
  processing.value = true
  if (staff.user?.isActive) {
    await deactiveUser(staff.user.uuid)
      .then(async (res) => {
        processing.value = false
        await fetchStaff()
      })
      .catch((err) => {
        processing.value = false
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: i18n.global.t('error.error'),
        })
      })
  }
  else {
    await createUserStaff(route.params.id as string, staff.value)
      .then(async (res) => {
        processing.value = false
        await fetchStaff()
      })
      .catch((err) => {
        processing.value = false
        showNotification({
          title: i18n.global.t('Error'),
          type: 'error',
          message: err.message,
        })
      })
  }
}
const syncLoading = ref(false)

const { fetchGoogleCalendarSyncLink, unSyncGoogleCalendar } = useAppsStore()

const syncWithGoogleCalendar = () => {
  syncLoading.value = true

  fetchGoogleCalendarSyncLink(staff.value.uuid)
    .then((response: string) => {
      window.location.href = response
    }).finally(() => {
      syncLoading.value = false
    })
}

const unSyncWithGoogleCalendar = () => {
  syncLoading.value = true

  unSyncGoogleCalendar(staff.value.uuid).then((res: any) => {
    if (res) {
      showNotification({
        title: i18n.global.t('Success'),
        type: 'success',
        message: i18n.global.t('googleCalendar.unSynced'),
      })

      router.push({ path: router.currentRoute.value.path })

      setTimeout(() => {
        window.location.reload()
      }, 3000)
    }
  }).finally(() => {
    syncLoading.value = false
  })
}
const fetchStaff = async () => {
  processing.value = true
  try {
    const res = await fetchStaffById(route.params.id as string)
    staff.value = res
  }
  catch (err) {
    showNotification({
      title: i18n.global.t('Error'),
      type: 'error',
      message: err.message,
    })
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <div>
    <AddStaffModal
      v-if="showModal"
      :staff="staff"
      :tags="tags?.data"
      :show-modal="showModal"
      title="editEmployee"
      @closed="toggleModel"
      @updated="updated"
    />
    <staff-zone-modal
      v-if="openCustomizeZoneModal"
      :staff="staff"
      :show-modal="openCustomizeZoneModal"
      @closed="openCustomizeZoneModal = false"
    />
    <confirmation-modal
      v-if="showConfModal"
      :dir="getLocale(locale)?.direction"
      :api-call="removeStaff"
      :record-id="staff?.uuid"
      redirect-url="/management/staffs"
      :is-open="showConfModal"
      @closed="showConfModal = false"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.deleteStaff") }}
      </p>
    </confirmation-modal>
    <div class="flex flex-col">
      <bread-crumb :crumbs="crumbs" class="mt-5" />

      <div
        class="flex flex-col gap-4 justify-between items-center mt-6 mb-4 sm:flex-row sm:gap-0"
      >
        <div class="flex gap-1 items-center sm:gap-3">
          <h1 class="text-base font-semibold sm:text-3xl">
            {{ staff?.name }}
          </h1>
        </div>
        <div class="inline-flex w-full rounded-md shadow-sm sm:w-auto">
          <button
            type="button"
            class="inline-flex relative items-center px-3 py-2 w-3/4 text-sm font-semibold text-gray-900 bg-white rounded-l-md ring-1 ring-inset ring-gray-300 sm:w-auto hover:bg-gray-50 focus:z-10"
            :class="[
              getLocale(locale)?.direction === 'rtl' ? 'order-2' : 'order-1',
            ]"
            @click="editRecored()"
          >
            {{ $t("form.edit") }}
          </button>
          <Menu
            as="div"
            class="block relative -ml-px w-1/4 sm:w-auto"
            :class="
              getLocale(locale)?.direction === 'rtl' ? 'order-1' : 'order-2'
            "
          >
            <MenuButton
              class="inline-flex relative justify-center items-center px-2 py-2 w-full text-gray-400 bg-white rounded-r-md ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10"
            >
              <span class="sr-only">Open options</span>
              <ChevronDownIcon class="w-5 h-5" aria-hidden="true" />
            </MenuButton>
            <transition
              enter-active-class="transition duration-100 ease-out"
              enter-from-class="opacity-0 transform scale-95"
              enter-to-class="opacity-100 transform scale-100"
              leave-active-class="transition duration-75 ease-in"
              leave-from-class="opacity-100 transform scale-100"
              leave-to-class="opacity-0 transform scale-95"
            >
              <MenuItems
                class="absolute z-10 mt-2 -mr-1 w-48 bg-white rounded-md ring-1 ring-black ring-opacity-5 shadow-lg origin-top-right focus:outline-none"
                :class="[
                  getLocale(locale)?.direction === 'rtl'
                    ? '-right-20'
                    : '-left-40',
                ]"
              >
                <div class="py-1">
                  <MenuItem v-if="staff">
                    <button
                      type="button" class="block p-2 w-full text-sm text-red-600 text-start"
                      @click="deactivateUser(staff)"
                    >
                      <span v-if="staff?.user" class="flex text-red-600">
                        {{ $t('form.toggleInActive') }}
                        <LockClosedIcon class="w-4 h-4" aria-hidden="true" />
                      </span>
                      <span v-else class="flex text-green-600">
                        {{ $t('form.toggleActive') }}
                        <LockOpenIcon class="w-4 h-4" aria-hidden="true" />
                      </span>
                    </button>
                  </MenuItem>
                  <MenuItem>
                    <button
                      type="button"
                      class="block p-2 w-full text-sm text-red-600 text-start"
                      @click="removed()"
                    >
                      {{ $t("form.deleteStaff") }}
                    </button>
                  </MenuItem>
                </div>
              </MenuItems>
            </transition>
          </Menu>
        </div>
      </div>
      <TagsComp :tags="staff?.tags" />
      <div class="relative pb-8">
        <overlay-loader v-if="processing" :full-screen="false" />
        <img
          v-if="staff?.imageLink"
          id="image"
          class="block mx-auto mb-3 w-40 h-40 rounded-full"
          :src="staff.imageLink"
          :link="null"
        >
        <div class="grid grid-cols-1 sm:grid-cols-2">
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="customer"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("booking.staff") }} :</label>
            <div class="flex mt-1 font-semibold">
              <div
                class="flex relative flex-grow items-stretch focus-within:z-10"
              >
                {{ staff?.name }}
              </div>
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="staff"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("form.phone") }} :
            </label>
            <div class="flex gap-1 items-center mt-1 font-semibold">
              <span v-if="staff?.phone" class="">
                {{ staff?.phone }}
              </span>
              <span v-else>-</span>
              <a
                v-if="staff?.phone"
                :href="`https://wa.me/${staff?.phone}`"
                target="_blank"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-5 h-5"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="#25d366"
                    d="M19.05 4.91A9.816 9.816 0 0 0 12.04 2c-5.46 0-9.91 4.45-9.91 9.91c0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21c5.46 0 9.91-4.45 9.91-9.91c0-2.65-1.03-5.14-2.9-7.01zm-7.01 15.24c-1.48 0-2.93-.4-4.2-1.15l-.3-.18l-3.12.82l.83-3.04l-.2-.31a8.264 8.264 0 0 1-1.26-4.38c0-4.54 3.7-8.24 8.24-8.24c2.2 0 4.27.86 5.82 2.42a8.183 8.183 0 0 1 2.41 5.83c.02 4.54-3.68 8.23-8.22 8.23zm4.52-6.16c-.25-.12-1.47-.72-1.69-.81c-.23-.08-.39-.12-.56.12c-.17.25-.64.81-.78.97c-.14.17-.29.19-.54.06c-.25-.12-1.05-.39-1.99-1.23c-.74-.66-1.23-1.47-1.38-1.72c-.14-.25-.02-.38.11-.51c.11-.11.25-.29.37-.43s.17-.25.25-.41c.08-.17.04-.31-.02-.43s-.56-1.34-.76-1.84c-.2-.48-.41-.42-.56-.43h-.48c-.17 0-.43.06-.66.31c-.22.25-.86.85-.86 2.07c0 1.22.89 2.4 1.01 2.56c.12.17 1.75 2.67 4.23 3.74c.59.26 1.05.41 1.41.52c.59.19 1.13.16 1.56.1c.48-.07 1.47-.6 1.67-1.18c.21-.58.21-1.07.14-1.18s-.22-.16-.47-.28z"
                  />
                </svg>
              </a>
            </div>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="staff"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("form.email") }} :
            </label>
            <div class="mt-1 font-semibold">
              <span v-if="staff?.email">
                {{ staff?.email }}
              </span>
              <span v-else>-</span>
            </div>
          </div>

          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("form.clientSaameTime") }} :</label>
            <div v-if="staff?.clients_in_same_time" class="mt-1 font-semibold">
              {{ staff?.clients_in_same_time }}
            </div>
            <span v-else>-</span>
          </div>
          <div
            v-if="hasCommission"
            class="flex gap-2 items-center py-3 border-b border-stone-100"
          >
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("form.commission") }} :</label>
            <div v-if="staff?.commission" class="mt-1 font-semibold">
              {{ staff?.commission }} %
            </div>
            <span v-else>-</span>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("modalPlacholder.branch") }} :</label>
            <div v-if="staff?.team?.uuid" class="mt-1 font-semibold">
              {{ staff?.team?.name }}
            </div>
            <span v-else>-</span>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <label for="booking-start" class="block text-sm font-medium text-neutral-500 w-15">
              <img src="/src/assets/googleCalendar.svg" alt="google-calendar-icon" width="40">
            </label>
            <button
              v-if="staff.isGoogleCalendarInstalled" type="button" :disabled="syncLoading"
              class="flex items-center p-2 w-full text-sm text-red-600 text-start"
              @click.prevent="unSyncWithGoogleCalendar"
            >
              {{ $t('googleCalendar.unSync') }} <the-loader v-if="syncLoading" class="mx-2" />
            </button>
            <button
              v-else type="button" :disabled="syncLoading"
              class="flex items-center p-2 w-full text-sm text-green-600 text-start"
              @click.prevent="syncWithGoogleCalendar"
            >
              {{ $t('googleCalendar.title') }} <the-loader v-if="syncLoading" class="mx-2" />
            </button>
          </div>
          <div class="flex gap-2 items-center py-3 border-b border-stone-100">
            <button
              type="button"
              class="flex items-center p-2 w-full text-sm text-red-600 text-start"
              @click="deactivateUser(staff)"
            >
              <DevicePhoneMobileIcon class="mx-1 w-5 h-5" />
              <span v-if="staff?.user" class="flex text-red-600">
                {{ $t("form.toggleInActive") }}
                <UserMinusIcon class="w-6 h-6" aria-hidden="true" />
              </span>
              <span v-else class="flex text-green-600">
                {{ $t("form.toggleActive") }}
                <UserPlusIcon class="w-6 h-6" aria-hidden="true" />
              </span>
            </button>
          </div>
        </div>
      </div>
      <StaffTabs v-if="!processing" :staff="staff" />
    </div>
  </div>
</template>

<style></style>
