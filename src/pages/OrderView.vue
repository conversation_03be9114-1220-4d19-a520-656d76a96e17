<script setup lang="ts">
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/vue'
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  PencilSquareIcon,
} from '@heroicons/vue/20/solid'

import { storeToRefs } from 'pinia'

const settings = ref({})
const user = useAuthStore()
const { previewInvoicePdf, sendInvoicePdf, sendWhatsAppInvoice }
  = usePosStore()

const {
  fetchSingleOrder,
  deleteOrder,
  createOrderInvoice,
  getRefundItems,
  getOrderNotifications,
} = useOrder()

const showConfirmInvoiceModal = ref(false)

const fetchOrderDetails = async (id: string = route.params.id as string) => {
  processing.value = true
  try {
    await fetchSingleOrder(id)
  }
  finally {
    processing.value = false
  }
}

async function creatorderInvoice(id: string) {
  try {
    showConfirmInvoiceModal.value = false
    processing.value = true
    await createOrderInvoice(id)
    await fetchOrderDetails()
  }
  finally {
    processing.value = false
  }
}
const { getOrderDetails } = storeToRefs(useOrder())
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const processingbtn = ref(false)
const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const showConfModal = ref(false)
const processing = ref(false)
const invoiceProcessing = ref(false)
const crumbs = computed(() => {
  return [
    {
      name: 'homepage.orders',
      path: '/orders',
    },
    {
      name: `#${getOrderDetails.value?.OrderNum}`,
      path: '',
    },
  ]
})
const notifications = ref([])
onBeforeMount(async () => {
  try {
    processing.value = true
    getOrderNotifications(route.params.id as string).then((data) => {
      notifications.value = data
    })
    await fetchOrderDetails()
    await generalCustomeSetting('order').then((res) => {
      settings.value = res
    })
  }
  catch (error) {
    router.push({ path: '/orders' })
  }
  finally {
    processing.value = false
  }
})

const printOptions = computed(() => {
  if (getOrderDetails.value.invoices) {
    return [
      {
        name: t('order_summary'),
        id: 'order_summary',
        icon: 'summary',
        event: openOrderSummary,
      },
      {
        name: t('export_invoice'),
        id: 'export_invoice',
        icon: 'summary',
        event: downloadPdfInvioce,
      },
      {
        name: t('preview_invoice'),
        id: 'preview_invoice',
        icon: 'invoice',
        event: openInvoicePdf,
      },
      {
        name: t('send_invoice_email'),
        id: 'send_invoice_email',
        icon: 'invoice',
        event: sendInvoice,
      },
      {
        name: t('send_invoice_whatsapp'),
        id: 'send_invoice_whatsapp',
        icon: 'invoice',
        event: sendInvoiceWhatsApp,
      },
    ]
  }
  else {
    return [
      {
        name: t('order_summary'),
        id: 'order_summary',
        icon: 'summary',
        event: openOrderSummary,
      },
    ]
  }
})

function previewTicket(id: String) {
  window.open(`/print-invoice/${id}`, '_blank')
}
function downloadPdfInvioce() {
  invoiceProcessing.value = true
  previewInvoicePdf(getOrderDetails.value?.invoices?.id as string).finally(
    () => {
      invoiceProcessing.value = false
    },
  )
}
function openOrderSummary() {
  const btn = document.getElementById('add-transaction-button')
  const copy_btn = document.getElementById('copy-payment-link')
  btn?.style.setProperty('visibility', 'hidden')
  copy_btn?.style.setProperty('visibility', 'hidden')
  const prtHtml = (document.getElementById('order') as HTMLElement).innerHTML
  // Get all stylesheets HTML
  let stylesHtml = ''
  const stylesheets = document.querySelectorAll(
    'link[rel="stylesheet"], style',
  )
  stylesheets.forEach((node) => {
    stylesHtml += node.outerHTML
  })
  // Open the print window
  const WinPrint = window.open(
    '',
    '',
    'left=0,top=0,width=800,height=900,toolbar=0,scrollbars=0,status=0',
  ) as Window & { document: Document }

  WinPrint.document.write(`<!DOCTYPE html>
  <html dir="rtl">
    <head>
      ${stylesHtml}
    </head>
    <body>
      ${prtHtml}
    </body>
  </html>`)

  WinPrint.document.close()
  WinPrint.focus()
  WinPrint.addEventListener('load', async () => {
    copy_btn?.style.setProperty('visibility', 'visible')
    btn?.style.setProperty('visibility', 'visible')
    await WinPrint.print()
  })
}
function openInvoicePdf() {
  window.open(`/preview-invoices/${getOrderDetails.value.id}`, '_blank')
}
function sendInvoice() {
  invoiceProcessing.value = true
  sendInvoicePdf(getOrderDetails.value?.invoices?.id as string).finally(() => {
    invoiceProcessing.value = false
  })
}
function sendInvoiceWhatsApp(id: String) {
  invoiceProcessing.value = true
  sendWhatsAppInvoice(getOrderDetails.value?.invoices?.id as string).finally(
    () => {
      invoiceProcessing.value = false
    },
  )
}

function deleteOrderModal() {
  showConfModal.value = true
  processingbtn.value = true
}

const showPaymentLink = computed(() => {
  const customer = getOrderDetails.value.customer

  return !!(customer?.phone || customer?.email)
})

const refundItems = ref([])
const showRefundModal = ref(false)
const openRefundOrderModal = async () => {
  processing.value = true
  const items = await getRefundItems(getOrderDetails.value.id as string)
  refundItems.value = await items.data
  showRefundModal.value = true
  processing.value = false
}
const closeRefundModal = (orderId = null) => {
  showRefundModal.value = false
  if (orderId) {
    // router.push({ name: "order", params: { id: orderId } });
    window.open('/orders/' + `?order_id=${orderId}`, '_self')
    // fetchSingleOrder(orderId).then(()=>{
    //   processing.value = false;
    // }).catch(()=>{
    //   router.push({ path: "/orders" });
    // })
  }
}
const confirmed_status = ['confirmed', 'in-progress', 'completed', 'pending']
async function redirectTo(id: string) {
  if (id) {
    try {
      await fetchOrderDetails(id)
      router.push({ name: 'order', params: { id } })
    }
    catch (error) {
      router.push({ path: '/orders' })
    }
  }
}
const isOpenEditOrderItemsModal = ref(false)
const openEditOrderItemsModal = () => {
  isOpenEditOrderItemsModal.value = true
}
const closeEditItemsModal = () => {
  isOpenEditOrderItemsModal.value = false
}
</script>

<template>
  <div class="flex relative flex-col">
    <confirm-modal
      v-if="showConfirmInvoiceModal"
      :is-open="showConfirmInvoiceModal"
      :title="$t('create_invoice')"
      @cancel="showConfirmInvoiceModal = false"
      @confirm="creatorderInvoice(getOrderDetails.id)"
    >
      <template #body>
        <p
          class="flex gap-2 justify-end items-center text-base font-medium text-red-500 text-start"
        >
          {{ $t("create_invoice_body") }}
          <ExclamationTriangleIcon class="w-5 h-5" />
        </p>
      </template>
    </confirm-modal>
    <div class="flex justify-between">
      <BaseButton
        :disabled="getOrderDetails?.prev_order == null"
        class="border rounded-s-2xl rounded-e-2xl bg-slate-50"
        @click="redirectTo(getOrderDetails?.prev_order)"
      >
        <ArrowRightIcon class="w-4 h-4 text-black" />
        <span class="text-xs text-black">{{ $t("prev_order") }}</span>
      </BaseButton>
      <BaseButton
        :disabled="getOrderDetails?.next_order == null"
        class="text-xs border rounded-s-2xl rounded-e-2xl bg-slate-50"
        @click="redirectTo(getOrderDetails?.next_order)"
      >
        <span class="text-black">{{ $t("next_order") }}</span>
        <ArrowLeftIcon class="w-4 h-4 text-black" />
      </BaseButton>
    </div>
    <bread-crumb v-if="!processing" :crumbs="crumbs" class="mt-5" />
    <div class="flex gap-3 justify-end">
      <button
        v-if="getOrderDetails?.refundable"
        class="flex p-3 mx-2 font-bold text-white bg-rose-500 rounded-md"
        @click="openRefundOrderModal()"
      >
        {{ $t("refund_order") }}
      </button>
      <BaseButton
        v-if="!getOrderDetails.invoices"
        class="flex justify-center items-center px-4 py-2 mx-2 text-sm font-medium text-white rounded-md transition duration-100 ease-in cursor-pointer focus:transform active:scale-95"
        :default-style="false"
        custome-bg="bg-green-600 gap-2"
        @click="showConfirmInvoiceModal = true"
      >
        {{ $t("create_invoice") }}
        <icons name="invoice" aria-hidden="true" color="#FFF" class="" />
      </BaseButton>

      <div class="flex font-bold text-white bg-sky-500 rounded-md">
        <Listbox as="div" model-value="" class="relative w-full">
          <div class="">
            <ListboxButton
              class="flex justify-between items-center px-3 py-3 bg-sky-500 rounded-lg border border-gray-200 shadow-md cursor-pointer text-start focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <TheLoader v-if="invoiceProcessing" />
              <span v-else class="flex items-center rtl:left-auto rtl:right-0">
                <icons name="print" color="#FFF" />
              </span>
            </ListboxButton>

            <transition
              enter-active-class="transition duration-100 ease-out"
              enter-from-class="opacity-0 transform scale-95"
              enter-to-class="opacity-100 transform scale-100"
              leave-active-class="transition duration-75 ease-out"
              leave-from-class="opacity-100 transform scale-100"
              leave-to-class="opacity-0 transform scale-95"
            >
              <ListboxOptions
                class="overflow-auto absolute z-10 py-1 mt-1 w-60 max-h-80 text-base bg-white rounded-md ring-1 ring-black ring-opacity-5 shadow-lg focus:outline-none sm:text-sm"
                :style="
                  getLocale(locale)?.direction === 'ltr'
                    ? 'right: 0;'
                    : 'left: 0;'
                "
              >
                <ListboxOption
                  v-for="option in printOptions"
                  :key="option.id"
                  v-slot="{ active, selected }"
                  as="template"
                  :value="option"
                  @click="option.event()"
                >
                  <li
                    class="relative cursor-pointer  select-none py-2 px-2" :class="[
                      active ? 'bg-gray-600 text-white' : 'text-gray-900',
                    ]"
                  >
                    <div class="flex gap-3 justify-start items-center">
                      <span
                        class="flex justify-center items-center mx-1 w-44 h-6 text-gray-400 md:w-6 md:h-6"
                      >
                        <icons :name="option.icon" />
                      </span>
                      <span
                        class=" text-xs sm:text-sm" :class="[
                          selected ? 'font-semibold' : 'font-normal',
                        ]"
                      >
                        {{ option.name }}
                      </span>
                    </div>

                    <span
                      v-if="selected"
                      class="relative inset-y-0 right-0 flex items-center pr-4" :class="[
                        active ? 'text-white' : 'text-indigo-600',
                      ]"
                    >
                      <CheckIcon class="w-5 h-5" aria-hidden="true" />
                    </span>
                  </li>
                </ListboxOption>
              </ListboxOptions>
            </transition>
          </div>
        </Listbox>
      </div>
    </div>
    <overlay-loader v-if="processing" :full-screen="false" />
    <div v-else>
      <div id="order">
        <OrderInfoCard :order="getOrderDetails" @refresh="fetchOrderDetails" />
        <div class="mb-6">
          <div
            v-if="getOrderDetails.type !== 'refund'"
            class="grid grid-cols-1 gap-6 w-full xl:grid-cols-3 sm:grid-cols-2"
          >
            <OrderCustomerCard
              :customer="getOrderDetails.customer"
              :order-id="getOrderDetails.id"
              :editable="getOrderDetails.source != 'bookingPage'"
              :pin_code="
                settings.require_pin_code_to_close_order?.value == true
                  ? getOrderDetails.pin_code
                  : null
              "
            />
            <OrderPaymentCard
              :payments="getOrderDetails.payments"
              :order-id="getOrderDetails.id"
              :status="getOrderDetails.status?.status"
              :show-payment-link="showPaymentLink"
            />
            <OrderLocationCard
              v-if="getOrderDetails.branch.location == 'home-service'"
              :address="getOrderDetails.address"
              :long="getOrderDetails.longitude"
              :lat="getOrderDetails.latitude"
              :order-id="getOrderDetails.id"
              @update="fetchOrderDetails(getOrderDetails?.id)"
            />
          </div>
        </div>

        <RefundOrderItemsModal
          :is-open="showRefundModal"
          :order_id="getOrderDetails.id"
          :items="refundItems"
          @close="closeRefundModal"
        />

        <!-- Items -->
        <div class="grid grid-cols-1 mb-8">
          <div
            class="flex relative flex-col text-black rounded-lg border border-gray-200 text-md"
          >
            <OrderItemsTable
              :processing="processing"
              :items="getOrderDetails.items"
              @refresh="fetchOrderDetails"
            />

            <div
              class="flex z-10 gap-2 justify-between items-center px-5 py-3 text-lg font-medium text-black bg-gray-100"
            >
              <h3 class="font-medium text-black text-md">
                {{ $t("subtotal") }}
              </h3>
              <h3 class="font-medium text-black text-md">
                <price-format
                  :form-data="{
                    price: getOrderDetails.summary?.sub_total,
                    currency: user.tenant.currency || '',
                  }"
                />
              </h3>
            </div>
            <div
              v-if="getOrderDetails.summary?.discount_amount > 0"
              class="flex z-10 gap-2 justify-between items-center px-5 py-3 text-lg font-medium text-black bg-gray-100"
            >
              <h3 class="font-medium text-black text-md">
                {{ $t("pos.discount") }}
              </h3>
              <h3 class="font-medium text-black text-md">
                <price-format
                  :form-data="{
                    price: getOrderDetails.summary.discount_amount,
                    currency: user.tenant.currency || '',
                  }"
                />
              </h3>
            </div>

            <div
              v-if="getOrderDetails.summary?.coupon?.code"
              class="flex z-10 gap-2 justify-between items-center px-5 py-3 text-lg font-medium text-black bg-gray-100"
            >
              <h3 class="font-medium text-black text-md">
                {{ $t("coupon_if_applied") }}
                <span
                  v-if="
                    getOrderDetails.summary.coupon_discount_type == 'percentage'
                  "
                  class="text-sm text-underline"
                >
                  {{ getOrderDetails.summary.coupon_discount_amount }}%
                </span>
                <span
                  class="items-center px-2 py-1 text-green-800 bg-green-200 rounded-md"
                >{{ getOrderDetails.summary?.coupon?.code }}</span>
              </h3>
              <h3 class="font-medium text-black text-md">
                <price-format
                  :form-data="{
                    price: getOrderDetails.summary.coupon?.discount_amount,
                    currency: user.tenant.currency || '',
                  }"
                />
              </h3>
            </div>
            <div
              class="flex z-10 gap-2 justify-between items-center px-5 py-3 text-lg font-medium text-black bg-gray-100"
            >
              <h3 class="font-medium text-black text-md">
                {{ $t("total_order") }}
              </h3>
              <h3 class="font-medium text-black text-md">
                <price-format
                  :form-data="{
                    price: getOrderDetails.summary?.total_amount,
                    currency: user.tenant.currency || '',
                  }"
                />
              </h3>
            </div>
          </div>
        </div>

        <!-- Custom Fields custom_fields -->
        <div v-if="getOrderDetails.summary.transportation_fees" class="grid grid-cols-1 mb-8">
          <div class="flex relative flex-col text-black rounded-lg border border-gray-200 text-md">
            <div class="flex justify-between items-center px-2 py-4 text-lg text-black bg-gray-100">
              <span class="flex gap-2 items-center font-medium">
                <icons name="items" aria-hidden="true" color="#26405B" />
                {{ $t("order_fees") }}
              </span>
            </div>
            <table class="min-w-full divide-y divide-gray-300">
              <thead class="bg-gray-50">
                <tr>
                  <grid-th> {{ $t("fee_title") }} </grid-th>
                  <grid-th> {{ $t("fee_value") }} </grid-th>
                </tr>
              </thead>
              <tbody class="relative bg-white divide-y divide-gray-200">
                <tr>
                  <grid-td>{{ $t("form.boundary_fees") }}</grid-td>
                  <grid-td>{{ getOrderDetails.summary.transportation_fees }}</grid-td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Custom Fields custom_fields -->
        <div
          v-if="getOrderDetails.meta_data?.length > 0"
          class="grid grid-cols-1 mb-8"
        >
          <div
            class="flex relative flex-col text-black rounded-lg border border-gray-200 text-md"
          >
            <metaDataTable
              :processing="processing"
              :items="getOrderDetails.meta_data"
              :order-id="getOrderDetails?.id"
              :team-id="getOrderDetails?.branch?.uuid"
              @update="fetchOrderDetails(getOrderDetails?.id)"
            />
          </div>
        </div>

        <!-- Transactions Logs -->
        <div
          v-if="getOrderDetails.type !== 'refund'"
          class="grid grid-cols-1 mb-6"
        >
          <div
            v-if="Number(getOrderDetails.summary?.total_amount)"
            class="flex relative flex-col text-black rounded-lg border border-gray-200 text-md"
          >
            <orderTransactionsTable
              :transcations="getOrderDetails.payments.transcations"
              @refresh="fetchOrderDetails"
            />
          </div>
        </div>
      </div>
      <disv
        v-if="settings.require_upload_image_to_close_order?.value == true && getOrderDetails.images?.length > 0"
        class="grid grid-cols-1 mb-6"
      >
        <orderCloseImages :images="getOrderDetails.images" />
      </disv>

      <div v-if="notifications?.length > 0" class="grid grid-cols-1 mb-6">
        <orderNotifications :notifications="notifications" />
      </div>

      <!-- Refunded orders -->
      <div
        v-if="
          getOrderDetails.type !== 'refund'
            && getOrderDetails?.refunded_orders?.length > 0
        "
        class="grid grid-cols-1 mb-6"
      >
        <RefundsOrderRef :orders="getOrderDetails?.refunded_orders" />
      </div>

      <!-- Notes -->
      <div
        v-if="getOrderDetails.type !== 'refund'"
        class="grid grid-cols-1 mb-6"
      >
        <OrderNotes
          :note="getOrderDetails.note"
          :order-id="getOrderDetails?.id"
          @update="fetchOrderDetails"
        />
      </div>

      <!-- Order Logs -->
      <div class="grid grid-cols-1 mb-6">
        <orderLogs :order-log="getOrderDetails.activity_log" />
      </div>

      <div
        class="flex gap-y-8 justify-center items-center w-full rounded-md sm:w-auto"
      >
        <confirmation-modal
          v-if="showConfModal"
          :dir="getLocale(locale)?.direction"
          :is-open="showConfModal"
          redirect-url="/orders"
          :api-call="deleteOrder"
          :record-id="getOrderDetails?.id"
          @closed="showConfModal = false"
        >
          <p class="leading-7 text-center">
            {{ $t("confirmModal.deleteOder") }}
          </p>
        </confirmation-modal>
        <BaseButton
          v-if="!getOrderDetails.invoices"
          class="flex col-span-4 justify-center items-center px-4 py-2 text-sm font-medium text-white rounded-md transition duration-100 ease-in cursor-pointer md:col-span-2 focus:transform active:scale-95"
          custome-bg="bg-red-600"
          show-icon
          type="button"
          :processing="processingbtn"
          @click="showConfModal = true"
        >
          {{ $t("form.deleteOrder") }}
        </BaseButton>
      </div>
    </div>
  </div>
</template>
