<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { ChevronDownIcon, HomeIcon } from '@heroicons/vue/20/solid'
import type { Products } from '@/types/products'
import { useProductStore } from '@/stores/products'
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { fetchProductById, deleteProduct, removeImageFromProduct, addImageToProduct, makeMainImage } = useProductStore()
const { userInfo } = useAuthStore()
const processing = ref(false)
const route = useRoute()
const router = useRouter()

const crumbs = ref([
  {
    name: 'homepage.products',
    path: '/management/products',
  },
  {
    name: '',
    path: '',
  },
])
const product = ref<Omit<Products, 'uuid'>>({
  name: '',
  price: 0,
  image: null,
  category_id: '',
  team_id: '',
  description: '',
  display_on_booking_page: false,
})
const showModal = ref(false)
const showConfirmModal = ref(false)
const processingImageUpload = ref(false)

const toggleModel = () => {
  showModal.value = false
}
const updated = (resUpdated: Products) => {
  product.value = resUpdated
}
const productUuid = ref('')
onBeforeMount(() => {
  processing.value = true
  fetchProductById(route.params.id as string).then((res) => {
    product.value = res
    productUuid.value = product.value.uuid
    crumbs.value[crumbs.value.length - 1].name = `${res.name}`
  }).catch(() => {
    router.push({ path: '/products' })
  }).finally(() => {
    processing.value = false
  })
})
const openEditModal = () => {
  product.value.uuid = productUuid.value
  showModal.value = true
}

const deleteRecord = () => {
  if (!product.value?.uuid)
    return
  showConfirmModal.value = false
  deleteProduct(product.value?.uuid).then(() => {
    router.push({ path: '/products' })
  }).finally(() => {
    processing.value = false
  })
}
const duplicate = () => {
  product.value.uuid = ''
  showModal.value = true
}
const duplicated = computed(() => {
  return Boolean(!product.value.uuid)
})
function removeImage(imageIndex, image, confirmCallBack) {
  processingImageUpload.value = true
  removeImageFromProduct(product.value.uuid, image.id)
    .then((res) => {
      confirmCallBack()
    })
    .finally(() => {
      processingImageUpload.value = false
    })
}
function uploadImage(imageFormData: FormData, _, _1, imageUploaded) {
  processingImageUpload.value = true
  addImageToProduct(product.value.uuid, imageFormData)
    .then((res) => {
      imageUploaded(res.data.id)
    })
    .finally(() => {
      processingImageUpload.value = false
    })
}
function makeImageAsMain(imageIndex: number, image: [key: string, value: any]) {
  processingImageUpload.value = true
  makeMainImage(product.value.uuid, image.id)
    .then((_) => {
      fetchProductById(route.params.id as string).then((res) => {
        product.value = res
        crumbs.value[crumbs.value.length - 1].name = `${res.name}`
      }).catch(() => {
        router.push({ path: '/products' })
      }).finally(() => {
        processing.value = false
      })
    })
    .finally(() => {
      processingImageUpload.value = false
    })
}
</script>

<template>
  <div>
    <add-products-modal
      v-if="showModal"
      :duplicated="duplicated"
      :products="product"
      :title="duplicated ? 'duplicateProduct' : 'editProduct'"
      :show-modal="showModal"
      @updated="updated"
      @closed="toggleModel"
    />
    <confirmation-modal
      :dir="getLocale(locale)?.direction"
      :is-open="showConfirmModal" @removed="deleteRecord" @closed="showConfirmModal = false"
    >
      <p class="leading-7 text-start">
        {{ $t('confirmModal.msg') }}
      </p>
    </confirmation-modal>
    <div class="relative flex flex-col">
      <overlay-loader v-if="processing" :full-screen="false" />
      <bread-crumb :crumbs="crumbs" class="mt-5" />
      <!-- <div class="pb-8">
        <img
          v-if="product?.imageLink"
          id="image"
          class="block mx-auto mb-3 rounded-full w-36 h-36"
          :src="product.imageLink"
          :link="null"
        />
      </div> -->
      <div class="flex flex-col items-center justify-between gap-4 mt-6 mb-4 sm:flex-row sm:gap-0">
        <div class="flex items-center gap-1 sm:gap-3 ">
          <h1 class="text-base font-semibold sm:text-3xl">
            {{ product?.name }}
          </h1>
        </div>
        <div class="inline-flex w-1/4 rounded-md shadow-sm sm:w-auto">
          <button type="button" class="relative inline-flex items-center w-3/4 px-3 py-2 text-sm font-semibold text-gray-900 bg-white sm:w-auto rounded-l-md ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10 " :class="[getLocale(locale)?.direction === 'rtl' ? 'order-2' : 'order-1']" @click="openEditModal()">
            {{ $t('form.edit') }}
          </button>
          <!-- edit option select  -->
          <Menu as="div" class="relative block w-1/4 -ml-px sm:w-auto" :class="getLocale(locale)?.direction === 'rtl' ? 'order-1' : 'order-2' ">
            <MenuButton class="relative inline-flex items-center justify-center w-full px-2 py-2 text-gray-400 bg-white rounded-r-md ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10">
              <ChevronDownIcon class="w-5 h-5" aria-hidden="true" />
            </MenuButton>
            <transition enter-active-class="transition duration-100 ease-out" enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100" leave-active-class="transition duration-75 ease-in" leave-from-class="transform scale-100 opacity-100" leave-to-class="transform scale-95 opacity-0">
              <MenuItems class="absolute z-10 w-48 mt-2 -mr-1 origin-top-right bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none " :class="[getLocale(locale)?.direction === 'rtl' ? '-right-20' : '-left-40']">
                <div class="py-1">
                  <MenuItem>
                    <button
                      type="button"
                      class="block w-full p-2 text-sm text-gray-600 text-start"
                      @click="duplicate()"
                    >
                      {{ $t("form.duplicate") }}
                    </button>
                  </MenuItem>
                  <MenuItem>
                    <button type="button" class="block w-full p-2 text-sm text-red-600 text-start" @click="showConfirmModal = true">
                      {{ $t('form.deleteProduct') }}
                    </button>
                  </MenuItem>
                </div>
              </MenuItems>
            </transition>
          </Menu>
        </div>
      </div>
      <div class="pb-8">
        <!-- <img v-if="product?.imageLink" id="image" class="block mx-auto mb-3 rounded-full w-36 h-36" :src="product.imageLink" :link="null"> -->

        <div class="grid grid-cols-1 sm:grid-cols-2">
          <div class="flex items-center gap-2 py-3 border-b border-stone-100">
            <span class="block text-sm font-medium text-neutral-500 w-15">{{
              $t("products.name")
            }} </span>
            <div class="flex mt-1 font-semibold">
              <div class="relative flex items-stretch flex-grow focus-within:z-10">
                {{ product?.name }}
              </div>
            </div>
          </div>
          <div class="flex items-center gap-2 py-3 border-b border-stone-100">
            <span class="block text-sm font-medium text-neutral-500 w-15 ">{{
              $t("form.category")
            }}  </span>
            <div class="flex items-center gap-1 mt-1 font-semibold">
              <span v-if="product?.category_id" class="">
                {{ product?.category_id?.name }}
              </span>

              <span v-else class="text-neutral-400">-</span>
            </div>
          </div>
          <div class="flex items-center gap-2 py-3 border-b border-stone-100">
            <span class="block text-sm font-medium text-neutral-500 w-15">{{ $t("price") }} </span>
            <div class="mt-1 font-semibold">
              {{ product?.price.toFixed(2) }}
              <span class="text-gray-500 sm:text-sm ms-2">{{ $t(`currenices.${userInfo.tenant?.currency}`) }}</span>
            </div>
          </div>
          <div class="flex items-center gap-2 py-3 border-b border-stone-100">
            <span class="block text-sm font-medium text-neutral-500 w-15">{{ $t("modalPlacholder.branch") }} :</span>
            <div v-if="product?.team?.uuid" class="mt-1 font-semibold">
              {{ product?.team?.name }}
            </div>
            <span v-else>-</span>
          </div>
          <div class="flex items-center gap-2 py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("displayOnBookingPage") }}
            </label>
            <div class="mt-1 font-semibold">
              {{ product?.display_on_booking_page ? $t("yes") : $t("no") }}
            </div>
          </div>
          <div class="flex items-center gap-2 py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("form.sort_order") }}
            </label>
            <div class="mt-1 font-semibold">
              {{ product?.sort_order }}
            </div>
          </div>
          <div class="flex items-center gap-2 py-3 border-b border-stone-100">
            <label
              for="booking-start"
              class="block text-sm font-medium text-neutral-500 w-15"
            >{{ $t("description") }}
            </label>
            <div
              v-if="product?.description?.length > 0"
              class="mt-1 font-semibold"
            >
              <p>{{ product?.description }}</p>
            </div>
            <span v-else class="text-neutral-400">-</span>
          </div>
        </div>
        <div class="w-full my-4">
          <ShareBox :link="product.full_link" />
        </div>
        <div class="w-full my-4">
          <h2 class="mb-4 text-2xl font-semibold">
            {{ $t(`images`) }}
          </h2>

          <images-uploader
            :data-images="product?.images"
            class="flex w-full"
            small-images-container-classes="mt-0 !max-w-full gap-2"
            small-images-classes="h-full !w-[190px] !h-[180px]"
            :show-controls-for-single-image="true"
            add-button-classes="!w-[190px] !h-[180px] relative rounded-md border-2 border-gray-300 flex relative text-center border border-gray-300 rounded-md items-center justify-center"
            :show-edit="false"
            :loading="processingImageUpload"
            :drag-text="$t('drag_product_image')"
            browse-text=""
            :drop-text="$t('drop_product_image')"
            id-upload="product"
            :show-custom-icons="true"
            @before-remove="removeImage"
            @before-upload="uploadImage"
            @custom-action="makeImageAsMain"
          >
            <template #custom-button="{ image }">
              <button
                class="flex items-center justify-center w-full h-full p-2 text-sm font-medium text-blue-500 rounded-md"
                :title="image?.is_main_image ? $t('main_image') : $t('make_main_image') "
                :class="{
                  'opacity-50	event-none': Boolean(image?.is_main_image),
                }"
              >
                <HomeIcon class="w-10 h-10 " aria-hidden="true" />
              </button>
            </template>
          </images-uploader>
        </div>
      </div>
    </div>
  </div>
</template>

<style>

</style>
