<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { onBeforeMount, onBeforeUnmount, ref, watch } from 'vue'
import TheLoader from '@/components/TheLoader.vue'
import { useWhatsapp } from '@/stores/whatsapp'

const { checkStatus, activate, deactivate, qrCode } = useWhatsapp()
const { getIsActivated, getProcessing, getStatus, getPhone } = storeToRefs(useWhatsapp())

const loadingQrCode = ref(false)
const qrCodeSrc = ref<string | null>(null)
const isCheckingStatus = ref(false)

watch(() => getStatus.value, (value) => {
  if (value === 'SCAN_QR_CODE') {
    loadingQrCode.value = true
    qrCode().then((res) => {
      qrCodeSrc.value = URL.createObjectURL(res.data)
    }).finally(() => {
      loadingQrCode.value = false
    })

    intervalId.value = window.setInterval(() => {
      checkStatusWithInterval()
    }, 5000)
  }
  else if (value === 'WORKING') {
    qrCodeSrc.value = null
  }
})

const checkStatusWithInterval = () => {
  if (!isCheckingStatus.value && getStatus.value !== 'WORKING') {
    isCheckingStatus.value = true
    checkStatus().finally(() => {
      isCheckingStatus.value = false
    })
  }
}

let intervalId = ref<number | undefined>()

onBeforeMount(() => {
  checkStatusWithInterval()
})

onBeforeUnmount(() => {
  if (intervalId.value)
    clearInterval(intervalId.value)
})
</script>

<template>
  <div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Main Container - constrained on large screens, full width on mobile -->
    <div class="max-w-4xl">
      <div class="flex flex-col gap-6 sm:gap-8">
        <div class="sm:flex sm:items-center">
          <div class="sm:flex-auto">
            <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl">
              {{ $t("whatsapp.title") }}
            </h1>
          </div>
        </div>

        <!-- Control Section -->
        <div class="flex flex-col gap-4 w-full sm:flex-row sm:justify-between sm:items-center">
          <div class="text-sm sm:text-base">
            {{ $t('whatsapp.description') }}
          </div>
          <div class="flex justify-center sm:justify-end">
            <!-- Enhanced loading indicator for processing - matches button styling -->

            <BaseButton
              v-if="getIsActivated"
              :processing="getProcessing && !isCheckingStatus"
              class="w-full sm:w-auto min-w-[120px]"
              @click="deactivate"
            >
              {{ $t(`whatsapp.deactivate`) }}
            </BaseButton>
            <BaseButton
              v-else
              :processing="getProcessing"
              class="w-full sm:w-auto min-w-[120px]"
              @click="activate"
            >
              {{ $t(`whatsapp.activate`) }}
            </BaseButton>
          </div>
        </div>
        <div v-if="getIsActivated" class="w-full">
          <div v-if="getStatus === 'SCAN_QR_CODE'">
            <!-- Enhanced QR Code loading state -->
            <div v-if="loadingQrCode" class="relative">
              <div class="flex flex-col justify-center items-center px-6 py-12">
                <!-- Skeleton placeholder for QR code -->
                <div class="relative p-4 bg-white rounded-lg border-2 border-blue-300 border-dashed shadow-lg">
                  <div class="w-64 h-64 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg animate-pulse" />
                  <!-- Subtle loading overlay -->
                  <div class="flex absolute inset-0 justify-center items-center bg-white bg-opacity-80 rounded-lg">
                    <div class="flex flex-col items-center space-y-3">
                      <div class="relative">
                        <div class="w-8 h-8 rounded-full border-4 border-blue-200" />
                        <div class="absolute top-0 left-0 w-8 h-8 rounded-full border-4 border-blue-500 animate-spin border-t-transparent" />
                      </div>
                      <span class="text-sm font-medium text-blue-600">{{ $t('whatsapp.loading_qr') }}</span>
                    </div>
                  </div>
                </div>
                <div class="flex items-center mt-4 space-x-2 text-gray-500">
                  <div class="w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse" />
                  <span class="text-sm">{{ $t('whatsapp.generating_qr_code') }}</span>
                </div>
              </div>
            </div>
            <div v-else-if="qrCodeSrc">
              <div class="text-center">
                <h4 class="px-2 mb-4 text-base font-medium text-gray-900 sm:text-lg">
                  {{ $t('whatsapp.installation_guide.scan_instruction') }}
                </h4>
                <div class="inline-block relative p-3 bg-white rounded-lg border-2 border-blue-300 border-dashed shadow-lg sm:p-4">
                  <img
                    :src="qrCodeSrc"
                    alt="QR Code"
                    class="mx-auto max-w-[250px] sm:max-w-[300px] w-full h-auto"
                  >
                  <!-- Status checking indicator -->
                  <div v-if="isCheckingStatus" class="absolute top-2 right-2">
                    <div class="flex items-center px-2 py-1 space-x-1 bg-blue-100 rounded-full">
                      <div class="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse" />
                      <span class="text-xs text-blue-600">{{ $t('whatsapp.checking') }}</span>
                    </div>
                  </div>
                </div>
                <p class="px-2 mt-3 text-xs text-gray-600 sm:text-sm">
                  {{ $t('whatsapp.installation_guide.scan_instruction') }}
                </p>
              </div>
            </div>
            <div v-else>
              <div class="p-4 text-center">
                <strong class="text-sm font-bold text-red-700 sm:text-base">
                  {{ $t('whatsapp.error_loading_qrcode') }}
                </strong>
              </div>
            </div>
          </div>
          <div v-else-if="getStatus === 'WORKING'">
            <div class="overflow-hidden relative bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200 shadow-lg">
              <!-- Success Icon Background -->
              <div class="absolute top-0 right-0 w-20 h-20 opacity-10 transform translate-x-6 -translate-y-6">
                <div class="w-full h-full bg-green-500 rounded-full" />
              </div>

              <!-- Main Content -->
              <div class="relative p-6 sm:p-8">
                <div class="flex justify-center items-center mb-4">
                  <!-- Success Checkmark Icon -->
                  <div class="flex justify-center items-center w-16 h-16 bg-green-500 rounded-full shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>

                <!-- Status Text -->
                <div class="text-center">
                  <h3 class="mb-2 text-lg font-bold text-green-800 sm:text-xl">
                    {{ $t('whatsapp.working_status.success_title') }}   🎉
                  </h3>
                  <div v-if="getPhone" class="flex justify-center items-center space-x-2 rtl:space-x-reverse">
                    <span class="text-sm font-medium text-green-700 sm:text-base">{{ $t('whatsapp.working_status.connected_phone_label') }}</span>
                    <span class="px-3 py-1 text-sm font-bold text-green-800 bg-green-100 rounded-full border border-green-300 sm:text-base">
                      {{ getPhone }}
                    </span>
                  </div>

                  <!-- Status Indicator -->
                  <div class="flex justify-center items-center mt-4 space-x-2 rtl:space-x-reverse">
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    <span class="text-xs font-medium text-green-600 sm:text-sm">{{ $t('whatsapp.working_status.status_connected') }}</span>
                  </div>
                </div>
              </div>

              <!-- Decorative Bottom Border -->
              <div class="h-1 bg-gradient-to-r from-green-400 to-emerald-500" />
            </div>
          </div>
        </div>

        <!-- Installation Steps Section - Always visible -->
        <div v-if="!getIsActivated || getStatus === 'SCAN_QR_CODE'" class="w-full">
          <div class="p-4 mb-6 bg-blue-50 rounded-lg border border-blue-200 sm:p-6 sm:mb-8">
            <div class="">
              <!-- Installation Instructions -->
              <div class="mx-auto max-w-full sm:max-w-2xl text-start">
                <h3 class="flex justify-center items-center mb-4 text-base font-semibold text-blue-900 sm:text-lg">
                  <span class="ms-2">🟢</span>
                  {{ $t('whatsapp.installation_guide.title') }}
                </h3>
                <div class="mb-3 text-sm text-blue-800 sm:text-base text-start">
                  <span class="font-medium">📱</span> {{ $t('whatsapp.installation_guide.method') }}
                </div>
                <ol class="space-y-3 text-sm text-left text-gray-700 sm:space-y-2 sm:text-base text-start">
                  <li class="flex items-start">
                    <span class="flex-shrink-0 mt-0.5 font-bold text-blue-600 ms-2">1.</span>
                    <span>{{ $t('whatsapp.installation_guide.step1') }}</span>
                  </li>
                  <li class="flex items-start">
                    <span class="flex-shrink-0 mt-0.5 font-bold text-blue-600 ms-2">2.</span>
                    <span>{{ $t('whatsapp.installation_guide.step2') }}</span>
                  </li>
                  <li class="flex items-start">
                    <span class="flex-shrink-0 mt-0.5 font-bold text-blue-600 ms-2">3.</span>
                    <span>{{ $t('whatsapp.installation_guide.step3') }}</span>
                  </li>
                  <li class="flex items-start">
                    <span class="flex-shrink-0 mt-0.5 font-bold text-blue-600 ms-2">4.</span>
                    <span>{{ $t('whatsapp.installation_guide.step4') }}</span>
                  </li>
                </ol>
                <div class="p-3 mt-4 bg-yellow-50 rounded-md border-l-4 border-yellow-400">
                  <p class="text-xs text-yellow-800 sm:text-sm">
                    <span class="font-medium">💡</span>
                    {{ $t('whatsapp.installation_guide.note') }}
                  </p>
                </div>
              </div>

              <!-- Visual Guide Image -->
              <div class="mt-6">
                <img
                  src="@/assets/whatsapp_install.png"
                  alt="WhatsApp Installation Steps"
                  class="mx-auto w-full max-w-sm h-auto rounded-lg border border-gray-200 shadow-md sm:max-w-md lg:max-w-lg"
                >
                <p class="px-2 mt-2 text-xs text-gray-600 sm:text-sm">
                  {{ $t('whatsapp.installation_guide.visual_guide') }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
