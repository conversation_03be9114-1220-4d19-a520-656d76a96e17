<script setup lang="ts">
import { CheckIcon } from '@heroicons/vue/20/solid'
import { storeToRefs } from 'pinia'
const { getOnboarding, changeStepManually } = useOnBoardingStore()
const { getCurrentStep, processing } = storeToRefs(useOnBoardingStore())
const { logout, hasUserData } = useAuthStore()

const { t } = useI18n()
const mahjozFeatures = [
  'mahjozFeatures.Comprehensive_Booking_Scheduling_Management_System',
  'mahjozFeatures.Online_payment_invoices',
  'mahjozFeatures.integrationed_online_store',
]
const ourClientsImages = [
  'pa.svg',
  'ro.svg',
  'ti.svg',
  'zo.svg',
  'fr.svg',
]
const renderCLientImage = (image) => {
  const imageUrl = new URL(
    `../assets/${image}`,
    import.meta.url,
  ).href
  return imageUrl
}
const router = useRouter()
const goToSignIn = () => {
  if (hasUserData.value) {
    logout().then(() => {
      router.push({ name: 'auth', query: { section: 'sign-in' } })
    })
  }
  else {
    router.push({ name: 'auth', query: { section: 'sign-in' } })
  }
}
</script>

<template>
  <div class="flex min-h-full">
    <div
      class="flex flex-col flex-1 justify-center py-12 ps-4 pe-4 sm:px-6 lg:px-20 xl:px-24 "
    >
      <div class="text-center">
        <img
          class="w-auto mx-auto h-12"
          src="../assets/logo.svg"
          alt="Your Company"
        >
        <div class="flex flex-col bg-primary-300 text-white rounded-lg p-4 my-5 min-w-fit lg:w-1/2 md:w-1/2	xl:hidden block">
          <h2 class="text-sm font-semibold mb-1 leading-6">
            {{ $t("sign_up_description") }}
          </h2>
          <p class="text-center my-3 font-semibold text-primary-800">
            {{ $t('trail_days') }} !
          </p>
        </div>
        <h2 class="mt-6 text-2xl font-bold tracking-tight text-gray-900">
          {{ $t("form.signupFor") }}
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          {{ $t("homepage.or") }}
          <span
            class="font-medium text-primary-600 cursor-pointer hover:text-primary-500"
            @click="goToSignIn"
          >{{ $t("form.signinHere") }}</span>
        </p>
      </div>
      <div class="sm:px-10">
        <WizerdForm />
      </div>
    </div>
    <div class="relative flex-1 hidden w-0 xl:block bg-primary">
      <div class="flex flex-col px-10 py-6 items-center">
        <div class="flex justify-center items-center gap-6">
          <img
            class="w-auto h-12   rounded-squircle"
            src="../assets/mahjozLogoWithoutWord.png"
            alt="Your Company"
          >
        </div>
        <div class="flex flex-col bg-primary-300 text-white rounded-lg p-10 mt-16 min-w-fit lg:w-1/2 md:w-1/2	">
          <h2 class="text-md font-semibold mb-1">
            {{ $t("sign_up_description") }}
          </h2>

          <ul class="mt-3">
            <li v-for="feature in mahjozFeatures" class="flex gap-3 items-center mb-4">
              <CheckIcon class="w-5 h-5 text-green-400 " />
              <span>
                {{ $t(feature) }}
              </span>
            </li>
          </ul>
          <p class="text-center my-3 font-semibold text-primary-800">
            {{ $t('trail_days') }} !
          </p>
        </div>
      </div>
      <div class="text-white flex flex-col px-10 py-6 items-center">
        <p class="text-lg font-semibold">
          {{ $t("our_clients") }}
        </p>
        <ul class="mt-3 flex flex-wrap justify-center gap-10">
          <li v-for="image in ourClientsImages" :key="image" class="flex gap-3 items-center mb-4">
            <img :src="renderCLientImage(image)" class="sm:w-12 sm:h-12 w-8 h-8">
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
