<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import useVuelidate from '@vuelidate/core'
import { ShieldExclamationIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import { email, minLength, required, sameAs } from '@/utils/i18n-validators'
import i18n from '@/i18n'
import { useAuthStore } from '@/stores/auth'
const { UpdateProfile, updateAccountPassword, sendVerificationLink, updateAccountEmail } = useAuthStore()
const { getUserInfo } = storeToRefs(useAuthStore())
const { locale } = useI18n()
const processing = ref(false)
const passprocessing = ref(false)
const email_verified = ref<Boolean>(false)
const overProcessing = ref(false)
const formData = reactive({
  name: '',
  phone: '',
  phone_country: '',
  profile_photo: null,
  lang: '',
  email: '',
})
const formPassword = reactive({
  current_password: '',
  password: '',
  password_confirmation: '',
})
const formEmail = reactive({
  new_email: '',
  current_password: '',
})

const formRules = {
  name: {
    required,
    minLength: minLength(3),
  },
  phone: {
    required,
  },
}
const formPasswordRules = {
  current_password: {
    required,
    minLength: minLength(8),
  },
  password: {
    required,
    minLength: minLength(8),
  },
  password_confirmation: {
    required,
    sameAs: sameAs(computed(() => formPassword.password)),
  },
}
const formEmailRules = {
  new_email: {
    required,
    email,
  },
  current_password: {
    required,
    minLength: minLength(8),
  },
}
const v1$ = useVuelidate(formRules, formData)
const v$ = useVuelidate(formPasswordRules, formPassword)
const vEmail$ = useVuelidate(formEmailRules, formEmail)

const languages = [
  { name: 'Arabic', id: 'ar' },
  { name: 'English', id: 'en' },
]

const userProfileImg = ref<String>('')
const sendingLink = ref(false)

watch(
  () => getUserInfo.value,
  (val) => {
    formData.name = val?.name || ''
    formData.phone = val?.phone || ''
    formData.phone_country = val?.phone_country || ''
    formData.lang = getUserInfo.value?.lang
    formData.email = val?.email || ''
    email_verified.value = val?.email_verified || false
    userProfileImg.value = val?.profile_photo || ''
  },
  { deep: true, immediate: true },
)

const updateProfileInfo = async () => {
  v1$.value.$touch()
  if (v1$.value.$invalid)
    return
  processing.value = true
  UpdateProfile(formData)
    .then(() => {
      userProfileImg.value = getUserInfo.value?.profile_photo || ''
      formData.profile_photo = null
      langChanged()
      v1$.value.$reset()
    })
    .finally(() => {
      processing.value = false
    })
}

const updatePassword = async () => {
  v$.value.$touch()
  if (v$.value.$invalid)
    return
  passprocessing.value = true
  updateAccountPassword(formPassword)
    .then(() => {
      resetPasswordForm()
      v$.value.$reset()
    })
    .finally(() => {
      passprocessing.value = false
    })
}
const resetPasswordForm = () => {
  formPassword.current_password = ''
  formPassword.password = ''
  formPassword.password_confirmation = ''
}
const resetEmailForm = () => {
  formEmail.new_email = ''
  formEmail.current_password = ''
}

const updateEmail = async () => {
  vEmail$.value.$touch()
  if (vEmail$.value.$invalid)
    return
  passprocessing.value = true
  updateAccountEmail(formEmail)
    .then(() => {
      resetEmailForm()
      vEmail$.value.$reset()
    })
    .finally(() => {
      passprocessing.value = false
    })
}
const setPhoneNumber = (phoneNumber: string, phoneObject: { countryCode: string }) => {
  formData.phone = phoneNumber
  formData.phone_country = phoneObject.countryCode
}
const langChanged = () => {
  i18n.global.locale.value = formData.lang
  localStorage.setItem('selected-locale', JSON.stringify(i18n.global.locale.value))
}

const resendLink = () => {
  sendingLink.value = true
  sendVerificationLink().finally(() => {
    sendingLink.value = false
  })
}
</script>

<template>
  <div class="relative">
    <overlay-loader v-if="overProcessing" :full-screen="false" />
    <AppWideAlert
      v-if="!email_verified"
      class="text-white bg-yellow-500 absolute inset-x-0 top-0"
    >
      <ShieldExclamationIcon class="h-5 w-5 mx-2" />
      <span>{{ $t("unverified_email.alert") }}</span>
      <a
        :class="locale === 'ar' ? 'mr-2 font-bold' : 'ml-2 font-bold'"
        href=""
        @click.prevent="resendLink"
      >
        <span v-if="!sendingLink">{{ $t("unverified_email.resend") }}</span>
        <svg
          v-else
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
          class="w-6 h-6"
        >
          <path
            class="spinner_0XTQ"
            d="M12,23a9.63,9.63,0,0,1-8-9.5,9.51,9.51,0,0,1,6.79-9.1A1.66,1.66,0,0,0,12,2.81h0a1.67,1.67,0,0,0-1.94-1.64A11,11,0,0,0,12,23Z"
          />
        </svg>
      </a>
    </AppWideAlert>
    <div class="bg-gray-100 h-screen">
      <div class="mt-10 sm:mt-0 bg-gray-100 py-6 px-5 rounded-md">
        <div class="md:grid md:grid-cols-3 md:gap-6">
          <div class="md:col-span-1">
            <div class="px-4 sm:px-0">
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                {{ $t("profile.personal") }}
              </h3>
            </div>
          </div>
          <div class="mt-5 md:col-span-2 md:mt-0">
            <form @submit.prevent="updateProfileInfo">
              <div class="overflow-hidden shadow sm:rounded-md">
                <div class="bg-white px-4 py-5 sm:p-6">
                  <div class="grid grid-cols-6 gap-6 mb-8">
                    <div class="col-span-6 sm:col-span-3">
                      <form-group :validation="v1$" name="name">
                        <template #default="{ attrs }">
                          <TextInput
                            v-bind="attrs"
                            id="user-name"
                            v-model="formData.name"
                            :label="$t('form.name')"
                            :placeholder="$t('formPlaceHolder.name')"
                            custom-classes="mt-1"
                          />
                        </template>
                      </form-group>
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                      <TextInput
                        id="first-name"
                        v-model="formData.email"
                        :label="$t('form.email')"
                        :placeholder="$t('formPlaceHolder.email')"
                        readonly
                        autocomplete="email"
                        custom-classes="mt-1 bg-gray-100"
                      />
                    </div>
                  </div>
                  <div class="grid grid-cols-6 gap-6 mb-8">
                    <div class="col-span-6 sm:col-span-3">
                      <form-group :validation="v1$" name="phone">
                        <template #default="{ attrs }">
                          <PhoneInput
                            v-bind="attrs"
                            :model-value="formData.phone"
                            label="form.phone"
                            mode="international"
                            @update:model-value="setPhoneNumber"
                          />
                        </template>
                      </form-group>
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                      <SelectInput
                        id="lang"
                        v-model="formData.lang"
                        :label="$t('profile.language')"
                      >
                        <option
                          v-for="lang in languages"
                          id="lang"
                          :key="lang.name"
                          :value="lang.id"
                        >
                          {{ lang.name }}
                        </option>
                      </SelectInput>
                    </div>
                  </div>
                  <ImageInput
                    v-model="formData.profile_photo"
                    :link="getUserInfo.profile_photo"
                    name="profile_photo"
                  />
                </div>
                <div class="bg-gray-50 px-4 py-3 text-right sm:px-6">
                  <BaseButton :processing="processing" class="w-fit ms-auto">
                    {{ $t("form.update") }}
                  </BaseButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
      <!-- divider -->
      <div class="hidden sm:block" aria-hidden="true">
        <div class="py-5">
          <div class="border-t border-gray-200" />
        </div>
      </div>

      <div class="mt-10 sm:mt-0 bg-gray-100 py-6 px-5 rounded-md">
        <div class="md:grid md:grid-cols-3 md:gap-6">
          <div class="md:col-span-1">
            <div class="px-4 sm:px-0">
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                {{ $t("profile.change_email") }}
              </h3>
            </div>
          </div>
          <div class="mt-5 md:col-span-2 md:mt-0">
            <form @submit.prevent="updateEmail">
              <div class="overflow-hidden shadow sm:rounded-md">
                <div class="bg-white px-4 py-5 sm:p-6">
                  <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 lg:col-span-3 xl:col-span-2">
                      <form-group :validation="vEmail$" name="new_email">
                        <template #default="{ attrs }">
                          <TextInput
                            v-bind="attrs"
                            id="new-email"
                            v-model="formEmail.new_email"
                            :label="$t('form.new_email')"
                            autocomplete="email"
                            custom-classes="mt-1"
                          />
                        </template>
                      </form-group>
                    </div>

                    <div class="col-span-6 lg:col-span-3 xl:col-span-2">
                      <form-group :validation="vEmail$" name="current_password">
                        <template #default="{ attrs }">
                          <TextInput
                            v-bind="attrs"
                            id="current-password-email"
                            v-model="formEmail.current_password"
                            :label="$t('form.currentpassword')"
                            type="password"
                            autocomplete="current-password"
                            custom-classes="mt-1"
                          />
                        </template>
                      </form-group>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 text-right sm:px-6">
                  <BaseButton :processing="passprocessing" class="w-fit ms-auto">
                    {{ $t("form.update") }}
                  </BaseButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="hidden sm:block" aria-hidden="true">
        <div class="py-5">
          <div class="border-t border-gray-200" />
        </div>
      </div>

      <div class="mt-10 sm:mt-0 bg-gray-100 py-6 px-5 rounded-md">
        <div class="md:grid md:grid-cols-3 md:gap-6">
          <div class="md:col-span-1">
            <div class="px-4 sm:px-0">
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                {{ $t("profile.passwords") }}
              </h3>
            </div>
          </div>
          <div class="mt-4 md:col-span-2 md:mt-0">
            <form @submit.prevent="updatePassword">
              <div class="overflow-hidden shadow sm:rounded-md">
                <div class="bg-white px-4 py-5 sm:p-6">
                  <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 lg:col-span-6 xl:col-span-2">
                      <form-group name="current_password" :validation="v$">
                        <template #default="{ attrs }">
                          <PasswordInput
                            v-bind="attrs"
                            id="current-password"
                            v-model="formPassword.current_password"
                            :label="$t('form.currentpassword')"
                            :placeholder="$t('formPlaceHolder.currentPass')"
                          />
                        </template>
                      </form-group>
                    </div>

                    <div class="col-span-6 lg:col-span-3 xl:col-span-2">
                      <form-group name="password" :validation="v$">
                        <template #default="{ attrs }">
                          <PasswordInput
                            v-bind="attrs"
                            id="new-password"
                            v-model="formPassword.password"
                            :label="$t('form.newpassword')"
                            :placeholder="$t('formPlaceHolder.newPass')"
                          />
                        </template>
                      </form-group>
                    </div>

                    <div class="col-span-6 lg:col-span-3 xl:col-span-2">
                      <form-group name="password_confirmation" :validation="v$">
                        <template #default="{ attrs }">
                          <PasswordInput
                            v-bind="attrs"
                            id="password_confirmation"
                            v-model="formPassword.password_confirmation"
                            :label="$t('form.newpasswordconfirmation')"
                            :placeholder="$t('formPlaceHolder.confirmPass')"
                          />
                        </template>
                      </form-group>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 text-right sm:px-6">
                  <BaseButton :processing="passprocessing" class="w-fit ms-auto">
                    {{ $t("form.update") }}
                  </BaseButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.spinner_0XTQ {
  transform-origin: center;
  animation: spinner_AtaB 0.75s infinite linear
}

@keyframes spinner_AtaB {
  100% {
    transform: rotate(360deg)
  }
}
</style>
