<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { CheckCircleIcon } from '@heroicons/vue/24/solid'
import { CircleStackIcon, ClockIcon, CreditCardIcon, CurrencyDollarIcon, DocumentCheckIcon, PaintBrushIcon, UserGroupIcon } from '@heroicons/vue/24/outline'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'
import AuthService from '@/services/AuthService'
import { getMobileOperatingSystem } from '@/utils/mobileDetecation'
import { useAuthStore } from '@/stores/auth'
const router = useRouter()
const authStore = useAuthStore()
const { getUserInfo } = storeToRefs(useAuthStore())
const { t } = useI18n()

const onboardingStatus = ref({
  store_configured: false,
  booking_page_configured: false,
  services_created: false,
  staff_added: false,
  working_hours_setup: false,
  payment_methods_setup: false,
  plans_explored: false,
})

const baseSteps = [
  {
    id: 'customize_store',
    titleKey: 'onbord.customize_store_title',
    descriptionKey: 'onbord.customize_store_desc',
    icon: PaintBrushIcon,
    route: '/store/appearance',
    isCompleted: computed(() => onboardingStatus.value.booking_page_configured),
    forTenantType: 'store',
  },
  {
    id: 'choose_design',
    titleKey: 'onbord.choose_design_title',
    descriptionKey: 'onbord.choose_design_desc',
    icon: CircleStackIcon,
    route: '/settings/general',
    isCompleted: computed(() => onboardingStatus.value.store_configured),
    forTenantType: 'store',
  },
  {
    id: 'add_first_product',
    titleKey: 'onbord.add_first_product_title',
    descriptionKey: 'onbord.add_first_product_desc',
    icon: CurrencyDollarIcon,
    route: '/management/services',
    isCompleted: computed(() => onboardingStatus.value.services_created),
  },
  {
    id: 'add_staff',
    titleKey: 'onbord.add_staff_title',
    descriptionKey: 'onbord.add_staff_desc',
    icon: UserGroupIcon,
    route: '/management/staffs',
    isCompleted: computed(() => onboardingStatus.value.staff_added),
  },
  {
    id: 'set_working_hours',
    titleKey: 'onbord.set_working_hours_title',
    descriptionKey: 'onbord.set_working_hours_desc',
    icon: ClockIcon,
    route: '/settings/advanced',
    isCompleted: computed(() => onboardingStatus.value.working_hours_setup),
  },
  {
    id: 'setup_payment',
    titleKey: 'onbord.setup_payment_title',
    descriptionKey: 'onbord.setup_payment_desc',
    icon: CreditCardIcon,
    route: '/settings/payments',
    isCompleted: computed(() => onboardingStatus.value.payment_methods_setup),
  },
  {
    id: 'subscribe_plan',
    titleKey: 'onbord.subscribe_plan_title',
    descriptionKey: 'onbord.subscribe_plan_desc',
    icon: DocumentCheckIcon,
    route: '/settings/subscriptions-panel/plans',
    isCompleted: computed(() => onboardingStatus.value.plans_explored),
  },
]

const onboardingSteps = computed(() => {
  // Get tenant type from the correct path
  const tenantType = authStore.onboardingData?.systemType
  console.log('Current tenant type:', tenantType)

  return baseSteps.filter((step) => {
    // If step has forTenantType property, show it only for that tenant type
    if (step.forTenantType)
      return step.forTenantType === tenantType

    // If no forTenantType specified, show for all tenant types
    return true
  })
})

const completionProgress = computed(() => {
  const totalSteps = onboardingSteps.value.length
  const completedSteps = onboardingSteps.value.filter(step => step.isCompleted.value).length
  const percentage = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0
  return { completedSteps, totalSteps, percentage }
})

const isOnboardingComplete = ref(true)

const navigateToStep = (route: string) => {
  router.push(route)
}

const skipOnboarding = async () => {
  // Simply mark all steps as complete in the local state
  Object.keys(onboardingStatus.value).forEach((key) => {
    onboardingStatus.value[key] = true
  })
  isOnboardingComplete.value = true
  await authStore.skipOnboarding()
  await authStore.getProfile()
  // Update the completion status
}

const fetchOnboardingStatus = async () => {
  try {
    const response = await AuthService.updateOnboardingStatus()
    onboardingStatus.value = response.data
    const onboardingSteps = Object.keys(response.data)
    isOnboardingComplete.value = onboardingSteps.every(step => response.data[step])
  }
  catch (error) {
    console.error('Failed to fetch onboarding status:', error)
  }
}

onBeforeMount(async () => {
  await fetchOnboardingStatus()
})

const appUrl = computed(() => {
  const mobileOS = getMobileOperatingSystem()
  if (mobileOS === 'iOS')
    return 'https://apps.apple.com/us/app/mahjoz-%D9%85%D8%AD%D8%AC%D9%88%D8%B2/id6481259819'
  return 'https://play.google.com/store/apps/details?id=app.mahjoz.io&hl=en&pli=1'
})
const openAppStore = () => {
  window.open(appUrl.value, '_blank')
}
</script>

<template>
  <div>
    <!-- Show onboarding if not complete -->
    <div v-if="!isOnboardingComplete" class="flex flex-col gap-6 px-4 mt-12 lg:flex-row lg:px-0">
      <div class="w-full px-6 py-8 bg-white rounded-2xl shadow-[1px_3px_15px_0px_rgba(0,0,0,0.08)]">
        <!-- Overall Progress Bar -->
        <div class="mb-6">
          <div class="flex flex-col gap-2">
            <h3 class="font-bold isLargeScreen:text-h1 text-h3 text-secondary">
              {{ `${$t('onbord.hi')} ${getUserInfo.name}` }} 👋
            </h3>
            <p class="text-secondary isLargeScreen:text-h3">
              {{ $t('onbord.onboard_title') }}
            </p>
          </div>
          <div class="flex justify-between items-center mb-2" />
          <div class="overflow-hidden relative w-full h-5 bg-gray-100 rounded-full">
            <div
              class="h-full bg-gradient-to-r from-blue-400 to-green-400 rounded-full"
              :style="`width: ${completionProgress.percentage}%`"
            />
          </div>
        </div>

        <div class="flex flex-col gap-2">
          <!-- Progress Steps -->
          <div class="flex flex-col gap-4 mt-6">
            <div
              v-for="(step, index) in onboardingSteps"
              :key="step.id"
              class="flex relative gap-4 items-start p-4 rounded-lg transition-colors cursor-pointer hover:bg-gray-50"
              @click="navigateToStep(step.route)"
            >
              <!-- Progress Line -->

              <!-- Icon -->
              <div
                class="relative p-2 rounded-full"
                :class="step.isCompleted.value ? 'bg-gradient-to-br from-blue-500 to-green-400' : 'bg-white border border-gray-200'"
              >
                <component
                  :is="step.icon"
                  class="w-8 h-8"
                  :class="step.isCompleted.value ? 'text-white' : 'text-gray-400'"
                />
              </div>

              <!-- Content -->
              <div class="flex flex-col flex-1 gap-1">
                <h4 class="font-bold text-secondary">
                  {{ $t(step.titleKey) }}
                </h4>
                <p class="text-sm text-gray-500">
                  {{ $t(step.descriptionKey) }}
                </p>
              </div>

              <!-- Check Icon (at the end) -->
              <div
                v-if="step.isCompleted.value"
                class="flex flex-shrink-0 justify-center items-center self-center w-8 h-8 text-white bg-gradient-to-r from-blue-500 to-cyan-400 rounded-full"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>

              <!-- Empty Circle for unchecked steps -->
              <div
                v-else
                class="flex-shrink-0 self-center w-8 h-8 bg-white rounded-full border-2 border-gray-200 shadow-sm"
              />
            </div>
          </div>
        </div>
        <div v-if="!isOnboardingComplete" class="flex justify-end mt-8">
          <button
            class="flex gap-2 items-center px-6 py-3 text-gray-600 bg-white rounded-lg border border-gray-200 shadow-sm transition-colors hover:text-gray-800 hover:bg-gray-50"
            @click="skipOnboarding"
          >
            <span>{{ $t('onbord.skip') || 'Skip Onboarding' }}</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 rtl:rotate-180" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      <!-- App Download -->
      <div class="flex flex-col gap-6 self-stretch w-full lg:w-auto">
        <div class="rounded-2xl shadow-[1px_3px_15px_0px_rgba(0,0,0,0.08)] bg-gradient-to-l from-sky-400 to-green-300 relative overflow-hidden cursor-pointer" @click="openAppStore()">
          <div class="flex flex-col gap-2 px-6 pt-8">
            <h3 class="font-bold text-white isLargeScreen:text-h1 text-h3">
              {{ $t('onbord.app_title') }}
            </h3>
            <p class="self-stretch text-white isLargeScreen:text-h3 isLargeScreen:whitespace-nowrap">
              {{ $t('onbord.app_desc') }}
            </p>
          </div>
          <div class="flex justify-center">
            <img src="@/assets/onboarding/Ads.svg" class="w-1/2 sm:w-auto">
          </div>
        </div>

        <!-- Launch Store -->
        <div class="rounded-2xl shadow-[1px_3px_15px_0px_rgba(0,0,0,0.08)] bg-[#EDF0FF] relative overflow-hidden">
          <div class="flex flex-col gap-2 self-stretch px-6 pt-8">
            <h3 class="font-bold isLargeScreen:text-h1 text-h3 text-secondary">
              {{ $t('onbord.launch_title') }}
            </h3>
            <p class="self-stretch text-secondary isLargeScreen:text-h3 isLargeScreen:whitespace-nowrap">
              {{ $t('onbord.launch_desc') }}
            </p>
          </div>
          <div class="flex justify-center">
            <img src="@/assets/onboarding/rocket_launch.svg" class="w-1/2 sm:w-auto">
          </div>
        </div>
      </div>
    </div>
    <!-- Show dashboard stats if onboarding is complete -->
    <DashboardStats v-else />
  </div>
</template>
