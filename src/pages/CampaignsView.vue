<script lang="ts" setup>
import { DocumentCheckIcon, PlusIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import { chunk } from 'lodash'
import type { ComputedRef } from 'vue'
import { useCampaigns } from '@/stores/campaigns'
import type { header } from '@/types'
const { fetchCampaignsList, changeCampaignStatus, checkCampaignCustomers } = useCampaigns()
const { getCampaignsList } = storeToRefs(useCampaigns())
const selectedCampaign = ref({})
const selectedCampaignDetails = ref({})
const { t, locale } = useI18n()
const processing = ref(false)
const servicesOptions = ref([])
onMounted(async () => {
  try {
    processing.value = true
    await fetchCampaignsList()
  }
  finally {
    processing.value = false
  }

  setTimeout(() => {
    checkOrdersCount()
    setInterval(checkOrdersCount, 10000)
  }, 5000)
})

const checkOrdersCount = async () => {
  const customersToUpdate = getCampaignsList.value.filter(c => c.all_customers === 0)

  if (customersToUpdate.length === 0)
    return

  const customerIds = customersToUpdate.map(c => c.uuid)
  const batches = chunk(customerIds, 20)

  const responses = await Promise.all(batches.map(batch => checkCampaignCustomers(batch)))

  const mergedResponse = Object.assign({}, ...responses)

  getCampaignsList.value.forEach((item) => {
    if (mergedResponse[item.uuid] !== undefined)
      item.all_customers = mergedResponse[item.uuid]
  })
}

const showModal = ref(false)
const headers: ComputedRef<header[]> = computed(() => {
  return [
    { title: t('form.name') },
    { title: t('app_id') },
    { title: t('scheduled_at') },
    { title: t('form.status') },
    { title: t('campaign_targeted_customers_count') },
    { title: t('report_campaign') },
  ]
})
const redirectToCustomer = (item) => {

}
const changeStatus = async (itemUuid: string) => {
  try {
    processing.value = true
    await changeCampaignStatus(itemUuid)
  }
  finally {
    processing.value = false
  }
}
const openCreateModal = () => {
  selectedCampaign.value = null
  showModal.value = true
}

const openEditModal = (campaign) => {
  selectedCampaign.value = campaign
  showModal.value = true
}
const showDetailsModal = ref(false)
const openDetailsModal = (campaign: any) => {
  selectedCampaignDetails.value = campaign
  showDetailsModal.value = true
}
</script>

<template>
  <div class="flex flex-col gap-8">
    <CampaignModal
      v-if="showModal"
      :is-open="showModal"
      :selected-campaign="selectedCampaign"
      @close="showModal = false"
    />
    <CampaignDetailsModal
      v-if="showDetailsModal"
      :is-open="showDetailsModal"
      :selected-campaign="selectedCampaignDetails"
      @close="showDetailsModal = false"
    />

    <div class="flex justify-between itmes-center">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("marketing_campaigns") }}
      </h1>
      <BaseButton
        class="inline-flex w-auto hover:bg-green-700"
        custome-bg="bg-green-600"
        @click="openCreateModal"
      >
        {{ $t("create_new_marketing_campaign") }}
        <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </div>
    <div class="flex flex-col mt-4">
      <generic-table
        :headers="headers"
        :data="getCampaignsList"
        tr-class="cursor-pointer"
        :on-row-click="openEditModal"
        :is-loading="processing"
      >
        <template #row="{ item }">
          <grid-td>
            <div class="text-gray-900">
              <span v-if="item?.name">
                {{ item?.name }}
              </span>
              <span v-else class="text-zinc-400"> - </span>
            </div>
          </grid-td>
          <grid-td>
            <div class="text-gray-900">
              <span v-if="item?.app">
                {{ item?.app.name[locale] }}
              </span>
              <span v-else class="text-zinc-400"> - </span>
            </div>
          </grid-td>
          <grid-td>
            <div class="text-gray-900">
              <span v-if="item?.app">
                {{ formatDateAndTime(item?.scheduled_at) }}
              </span>
              <span v-else class="text-zinc-400"> - </span>
            </div>
          </grid-td>
          <grid-td>
            <div v-if="item?.status !== 'completed' " class="text-gray-900">
              <label
                class="inline-flex items-center cursor-pointer"
                @click.stop
              >
                <input
                  type="checkbox"
                  value=""
                  class="sr-only peer"
                  :checked="item?.status === 'active'"
                  @change="() => changeStatus(item?.uuid)"
                >
                <div
                  class="relative w-11 h-6 rounded-full peer   bg-gray-200 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white  after:border after:rounded-full after:h-5 after:w-5 after:transition-all border-gray-600 peer-checked:bg-primary-600"
                />
              </label>
            </div>
            <div v-else class="text-gray-900">
              <span class="text-green">{{ $t('completed') }}</span>
            </div>
          </grid-td>
          <grid-td>
            <div class="text-gray-900">
              <span v-if="item.all_customers === 0" class="text-green">جار التحميل...</span>
              <span v-else class="text-green">{{ item.all_customers }}</span>
            </div>
          </grid-td>
          <grid-td>
            <div class="text-gray-900" @click.stop>
              <span class="text-center text-blue-600 cursor-pointer" @click="openDetailsModal(item)">
                <DocumentCheckIcon class="w-6 h-6 ms-2 -me-0.5" aria-hidden="true" />

              </span>
            </div>
          </grid-td>
        </template>
      </generic-table>
    </div>
  </div>
</template>
