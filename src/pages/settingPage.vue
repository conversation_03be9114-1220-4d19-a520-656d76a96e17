<script lang="ts" setup>
const route = useRoute()
const loadComp = computed(() => {
  return defineAsyncComponent(() => import(`../components/Settings/${route.params?.slug}.vue`))
})
const crumbs = ref([
  {
    name: 'setting',
    path: '/setting',
  },
  {
    name: `settings.settingCrumbs.${route.params.slug}`,
    path: '',
  },
])
</script>

<template>
  <div>
    <bread-crumb :crumbs="crumbs" class="my-5" />
    <Suspense>
      <template #default>
        <component :is="loadComp" />
      </template>
      <template #fallback>
        <overlay-loader />
      </template>
    </Suspense>
  </div>
</template>
