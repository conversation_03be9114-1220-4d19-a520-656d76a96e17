<script setup lang="ts">
import useVuelidate from '@vuelidate/core'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { email, minLength, required } from '@/utils/i18n-validators'
import { useAuthStore } from '@/stores/auth'
import useLogin from '@/composables/auth'

// Auth Store
const { performLogin } = useAuthStore()

// Login State and Validation
const loginState = reactive({
  email: '',
  password: '',
  remember: false,
})

const loginRules = {
  email: { required, email },
  password: { required, minLength: minLength(8) },
}

const loginV$ = useVuelidate(loginRules, loginState)
const loginProcessing = ref(false)
const router = useRouter()

// Handle Login
const handleLogin = async () => {
  loginV$.value.$touch()
  if (loginV$.value.$invalid || loginProcessing.value)
    return

  loginProcessing.value = true
  try {
    const authToken = await performLogin({
      email: loginState.email,
      password: loginState.password,
      remember: loginState.remember,
    })
    if (authToken)
      useLogin(authToken).then(() => router.push({ name: 'dashboard' }))
  }
  finally {
    loginProcessing.value = false
  }
}
</script>

<template>
  <form class="space-y-4 w-full sm:space-y-6" @submit.prevent="handleLogin">
    <div class="relative space-y-3 rounded-md sm:space-y-4">
      <form-group :validation="loginV$" name="email">
        <template #default="{ attrs }">
          <TextInput
            v-bind="attrs"
            id="email-address"
            v-model="loginState.email"
            :label="$t('fields.email')"
            autocomplete="email"
            :placeholder="$t('formPlaceHolder.email')"
          />
        </template>
      </form-group>

      <form-group :validation="loginV$" name="password">
        <template #default="{ attrs }">
          <PasswordInput
            v-bind="attrs"
            id="password"
            v-model="loginState.password"
            :label="$t('fields.password')"
            type="password"
            autocomplete="current-password"
            :placeholder="$t('formPlaceHolder.pass')"
          />
        </template>
      </form-group>
    </div>

    <div class="flex flex-wrap gap-y-2 justify-between items-center">
      <div class="flex items-center">
        <input
          id="remember-me"
          v-model="loginState.remember"
          name="remember-me"
          type="checkbox"
          class="w-4 h-4 rounded border-gray-300 text-primary-700 focus:ring-primary-700"
        >
        <label for="remember-me" class="block text-sm leading-6 text-gray-900 ms-3">
          {{ $t("form.remember") }}
        </label>
      </div>
      <div class="text-sm">
        <a
          href="#"
          class="font-medium text-primary-600 hover:text-primary-500"
          @click="router.push({ name: 'forgot-password' })"
        >
          {{ $t("password.yourPassword") }}
        </a>
      </div>
    </div>

    <div class="mt-2 sm:mt-4">
      <BaseButton show-icon :processing="loginProcessing" class="py-2.5 w-full sm:py-3">
        {{ $t("form.signIn") }}
      </BaseButton>
    </div>
  </form>
</template>
