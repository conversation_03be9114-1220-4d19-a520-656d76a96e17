<script setup lang="ts">
import useVuelidate from '@vuelidate/core'
import { email, required } from '@/utils/i18n-validators'
import { useAuthStore } from '@/stores/auth'
const { forgotPassword, forgetLink } = useAuthStore()

const state = reactive({
  email: '',
})
const rules = {
  email: {
    required,
    email,
  },
}
const v$ = useVuelidate(rules, state)
const processing = ref(false)

const sendDone = ref(false)
const forgetValid = reactive({
  sendDone: false,
  linkSent: '',
})
const performAction = async () => {
  forgetValid.sendDone = false
  forgetValid.linkSent = ''
  try {
    v$.value.$touch()
    if (v$.value.$invalid || processing.value)
      return
    processing.value = true
    const payload = {
      email: state.email,
    }
    await forgotPassword(payload).then((res) => {
      forgetValid.sendDone = true
      forgetValid.linkSent = res.data.status
    })
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <div
    class="flex flex-col justify-center w-full  py-6 sm:py-8 px-4 sm:px-6 "
  >
    <div class="w-full">
      <div class="flex flex-col items-center space-y-2 sm:space-y-4">
        <h2 class="mt-2 sm:mt-4 text-xl sm:text-2xl font-bold tracking-tight text-gray-900">
          {{ $t("password.title") }}
        </h2>
        <p class="w-full mt-1 sm:mt-2 text-sm sm:text-base text-gray-600 text-center">
          {{ $t("password.subtitle") }}
        </p>
      </div>

      <div class="mt-4 sm:mt-6">
        <form class="space-y-4 sm:space-y-6" @submit.prevent="performAction()">
          <form-group :validation="v$" name="email">
            <template #default="{ attrs }">
              <TextInput
                v-bind="attrs"
                id="email-address"
                v-model="state.email"
                :label="$t('fields.email')"
                autocomplete="email"
                :placeholder="$t('formPlaceHolder.email')"
                :validation="v$"
              />
            </template>
          </form-group>

          <!-- Success Message -->
          <p
            v-if="forgetValid.sendDone"
            class="text-green-700 mb-1 sm:mb-2 text-center text-xs sm:text-sm font-medium flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 sm:h-5 sm:w-5 m-1 sm:m-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.707a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              />
            </svg>
            {{ forgetValid.linkSent }}
          </p>

          <div class="flex flex-col items-center justify-center space-y-3 sm:space-y-4 mt-2 sm:mt-4">
            <BaseButton show-icon :processing="processing" class="w-full py-2.5 sm:py-3">
              {{ $t("password.reset") }}
            </BaseButton>
            <span
              class="text-sm font-medium text-primary-600 cursor-pointer hover:text-primary-500"
              @click="
                $router.push({
                  name: 'auth',
                  query: {
                    section: 'sign-in',
                  },
                })
              "
            >
              {{ $t("form.signIn") }}
            </span>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
