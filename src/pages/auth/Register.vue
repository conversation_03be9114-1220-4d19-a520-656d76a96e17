<script setup lang="ts">
import useVuelidate from '@vuelidate/core'
import { computed, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { email, minLength, required, sameAs } from '@/utils/i18n-validators'
import { useAuthStore } from '@/stores/auth'
import { AUTH_TOKEN } from '@/constants'

// Auth Store
const { performRegistration } = useAuthStore()

// Sign-Up State and Validation
const route = useRoute()
const signupState = reactive({
  email: route.query.member || '',
  password: '',
  name: '',
  confirmPassword: '',
  phone: '',
  phone_country: '',
})

const passwordRef = computed(() => signupState.password)

const signupRules = {
  name: { required },
  phone: { required },
  email: { required, email },
  password: { required, minLength: minLength(8) },
  confirmPassword: { required, sameAs: sameAs(passwordRef) },
}

const signupV$ = useVuelidate(signupRules, signupState)
const router = useRouter()
const signupProcessing = ref(false)

// Handle Sign-Up
const handleSignUp = async () => {
  signupV$.value.$touch()
  if (signupV$.value.$invalid || signupProcessing.value)
    return

  signupProcessing.value = true
  const payload = {
    name: signupState.name,
    email: signupState.email,
    password: signupState.password,
    phone: signupState.phone,
    phone_country: signupState.phone_country,
  }

  try {
    const authToken = await performRegistration(payload)
    if (authToken) {
      localStorage.setItem(AUTH_TOKEN, authToken)
      localStorage.setItem('userAuth', JSON.stringify(true))
      router.push({ name: 'VerificationOtp', query: { email: signupState.email } })
    }
  }
  finally {
    signupProcessing.value = false
  }
}

const setPhoneNumber = (phoneNumber: string, phoneObject: { countryCode: string }) => {
  signupState.phone = phoneNumber
  signupState.phone_country = phoneObject.countryCode
}
</script>

<template>
  <form class="space-y-4 w-full sm:space-y-6" @submit.prevent="handleSignUp">
    <form-group :validation="signupV$" name="name">
      <template #default="{ attrs }">
        <TextInput
          v-bind="attrs"
          id="name"
          v-model="signupState.name"
          :label="$t('fields.name')"
          autocomplete="name"
          :placeholder="$t('formPlaceHolder.name')"
        />
      </template>
    </form-group>

    <form-group :validation="signupV$" name="email">
      <template #default="{ attrs }">
        <TextInput
          v-bind="attrs"
          id="email-address"
          v-model="signupState.email"
          :label="$t('fields.email')"
          autocomplete="email"
          :placeholder="$t('formPlaceHolder.email')"
        />
      </template>
    </form-group>

    <form-group :validation="signupV$" name="phone">
      <template #default="{ attrs }">
        <PhoneInput
          v-bind="attrs"
          id="phone"
          v-model="signupState.phone"
          :label="$t('fields.phone')"
          type="tel"
          autocomplete="tel"
          :placeholder="$t('formPlaceHolder.phoneNumber')"
          class="w-full border-none no-padding"
          @update:model-value="setPhoneNumber"
        />
      </template>
    </form-group>

    <form-group :validation="signupV$" name="password">
      <template #default="{ attrs }">
        <PasswordInput
          v-bind="attrs"
          id="password"
          v-model="signupState.password"
          :label="$t('fields.password')"
          type="password"
          autocomplete="current-password"
          :placeholder="$t('formPlaceHolder.pass')"
        />
      </template>
    </form-group>

    <form-group :validation="signupV$" name="confirmPassword">
      <template #default="{ attrs }">
        <PasswordInput
          id="confirm-password"
          v-bind="attrs"
          v-model="signupState.confirmPassword"
          :label="$t('password.confirm')"
          name="confirm-password"
          type="password"
          :placeholder="$t('formPlaceHolder.registerConfirmPass')"
        />
      </template>
    </form-group>

    <div class="flex flex-col justify-center items-center mt-2 space-y-3 sm:mt-4">
      <BaseButton show-icon :processing="signupProcessing" class="py-2.5 w-full sm:py-3">
        {{ $t("form.register") }}
      </BaseButton>
      <p class="text-xs text-center sm:text-sm">
        <span class="text-primary">{{ $t('form.terms_conditions') }} </span>
        <span class="font-bold text-secondary"><a href="https://mahjoz.io/en/terms-conditions">{{ $t('Mahjoz') }}</a></span>
      </p>
    </div>
  </form>
</template>
