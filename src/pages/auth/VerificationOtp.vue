<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import VOtpInput from 'vue3-otp-input'
import { storeToRefs } from 'pinia'
import { useAuthStore } from '@/stores/auth'
import { AUTH_TOKEN } from '@/constants'
const authStore = useAuthStore()
const router = useRouter()
const route = useRoute()
const { getUserInfo } = storeToRefs(authStore)

const otpState = reactive({
  otp: '',
  email: (getUserInfo.value.email as string) || '',
})

const processing = ref(false)
const errorMessage = ref('')
const otpInput = ref<InstanceType<typeof VOtpInput> | null>(null)
const countdown = ref(20) // 20 seconds countdown
const canResend = ref(false)

// Start countdown timer
const startCountdown = () => {
  countdown.value = 20
  canResend.value = false
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      canResend.value = true
    }
  }, 1000)
}

// Initialize countdown on component mount
onMounted(() => {
  startCountdown()
})

const handleOnComplete = (value: string) => {
  otpState.otp = value
}

const handleOnChange = (value: string) => {
  otpState.otp = value
}

const handleVerifyOtp = async () => {
  if (otpState.otp.length !== 4) {
    errorMessage.value = 'Please enter the complete OTP code'
    return
  }

  processing.value = true
  errorMessage.value = ''

  try {
    const response = await authStore.verifyOtp({
      email: otpState.email,
      otp: otpState.otp,
    })

    // Redirect to onboarding flow instead of dashboard
    router.push({ name: 'update-info' })
  }
  catch (error: any) {
    errorMessage.value = error.response?.data?.message
                       || error.response?.data?.error
                       || error.message
                       || 'Verification failed. Please try again.'

    // Reset OTP fields
    otpInput.value?.clearInput()
  }
  finally {
    processing.value = false
  }
}

const handleResendOtp = async () => {
  if (!canResend.value)
    return

  processing.value = true
  errorMessage.value = ''

  try {
    await authStore.sendVerificationLink()

    // Reset OTP input
    otpInput.value?.clearInput()

    // Restart countdown
    startCountdown()
  }
  catch (error: any) {
    errorMessage.value = error.response?.data?.message
                       || error.response?.data?.error
                       || error.message
                       || 'Failed to resend code. Please try again.'
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <div class="p-4 w-full sm:p-6" dir="rtl">
    <div class="p-4 sm:p-6">
      <h2 class="mb-4 text-xl font-bold text-center text-gray-900 sm:text-2xl sm:mb-6">
        {{ $t(`unverified_email.verify_otp`) }}
      </h2>

      <p class="mb-6 text-sm text-center text-gray-600 sm:text-base">
        {{ $t(`unverified_email.verify_otp_desc1`) }}
        {{ getUserInfo.phone }}
        {{ $t(`unverified_email.verify_otp_desc2`) }}
      </p>

      <div class="flex justify-center mb-6">
        <VOtpInput
          ref="otpInput"
          v-model:value="otpState.otp"
          input-classes="otp-input"
          separator=""
          input-type="number"
          :num-inputs="4"
          :should-auto-focus="true"
          :should-focus-order="true"
          dir="ltr"
          @on-change="handleOnChange"
          @on-complete="handleOnComplete"
        />
      </div>

      <p v-if="errorMessage" class="px-3 py-2 mb-4 text-sm text-center text-red-500 bg-red-50 rounded-md">
        {{ errorMessage }}
      </p>

      <div class="space-y-4">
        <button
          :disabled="otpState.otp.length !== 4 || processing"
          class="px-4 py-2.5 w-full text-sm font-medium text-white rounded-md transition-colors sm:py-3 bg-primary hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed sm:text-base"
          @click="handleVerifyOtp"
        >
          <span v-if="!processing"> {{ $t("verify") }}</span>
          <span v-else class="flex justify-center items-center">
            <svg class="mr-2 -ml-1 w-4 h-4 text-white animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
            {{ $t("verifying") }}
          </span>
        </button>

        <div class="space-y-4 text-center">
          <div class="flex gap-2 justify-center items-center">
            <button
              :disabled="!canResend || processing"
              class="text-sm font-medium text-primary-600 hover:text-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
              @click="handleResendOtp"
            >
              {{ $t("unverified_email.resend_code") }}
            </button>
            <span v-if="!canResend" class="text-sm text-gray-500">
              ({{ countdown }}s)
            </span>
          </div>
        </div>

        <div class="flex gap-2 justify-center items-center pt-2 mt-6 text-center">
          <p class="text-sm text-gray-600">
            {{ $t("unverified_email.already_have_account") }}
          </p><button class="inline-flex justify-center items-center py-2 text-sm font-medium rounded-md transition-colors text-primary-600 hover:text-primary-700" @click="authStore.logout().then(() => router.push({ name: 'login' }))">
            {{ $t("form.signIn") }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.otp-input {
  width: 40px;
  height: 40px;
  padding: 5px;
  margin: 0 10px;
  font-size: 20px;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.3);
  text-align: center;
  direction: ltr;
}

.otp-input.is-complete {
  background-color: #e4e4e4;
}

.otp-input::-webkit-inner-spin-button,
.otp-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.otp-input input::placeholder {
  font-size: 15px;
  text-align: center;
  font-weight: 600;
}

@media (max-height: 600px) {
  h2 {
    margin-bottom: 0.5rem;
  }

  p {
    margin-bottom: 0.75rem;
  }

  .space-y-4 > * + * {
    margin-top: 0.5rem;
  }
}
</style>
