<script setup lang="ts">
import useVuelidate from '@vuelidate/core'
import { email, minLength, required, sameAs } from '@vuelidate/validators'
import { useAuthStore } from '@/stores/auth'
const { ResetPassword } = useAuthStore()
const router = useRouter()

const state = reactive({
  email: '',
  password: '',
  confirmPassword: '',
  token: router.currentRoute.value.query.token,
})

const feedback = ref('')

const rules = {
  email: {
    required,
    email,
  },
  password: {
    required,
    minLength: minLength(8),
  },
  confirmPassword:
  {
    required,
    minLength: minLength(8),
  },
}
const errHnadle = reactive({
  passwordNoMatch: {
    msg: 'errorMsg.passwordMatch',
    show: false,
  },
  linkDeprecated: {
    msg: 'The link has expired, try again',
    show: false,
  },
  email: [],
  password: [],
})
const v$ = useVuelidate(rules, state)
const processing = ref(false)

const performAction = async () => {
  try {
    v$.value.$touch()

    if (v$.value.$invalid || processing.value || errHnadle.passwordNoMatch.show)
      return

    processing.value = true

    const data = await ResetPassword({
      email: state.email,
      password: state.password,
      token: state.token,
      password_confirmation: state.confirmPassword,
    })

    feedback.value = data

    setTimeout(() => {
      router.push({ name: 'auth' })
    }, 2000)
  }
  catch (error: any) {
    if (error.errors) {
      if (error.message === 'This password reset token is invalid.') {
        errHnadle.linkDeprecated.show = true
        return
      }
      for (const prop in error.errors)
        errHnadle[prop] = error.errors[prop]
    }
  }
  finally {
    processing.value = false
  }
}
watch(() => state.confirmPassword, (value) => {
  if (state.password !== value)
    errHnadle.passwordNoMatch.show = true

  else
    errHnadle.passwordNoMatch.show = false
})
watch(() => state.password, (value) => {
  if (state.confirmPassword !== value && state.confirmPassword.length > 0)
    errHnadle.passwordNoMatch.show = true

  else
    errHnadle.passwordNoMatch.show = false
})
</script>

<template>
  <div class="flex flex-col justify-center px-4 py-6 mx-auto w-full max-w-md sm:py-8 sm:px-6">
    <div class="w-full">
      <div class="flex flex-col items-center mb-6 sm:mb-8">
        <h2 class="mt-2 text-xl font-bold tracking-tight text-gray-900 sm:mt-4 sm:text-2xl">
          {{ $t('password.reset') }}
        </h2>
      </div>

      <p v-if="!!feedback" class="px-3 py-2 mb-3 text-sm font-medium text-center text-green-700 bg-green-50 rounded-md">
        {{ feedback }}
      </p>

      <form class="space-y-4 sm:space-y-5" @submit.prevent="performAction">
        <div class="space-y-1">
          <label for="email" class="  mb-2 w-full flex items-center text-start text-[#261E27] text-base    ">
            {{ $t('form.email') }}</label>
          <div class="mt-1">
            <input
              id="email"
              v-model="state.email"
              class="block w-full py-2 px-3 placeholder-gray-400 border rounded-md   appearance-none focus:outline-none focus:ring-primary-500 sm:text-sm" :class="[
                v$.email.$errors.length ? 'border-red-500' : 'border-gray-300 focus:border-primary-500',
              ]"
              :placeholder="$t('formPlaceHolder.email')"
              name="email"
              type="email"
              autocomplete="email"
            >
            <p v-for="error of v$.email.$errors" :key="error.$uid" class="mt-1 text-xs text-red-500">
              {{ error.$message }}
            </p>
            <div v-if="(errHnadle.email.length > 0)">
              <ErrMsg v-for="err in errHnadle.email" :key="err" :msg="err" />
            </div>
          </div>
        </div>

        <div class="space-y-1">
          <div class="mt-1">
            <PasswordInput
              id="password"
              v-model="state.password"
              :label="$t('password.enterPassword')"
              name="password"
              autocomplete="new-password"
              :placeholder="$t('formPlaceHolder.newPass')"
            />
          </div>
          <p v-for="error of v$.password.$errors" :key="error.$uid" class="mt-1 text-xs text-red-500">
            {{ error.$message }}
          </p>
          <div v-if="(errHnadle.password.length > 0)">
            <ErrMsg v-for="err in errHnadle.password" :key="err" :msg="err" />
          </div>
        </div>

        <div class="space-y-1">
          <div class="mt-1">
            <PasswordInput
              id="confirm-password"
              v-model="state.confirmPassword"
              :label="$t('password.confirm')"
              name="confirm-password"
              type="password"
              autocomplete="new-password"
              :placeholder="$t('formPlaceHolder.confirmPass')"
              :class="[
                v$.confirmPassword.$errors.length || errHnadle.passwordNoMatch.show ? 'border-red-500' : 'border-gray-300 focus:border-primary-500',
              ]"
            />
          </div>
          <p v-for="error of v$.confirmPassword.$errors" :key="error.$uid" class="mt-1 text-xs text-red-500">
            {{ error.$message }}
          </p>
          <p v-if="errHnadle.passwordNoMatch.show" class="mt-1 text-xs text-red-500">
            {{ $t(`${errHnadle.passwordNoMatch.msg}`) }}
          </p>
        </div>

        <div v-if="errHnadle.linkDeprecated.show" class="px-3 py-2 text-sm text-center text-red-600 bg-red-50 rounded-md">
          {{ errHnadle.linkDeprecated.msg }}
        </div>

        <div class="mt-6">
          <BaseButton show-icon :processing="processing" class="py-2.5 w-full sm:py-3">
            {{ $t('password.reset') }}
          </BaseButton>
        </div>
      </form>
    </div>
  </div>
</template>

<style scoped>
/* Consistent styling for error messages */
.error-message {
  @apply text-red-500 text-xs mt-1;
}

@media (max-height: 600px) {
  form {
    margin-top: 0.5rem;
  }

  .space-y-4 {
    margin-top: 0.25rem;
  }
}
</style>
