<script setup lang="ts">
import { ArrowDownIcon, NoSymbolIcon, PlusIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import type { ComputedRef } from 'vue'
import excelExport from '@/composables/useExcelExport'
import { useTagStore } from '@/stores/tags'
import type { Booking, Customer, PaginationLinks, PaginationMeta, header } from '@/types'
const tagStore = useTagStore()
const { tags } = storeToRefs(tagStore)
const { fetchCustomer } = useCustomerStore()
const i18n = useI18n()
const router = useRouter()
const { processingExport, getExcel } = excelExport('Customer')
const tableData = reactive({
  customerList: [] as Customer[],
  paginationMeta: {
    current_page: 1,
    from: 1,
    last_page: 1,
    links: [],
    path: '',
    per_page: 15,
    to: 15,
    total: 1,
  } as PaginationMeta,
  paginationLinks: {
    first: '',
    last: '',
    prev: null,
    next: null,
  } as PaginationLinks,
  processing: false,
  selectedIndex: null as number | null,
  bookings: [] as Booking[],
  filters: {} as { [key: string]: string },
})
const headers: ComputedRef<header[]> = computed(() => {
  return [
    { title: i18n.t('form.name') },
    { title: i18n.t('form.phone') },
    { title: i18n.t('form.email') },
  ]
})

const fetchCustomers = async (page = 1, filters: { [key: string]: string } = {}) => {
  tableData.processing = true
  fetchCustomer(page, filters)
    .then((res) => {
      tableData.customerList = res.data
      tableData.paginationMeta = res.meta
      tableData.paginationLinks = res.links
    })
    .finally(() => {
      tableData.processing = false
    })
}

const created = (customer: Customer) => {
  // update local data
  tableData.customerList.unshift(customer)
  tableData.paginationMeta.total += 1
  tableData.paginationMeta.to += 1
}

onMounted(async () => {
  await Promise.all([fetchCustomers(), tagStore.fetchTags('customers')])
})
const filterCustomer = async (filters: any) => {
  const { customer, phone, tags, block } = filters
  const currentPage = tableData.paginationMeta.current_page
  const ids = [...tags].map(i => i.id)
  const params = {
    name: customer || '',
    phone: phone || '',
    tags: ids.join(',') || '',
    block: block || '',
  }
  tableData.filters = params
  await fetchCustomers(currentPage, tableData.filters)
}

const changeData = (page: number) => {
  fetchCustomers(page, tableData.filters)
}

const redirectToCustomer = (item: Customer) => {
  router.push({ name: 'customer', params: { id: item.uuid } })
}
const showModal = ref(false)
function toggleModel() {
  showModal.value = !showModal.value
}

const getExcelFile = () => {
  const { showNotification } = useNotifications()

  getExcel().then(() => {
    showNotification({
      title: i18n.t('Success'),
      type: 'success',
      message: i18n.t('operations.emailSent'),
    })
  })
}
</script>

<template>
  <div>
    <AddCustomerModel
      :tags="tags.data"
      :show-modal="showModal"
      @created="created"
      @closed="toggleModel"
    />
    <div class="flex justify-between items-center ms-auto me-auto max-w-12xl">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t(`homepage.${$route?.name}` || "homepage.title") }}
      </h1>
      <div class="mt-4 sm:mt-0 sm:flex-none">
        <BaseButton
          type="button"
          :disabled="processingExport"
          class="inline-flex w-4 bg-green-600 disabled:bg-green-300 me-2"
          @click="getExcelFile()"
        >
          {{ $t("table.export_excel") }}
          <ArrowDownIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
        <BaseButton
          class="inline-flex w-auto hover:bg-green-700"
          custome-bg="bg-green-600"
          @click="toggleModel()"
        >
          {{ $t("form.create") }}
          <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </div>
    <customer-filteration :tags="tags.data" @changed="filterCustomer" />
    <div class="flex flex-col mt-8">
      <div class="">
        <generic-table
          :headers="headers"
          :data="tableData.customerList"
          tr-class="cursor-pointer"
          :on-row-click="redirectToCustomer"
          :is-loading="tableData.processing"
        >
          <template #row="{ item }">
            <grid-td
              class="flex gap-1 items-center py-2 pr-2 pl-2 text-sm whitespace-nowrap sm:pl-6"
              :default-style="false"
            >
              <div>
                <svg
                  v-if="item.imageUrl === null"
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-10"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="#e1e1e1"
                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z"
                  />
                </svg>
                <img
                  v-else
                  id="image"
                  class="w-10 h-10 rounded-full"
                  :src="item.imageUrl"
                  :link="null"
                >
              </div>
              <span> {{ item?.first_name }} {{ item?.last_name }} </span>
              <span v-if="item.block" class="flex group"><NoSymbolIcon class="w-7 h-7 text-red-500 cursor-pointer" /> <span v-if="item.block" class="hidden mx-2 text-red-500 group-hover:inline-block">{{ $t('block_msg') }}</span></span>
            </grid-td>

            <grid-td>
              <div class="text-gray-900">
                <span v-if="item?.phone">
                  {{ item?.phone }}
                </span>
                <span v-else class="text-zinc-400"> - </span>
              </div>
            </grid-td>
            <grid-td>
              <div class="text-gray-900">
                <span v-if="item?.email">
                  {{ item?.email }}
                </span>
                <span v-else class="text-zinc-400"> - </span>
              </div>
            </grid-td>
          </template>
        </generic-table>
        <Pagination
          v-if="tableData.customerList.length"
          :pagination-meta="tableData.paginationMeta"
          :pagination-links="tableData.paginationLinks"
          class="px-4"
          @change="changeData"
        />
      </div>
    </div>
  </div>
</template>

<style>
.cust-box {
  min-height: 70px;
}
</style>
