<script setup lang="ts">
import { storeToRefs } from 'pinia'
import useVuelidate from '@vuelidate/core'
import { useOnBoardingStore } from '@/stores/onBoarding'
import GenericAlert from '@/components/Generic/GenericAlert.vue'
import NextSubmitButton from '@/components/SignUp/NextButton.vue'
import useLogin from '@/composables/auth'
import { email, minLength, required, sameAs } from '@/utils/i18n-validators'
const { getIndustries } = storeToRefs(useOnBoardingStore())
const { fetchIndusties, createAccount } = useOnBoardingStore()
const router = useRouter()
const state = reactive({
  company_name: '',
  email: '',
  phone: '',
  phone_country: '',
  password: '',
  pasword_confirmation: '',
  industry: '',
})

const setPhoneNumber = (
  phoneNumber: string,
  phoneObject: { countryCode: string },
) => {
  state.phone = phoneNumber
  state.phone_country = phoneObject.countryCode
}

const errMsg = ref(null)
const errValidation = ref({})
const registering = ref(false)

const rules = {
  company_name: {
    required,
  },
  email: {
    required,
    email,
  },
  phone: {
    required,
  },
  password: {
    required,
    minLength: minLength(8),
  },
  pasword_confirmation: {
    required,
    sameAs: sameAs(computed(() => state.password)),
  },
  industry: {
    required,
  },
}
const v$ = useVuelidate(rules, state)

const handleSubmit = async () => {
  v$.value.$touch()
  // if (v$.value.$invalid || registering.value) return;
  registering.value = true

  try {
    const { data } = await createAccount(state)

    useLogin(data.token).then(() => {
      router.push({
        path: '/verify-your-email',
        query: {
          reload: 1,
        },
      })
    })
  }
  catch (error: any) {
    const { message, errors } = error

    errMsg.value = message || null
    errValidation.value = errors || {}
  }
  finally {
    registering.value = false
  }
}

onBeforeMount(() => {
  fetchIndusties()
})
</script>

<template>
  <div class="flex flex-col justify-center py-12 min-h-full sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <img class="mx-auto w-auto h-10" src="/src/assets/logo.svg" alt="Your Company">
      <h2 class="mt-6 text-2xl font-bold tracking-tight leading-9 text-center text-gray-900">
        {{ $t("form.signupFor") }}
      </h2>
    </div>

    <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
      <div class="px-6 py-12 bg-white shadow sm:rounded-lg sm:px-12">
        <form class="space-y-6" @submit.prevent="handleSubmit">
          <div>
            <form-group v-slot="{ attrs }" name="company_name" :validation="v$">
              <div class="mt-1">
                <TextInput
                  v-bind="attrs" id="company_name" v-model="state.company_name" :label="$t('form.company_name')" required
                  name="company_name" autocomplete="company_name"
                  :placeholder="$t('formPlaceHolder.company_name')"
                />
              </div>
            </form-group>
          </div>

          <div>
            <LabelInput for="industry">
              {{
                $t(`onbord.choose_type_of_industry`) }}
            </LabelInput>
            <div class="mt-2">
              <form-group v-slot="{ attrs }" name="industry" :validation="v$">
                <SelectInput
                  id="industry" v-bind="attrs" v-model="state.industry"
                  :validation="v$"
                >
                  <option value="">
                    {{ $t("form.select") }}
                  </option>
                  <option
                    v-for="industry of getIndustries" v-if="getIndustries.length" :key="industry?.uuid"
                    :value="industry?.uuid"
                  >
                    {{ industry?.name }}
                  </option>
                </SelectInput>
                <errMsg v-if="errValidation.industry">
                  {{ errValidation.industry }}
                </errMsg>
              </form-group>
            </div>
          </div>

          <div>
            <div class="mt-2">
              <form-group name="email" :validation="v$">
                <template #default="{ attrs }">
                  <TextInput
                    id="email"
                    v-bind="attrs"
                    v-model="state.email"
                    :label="$t('form.email')"
                    autocomplete="email"
                    :placeholder="$t('formPlaceHolder.email')"
                  />
                </template>
              </form-group>
            </div>
          </div>

          <div>
            <form-group name="phone" :validation="v$">
              <template #default="{ attrs }">
                <PhoneInput
                  :model-value="state.phone" label="form.phone" mode="international"
                  v-bind="attrs"
                  @update:model-value="setPhoneNumber"
                />
              </template>
            </form-group>
          </div>

          <div>
            <div class="mt-1">
              <form-group name="password" :validation="v$">
                <template #default="{ attrs }">
                  <PasswordInput
                    v-bind="attrs" id="password" v-model="state.password" :label="$t('password.enterPassword')" name="password"
                    autocomplete="current-password" :placeholder="$t('formPlaceHolder.registerPass')"
                  />
                </template>
              </form-group>
            </div>
          </div>

          <div>
            <form-group name="pasword_confirmation" :validation="v$">
              <template #default="{ attrs }">
                <PasswordInput
                  v-bind="attrs" id="confirm-password" v-model="state.pasword_confirmation" :label="$t('password.confirm')"
                  name="confirm-password" :placeholder="$t('formPlaceHolder.registerConfirmPass')"
                />
              </template>
            </form-group>
          </div>

          <div>
            <NextSubmitButton type="submit" class="w-fit !px-8" :processing="registering">
              {{ $t('steps.done') }}
            </NextSubmitButton>
          </div>
        </form>
      </div>

      <p class="mt-10 text-sm text-center text-gray-500">
        {{ $t("homepage.or") }}
        <a
          href="#" class="font-semibold leading-6 text-indigo-600 hover:text-indigo-500"
          @click.prevent="$router.push({ name: 'auth', query: { section: 'sign-in' } })"
        >
          {{ $t("form.signinHere") }}
        </a>
      </p>
    </div>
  </div>
</template>
