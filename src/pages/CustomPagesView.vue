<script lang="ts" setup>
import type { ComputedRef } from 'vue'
import { PencilSquareIcon, TrashIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import type { header } from '@/types'
import { usePage } from '@/stores/pages'
const { fetchPages, deletePage } = usePage()

const headers: ComputedRef<header[]> = computed(() => {
  return [
    {
      title: t('pageTitle'),
    },
    {
      title: t('bookingPage.procedures'),
    },
  ]
})
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { t } = useI18n()
const router = useRouter()

const showConfModal = ref(false)
const selectedUuid = ref('')
const processing = ref(false)
const pages = ref([])
const getPages = () => {
  processing.value = true
  fetchPages()
    .then((res) => {
      pages.value = res.data.data
    })
    .finally(() => {
      processing.value = false
    })
}
onMounted(() => {
  getPages()
})
const openCreatePage = () => {
  router.push({ name: 'createCustomPage' })
}
const openEditPage = (uuid: string) => {
  router.push({ name: 'editCustomPage', params: { id: uuid } })
}
const openDeleteModal = (uuid: string) => {
  showConfModal.value = true
  selectedUuid.value = uuid
}
const pageRemoved = () => {
  showConfModal.value = false
  getPages()
}
</script>

<template>
  <div>
    <div class="flex justify-between w-full">
      <h2 class="text-3xl font-semibold">
        {{ $t("bookingPage.page") }}
      </h2>

      <div class="mt-4 sm:mt-0 sm:flex-none">
        <BaseButton
          class="inline-flex hover:bg-green-700 w-fit"
          custome-bg="bg-green-600"
          type="submit"
          @click="openCreatePage()"
        >
          {{ $t("form.create") }}
        </BaseButton>
      </div>
    </div>
    <confirmation-modal
      v-if="showConfModal"
      :dir="getLocale(locale)?.direction"
      :is-open="showConfModal"
      :api-call="deletePage"
      :record-id="selectedUuid"
      @removed="pageRemoved"
      @closed="showConfModal = false"
    >
      <p class="leading-7 text-start">
        {{ $t("confirmModal.msg") }}
      </p>
    </confirmation-modal>
    <div class="flex flex-col mt-4">
      <div class="">
        <generic-table :headers="headers" :data="pages" :is-loading="processing">
          <template #row="{ item }">
            <grid-td>
              <div class="block w-64" v-html="item.title" />
            </grid-td>
            <grid-td class="flex gap-3">
              <BaseButton
                class="inline-flex hover:bg-gray-600 w-fit"
                custome-bg="bg-gray-700"
                @click="openEditPage(item.id)"
              >
                <PencilSquareIcon class="w-4 h-4" aria-hidden="true" />
              </BaseButton>
              <BaseButton
                class="inline-flex hover:bg-red-700 w-fit"
                custome-bg="bg-red-600"
                @click="openDeleteModal(item.id)"
              >
                <TrashIcon class="w-4 h-4" aria-hidden="true" />
              </BaseButton>
            </grid-td>
          </template>
        </generic-table>
      </div>
    </div>
  </div>
</template>
