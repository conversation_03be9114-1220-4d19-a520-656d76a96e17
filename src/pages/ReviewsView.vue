<script setup lang="ts">
import { storeToRefs } from 'pinia'
import {
  CheckCircleIcon,
  ChevronRightIcon,
  EnvelopeIcon,
} from '@heroicons/vue/20/solid'
import type { PaginationLinks, PaginationMeta } from '@/types'
import i18n from '@/i18n'
const { getLocale } = storeToRefs(useLocalesStore())
const { locale } = useI18n()
const tabs = ref([
  { name: 'pending', href: '#', count: 0, current: true },
  { name: 'approved', href: '#', count: 0, current: false },
  { name: 'rejected', href: '#', count: 0, current: false },
])

const setSelectedTab = (tabName: string): void => {
  tabs.value.map((tab) => {
    tab.current = tab.name === tabName
    return tab
  })
}

const tableData = reactive({
  reviewsList: [],
  paginationMeta: {
    current_page: 1,
    from: 1,
    last_page: 1,
    links: [],
    path: '',
    per_page: 15,
    to: 15,
    total: 1,
  } as PaginationMeta,
  paginationLinks: {
    first: '',
    last: '',
    prev: null,
    next: null,
  } as PaginationLinks,
  processing: false,
  status: 'pending',
})

const { fetchReviews, changeStatus } = useReviewsStore()
const updateTabCounts = (counts): void => {
  tabs.value.map((tab) => {
    const status = counts.find(item => item.status.toLocaleLowerCase() === tab.name.toLocaleLowerCase())
    if (status)
      tab.count = status.status_count

    return tab
  })
}
const fetchReviewsPage = async (
  page = 1,
  status = 'pending',
) => {
  try {
    tableData.processing = true
    const { data, links, meta, counts } = await fetchReviews(page, status)
    tableData.reviewsList = data
    tableData.paginationMeta = meta
    tableData.paginationLinks = links
    updateTabCounts(counts)
  }
  finally {
    tableData.processing = false
  }
}

const currentTab = computed(() => tabs.value.filter(t => t.current)[0])

watch(currentTab, (current) => {
  const statusName = current?.name?.toLocaleLowerCase() || ''

  if (!statusName)
    return
  tableData.status = statusName
  fetchReviewsPage(tableData.paginationMeta.current_page, tableData.status)
})

const showModal = ref(false)

const selectedReview = ref(null)

const showReview = (review: any): void => {
  selectedReview.value = review
  showModal.value = true
}

const processing = ref(false)

const hideModal = () => {
  selectedReview.value = null
  showModal.value = false
}

const makeAction = async (action: string) => {
  if (!selectedReview.value?.uuid)
    return

  processing.value = true

  changeStatus(selectedReview.value.uuid, action).then(() => {
    hideModal()
    fetchReviewsPage(tableData.paginationMeta.current_page, currentTab.value?.name?.toLocaleLowerCase())
  }).finally(() => {
    processing.value = false
  })
}

onMounted(async () => {
  await fetchReviewsPage()
})

const paginationChange = async (page: number) => {
  await fetchReviewsPage(page, tableData.status)
}
</script>

<template>
  <div class="min-h-full">
    <Modal :open="showModal" @close="hideModal">
      <div class="px-6 py-6">
        <div class="flex justify-between items-center">
          <h3 class="text-xl font-semibold text-gray-900">
            {{ $t('reviews.details.heading') }}
          </h3>
          <button
            type="button"
            class="text-gray-400 hover:text-gray-500"
            @click="hideModal"
          >
            <span class="sr-only">Close</span>
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div v-if="selectedReview" class="mt-6">
          <div class="bg-white rounded-lg shadow-sm">
            <!-- Review Header -->
            <div class="px-6 py-4 border-b border-gray-200">
              <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <img
                      v-if="selectedReview?.user?.imageUrl?.length > 0"
                      class="w-10 h-10 rounded-full"
                      :src="selectedReview?.user?.imageUrl"
                      alt=""
                    >
                    <svg
                      v-else
                      class="w-10 h-10 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-900">
                      {{ selectedReview?.user?.name }}
                    </p>
                    <p class="text-sm text-gray-500">
                      {{ selectedReview?.user?.email || selectedReview?.user?.phone }}
                    </p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium rounded-full" :class="[
                      selectedReview.status === 'approved' ? 'bg-green-100 text-green-800'
                      : selectedReview.status === 'rejected' ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800',
                    ]"
                  >
                    <CheckCircleIcon class="mr-1 w-3 h-3" />
                    {{ $t(`reviews.${selectedReview.status}`) }}
                  </span>
                  <span class="text-sm text-gray-500">{{ selectedReview.creation_date }}</span>
                </div>
              </div>
            </div>

            <!-- Review Content -->
            <div class="px-6 py-4">
              <div class="flex justify-center items-center mb-4">
                <vue3-star-ratings
                  v-model="selectedReview.stars"
                  :show-control="false"
                  :disable-click="true"
                  star-color="#38bdf8"
                  inactive-color="#c5e6f5"
                  star-size="24"
                />
              </div>
              <p class="text-gray-700 whitespace-pre-wrap">
                {{ selectedReview.content }}
              </p>
            </div>

            <!-- Action Buttons -->
            <div class="px-6 py-4 bg-gray-50 rounded-b-lg">
              <div class="flex justify-end items-center space-x-3">
                <button
                  type="button"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  @click="hideModal"
                >
                  {{ $t('form.cancel') }}
                </button>

                <BaseButton
                  v-if="selectedReview?.status === 'pending' || selectedReview?.status === 'rejected'"
                  :disabled="processing"
                  show-icon
                  @click="makeAction('approved')"
                >
                  {{ $t('reviews.approve') }}
                </BaseButton>

                <button
                  v-if="selectedReview?.status === 'approved' || selectedReview?.status === 'pending'"
                  type="button"
                  class="px-4 py-2 text-sm font-medium text-white bg-gray-600 rounded-md border border-transparent shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  :disabled="processing"
                  @click="makeAction('rejected')"
                >
                  {{ $t('reviews.hide') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>

    <main class="pb-16">
      <div class="mx-auto">
        <div class="px-4 sm:px-0">
          <h1 class="text-lg font-medium text-gray-900">
            {{ $t('reviews.heading') }}
          </h1>
          <div class="sm:hidden">
            <SelectInput
              id="tabs"
              :label="$t('form.select')"
              name="tabs"
              class="block py-2 pr-10 pl-3 mt-4 w-full text-base rounded-md border-gray-300 focus:border-purple-500 focus:outline-none focus:ring-purple-500 sm:text-sm"
              @change="($e: Event) => setSelectedTab($e.target?.value)"
            >
              <option
                v-for="tab in tabs"
                :key="tab.name"
                :selected="tab.current"
              >
                {{ $t(`reviews.${tab.name}`) }}
              </option>
            </SelectInput>
          </div>
          <div class="hidden sm:block">
            <div class="border-b border-gray-200">
              <nav class="flex mt-2 -mb-px" aria-label="Tabs">
                <a
                  v-for="tab in tabs"
                  :key="tab.name"
                  :href="tab.href"
                  class="px-4 py-4 text-sm font-medium whitespace-nowrap border-b-2"
                  :class="[
                    tab.current
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-200',
                  ]" @click.prevent="setSelectedTab(tab.name)"
                >
                  {{ $t(`reviews.${tab.name}`) }}
                  <span
                    v-if="tab.count"
                    class="hidden px-2.5 py-0.5 text-xs font-medium rounded-full md:inline-block" :class="[
                      tab.current
                        ? 'bg-purple-100 text-purple-600'
                        : 'bg-gray-100 text-gray-900',
                    ]"
                  >{{ tab.count }}</span>
                </a>
              </nav>
            </div>
          </div>
        </div>

        <ul
          role="list"
          class="relative mt-5 border-t border-gray-200 divide-y divide-gray-200 sm:mt-0 sm:border-t-0"
        >
          <OverlayLoader v-if="tableData.processing" :full-screen="false" />
          <li
            v-for="review in tableData.reviewsList"
            :key="review.uuid"
            @click="showReview(review)"
          >
            <a href="#" class="block group">
              <div class="flex items-center px-4 py-5 sm:py-6 sm:px-0">
                <div class="flex flex-1 items-center min-w-0">
                  <div class="flex-shrink-0">
                    <img
                      v-if="review?.user?.imageUrl?.length > 0"
                      class="w-12 h-12 rounded-full group-hover:opacity-75"
                      :src="review?.user?.imageUrl"
                      alt=""
                    >
                    <svg
                      v-else
                      class="w-12 h-12 text-gray-300"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"
                      />
                    </svg>
                  </div>
                  <div
                    class="flex-1 px-4 min-w-0 md:grid md:grid-cols-2 md:gap-4"
                  >
                    <div>
                      <p class="text-sm font-medium text-purple-600 truncate">
                        {{ `${review?.user?.name}` }}
                      </p>
                      <span :dir="getLocale(locale)?.direction" class="mx-4 text-sm text-gray-600 font-small">{{ review?.content.length > 50 ? `${review?.content?.substring(0, 50)}...` : review?.content }}</span>
                      <p class="flex items-center mt-2 text-sm text-gray-500">
                        <EnvelopeIcon
                          class="flex-shrink-0 mx-1.5 w-5 h-5 text-gray-400"
                          aria-hidden="true"
                        />
                        <span class="truncate">{{ review?.user?.email || review?.user?.phone }}</span>
                      </p>
                    </div>
                    <div class="hidden md:block">
                      <div>
                        <p class="text-sm text-gray-900">
                          {{ $t('reviews.on') }}
                          {{ " " }}
                          <time :datetime="review.creation_date">{{
                            review.creation_date
                          }}</time>
                        </p>
                        <p class="flex items-center mt-2 text-sm text-gray-500">
                          <CheckCircleIcon
                            class="flex-shrink-0 mx-1.5 w-5 h-5" :class="[
                              review.status_color,
                            ]"
                            aria-hidden="true"
                          />
                          {{ $t(`reviews.${review.status}`) }}
                        </p>
                        <p class="flex items-center mt-2 text-sm text-gray-500">
                          <vue3-star-ratings
                            v-model="review.stars"
                            :show-control="false"
                            :disable-click="true"
                            star-color="#38bdf8"
                            inactive-color="#c5e6f5"
                            star-size="20"
                          />
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <ChevronRightIcon
                    class="w-5 h-5 text-gray-400 group-hover:text-gray-700"
                    aria-hidden="true"
                  />
                </div>
              </div>
            </a>
          </li>
          <li v-if="!tableData.reviewsList.length">
            <a href="#" class="block group">
              <div class="flex items-center px-4 py-5 sm:py-6 sm:px-0">
                <span>{{ $t("pagination.empty") }}</span>
              </div>
            </a>
          </li>
        </ul>
      </div>
    </main>
    <Pagination
      v-if="tableData.reviewsList.length"
      :pagination-meta="tableData.paginationMeta"
      :pagination-links="tableData.paginationLinks"
      @change="paginationChange"
    />
  </div>
</template>

<style scoped>
.vue3-star-ratings__wrapper {
  display: unset;
  margin: 0;
  text-align: unset;
  padding: 0;
}
</style>
