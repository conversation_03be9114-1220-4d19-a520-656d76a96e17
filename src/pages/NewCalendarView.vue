<script setup lang="ts">
// @ts-expect-error
import { getCurrentInstance, onMounted } from 'vue'
import dayjs from 'dayjs'
// full calendar import
import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import interactionPlugin from '@fullcalendar/interaction'
import timeGridPlugin from '@fullcalendar/timegrid'
import listPlugin from '@fullcalendar/list'
import resourceTimeGridPlugin from '@fullcalendar/resource-timegrid'
// @fullcalendar/scrollgrid
import scrollGridPlugin from '@fullcalendar/scrollgrid'

// import ui components
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import {
  ArrowPathIcon,
  BriefcaseIcon,
  CalendarIcon,
  ChevronDownIcon,
  ClockIcon,
  Cog6ToothIcon,
  RectangleGroupIcon,
} from '@heroicons/vue/20/solid'

// locales import
import arLocale from '@fullcalendar/core/locales/ar'
import enLocale from '@fullcalendar/core/locales/en-gb'

// import composers
import { storeToRefs } from 'pinia'
import { formatDate, formatTime } from '@/composables/dateFormat'
import useStaff from '@/composables/useStaff'
import useBooking from '@/composables/useBooking'
import type { Staff } from '@/types'

const { generalCustomeSetting } = useAccountSettingStore()

const instance = getCurrentInstance()

// import composers functions
const { tableData, fetchStaffPage } = useStaff()
const { fetchEventpage } = useBooking()

const { t } = useI18n()
const authStore = useAuthStore()
const { getUserInfo } = storeToRefs(authStore)
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())

const processing = ref(false)
const isOpenOrderDetailsModal = ref(false)
const isOpenMoveBookingModal = ref(false)
const isOpenConfigSlide = ref(false)
const isOpenCreateOrderModal = ref(false)
const orderId = ref('')
const events = ref([])
const eventId = ref('')
const eventMovingData = ref({})
const selectedSlot = ref({})
const openStaffSlide = ref(true)
const calendar = ref(null)
const todayDate = new Date(dayjs().startOf('day'))

const TODAY = dayjs(todayDate).format('YYYY-MM-DD')
const calendarViews = [
  {
    title: 'calendar.month_view',
    type: 'dayGridMonth',
    value: 'month_view',
    canSelectMultipleStaff: false,
  },
  {
    title: 'calendar.month_list',
    type: 'listMonth',
    value: 'month_list',
    canSelectMultipleStaff: false,
  },
  {
    title: 'calendar.week_view',
    type: 'resourceTimeGridWeek',
    value: 'week_view',
    canSelectMultipleStaff: true,
  },
  {
    title: 'calendar.week_list',
    type: 'listWeek',
    value: 'week_list',
    canSelectMultipleStaff: true,
  },
  {
    title: 'calendar.day_view',
    type: 'resourceTimeGridDay',
    value: 'day_view',
    canSelectMultipleStaff: true,
  },
  {
    title: 'calendar.day_list',
    type: 'listDay',
    value: 'day_list',
    canSelectMultipleStaff: true,
  },
]
const calendarView = ref(calendarViews[0])
const configCalendarInputs = ref({})

const fetchCalenderConfiguration = async () => {
  const calenderConfig = await generalCustomeSetting('calendar')
  configCalendarInputs.value = calenderConfig
  // set slot duration
  const formatDuration
    = calenderConfig.slot_duration.value.length == 1
      ? `0${calenderConfig.slot_duration.value}`
      : calenderConfig.slot_duration.value
  calendarOptions.slotDuration = `00:${formatDuration}:00`
  // set scrollTime
  calendarOptions.scrollTime = `${calenderConfig.first_hour.value}`
  // set default view
  const view
    = calendarViews[
      calendarViews.findIndex(
        view => view.value == calenderConfig.default_view.value,
      )
    ]
  initStaffSlide(view)
  setCalendarView(view)
}

const canSelectMultipleStaff = ref(false)
const initStaffSlide = (view) => {
  canSelectMultipleStaff.value = view.canSelectMultipleStaff
}
const openEvent = (event: any) => {
  orderId.value = event.extendedProps.order_id
  isOpenOrderDetailsModal.value = true
}

const calendarHours = ref([])
const calendarCurrentStart = ref('')
const calendarCurrentEnd = ref('')

const calendarOptions = reactive({
  schedulerLicenseKey: import.meta.env.VITE_FULL_CALENDAR_LICENSE_KEY,
  locales: [arLocale, enLocale],
  // locale.value => "en" || "ar" as string include double quotes
  locale: locale.value.replaceAll('"', ''),
  direction: locale.value.replaceAll('"', '') === 'ar' ? 'rtl' : 'ltr',
  plugins: [
    dayGridPlugin,
    interactionPlugin,
    timeGridPlugin,
    listPlugin,
    resourceTimeGridPlugin,
    scrollGridPlugin,
  ],
  headerToolbar: false,
  height: 800,
  contentHeight: 780,
  aspectRatio: 3,
  nowIndicator: true,
  selectable: true,

  datesSet({ start, end }) {
    calendarCurrentStart.value = dayjs(start).format('YYYY-MM-DD')
    calendarCurrentEnd.value = dayjs(end).format('YYYY-MM-DD')
  },
  select(info) {
    selectedSlot.value = info
    isOpenCreateOrderModal.value = true
  },
  eventResize(info) {
    eventMovingData.value = {
      info,
      uuid: info.event.extendedProps.uuid,
      start: info.event.start,
      end: info.event.end,
      staffId: info.event._def.resourceIds[0],
    }
    isOpenMoveBookingModal.value = true
  },
  eventDrop(info) {
    eventMovingData.value = {
      info,
      uuid: info.event.extendedProps.uuid,
      start: info.event.start,
      end: info.event.end,
      staffId: info.event._def.resourceIds[0],
    }
    isOpenMoveBookingModal.value = true
  },
  views: {
    dayGridMonth: { buttonText: 'month' },
    timeListMonth: { buttonText: 'list-month' },
    resourceTimeGridWeek: { buttonText: 'week', type: 'resourceTimeGridWeek' },
    timeListWeek: { buttonText: 'list-week' },
    resourceTimeGridDay: { buttonText: 'day', type: 'resourceTimeGridDay' },
    timeListDay: { buttonText: 'list-day' },
  },
  slotDuration: '00:20:00',
  slotLabelInterval: '01:00',
  allDaySlot: false,
  initialView: 'resourceTimeGridWeek',
  defaultView: 'resourceTimeGridWeek',
  scrollTime: '10:00:00', // default scrollTime
  scrollTimeReset: false,
  initialDate: TODAY,
  firstDay: computed(() => getUserInfo.value.tenant.start_of_week).value,
  editable: true,
  dayMaxEvents: true, // allow "more" link when too many events
  navLinks: true,
  events: [],
  businessHours: [] || null,
  dayMinWidth: 200,
})
watch(
  () => locale.value,
  (val) => {
    calendarOptions.locale = val
  },
)
const resources = computed(() => {
  if (tableData.selectAllStaff) {
    return tableData.staffList.map(staff => ({
      id: staff.uuid,
      title: staff.name,
    }))
  }
  else {
    return tableData.staffList
      .filter(staff => staff.selected)
      .map(staff => ({
        id: staff.uuid,
        title: staff.name,
      }))
  }
})
const formatBookingTime = (time) => {
  return formatTime(time)
}
const staff = useStaffStore()

const fetchEvent = async () => {
  processing.value = true
  events.value = []
  const params = {
    from: formatDate(calendar.value.calendar.view?.currentStart),
    to: formatDate(
      dayjs(calendar.value.calendar.view.currentEnd)
        .subtract(1, 'second')
        .toString(),
    ),
    staff: tableData.selectAllStaff
      ? ''
      : tableData.staffList
        .filter(staff => staff.selected)
        .map(staff => staff.uuid)
        .join(','),
  }
  const res = await fetchEventpage(true, 1, params)
  const op = res.data.map((event, ind) => ({
    ...event,
    appointmentDuration: event.duration,
    uuid: event.uuid,
    resourceId: event.resourceId,

    title: event.customer
      ? `${event.customer?.first_name ?? '--'} ${
          event.customer?.last_name ?? ''
        } `
      : t('customer_deleted'),
    start: new Date(event.start),
    end: new Date(event.end),
    date: event.date,
    className: 'fc-event-solid-info fc-event-light',
    description: '',
    background: event.service?.color,
    status: event.status,
    isRecurring: event.is_recurring,
    order_id: event.order_id,
  }))
  staff.calendarSelectedStaffUuid = tableData.selectedStaff?.uuid
  calendarOptions.events = op
  processing.value = false
}
const next = () => {
  calendar.value.calendar.next()
  instance?.proxy?.$forceUpdate()
  fetchEvent()
}
const prev = () => {
  calendar.value.calendar.prev()
  instance?.proxy?.$forceUpdate()
  fetchEvent()
}

const goToday = () => {
  calendar.value.calendar.today()
  instance?.proxy?.$forceUpdate()
  fetchEvent()
}

const setCalendarView = async (view) => {
  await calendar.value.calendar.changeView(view.type)
  await fetchEvent()
  // solud retry scroll after change view
  calendar.value.calendar.scrollToTime(calendarOptions.scrollTime)
  calendarView.value = view
  instance?.proxy?.$forceUpdate()
  initStaffSlide(view)
}
const calendarStore = useCalendar()
const currentSelectedStaff = computed(() => tableData.selectedStaff)
const selectedStaffs = computed(() =>
  tableData.staffList.filter(item => item.selected),
)

async function getBusinessHourStaffs(staffsIds) {
  const businessHoursWithStaffs = []
  const { data: staffsWorkingHours }
    = await calendarStore.fetchStaffsWorkingHourCalender(staffsIds)
  for (const [staff, workingHour] of Object.entries(staffsWorkingHours)) {
    const timeoffs = selectedStaffs.value?.length
      ? selectedStaffs.value?.find(item => item.uuid === staff)?.timeoffs
      : tableData.staffList?.find(item => item.uuid === staff)?.timeoffs
    if (!timeoffs)
      break

    const timeoffsRanges: Array<{ start_at: string; end_at: string }>
      = timeoffs?.map(({ start_at, end_at }) => ({
        start_at,
        end_at,
      }))
    const uniqueDates: Array<string> = []
    timeoffsRanges.forEach(({ start_at, end_at }) => {
      let currentDate = dayjs(start_at)
      while (
        currentDate.isBefore(dayjs(end_at))
        || currentDate.isSame(dayjs(end_at))
      ) {
        uniqueDates.push(currentDate.format('YYYY-MM-DD'))
        currentDate = currentDate.add(1, 'day')
      }
    })
    const uniqueTimeoffDates = Array.from(new Set(uniqueDates))
    const startDate = dayjs(calendarCurrentStart.value)
    const endDate = dayjs(calendarCurrentEnd.value)
    const datesArray = []
    let currentDate = startDate
    while (
      currentDate.isSame(endDate, 'day')
      || currentDate.isBefore(endDate, 'day')
    ) {
      datesArray.push(currentDate.format('YYYY-MM-DD'))
      currentDate = currentDate.add(1, 'day')
    }
    if (datesArray.length === 8)
      datesArray.pop()

    const daysInTimeoff = datesArray
      .filter(item => uniqueTimeoffDates.includes(item))
      .map(date => dayjs(date).day())

    const validBusinessHours = [...workingHour].map((item) => {
      let daysOfWeek = item.daysOfWeek

      if (
        !daysOfWeek
        || daysOfWeek[0] === undefined
        || daysInTimeoff.includes(daysOfWeek[0])
      )
        daysOfWeek = []
      else
        daysOfWeek = item.daysOfWeek

      return {
        ...item,
        daysOfWeek,
      }
    })
    businessHoursWithStaffs.push({
      uuid: staff,
      businessHours: validBusinessHours,
    })
  }
  return businessHoursWithStaffs
}
const selectStaff = async (staffs: Staff[]) => {
  let ids = []
  if (staffs?.length) {
    ids = staffs.map(staff => staff.uuid).join(',')
  }
  else {
    tableData.staffList[0].selected = true
    ids = tableData.staffList[0].uuid
  }

  const staffsWorkingHour = await getBusinessHourStaffs(ids)
  setResource(staffsWorkingHour)
  refreshCalenderData()
}
const refreshCalenderData = () => {
  instance?.proxy?.$forceUpdate()
  // setResource();
  fetchEvent()
}

const openCreateOrderModal = () => {
  isOpenCreateOrderModal.value = true
}
onMounted(async () => {
  await fetchStaffPage('*')
  await selectStaff([])
  await fetchCalenderConfiguration()
})

const setResource = (staffsWorkginHours) => {
  let resources = []
  if (tableData.selectAllStaff) {
    resources = tableData.staffList.map(staff => ({
      id: staff.uuid,
      title: staff.name,
      businessHours: staffsWorkginHours.find(item => item.uuid === staff.uuid)
        ?.businessHours,
    }))
  }
  else {
    resources = tableData.staffList
      .filter(staff => staff.selected)
      .map(staff => ({
        id: staff.uuid,
        title: staff.name,
        businessHours: staffsWorkginHours.find(
          item => item.uuid === staff.uuid,
        )?.businessHours,
      }))
  }
  calendar.value?.calendar.setOption('resources', resources)
}

watch(
  openStaffSlide,
  async (val) => {
    // destroy the calendar instance
    // create a new instance
    // instance?.proxy?.$forceUpdate();
    // await calendar.value.calendar.changeView(calendarView.value.type);
  },
  { immediate: true },
)
</script>

<template>
  <div class="relative">
    <ConfigCalenderSlide
      :is-open="isOpenConfigSlide"
      :config-calendar-inputs="configCalendarInputs"
      @close="isOpenConfigSlide = false"
      @refresh="fetchCalenderConfiguration"
    />

    <ModalOrder
      v-if="isOpenCreateOrderModal"
      :is-open="isOpenCreateOrderModal"
      @close="isOpenCreateOrderModal = false"
      @refresh="fetchEvent()"
    />
    <OrderDetailsModal
      v-if="isOpenOrderDetailsModal"
      :is-open="isOpenOrderDetailsModal"
      :order-id="orderId"
      @close="fetchEvent();isOpenOrderDetailsModal = false"
    />
    <drop-confirmation-modal
      v-if="isOpenMoveBookingModal"
      :dir="getLocale(locale)?.direction"
      :event-id="eventId"
      :class="[
        getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
      ]"
      :is-open="isOpenMoveBookingModal"
      :event-data="eventMovingData"
      @closed="isOpenMoveBookingModal = false"
    />
    <div
      class="flex flex-col-reverse gap-4 justify-start items-start pb-3 md:items-center md:justify-between md:flex-row md:gap-0"
    >
      <div class="w-full sm:w-[500px]">
        <div class="flex gap-4 md:flex md:items-center">
          <base-button
            custome-bg="bg-green-600 text-sm"
            @click="openCreateOrderModal"
          >
            {{ $t("form.create") }}
          </base-button>
        </div>
      </div>
      <div class="flex gap-2 items-center">
        <div class="flex gap-2 justify-end items-center">
          <div>{{ calendar?.calendar?.view?.title }}</div>
          <div
            class="h-[32px] px-[6px] rounded-[40px] hover:bg-gray-200 flex justify-center items-center cursor-pointer select-none"
            @click="prev"
          >
            <svg
              v-if="locale === 'en'"
              aria-hidden="true"
              class="stroke-current"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                class="awd-stroke-ic"
                d="M12.5 4.99996L7.5 9.99995L12.5 14.9999"
                stroke-width="1.2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <svg
              v-else
              aria-hidden="true"
              class="stroke-current"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                class="awd-stroke-ic"
                d="M7.49999 14.9999L12.5 9.99989L7.49999 4.99988"
                stroke-width="1.2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
          <div
            class="h-[32px] px-[15px] rounded-[40px] hover:bg-gray-200 flex justify-center items-center cursor-pointer select-none text-sm"
            @click="goToday"
          >
            {{ $t("today") }}
          </div>
          <div
            class="h-[32px] px-[6px] rounded-[40px] hover:bg-gray-200 flex justify-center items-center cursor-pointer select-none"
            @click="next"
          >
            <svg
              v-if="locale === 'ar'"
              aria-hidden="true"
              class="stroke-current"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                class="awd-stroke-ic"
                d="M12.5 4.99996L7.5 9.99995L12.5 14.9999"
                stroke-width="1.2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <svg
              v-else
              aria-hidden="true"
              class="stroke-current"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                class="awd-stroke-ic"
                d="M7.49999 14.9999L12.5 9.99989L7.49999 4.99988"
                stroke-width="1.2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>
      <div class="flex gap-4 items-center md:ml-4">
        <button
          class="flex gap-2 items-center px-3 py-2 text-white rounded-md shadow-sm bg-primary-800 hover:bg-primary-700"
          @click="openStaffSlide = !openStaffSlide"
        >
          <RectangleGroupIcon class="w-6 h-6" />
          {{ $t("customization_staff") }}
        </button>
        <Menu as="div" class="relative">
          <MenuButton
            type="button"
            class="flex gap-x-1.5 items-center px-3 py-2 text-sm font-semibold text-gray-900 bg-white rounded-md ring-1 ring-inset ring-gray-300 shadow-sm hover:bg-gray-50"
          >
            {{ $t(calendarView.title) }}
            <ChevronDownIcon
              class="-mr-1 w-5 h-5 text-gray-400"
              aria-hidden="true"
            />
          </MenuButton>
          <transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="opacity-0 transform scale-95"
            enter-to-class="opacity-100 transform scale-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="opacity-100 transform scale-100"
            leave-to-class="opacity-0 transform scale-95"
          >
            <MenuItems
              class="overflow-hidden absolute right-0 z-30 mt-3 w-36 bg-white rounded-md ring-1 ring-black ring-opacity-5 shadow-lg origin-top-right focus:outline-none"
            >
              <div class="py-1">
                <MenuItem
                  v-for="(view, index) in calendarViews"
                  :key="index"
                  v-slot="{ active }"
                  @click="setCalendarView(view)"
                >
                  <a
                    href="#"
                    class="block px-4 py-2 text-sm"
                    :class="[
                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                    ]"
                  >{{ $t(view.title) }}
                  </a>
                </MenuItem>
              </div>
            </MenuItems>
          </transition>
        </Menu>
        <button>
          <Cog6ToothIcon
            class="w-6 h-6 text-primary-800"
            @click="isOpenConfigSlide = true"
          />
        </button>
      </div>
    </div>

    <div class="overflow-x-auto relative mt-4 w-full h-full calender-container">
      <div
        class="flex justify-end w-full h-full"
        :class="openStaffSlide ? 'min-w-[1000px]' : 'w-full'"
      >
        <StaffSlide
          :is-open="openStaffSlide"
          :can-select-multiple="canSelectMultipleStaff"
          @close="openStaffSlide = false"
          @update-selected-staffs="selectStaff"
        />
        <div class="relative w-full">
          <OverlayLoader v-if="processing" :full-screen="false" />

          <FullCalendar
            ref="calendar"
            :options="calendarOptions"
            class="w-full h-full booking-calendar"
          >
            <template #eventContent="arg">
              <VTooltip
                :key="arg.event.extendedProps.uuid"
                placement="top"
                :skidding="10"
                class="w-full h-full"
                :delay="{
                  hide: 10,
                }"
                :auto-hide="true"
              >
                <div
                  class="flex relative justify-between items-start pt-1 pb-2 w-full h-full text-black"
                  :class="[
                    `${calendarView.type.includes('list') ? 'px-3' : 'px-1'}`,
                    {
                      'bg-[#F4433680]':
                        arg.event.extendedProps.status === 'no-show',
                      'bg-[#2196f380]':
                        arg.event.extendedProps.status === 'completed',
                      'bg-[#4CAF5080]':
                        arg.event.extendedProps.status === 'confirmed',
                    },
                  ]"
                  @click="openEvent(arg.event)"
                >
                  <div
                    class="absolute bottom-0 h-[10%] start-0 w-full z-10"
                    :style="{
                      backgroundColor: arg.event.extendedProps.background,
                    }"
                  />
                  <div
                    class="justify-start items-start w-full"
                    :class="[
                      calendarView.type.includes('month') ? 'flex' : 'flex-col',
                    ]"
                    dir="ltr"
                  >
                    <b class="text-base font-medium">{{ arg.event.title }}</b>
                    <b class="ml-2 font-semibold">{{
                      formatBookingTime(arg.event.start)
                    }}</b>
                  </div>
                  <div class="flex gap-1 items-center">
                    <ArrowPathIcon
                      v-if="arg.event.extendedProps.isRecurring"
                      class="block w-4 h-4"
                    />
                  </div>
                </div>
                <template #popper>
                  <div
                    class="px-2 py-3 text-sm bg-white text-gray-900 rounded-xl flex flex-col gap-2 min-w-[220px] text-start shadow-lg border border-gray-200"
                    :dir="getLocale(locale)?.direction"
                  >
                    <div class="flex gap-1 justify-start items-center w-full">
                      <img
                        v-if="arg.event.extendedProps?.staff?.imageLink"
                        :src="arg.event.extendedProps?.staff?.imageLink"
                        class="w-8 h-8 rounded-full"
                      >
                      <svg
                        v-else
                        xmlns="http://www.w3.org/2000/svg"
                        class="w-8 h-8"
                        viewBox="0 0 24 24"
                      >
                        <path
                          fill="#e1e1e1"
                          d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 14c-2.03 0-4.43-.82-6.14-2.88a9.947 9.947 0 0 1 12.28 0C16.43 19.18 14.03 20 12 20z"
                        />
                      </svg>
                      <span class="text-lg font-semibold">{{
                        arg.event.extendedProps?.staff?.name
                      }}</span>
                    </div>
                    <p class="flex gap-1 items-center text-sm font-normal">
                      <BriefcaseIcon class="w-8 h-8" />
                      <span class="text-lg font-semibold">
                        {{ arg.event.extendedProps.service?.name }}
                      </span>
                    </p>
                    <p class="flex gap-1 items-center text-sm font-normal">
                      <ClockIcon class="w-8 h-8" />
                      <span class="text-lg font-semibold">
                        {{ arg.event.extendedProps.appointmentDuration }}
                        {{ $t("booking.minutes") }}
                      </span>
                    </p>
                    <div>
                      <p class="flex gap-1 items-center text-sm font-normal">
                        <CalendarIcon class="w-8 h-8" />
                        <span class="text-lg font-semibold" dir="auto">
                          {{ dayjs(arg.event.start).format("hh:mm A") }} -
                          {{ dayjs(arg.event.end).format("hh:mm A") }}
                        </span>
                      </p>
                    </div>
                  </div>
                </template>
              </VTooltip>
            </template>
          </FullCalendar>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.fc-v-event {
  @apply bg-neutral-100 border w-full border-neutral-100 text-black flex px-1  justify-between py-2 items-center;
}
.fc-event-main {
  @apply w-full;
}
.fc-event {
  padding: 0 !important;
}
.staff-break-item {
  pointer-events: none;
}
.fc-view {
  overflow-x: auto;
}

.fc-view > table {
  min-width: 100%;
  width: 2000px;
}

.fc-time-grid .fc-slats {
  z-index: 4;
}

.fc-scroller.fc-time-grid-container {
  overflow: initial !important;
}

.fc-axis {
  position: sticky;
  left: 0;
  background: white;
}

.v-popper--theme-tooltip .v-popper__inner {
  background: transparent !important;
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
}

.tooltip-content-wrapper {
  position: relative;
  z-index: 100000;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Custom scrollbar styles for tooltip */
.tooltip-content-scrollable {
  max-height: 90vh;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.6) rgba(229, 231, 235, 0.3);
}

.tooltip-content-scrollable::-webkit-scrollbar {
  width: 6px;
}

.tooltip-content-scrollable::-webkit-scrollbar-track {
  background: rgba(229, 231, 235, 0.3);
  border-radius: 3px;
}

.tooltip-content-scrollable::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.6);
  border-radius: 3px;
}

.tooltip-content-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8);
}

/* Ensure calendar container doesn't clip tooltips */
.calender-container {
  overflow: visible !important;
}

.fc-view {
  overflow-x: auto;
  overflow-y: visible !important;
}

/* Ensure calendar events don't overflow in cells */
.fc-event {
  padding: 0 !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fc-daygrid-event {
  overflow: hidden !important;
}

.fc-daygrid-event .fc-event-main {
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Handle multiple events in day grid cells */
.fc-daygrid-more-link {
  background: rgba(59, 130, 246, 0.1) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  color: #3b82f6 !important;
  border-radius: 4px !important;
  padding: 2px 6px !important;
  font-size: 12px !important;
  margin: 1px 0 !important;
}
.fc .fc-more-popover {
  z-index: 20 !important;
}
</style>
