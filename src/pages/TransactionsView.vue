<script setup lang="ts">
import { storeToRefs } from 'pinia'
import VueTailwindDatepicker from 'vue-tailwind-datepicker'
import dayjs from 'dayjs'
import { ArrowDownIcon, DocumentIcon } from '@heroicons/vue/24/outline'
import { required } from '@vuelidate/validators'
import useVuelidate from '@vuelidate/core'
import useTransaction from '@/composables/useTransaction'
import { useTransactionState } from '@/stores/transaction'
import { useBank } from '@/stores/bank'
import { locatizedOption } from '@/composables/useDatePicker'
import excelExport from '@/composables/useExcelExport'

import i18n from '@/i18n'

const { processingExport, getExcel } = excelExport('Transaction')

const paymenet = useBank()
const payments = ref([])
const { locale, t } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())
const { getPaymentMethodsList } = usePaymentMethodsStore()
const { tableData, fetchTransactionPage } = useTransaction()
const { updateTrans, updateTransaction } = useTransactionState()
const showModal = ref(false)
const processing = ref(false)
const status = ref(['refused', 'pending', 'confirmed'])
const range: any = ref([])
const filterTransaction = ref('')
const paymentMethod = ref('')
const team = ref('')
const teamdebounced = debouncedRef(team, 500)
const rangeDebounced = debouncedRef(range, 500)
const statusDebounced = debouncedRef(filterTransaction, 500)
const paymentMethodDebounced = debouncedRef(paymentMethod, 500)
const paymentGatewayId = ref('')
const paymentGatewayIdDebounced = debouncedRef(paymentGatewayId, 500)
const transactionLoading = ref(false)
const payment_methods = ref([])
const formatDate = ref({
  date: 'DD-MM-YYYY',
  month: 'MMM',
})
const filters = ref({
  status: '',
  payment_method: '',
  from: '',
  to: '',
  team: '',
  payment_gateway_id: '',
})
const fetchTransaction = (page = 1, filters = {}) => {
  transactionLoading.value = true
  return fetchTransactionPage(page, filters).finally(() => {
    transactionLoading.value = false
  })
}
watch(
  [statusDebounced, rangeDebounced, paymentMethodDebounced, teamdebounced, paymentGatewayIdDebounced],
  (value) => {
    const [filterTransaction, range, payment_method, team, payment_gateway_id] = value

    const payload = {
      status: filterTransaction,
      payment_method,
      to: range[1] as string,
      from: range[0] as string,
      team,
      payment_gateway_id,
    }
    filters.value = {
      status: filterTransaction,
      payment_method: paymentMethod,
      from: range[0],
      to: range[1],
      team,
      payment_gateway_id,
    }
    const currentPage = tableData.paginationMeta.current_page
    fetchTransaction(1, payload)
  },
)

onMounted(() => {
  transactionLoading.value = true
  fetchTransaction().finally(() => {
    transactionLoading.value = false
  })
  getPaymentMethodsList().then((data) => {
    payment_methods.value = data
  })
})
const selectedTransaction = ref({
  date: '',
  amount: '',
  customer: '',
  payment_method: { name: '' },
  status: '',
})
const openModal = (selectedTrans: Object) => {
  selectedTransaction.value = { ...selectedTrans }
  showModal.value = true
}
const rules = {
  date: {
    required,
  },
  amount: {
    required,
  },
}
const v$ = useVuelidate(rules, selectedTransaction)
const updateTransStatus = (status: string) => {
  processing.value = true
  updateTrans(selectedTransaction?.value.id, status)
    .then(() => {
      fetchTransaction()
    })
    .finally(() => {
      processing.value = false
      showModal.value = false
    })
}
const changeData = (page: number) => {
  fetchTransaction(page, filters.value)
}
const router = useRouter()
const navigateToPage = ({ id }) => {
  router.push({ name: 'transaction', params: { id } })
}
const setDate = (date: string) => (selectedTransaction.value.date = date)
const errHandle = reactive<{ [key: string]: string[] }>({})
const updateTransacrtion = () => {
  if (!selectedTransaction.value)
    return

  const formData = {
    date: selectedTransaction.value.date,
    amount: selectedTransaction.value.amount,
  }
  processing.value = true

  for (const err in errHandle) errHandle[err] = []

  updateTransaction(selectedTransaction.value.id, formData)
    .then(({ data }) => {
      tableData.transactionList = [...tableData.transactionList].map((item) => {
        return item.id === data.id ? data : item
      })
      showModal.value = false
    })
    .catch((err) => {
      for (const prop in err.errors) errHandle[prop] = err.errors[prop]
    })
    .finally(() => {
      processing.value = false
    })
}
const headers = computed(() => {
  return [
    {
      title: t('transaction.transaction_number'),
    },
    {
      title: t('transaction.date'),
    },
    {
      title: t('transaction.customer'),
    },
    {
      title: t('transaction.order_number'),
    },
    {
      title: t('transaction.payment_method'),
    },
    {
      title: t('transaction.amount'),
    },
    {
      title: t('transaction.status'),
    },
  ]
})
</script>

<template>
  <div>
    <Modal
      :dir="getLocale(locale)?.direction"
      :open="showModal"
      title="showTransaction"
      @close="showModal = false"
    >
      <err-validations
        v-if="Object.values(errHandle)[0]?.length"
        :err-handle="errHandle"
      />

      <div
        class="relative mt-10"
        :class="[
          getLocale(locale)?.direction === 'rtl' ? 'text-right' : 'text-left',
        ]"
      >
        <overlay-loader v-if="processing" :full-screen="false" />

        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div class="flex flex-col">
            <TextInput
              id="grid-first-name"
              v-model="selectedTransaction.amount"
              :label="$t('dashboard.booking.amount')"
              :placeholder="$t('dashboard.booking.amount')"
              :readonly="!selectedTransaction.is_editable"
            />
          </div>

          <div>
            <label
              for="booking-start"
              class="block mb-1 text-sm font-medium text-gray-700"
            >{{ $t("booking.day") }}<span class="text-red-600">*</span></label>
            <v-date-picker
              mode="dateTime"
              is24hr
              :model-value="selectedTransaction.date"
              :locale="i18n.global.locale.value"
              :first-day-of-week="1"
              :disabled="!selectedTransaction.is_editable"
              @update:model-value="setDate"
            >
              <template #default="{ inputValue, inputEvents }">
                <input
                  :class="{ 'error-input': v$.date.$errors.length }"
                  :disabled="!selectedTransaction.is_editable"
                  class="block px-3 py-3 w-full border border-gray-700 focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                  :value="inputValue"
                  mode="mode"
                  v-on="inputEvents"
                >
              </template>
            </v-date-picker>
            <template v-if="!selectedTransaction.date">
              <p
                v-for="error of v$.date.$errors"
                :key="error.$uid"
                class="error-message"
              >
                {{ $t(error.$message) }}
              </p>
            </template>
          </div>

          <div class="flex flex-col">
            <TextInput
              id="grid-first-name"
              v-model="selectedTransaction.payment_method.name"
              :label="$t('dashboard.booking.paymentMethod')"
              :placeholder="$t('dashboard.booking.paymentMethod')"
              class="block py-3 mb-3 w-full leading-tight text-gray-700 rounded appearance-none ps-4 pe-4 focus:outline-none focus:bg-white disabled:bg-gray-300 disabled:text-gray-500 disabled:ring-gray-200"
              disabled
            />
          </div>
          <div class="flex flex-col">
            <TextInput
              id="grid-first-name"
              v-model="selectedTransaction.customer"
              :label="$t('form.customerName')"
              :placeholder="$t('form.customerName')"
              disabled
            />
          </div>
        </div>

        <div class="flex flex-col mt-4">
          <TextInput
            id="grid-first-name"
            v-model="selectedTransaction.status"
            :label="$t('booking.status')"
            :placeholder="$t('booking.status')"
            class="block py-3 mb-3 w-full leading-tight text-gray-700 rounded appearance-none ps-4 pe-4 focus:outline-none focus:bg-white disabled:bg-gray-300 disabled:text-gray-500 disabled:ring-gray-200"
            :class="[
              selectedTransaction.status === 'confirmed'
                ? 'border border-green-400 text-green-400'
                : selectedTransaction.status === 'refused'
                  ? 'border border-red-400 text-red-400'
                  : '',
            ]"
            disabled
          />
        </div>
      </div>
      <div
        v-if="selectedTransaction.status === 'pending'"
        class="flex gap-4 mt-6"
      >
        <BaseButton
          class="inline-flex hover:bg-green-700"
          custome-bg="bg-green-600"
          show-icon
          :processing="processing"
          @click="updateTransStatus('confirmed')"
        >
          {{ $t("form.approveTrans") }}
        </BaseButton>
        <BaseButton
          class="inline-flex hover:bg-red-700"
          custome-bg="bg-red-600"
          :processing="processing"
          @click="updateTransStatus('refused')"
        >
          {{ $t("form.rejectTrans") }}
        </BaseButton>
      </div>
      <BaseButton
        v-if="selectedTransaction.is_editable"
        class="inline-flex mt-6 hover:bg-primary-700"
        custome-bg="bg-primary-600"
        :processing="processing"
        @click="updateTransacrtion()"
      >
        {{ $t("form.update") }}
      </BaseButton>
    </Modal>

    <div class="justify-between items-center sm:flex">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("homepage.transaction") }}
      </h1>
      <div>
        <BaseButton
          type="button"
          :disabled="processingExport"
          class="inline-flex w-4 bg-green-600 disabled:bg-green-300 me-2"
          @click="getExcel()"
        >
          {{ $t("table.export_excel") }}
          <ArrowDownIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </div>
    <div class="grid grid-cols-1 gap-4 mt-8 md:grid-cols-3">
      <div>
        <SelectInput
          id="category"
          v-model="filterTransaction"
          :label="$t('table.status')"
          :placeholder="$t('form.select')"
        >
          <option value="" selected>
            {{ $t("form.all") }}
          </option>
          <option v-for="category of status" :key="category" :value="category">
            {{ $t(`transaction.${category}`) }}
          </option>
        </SelectInput>
      </div>

      <div>
        <label class="  mb-2 w-full flex items-center text-start text-[#261E27] text-base    ">{{
          $t("Date")
        }}</label>
        <div class="mt-1">
          <VueTailwindDatepicker
            v-model="range"
            :formatter="formatDate"
            :placeholder="$t('form.date_rage')"
            use-range
            input-classes="block w-full border-base-gray rounded    focus:border-primary-500 focus:ring-primary-500 sm:text-sm py-3  h-[48px]"
            :i18n="getLocale(locale)?.id === 'ar' ? 'ar-sa' : 'en'"
            :dir="getLocale(locale)?.direction === 'rtl' ? 'ltr' : 'rtl'"
            :options="locatizedOption"
          />
        </div>
      </div>
      <div>
        <SelectInput
          id="staff"
          v-model="paymentMethod"
          :placeholder="$t('transaction.payment_method')"
          :label="$t('transaction.payment_method')"
        >
          <option value="" selected>
            {{ $t("form.all") }}
          </option>
          <option
            v-for="method in payment_methods.data"
            :key="method.uuid"
            :value="method.id"
          >
            <span v-if="method.type == 'bank_transfer'">{{ method.bank }} ( {{ method.name }} )</span>
            <span v-else-if="method.type == 'third-party'">{{ $t("settings.apps.payment_app") }} (
              {{ method.name }} )</span>
            <span v-else>{{ method.name }}</span>
          </option>
        </SelectInput>
      </div>
      <filter-with-team v-model="team" />
      <div>
        <TextInput
          id="payment-gateway"
          v-model="paymentGatewayId"
          :placeholder="$t('transaction.payment_gateway_id')"
          :label="$t('transaction.payment_gateway_id')"
        />
      </div>
    </div>

    <div class="flex flex-col mt-8">
      <div class="">
        <generic-table
          :is-loading="transactionLoading"
          :data="tableData.transactionList"
          :headers="headers"
          tr-class="cursor-pointer"
          :on-row-click="navigateToPage"
          item-key="id"
        >
          <template #row="{ item }">
            <grid-td>
              {{ item.transaction_no }}
            </grid-td>
            <grid-td>
              {{ item.date }}
            </grid-td>
            <grid-td>{{ item.customer }}</grid-td>
            <grid-td>
              <span v-if="item.order" class="text-gray-900">
                {{ item.order.orderNum }}
              </span>
              <span v-else>-</span>
            </grid-td>
            <grid-td>{{ item.payment_method?.name }}</grid-td>
            <grid-td>
              <price-format
                :form-data="{
                  price: item?.amount,
                  currency: item?.currency,
                }"
              />
            </grid-td>
            <grid-td>
              <span
                v-if="item.status === 'refused'"
                class="inline-flex items-center px-2.5 py-0.5 text-sm font-medium text-red-800 bg-red-100 rounded"
              >{{ $t(`transaction.${item.status}`) }}</span>
              <span
                v-if="item.status === 'pending'"
                class="inline-flex items-center px-2.5 py-0.5 text-sm font-medium text-yellow-800 bg-yellow-100 rounded"
              >{{ $t(`transaction.${item.status}`) }}</span>
              <span
                v-if="item.status === 'confirmed'"
                class="inline-flex items-center px-2.5 py-0.5 text-sm font-medium text-green-800 bg-green-100 rounded"
              >{{ $t(`transaction.${item.status}`) }}</span>
            </grid-td>
          </template>
        </generic-table>
        <Pagination
          v-if="tableData.transactionList.length"
          :pagination-meta="tableData.paginationMeta"
          :pagination-links="tableData.paginationLinks"
          @change="changeData"
        />
      </div>
    </div>
  </div>
</template>
