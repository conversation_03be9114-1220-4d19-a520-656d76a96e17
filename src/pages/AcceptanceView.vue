<script setup lang='ts'>
const store = useTeamStore()

const route = useRoute()
const router = useRouter()

const acceptInvitation = (id: string, sign: string) => {
  store.acceptInvitation(id, sign)
    .then(async () => {
      await store.fetchTeams()
    })
    .finally(() => {
      router.push({ name: 'dashboard' })
    })
}

onMounted(() => {
  acceptInvitation(`${route.params.id}`, `${route.query.signature}`)
})
</script>

<template>
  <div class="flex min-h-full">
    <OverlayLoader />
  </div>
</template>
