<script setup lang="ts">
import { ArrowDownIcon, PlusIcon } from '@heroicons/vue/24/outline'
import type { PaginationLinks, PaginationMeta, Staff } from '@/types'
import type { Products } from '@/types/products'
import excelExport from '@/composables/useExcelExport'
import { useProductStore } from '@/stores/products'
const i18n = useI18n()
const { processingExport, getExcel } = excelExport('Products')
const { fetchProducts } = useProductStore()
const tableData = reactive({
  productList: [] as Products[],
  paginationMeta: {
    current_page: 1,
    from: 1,
    last_page: 1,
    links: [],
    path: '',
    per_page: 15,
    to: 15,
    total: 1,
  } as PaginationMeta,
  paginationLinks: {
    first: '',
    last: '',
    prev: null,
    next: null,
  } as PaginationLinks,
  processing: false,
  filters: {} as { [key: string]: string },
})

const created = (product: Products) => {
  tableData.productList = [...tableData.productList, product]
}
const showModal = ref(false)

function toggleModel() {
  showModal.value = !showModal.value
}

const getProducts = async (currentPage: number, filters: { [key: string]: string }) => {
  tableData.processing = true
  await fetchProducts(currentPage, filters).then((res) => {
    tableData.productList = res.data
    tableData.paginationLinks = res.links
    tableData.paginationMeta = res.meta
  }).finally(() => {
    tableData.processing = false
  })
}

onMounted(() => {
  getProducts(tableData.paginationMeta.current_page, tableData.filters)
})
const filterProducts = async (filters: { name: string; category: string; team: string }) => {
  tableData.filters = filters
  await getProducts(tableData.paginationMeta.current_page, filters)
}
const paginationChange = async (page: number) => {
  await getProducts(page, tableData.filters)
}

const getExcelFile = () => {
  const { showNotification } = useNotifications()
  getExcel().then(() => {
    showNotification({
      title: i18n.t('Success'),
      type: 'success',
      message: i18n.t('operations.emailSent'),
    })
  })
}
</script>

<template>
  <div>
    <add-products-modal
      v-if="showModal" :show-modal="showModal" :products="null" @created="created"
      @closed="toggleModel"
    />
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t(`homepage.${$route?.name}` || 'homepage.title') }}
      </h1>
      <div class="mt-4 sm:mt-0 sm:flex-none">
        <BaseButton
          type="button" :disabled="processingExport" class="inline-flex mr-1 w-4 bg-green-600 disabled:bg-green-300 me-2"
          @click="getExcelFile()"
        >
          {{ $t("table.export_excel") }}
          <ArrowDownIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
        <BaseButton class="inline-flex w-auto hover:bg-green-700" custome-bg="bg-green-600" @click="toggleModel()">
          {{ $t("form.create") }}
          <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </div>
    <products-filteration @changed="filterProducts" />
    <div class="flex flex-col mt-8">
      <div class="">
        <products-grid
          :products-list="tableData.productList"
          :is-loading="tableData.processing"
        />
        <Pagination
          v-if="tableData.productList.length" :pagination-meta="tableData.paginationMeta"
          :pagination-links="tableData.paginationLinks" class="px-4" @change="paginationChange"
        />
      </div>
    </div>
  </div>
</template>

