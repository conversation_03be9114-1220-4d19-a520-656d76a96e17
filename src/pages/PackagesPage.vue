<script setup lang="ts">
import { ArrowDownIcon, PlusIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import type { PaginationLinks, PaginationMeta, Service, Staff } from '@/types'
import excelExport from '@/composables/useExcelExport'
import { useTagStore } from '@/stores/tags'
import { useStaffStore } from '@/stores/staff'
import { randomBg } from '@/composables/randomBg'
const { fetchTags } = useTagStore()
const { tags } = storeToRefs(useTagStore())
const packagefilter = reactive({
  service_type: 'package',
})

const { processingExport, getExcel } = excelExport('Service', packagefilter)
const { fetchPackages } = useServicesStore()
const { fetchAllStaff } = useStaffStore()
const showModal = ref(false)
const tableData = reactive({
  servicesList: [] as Service[],
  paginationMeta: {
    current_page: 1,
    from: 1,
    last_page: 1,
    links: [],
    path: '',
    per_page: 15,
    to: 15,
    total: 1,
  } as PaginationMeta,
  paginationLinks: {
    first: '',
    last: '',
    prev: null,
    next: null,
  } as PaginationLinks,
  processing: false,
  filters: {} as { [key: string]: string },
})

const getServices = async (
  currentPage: number,
  filters: { [key: string]: string },
) => {
  tableData.processing = true
  await fetchPackages(currentPage, filters)
    .then((res) => {
      tableData.servicesList = res.data
      tableData.paginationLinks = res.links
      tableData.paginationMeta = res.meta
    })
    .finally(() => {
      tableData.processing = false
    })
}

onMounted(() => {
  Promise.all([
    getServices(tableData.paginationMeta.current_page, tableData.filters),
    fetchTags('services'),
  ])
})
const changeData = (page: number) => {
  getServices(page, tableData.filters)
}

const filterService = async (filters: {}) => {
  const tagsIds: string = filters.tags.join(',')
  tableData.filters = filters
  tableData.filters.tags = tagsIds
  await getServices(tableData.paginationMeta.current_page, tableData.filters)
}

const created = (service: Service) => {
  // update local data
  tableData.servicesList.unshift(service)
  tableData.paginationMeta.total += 1
  tableData.paginationMeta.to += 1
}

function toggleModel() {
  showModal.value = !showModal.value
}
</script>

<template>
  <div>
    <AddPacakagesModal
      v-if="showModal"
      :show-modal="showModal"
      :random="randomBg()"
      @created="created"
      @closed="toggleModel"
    />
    <div class="flex justify-between ms-auto me-auto max-w-12xl">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t(`homepage.${$route?.name}` || "homepage.title") }}
      </h1>
      <div class="mt-4 sm:mt-0 sm:flex-none">
        <!-- <BaseButton
          type="button"
          :disabled="processingExport"
          class="inline-flex mr-1 w-4 bg-green-600 disabled:bg-green-300 me-2"
          @click="getExcel()"
        >
          {{ $t("table.export_excel") }}
          <ArrowDownIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton> -->
        <BaseButton
          class="inline-flex w-auto hover:bg-green-700"
          custome-bg="bg-green-600"
          @click="toggleModel()"
        >
          {{ $t("form.create") }}
          <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </div>
    <!-- <service-filteration :tags="tags.data" @changed="filterService" /> -->
    <div class="flex flex-col mt-8">
      <div class="">
        <services-grid
          :service-list="tableData.servicesList"
          :is-loading="tableData.processing"
        />

        <Pagination
          v-if="tableData.servicesList.length"
          :pagination-meta="tableData.paginationMeta"
          :pagination-links="tableData.paginationLinks"
          class="px-4"
          @change="changeData"
        />
      </div>
    </div>
  </div>
</template>
