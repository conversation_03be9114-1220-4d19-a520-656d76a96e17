<script setup lang="ts">
import type { ComputedRef } from 'vue'
import type { ServiceMetricsItem, ServiceMetricsResponse, header } from '@/types'
import ServicesMetrics from '@/services/ServicesMetrics'

const { showNotification } = useNotifications()

const metrics = ref<ServiceMetricsResponse>({
  providers: [],
  services: [],
})
const processing = ref(true)
const loadingStates = ref<Record<string, boolean>>({})
const selectedProviders = ref<string[]>([])
const currentView = ref('table')
const providerSearch = ref('')
const serviceSearch = ref('')
const assignmentFilter = ref<'all' | 'assigned' | 'unassigned'>('all')

const { t } = useI18n()

const headers: ComputedRef<header[]> = computed(() => {
  const baseHeaders = [
    {
      title: t('service_metrics.services_and_providers'),
    },
  ]

  // If providers are selected, only show those provider columns
  const providerHeaders = selectedProviders.value.length > 0
    ? metrics.value.providers.filter(p => selectedProviders.value.includes(p.uuid))
    : metrics.value.providers

  const mappedHeaders = providerHeaders.map(provider => ({
    title: provider.name,
  }))

  return [...baseHeaders, ...mappedHeaders]
})

const getProviderIndex = (uuid: string): number => {
  return metrics.value.providers.findIndex(p => p.uuid === uuid)
}

const filteredProviders = computed(() => {
  if (!providerSearch.value)
    return metrics.value.providers
  return metrics.value.providers.filter(provider =>
    provider.name.toLowerCase().includes(providerSearch.value.toLowerCase()),
  )
})

const isProviderSelected = (uuid: string): boolean => {
  return selectedProviders.value.includes(uuid)
}

const hasSelectedProviders = computed(() => selectedProviders.value.length > 0)
const selectedProvidersCount = computed(() => selectedProviders.value.length)
const hasSingleSelectedProvider = computed(() => selectedProviders.value.length === 1)

const isServiceActiveForSelectedProvider = (service: ServiceMetricsItem): boolean => {
  if (selectedProviders.value.length !== 1)
    return false

  const providerUuid = selectedProviders.value[0]
  const providerIndex = getProviderIndex(providerUuid)

  return service.providers[providerIndex] === 1
}

const clearFilters = () => {
  selectedProviders.value = []
  providerSearch.value = ''
  serviceSearch.value = ''
  assignmentFilter.value = 'all'
}

const filteredServices = computed(() => {
  let services = metrics.value.services

  // Filter by assignment status first
  if (assignmentFilter.value !== 'all' && selectedProviders.value.length > 0) {
    const providerUuid = selectedProviders.value[0]
    const providerIndex = getProviderIndex(providerUuid)

    services = services.filter((service) => {
      const isAssigned = service.providers[providerIndex] === 1
      return assignmentFilter.value === 'assigned' ? isAssigned : !isAssigned
    })
  }
  else if (assignmentFilter.value !== 'all') {
    services = services.filter((service) => {
      const hasAnyAssignment = service.providers.includes(1)
      return assignmentFilter.value === 'assigned' ? hasAnyAssignment : !hasAnyAssignment
    })
  }

  // Filter by service name search if any
  if (serviceSearch.value.trim()) {
    const searchTerm = serviceSearch.value.toLowerCase().trim()
    services = services.filter(service =>
      service.name.toLowerCase().includes(searchTerm),
    )
  }

  return services
})

const toggleProviderFilter = (providerUuid: string) => {
  const index = selectedProviders.value.indexOf(providerUuid)
  if (index > -1)
    selectedProviders.value.splice(index, 1)
  else
    selectedProviders.value.push(providerUuid)
}

const fetchMetrics = async () => {
  const response = await ServicesMetrics.getServiceMetrics()
  metrics.value = response.data
}

const toggleService = async (serviceId: string, providerUuid: string) => {
  const key = `${serviceId}-${providerUuid}`
  loadingStates.value[key] = true

  try {
    const service = metrics.value.services.find(s => s.id === serviceId)
    const provider = metrics.value.providers.find(
      p => p.uuid === providerUuid,
    )

    if (!service || !provider)
      throw new Error(t('operations.error'))

    await ServicesMetrics.assignService(service.id, provider.uuid)

    const providerIndex = getProviderIndex(providerUuid)
    if (providerIndex >= 0 && providerIndex < service.providers.length) {
      const newStatus = service.providers[providerIndex] === 1 ? 0 : 1
      service.providers[providerIndex] = newStatus

      showNotification({
        title: newStatus === 1 ? t('service_metrics.service_assigned') : t('service_metrics.service_unassigned'),
        message: newStatus === 1
          ? t('service_metrics.service_assigned_success', { service: service.name, provider: provider.name })
          : t('service_metrics.service_unassigned_success', { service: service.name, provider: provider.name }),
        type: 'success',
      })
    }
    await fetchMetrics()
  }
  catch (error: any) {
    console.error(error)
    showNotification({
      title: t('service_metrics.update_error_title'),
      message: error.message || t('service_metrics.update_error_message'),
      type: 'error',
    })
  }
  finally {
    loadingStates.value[key] = false
  }
}

// Create computed properties for assigned and unassigned services
const assignedServices = computed(() => {
  if (selectedProviders.value.length !== 1)
    return filteredServices.value

  const providerUuid = selectedProviders.value[0]
  const providerIndex = getProviderIndex(providerUuid)

  return filteredServices.value.filter(service =>
    service.providers[providerIndex] === 1,
  )
})

const unassignedServices = computed(() => {
  if (selectedProviders.value.length !== 1)
    return []

  const providerUuid = selectedProviders.value[0]
  const providerIndex = getProviderIndex(providerUuid)

  return filteredServices.value.filter(service =>
    service.providers[providerIndex] === 0,
  )
})

onMounted(async () => {
  try {
    processing.value = true
    await fetchMetrics()
  }
  catch (error) {
    console.error(error)
  }
  finally {
    processing.value = false
  }
})

const refreshMetrics = async () => {
  try {
    processing.value = true
    await fetchMetrics()
    showNotification({
      title: t('service_metrics.metrics_refreshed'),
      message: t('service_metrics.metrics_refreshed_message'),
      type: 'success',
    })
  }
  finally {
    processing.value = false
  }
}
</script>

<template>
  <div class="flex justify-between items-center mb-8">
    <h1 class="text-2xl font-bold text-gray-800">
      {{ t('altNav.ServiceMetrics') }}
    </h1>
    <div class="flex gap-4">
      <!-- View toggle buttons -->
      <div class="flex overflow-hidden bg-gray-100 rounded-lg">
        <button
          class="px-4 py-2 text-sm font-medium transition-colors duration-200"
          :class="currentView === 'cards' ? 'bg-primary text-white' : 'text-gray-700 hover:bg-gray-200'"
          @click="currentView = 'cards'"
        >
          <span>{{ t('service_metrics.cards') }}</span>
        </button>
        <button
          class="px-4 py-2 text-sm font-medium transition-colors duration-200"
          :class="currentView === 'table' ? 'bg-primary text-white' : 'text-gray-700 hover:bg-gray-200'"
          @click="currentView = 'table'"
        >
          <span>{{ t('service_metrics.table') }}</span>
        </button>
      </div>
      <!-- Refresh button -->
      <button
        class="flex gap-2 items-center px-4 py-2 text-white rounded-lg shadow-md transition-all duration-300 transform bg-primary hover:bg-primary-dark hover:shadow-lg hover:scale-105"
        :disabled="processing"
        @click="refreshMetrics"
      >
        <span v-if="processing" class="inline-block animate-spin">⟳</span>
        <span>{{ t('service_metrics.refresh') }}</span>
      </button>
    </div>
  </div>

  <div
    v-if="processing"
    class="flex justify-center items-center h-64"
  >
    <overlay-loader :full-screen="false" />
  </div>

  <div v-else-if="metrics.services.length === 0" class="py-12 text-center bg-white rounded-lg shadow-md">
    <img src="@/assets/empty-state.svg" alt="No services" class="mx-auto mb-4 w-32 h-32 opacity-50">
    <p class="text-lg text-gray-500">
      {{ t('emptyState.services') }}
    </p>
  </div>

  <div v-else class="space-y-8">
    <!-- Provider Pills -->
    <div class="p-6 bg-white rounded-lg shadow-md">
      <div class="flex flex-col space-y-4">
        <!-- Header and Clear Button -->
        <div class="flex justify-between items-center">
          <div>
            <h2 class="text-lg font-semibold text-gray-800">
              {{ t('service_metrics.filter_by_providers') }}
            </h2>
            <p class="mt-1 text-sm text-gray-500">
              {{ t('service_metrics.select_providers_to_filter') }}
            </p>
          </div>
          <button
            v-if="hasSelectedProviders"
            class="flex gap-2 items-center px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg transition-all duration-200 hover:bg-gray-200 hover:text-gray-800"
            @click="clearFilters"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
            {{ t('service_metrics.clear_filter') }}
          </button>
        </div>

        <!-- Search input -->
        <div v-if="metrics.providers.length > 5" class="relative">
          <div class="flex absolute inset-y-0 right-0 items-center pr-3 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
          </div>
          <input
            v-model="providerSearch"
            type="text"
            :placeholder="t('service_metrics.search_provider')"
            class="block py-3 pr-10 pl-4 w-full text-gray-700 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
        </div>

        <!-- Filter status summary -->
        <div v-if="hasSelectedProviders" class="p-3 bg-blue-50 rounded-lg border border-blue-100">
          <div class="flex gap-2 items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
            </svg>
            <span class="font-medium text-blue-700">{{ t('service_metrics.active_filter') }}:</span>
            <span class="text-blue-600">{{ selectedProvidersCount }} {{ t('service_metrics.providers') }}</span>
          </div>
        </div>

        <!-- Provider selection -->
        <div class="flex flex-wrap gap-3">
          <div
            v-for="provider in filteredProviders"
            :key="provider.uuid"
            class="flex relative gap-2 items-center px-4 py-2.5 rounded-lg transition-all duration-200 cursor-pointer group"
            :class="{
              'bg-primary text-white shadow-md': isProviderSelected(provider.uuid),
              'bg-gray-100 text-gray-700 hover:bg-gray-200': !isProviderSelected(provider.uuid),
            }"
            @click="toggleProviderFilter(provider.uuid)"
          >
            <span class="text-sm font-medium">{{ provider.name }}</span>
            <div
              v-if="isProviderSelected(provider.uuid)"
              class="flex justify-center items-center w-5 h-5 bg-white bg-opacity-20 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="w-3 h-3" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        <!-- No search results -->
        <div
          v-if="filteredProviders.length === 0 && providerSearch"
          class="py-8 text-center text-gray-500 bg-gray-50 rounded-lg"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto mb-3 w-12 h-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <p>{{ t('service_metrics.no_matching_services') }}</p>
        </div>
      </div>
    </div>

    <!-- Search Services -->
    <div class="p-6 bg-white rounded-lg shadow-md">
      <div class="flex flex-col space-y-4">
        <div>
          <h2 class="text-lg font-semibold text-gray-800">
            {{ t('service_metrics.search_services') }}
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            {{ t('service_metrics.filter_by_assignment') }}
          </p>
        </div>

        <div class="relative">
          <div class="flex absolute inset-y-0 right-0 items-center pr-3 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
          </div>
          <input
            v-model="serviceSearch"
            type="text"
            :placeholder="t('service_metrics.search_service_name')"
            class="block py-3 pr-10 pl-4 w-full text-gray-700 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
        </div>

        <!-- Assignment filter buttons -->
        <div class="space-y-3">
          <h3 class="text-sm font-medium text-gray-600">
            {{ t('service_metrics.filter_by_assignment_status') }}
          </h3>
          <div class="flex gap-3">
            <button
              class="flex-1 py-2.5 text-sm font-medium rounded-lg transition-all duration-200"
              :class="assignmentFilter === 'all'
                ? 'bg-primary text-white shadow-md'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
              @click="assignmentFilter = 'all'"
            >
              {{ t('service_metrics.all') }}
            </button>
            <button
              class="flex flex-1 gap-2 justify-center items-center py-2.5 text-sm font-medium rounded-lg transition-all duration-200"
              :class="assignmentFilter === 'assigned'
                ? 'bg-primary text-white shadow-md'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
              @click="assignmentFilter = 'assigned'"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              {{ t('service_metrics.assigned') }}
            </button>
            <button
              class="flex flex-1 gap-2 justify-center items-center py-2.5 text-sm font-medium rounded-lg transition-all duration-200"
              :class="assignmentFilter === 'unassigned'
                ? 'bg-primary text-white shadow-md'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
              @click="assignmentFilter = 'unassigned'"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
              {{ t('service_metrics.unassigned') }}
            </button>
          </div>
        </div>

        <!-- Filter results summary -->
        <div
          v-if="serviceSearch.trim() || selectedProviders.length > 0 || assignmentFilter !== 'all'"
          class="p-3 bg-gray-50 rounded-lg border border-gray-200"
        >
          <div v-if="filteredServices.length === 0" class="text-center text-gray-500">
            {{ t('service_metrics.no_matching_services') }}
          </div>
          <div v-else class="flex gap-2 items-center text-gray-600">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
              <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
            </svg>
            <span class="font-medium">{{ filteredServices.length }}</span> {{ t('service_metrics.matching_services') }}
            <span v-if="metrics.services.length !== filteredServices.length">
              {{ t('service_metrics.out_of') }} <span class="font-medium">{{ metrics.services.length }}</span>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Table View -->
    <div v-if="currentView === 'table'" class="">
      <generic-table
        :headers="headers"
        :data="filteredServices"
        :is-loading="processing"
        item-key="id"
        sticky
        class="table-fixed"
      >
        <template #row="{ item }">
          <grid-td
            class="py-4 pr-3 pl-4 text-sm whitespace-nowrap sm:pl-6 w-[200px] min-w-[200px]"
            :default-style="false"
          >
            <div class="truncate">
              <span class="font-medium">{{ item.name }}</span>
            </div>
          </grid-td>

          <grid-td
            v-for="provider in (selectedProviders.length > 0
              ? metrics.providers.filter(p => selectedProviders.includes(p.uuid))
              : metrics.providers)"
            :key="provider.uuid"
            class="px-3 py-4 text-center whitespace-nowrap w-[140px] min-w-[140px]"
            :default-style="false"
          >
            <div class="text-gray-900">
              <button
                :disabled="loadingStates[`${item.id}-${provider.uuid}`]"
                class="px-3 py-1 rounded transition-all duration-300 text-sm font-medium"
                :class="[
                  loadingStates[`${item.id}-${provider.uuid}`]
                    ? 'opacity-60 cursor-not-allowed'
                    : '',
                  item.providers[getProviderIndex(provider.uuid)]
                    ? 'bg-green-500 hover:bg-green-600 text-white'
                    : 'bg-red-500 hover:bg-red-600 text-white',
                ]" @click.stop="toggleService(item.id, provider.uuid)"
              >
                <span
                  v-if="loadingStates[`${item.id}-${provider.uuid}`]"
                  class="inline-block mr-1 animate-spin"
                >⟳</span>
                <span v-else-if="item.providers[getProviderIndex(provider.uuid)]" class="flex gap-1 items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ t('service_metrics.assigned') }}
                </span>
                <span v-else class="flex gap-1 items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                  {{ t('service_metrics.unassigned') }}
                </span>
              </button>
            </div>
          </grid-td>
        </template>
      </generic-table>
    </div>

    <!-- Card View -->
    <div v-else>
      <!-- Assigned Services Section -->
      <div v-if="assignedServices.length > 0" class="mb-8">
        <h2 class="mb-4 text-xl font-medium text-gray-700">
          {{ t('service_metrics.assigned_services') }}
        </h2>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div
            v-for="service in assignedServices"
            :key="service.id"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02]"
            :class="{
              'border-l-4 border-green-500': hasSingleSelectedProvider && isServiceActiveForSelectedProvider(service),
              'border-l-4 border-gray-300': hasSingleSelectedProvider && !isServiceActiveForSelectedProvider(service),
            }"
          >
            <div
              class="p-5 border-b"
              :class="{
                'bg-green-50': hasSingleSelectedProvider && isServiceActiveForSelectedProvider(service),
                'bg-gray-50': hasSingleSelectedProvider && !isServiceActiveForSelectedProvider(service),
              }"
            >
              <h3 class="text-lg font-medium text-gray-800">
                {{ service.name }}
              </h3>
              <div v-if="hasSingleSelectedProvider" class="mt-1 text-sm">
                <span v-if="isServiceActiveForSelectedProvider(service)" class="text-green-600">
                  {{ t('service_metrics.active_for_selected_provider') }}
                </span>
                <span v-else class="text-gray-500">
                  {{ t('service_metrics.inactive_for_selected_provider') }}
                </span>
              </div>
            </div>

            <div class="p-5">
              <h4 class="mb-3 text-sm font-medium text-gray-500">
                {{ t('service_metrics.available_providers') }}
              </h4>
              <div class="space-y-3">
                <div
                  v-for="provider in metrics.providers"
                  :key="provider.uuid"
                  class="flex justify-between items-center"
                >
                  <span
                    class="text-gray-700"
                    :class="{
                      'font-bold': isProviderSelected(provider.uuid),
                      'text-primary': isProviderSelected(provider.uuid),
                    }"
                  >
                    {{ provider.name }}
                    <span v-if="isProviderSelected(provider.uuid)" class="ml-1 text-primary">★</span>
                  </span>
                  <button
                    :disabled="loadingStates[`${service.id}-${provider.uuid}`]"
                    class="px-4 py-2 rounded-md transition-all duration-300 text-sm font-medium relative overflow-hidden"
                    :class="[
                      loadingStates[`${service.id}-${provider.uuid}`]
                        ? 'opacity-60 cursor-not-allowed'
                        : '',
                      service.providers[getProviderIndex(provider.uuid)]
                        ? 'bg-green-500 hover:bg-green-600 text-white'
                        : 'bg-red-500 hover:bg-red-600 text-white',
                    ]" @click="toggleService(service.id, provider.uuid)"
                  >
                    <span
                      v-if="loadingStates[`${service.id}-${provider.uuid}`]"
                      class="inline-block mr-1 animate-spin"
                    >⟳</span>
                    <span v-else-if="service.providers[getProviderIndex(provider.uuid)]" class="flex gap-1 items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      {{ t('service_metrics.assigned') }}
                    </span>
                    <span v-else class="flex gap-1 items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                      </svg>
                      {{ t('service_metrics.unassigned') }}
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unassigned Services Section -->
      <div v-if="hasSingleSelectedProvider && unassignedServices.length > 0" class="mb-8">
        <h2 class="mb-4 text-xl font-medium text-gray-700">
          {{ t('service_metrics.unassigned_services') }}
        </h2>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div
            v-for="service in unassignedServices"
            :key="service.id"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] border-l-4 border-gray-300"
          >
            <div class="p-5 bg-gray-50 border-b">
              <h3 class="text-lg font-medium text-gray-800">
                {{ service.name }}
              </h3>
              <div class="mt-1 text-sm">
                <span class="text-gray-500">
                  {{ t('service_metrics.inactive_for_selected_provider') }}
                </span>
              </div>
            </div>

            <div class="p-5">
              <h4 class="mb-3 text-sm font-medium text-gray-500">
                {{ t('service_metrics.available_providers') }}
              </h4>
              <div class="space-y-3">
                <div
                  v-for="provider in metrics.providers.filter(p => selectedProviders.includes(p.uuid))"
                  :key="provider.uuid"
                  class="flex justify-between items-center"
                >
                  <span class="font-bold text-gray-700 text-primary">
                    {{ provider.name }}
                    <span class="ml-1 text-primary">★</span>
                  </span>
                  <button
                    :disabled="loadingStates[`${service.id}-${provider.uuid}`]"
                    class="overflow-hidden relative px-4 py-2 text-sm font-medium text-white bg-red-500 rounded-md transition-all duration-300 hover:bg-red-600"
                    :class="{ 'opacity-60 cursor-not-allowed': loadingStates[`${service.id}-${provider.uuid}`] }"
                    @click="toggleService(service.id, provider.uuid)"
                  >
                    <span
                      v-if="loadingStates[`${service.id}-${provider.uuid}`]"
                      class="inline-block mr-1 animate-spin"
                    >⟳</span>
                    <span class="flex gap-1 items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                      </svg>
                      {{ t('service_metrics.unassigned') }}
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Services Message -->
      <div v-if="assignedServices.length === 0 && (!hasSingleSelectedProvider || unassignedServices.length === 0)" class="py-12 text-center bg-white rounded-lg shadow-md">
        <img src="@/assets/empty-state.svg" alt="No services" class="mx-auto mb-4 w-32 h-32 opacity-50">
        <p class="text-lg text-gray-500">
          {{ t('service_metrics.no_matching_filters') }}
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.shadow-l {
  box-shadow: -4px 0 6px -1px rgb(0 0 0 / 0.1);
}
</style>
