<script lang="ts" setup>
import { storeToRefs } from 'pinia'
const { getUserInfo } = storeToRefs(useAuthStore())
const { hideUpperNav, chanageUpperNavVisibiltiy } = useLayout()
onMounted(() => {
  chanageUpperNavVisibiltiy(false)
})
onUnmounted(() => {
  chanageUpperNavVisibiltiy(true)
})
</script>

<template>
  <div class="w-full h-screen">
    <embed
      v-if="getUserInfo.tenant.analytics_link"
      :src="getUserInfo.tenant.analytics_link"
      type="text/html"
      width="100%"
      height="100%"
    >
    <div v-else class="flex flex-col items-center mt-3 h-full">
      <div class="w-full text-center rounded-lg">
        <h2 class="mb-4 text-3xl font-bold text-primary-700">
          {{ $t("view_store_statistics") }}
        </h2>
        <button
          class="px-4 py-2 font-bold text-white rounded transition duration-300 ease-in-out transform bg-primary-600 hover:bg-primary-700 hover:scale-105 min-w-[200px] animate-pulse hover:animate-none mb-4"
          @click="$router.push({ name: 'plans' })"
        >
          {{ $t("subscribe") }}
        </button>
        <img
          src="../assets/AnalyticsPagePlaceHolder.jpg"
          alt="analytics"
          class="object-contain w-full rounded-lg shadow-sm"
        >
      </div>
    </div>
  </div>
</template>
