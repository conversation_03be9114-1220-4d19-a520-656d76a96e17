<script setup lang="ts">
import { storeToRefs } from 'pinia'
import type { PluginType } from '@/types'
const { installPlugin, fetchPlugins, uninstallPlugin } = usePluginsStore()
const { getPlugins, pluginLoading } = storeToRefs(usePluginsStore())

const toggleActive = async (item: PluginType) => {
  if (!item.installed)
    await install(item.uuid)
  else
    await uninstall(item.uuid)

  fetchPlugins()
}
const install = async (app: string) => {
  await installPlugin(app)
}
const uninstall = async (app: string) => {
  await uninstallPlugin(app)
}
onMounted(async () => {
  await fetchPlugins()
})
</script>

<template>
  <div>
    <div class="flex flex-col gap-8">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-semibold text-gray-900">
            {{ $t("plugins.heading") }}
          </h1>
        </div>
      </div>
      <div
        class="relative grid grid-cols-1 gap-8 py-5 lg:grid-cols-3 md:grid-cols-2 auto-rows-fr"
      >
        <OverlayLoader v-if="pluginLoading" :full-screen="false" />
        <PluginCard
          v-for="item in getPlugins"
          :key="item.uuid"
          class="flex flex-grow"
          :app="item"
          @toggle-active="toggleActive(item)"
        />
      </div>
    </div>
  </div>
</template>
