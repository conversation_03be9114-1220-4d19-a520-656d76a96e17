<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { difference } from 'lodash'
import { GoogleMap, InfoWindow, Marker, Polygon } from 'vue3-google-map'
const emits = defineEmits(['close'])
const API_KEY = import.meta.env.VITE_GOOGLE_MAP_KEY
const { coords } = useMap()

const router = useRouter()
const center = ref()
const polygonTemplate = ref({
  strokeColor: '#FF0000',
  strokeOpacity: 0.8,
  strokeWeight: 2,
  fillColor: '#FF0000',
  fillOpacity: 0.35,
  editable: false,
  draggable: false,
  geodesic: true,
  paths: [],
  InfoWindow: {
    content: '',
    position: {},
  },
})

const polygons = ref([]) as any[]

onMounted(() => {
  center.value = {
    lat: coords.value.latitude,
    lng: coords.value.longitude,
  }
})
const proccessing = ref(false)

const { userInfo } = storeToRefs(useAuthStore())
const { fetchStaffPage, tableData, fetchStaffBoundariesList } = useStaff()

const staffList = computed(() => {
  return tableData.staffList.filter(
    staff => staff.team_id === form.value.team_id,
  )
})

onMounted(async () => {
  await fetchStaffPage()
})
const form = ref({
  team_id: '',
  staff_id: [],
})
const crumbs = ref([
  {
    name: 'homepage.boundaries',
    path: '/management/boundaries',
  },
  {
    name: 'staffs_boundaries',
    path: '/management/boundaries/staffs',
  },
])
const { t } = useI18n()
const updateStaff = async (value) => {
  const [selectedStaff] = difference(value, form.value.staff_id)
  const data = await fetchStaffBoundariesList(selectedStaff)
  const avaliablesPolygons = data
    .filter(polygon => polygon.selected)
    .map((polygon) => {
      return {
        polygon: polygon.boundary.coordinates[0].map(([lng, lat]) => {
          return { lat, lng }
        }),
        content: `<div>${t('name_of_boundary')} : ${polygon.name}</div>
                <div>${t('fields.staff_id')} : ${selectedStaff.name}</div>
                <div>${t('fields.team_id')} : ${selectedStaff.team?.name}</div>
                `,
      }
    })
  avaliablesPolygons.forEach((polygon) => {
    polygons.value.push({
      staffId: selectedStaff.uuid,
      ...polygonTemplate.value,
      paths: polygon.polygon,
      InfoWindow: {
        content: polygon.content,
        position: calcRandomPointInPolygon(polygon.polygon),
      },
    })
  })
}

const remove = (removedOption) => {
  const staffId = removedOption.uuid
  polygons.value = polygons.value.filter(
    polygon => polygon.staffId !== staffId,
  )
  // let index = polygons.findIndex((polygon) => polygon.staffId === staffId);
  // polygons.splice(index, 1);
}

const calcRandomPointInPolygon = (polygon) => {
  // Pick two random points from the polygon edges
  const index1 = Math.floor(Math.random() * polygon.length)
  const index2 = Math.floor(Math.random() * polygon.length)

  const point1 = polygon[index1]
  const point2 = polygon[index2]

  // Calculate a random point between the two points
  const lat = (point1.lat + point2.lat) / 2
  const lng = (point1.lng + point2.lng) / 2

  return { lat, lng }
}
const clearMap = () => {
  polygons.value.splice(0, polygons.value.length)
  form.value.staff_id = []
  form.value.team_id = ''
  center.value = {
    lat: coords.value.latitude,
    lng: coords.value.longitude,
  }
}
</script>

<template>
  <div>
    <div class="flex flex-col">
      <bread-crumb :crumbs="crumbs" class="mt-5" />
      <div class="grid w-full grid-cols-1 mt-6 gap-y-6 gap-x-4 md:grid-cols-3">
        <div>
          <SelectInput
            id="branch"
            v-model="form.team_id"
            :label="$t('fields.team_id')"
            class="block w-full text-gray-700 border-gray-300 rounded-md"
            @change="form.staff_id = ''"
          >
            <option value="">
              {{ $t("form.select") }}
            </option>
            <option
              v-for="team in userInfo.teams"
              :key="team.uuid"
              :value="team.uuid"
            >
              {{ team.name }}
            </option>
          </SelectInput>
        </div>
        <div>
          <LabelInput for="staff" class="mb-2">
            {{ $t("fields.staff_id") }}
          </LabelInput>
          <Multiselect
            :multiple="true"
            track-by="uuid"
            id="staff"
            label="name"
            v-model="form.staff_id"
            :options="staffList"
            :placeholder="$t('form.select')"
            class="block w-full text-gray-700 border-gray-300 rounded-md"
            @remove="remove"
            @update:model-value="updateStaff"
          />
        </div>
        <div v-if="polygons.length" class="self-end ms-auto">
          <base-button custome-bg="bg-red-600" @click="clearMap">
            {{ $t("clear_map") }}
          </base-button>
        </div>
      </div>

      <div class="w-full h-[1000px] rounded-lg mt-4 overflow-hidden">
        <GoogleMap
          ref="map"
          :api-key="API_KEY"
          class="w-full h-full rounded-lg"
          :center="center"
          :zoom="13"
        >
          <Polygon v-for="polygon in polygons" :options="polygon" />
          <InfoWindow
            v-for="polygon in polygons"
            :options="polygon.InfoWindow"
          />
        </GoogleMap>
      </div>
    </div>
  </div>
</template>
