<script lang="ts" setup>
import { PlusIcon } from '@heroicons/vue/24/outline'
import type { Offers } from '@/types'
import { useOffers } from '@/stores/offers'
const { fetchAllServices } = useServicesStore()
const { getListOffers } = useOffers()
const selectedOffers = ref<Offers | null>({
  name: '',
  start_at: new Date(),
  end_at: new Date(),
  // status: true,
  booking_page: true,
  webapp: true,
  services: [],
})

const servicesOptions = ref([])
onMounted(async () => {
  await getListOffers()
  await fetchAllServices().then((res) => {
    servicesOptions.value = res.data.map((service) => {
      return {
        label: service.name,
        value: service.uuid,
        price: service.price,
      }
    })
  })
})
const showModal = ref(false)
const openCreateModal = () => {
  selectedOffers.value = null
  showModal.value = true
}

const openEditModal = (offfer: Offers) => {
  selectedOffers.value = offfer
  showModal.value = true
}
</script>

<template>
  <div class="flex flex-col gap-8">
    <OffersModal
      v-if="showModal"
      :show-modal="showModal"
      :offer="selectedOffers"
      :services-options="servicesOptions"
      @closed="showModal = false"
    />
    <div class="flex justify-between itmes-center">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("offers.title") }}
      </h1>
      <BaseButton
        class="inline-flex w-auto hover:bg-green-700"
        custome-bg="bg-green-600"
        @click="openCreateModal"
      >
        {{ $t("form.create") }}
        <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </div>
    <div class="flex flex-col mt-4">
      <div class="">
        <offers-grid @open-edit-modal="openEditModal" />
      </div>
    </div>
  </div>
</template>
