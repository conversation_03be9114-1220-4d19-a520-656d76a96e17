<script setup lang="ts">
import { ArrowDownIcon, PlusIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import type { PaginationLinks, PaginationMeta, Service, Staff } from '@/types'
import excelExport from '@/composables/useExcelExport'
import { useTagStore } from '@/stores/tags'
import { useStaffStore } from '@/stores/staff'
import { randomBg } from '@/composables/randomBg'
const { fetchTags } = useTagStore()
const { tags } = storeToRefs(useTagStore())
const { processingExport, getExcel } = excelExport('Service')
const { fetchServices } = useServicesStore()
const { fetchAllStaff } = useStaffStore()
const i18n = useI18n()

const showModal = ref(false)
const tableData = reactive({
  servicesList: [] as Service[],
  paginationMeta: {
    current_page: 1,
    from: 1,
    last_page: 1,
    links: [],
    path: '',
    per_page: 15,
    to: 15,
    total: 1,
  } as PaginationMeta,
  paginationLinks: {
    first: '',
    last: '',
    prev: null,
    next: null,
  } as PaginationLinks,
  processing: false,
  filters: {} as { [key: string]: string },
})

const teamStaff = ref<Staff[]>([])

const getServices = async (
  currentPage: number,
  filters: { [key: string]: string },
) => {
  tableData.processing = true
  await fetchServices(currentPage, filters)
    .then((res) => {
      tableData.servicesList = res.data
      tableData.paginationLinks = res.links
      tableData.paginationMeta = res.meta
    })
    .finally(() => {
      tableData.processing = false
    })
}

const fetchAllBranchStaff = async () => {
  const { data } = await fetchAllStaff()
  console.log(data)
  teamStaff.value = data
}

onMounted(() => {
  Promise.all([
    getServices(tableData.paginationMeta.current_page, tableData.filters),
    fetchTags('services'),
    fetchAllBranchStaff(),
  ])
})
const changeData = (page: number) => {
  getServices(page, tableData.filters)
}

const filterService = async (filters: {}) => {
  const tagsIds: string = filters.tags.join(',')
  tableData.filters = filters
  tableData.filters.tags = tagsIds
  await getServices(tableData.paginationMeta.current_page, tableData.filters)
}

const created = (service: Service) => {
  // update local data
  tableData.servicesList.unshift(service)
  tableData.paginationMeta.total += 1
  tableData.paginationMeta.to += 1
}

function toggleModel() {
  showModal.value = !showModal.value
}

const getExcelFile = () => {
  const { showNotification } = useNotifications()

  getExcel().then(() => {
    showNotification({
      title: i18n.t('Success'),
      type: 'success',
      message: i18n.t('operations.emailSent'),
    })
  })
}
const isOpenDetailsModal = ref(false)
const serviceUuid = ref('')
const openShowDetailsModal = (uuid: string) => {
  serviceUuid.value = uuid
  isOpenDetailsModal.value = true
}
</script>

<template>
  <div>
    <add-services-modal
      v-if="showModal"
      :show-modal="showModal"
      :random="randomBg()"
      :title="$t(`modalHeader.service`)"
      :subtitle="$t(`modalSubtitle.service`)"
      :team-staff="teamStaff"
      @created="created"
      @closed="toggleModel"
    />
    <show-service-modal
      v-if="isOpenDetailsModal"
      :show-modal="isOpenDetailsModal"
      :service-uuid="serviceUuid"
      @closed="isOpenDetailsModal = false"
      @refresh="
        getServices(tableData.paginationMeta.current_page, tableData.filters)
      "
    />
    <div class="flex justify-between ms-auto me-auto max-w-12xl">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t(`homepage.${$route?.name}` || "homepage.title") }}
      </h1>
      <div class="gap-2 mt-4 sm:mt-0 sm:flex-none">
        <router-link
          to="/management/service-metrics"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md border border-transparent shadow-sm bg-primary hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        >
          {{ $t("service_metrics.assign_providers") }}
        </router-link>
        <BaseButton
          type="button"
          :disabled="processingExport"
          class="inline-flex mr-1 w-4 bg-green-600 disabled:bg-green-300 me-2"
          @click="getExcelFile()"
        >
          {{ $t("table.export_excel") }}
          <ArrowDownIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
        <BaseButton
          class="inline-flex w-auto hover:bg-green-700"
          custome-bg="bg-green-600"
          @click="toggleModel()"
        >
          {{ $t("form.create") }}
          <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </div>
    <service-filteration :tags="tags.data" @changed="filterService" />
    <div class="flex flex-col mt-8">
      <div class="">
        <services-grid
          :service-list="tableData.servicesList"
          :is-loading="tableData.processing"
          @open-show-details-modal="openShowDetailsModal"
        />
        <Pagination
          v-if="tableData.servicesList.length"
          :pagination-meta="tableData.paginationMeta"
          :pagination-links="tableData.paginationLinks"
          class="px-4"
          @change="changeData"
        />
      </div>
    </div>
  </div>
</template>
