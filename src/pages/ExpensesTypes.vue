<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { PlusIcon } from '@heroicons/vue/20/solid'
import type { ExpenseType } from '@/types'
import AddExpenseTypeModal from '@/components/Expenses/AddExpenseTypeModal.vue'
import ExpenseTypesTable from '@/components/Expenses/ExpenseTypesTable.vue'
import { useExpenseTypesStore } from '@/stores/expenseTypes'

const { getLocale } = storeToRefs(useLocalesStore())
const { locale, t } = useI18n()
const route = useRoute()
const router = useRouter()
const processing = ref(false)
const showModal = ref(false)
const showConfModal = ref(false)
const selectedExpenseType = ref<ExpenseType | null>(null)
const expenseTypeToDelete = ref<ExpenseType | null>(null)
const expenseTypes = ref<ExpenseType[]>([])

const { fetchExpenseTypes, createExpenseType, updateExpenseType, deleteExpenseType } = useExpenseTypesStore()

const crumbs = ref([
  {
    name: 'homepage.expense-types',
    path: '/management/expense-types',
  },
  {
    name: '',
    path: '',
  },
])

const loadExpenseTypes = async () => {
  processing.value = true
  try {
    const response = await fetchExpenseTypes()
    expenseTypes.value = response.data
  }
  finally {
    processing.value = false
  }
}

const handleCreate = () => {
  selectedExpenseType.value = null
  showModal.value = true
}

const handleEdit = (expenseType: ExpenseType) => {
  selectedExpenseType.value = expenseType
  showModal.value = true
}

const handleDelete = (expenseType: ExpenseType) => {
  expenseTypeToDelete.value = expenseType
  showConfModal.value = true
}

const handleCreated = async () => {
  showModal.value = false
  await loadExpenseTypes()
}

const handleUpdated = async (updatedExpenseType: ExpenseType) => {
  showModal.value = false
  await loadExpenseTypes()
}

onMounted(() => {
  loadExpenseTypes()
})
</script>

<template>
  <div class="flex relative flex-col m-4">
    <bread-crumb :crumbs="crumbs" class="mt-5" />
  </div>

  <confirmation-modal
    v-if="showConfModal"
    :is-open="showConfModal"
    :api-call="deleteExpenseType"
    :record-id="expenseTypeToDelete?.uuid"
    @closed="showConfModal = false"
    @removed="loadExpenseTypes"
  >
    <p class="leading-7 text-start">
      {{ $t("confirmModal.msg") }}
    </p>
  </confirmation-modal>

  <div class="flex justify-between ms-auto me-auto max-w-12xl">
    <h1 class="text-2xl font-semibold text-gray-900">
      {{ $t(`homepage.${String($route?.name)}` || "homepage.title") }}
    </h1>
    <div class="mt-4 sm:mt-0 sm:flex-none">
      <BaseButton class="inline-flex w-auto hover:bg-green-700" custome-bg="bg-green-600" @click="handleCreate">
        {{ $t("form.create") }}
        <PlusIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </div>
  </div>

  <AddExpenseTypeModal
    v-if="showModal"
    :show-modal="showModal"
    :expense-type="selectedExpenseType"
    @created="handleCreated"
    @updated="handleUpdated"
    @closed="showModal = false"
  />

  <div class="flex flex-col mt-8">
    <div class="">
      <ExpenseTypesTable
        :expense-types="expenseTypes"
        :is-loading="processing"
        @edit="handleEdit"
        @delete="handleDelete"
      />
    </div>
  </div>
</template>
