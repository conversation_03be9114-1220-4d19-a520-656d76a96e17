<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import VueTailwindDatepicker from 'vue-tailwind-datepicker'
import {
  ArrowDownIcon,
  MagnifyingGlassPlusIcon,
} from '@heroicons/vue/24/outline'
import daysjs from 'dayjs'
import reportServices from '@/services/ReportServices'
import { locatizedOption } from '@/composables/useDatePicker'
const { locale } = useI18n()
const { getLocale } = storeToRefs(useLocalesStore())

const formatDate = ref({
  date: 'DD-MM-YYYY',
  month: 'MMM',
})
const proccessing = ref(false)
const reportGroups = ref([])
const selectedReport = ref('')
const filters = ref({})
const reportHtml = ref(null)

const getReport = async () => {
  proccessing.value = true
  const report = selectedReport.value

  if (!report)
    return

  try {
    const res = await reportServices.fetchReport(
      report.slug,
      report.parent,
      filters.value,
    )
    reportHtml.value = res.data
  }
  finally {
    proccessing.value = false
  }
}
onMounted(async () => {
  try {
    proccessing.value = true
    const res = await reportServices.fetchReports()
    reportGroups.value = res.data
  }
  finally {
    proccessing.value = false
  }
})

function download_table_as_csv(separator = ',') {
  if (selectedReport && selectedReport.value) {
    const rows = document.querySelectorAll('table' + ' tr')
    const csv = []
    for (let i = 0; i < rows.length; i++) {
      const row = []
      const cols = rows[i].querySelectorAll('td, th')
      for (let j = 0; j < cols.length; j++) {
        let data = cols[j].innerText
          .replace(/(\r\n|\n|\r)/gm, '')
          .replace(/(\s\s)/gm, ' ')
        data = data.replace(/"/g, '""')
        row.push(`"${data}"`)
      }
      csv.push(row.join(separator))
    }
    const csv_string = `\uFEFF${csv.join('\n')}` // Add BOM character at the beginning
    const filename = `${selectedReport.value.parent}_${
      selectedReport.value.group
    }_${new Date().toLocaleDateString()}.csv`
    const link = document.createElement('a')
    link.style.display = 'none'
    link.setAttribute('target', '_blank')
    link.setAttribute(
      'href',
      `data:text/csv;charset=utf-8,${encodeURIComponent(csv_string)}`,
    )
    link.setAttribute('download', filename)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}
watch(selectedReport, (newValue) => {
  if (!newValue)
    return
  const types = Object.entries(newValue.filters).map(([key, value]) => [
    key,
    value.type,
  ])
  types.forEach(([key, type]) => {
    filters.value[key] = type === 'date_range' ? [] : ''
  })
})
// const generateCSV = () => {
//   if (reportHtml.value) {
//     createDownloadLink(`${selectedReport.value.parent}_${selectedReport.value.group}`, reportHtml.value)
//       .then((link) => {
//         link.click()
//       })
//   }
// }

// check after select date range if more than 31 days range then reset the range to only 31 days and show error message
const validateRange = (date) => {
  if (date[0] && date[1]) {
    // Convert DD-MM-YYYY to YYYY-MM-DD for proper parsing
    const [startDay, startMonth, startYear] = date[0].split('-')
    const [endDay, endMonth, endYear] = date[1].split('-')

    const startFormatted = `${startYear}-${startMonth}-${startDay}`
    const endFormatted = `${endYear}-${endMonth}-${endDay}`

    const start = daysjs(startFormatted)
    const end = daysjs(endFormatted)
    const diffDays = end.diff(start, 'day')

    if (diffDays > 30) {
      // Reset to only show 30 days from start date
      const newEndDate = start.add(30, 'day').format('DD-MM-YYYY')
      const dateRangeKey = Object.keys(filters.value).find(key =>
        selectedReport.value?.filters[key]?.type === 'date_range',
      )
      if (dateRangeKey) {
        filters.value[dateRangeKey] = [date[0], newEndDate]
        alert('Maximum range is 30 days. End date has been adjusted.')
      }
    }
  }
}
</script>

<template>
  <div>
    <h1 class="text-xl font-semibold text-gray-900">
      {{ $t("settings.reports.heading") }}
    </h1>
    <div class="mt-3">
      <div class="grid relative grid-cols-1 gap-2 md:grid-cols-4">
        <OverlayLoader v-if="proccessing" />

        <div class="flex relative flex-col justify-between">
          <label
            for="selectedReport"
            class="mb-2 w-full flex items-center text-start text-[#261E27] text-base "
          >{{ $t("choose report") }}</label>
          <select
            id="selectedReport"
            v-model="selectedReport"
            class="block w-full rounded-md border-gray-300 focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          >
            <option value="" disabled selected class="relative text-gray-400">
              {{ $t("form.select") }}
            </option>

            <optgroup
              v-for="(reports, group) in reportGroups"
              :label="$t(`reports.group.${group}`)"
            >
              <option
                v-for="report in reports"
                :value="{ ...report, g_name: group }"
              >
                {{ $t(`reports.slug.${report.parent}`) }} - {{ report.title }}
              </option>
            </optgroup>
          </select>
        </div>
        <template v-if="selectedReport">
          <div
            v-for="(value, key) in selectedReport.filters"
            :key="filters[key]"
            class="flex flex-col justify-between"
          >
            <label
              for=""
              class="mb-2 w-full flex items-center text-start text-[#261E27] text-base    "
            >{{ $t(`form.${key}`) }}</label>
            <input
              v-if="value.type === 'text'"
              v-model="filters[key]"
              :placeholder="$t(`form.${key}`)"
              class="block px-3 py-2 w-full rounded-md border border-gray-300 focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
            <select
              v-if="value.type === 'select'"
              v-model="filters[key]"
              class="block w-full rounded-md border-gray-300 focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              <option value="">
                {{ $t("form.select") }}
              </option>
              <option
                v-for="option in value.options"
                :value="option.value || option"
              >
                {{ $t(option.label || option) }}
              </option>
            </select>

            <VueTailwindDatepicker
              v-if="value.type === 'date_range'"
              v-model="filters[key]"
              :formatter="formatDate"
              use-range

              class="block rounded-md border-gray-300 sm:text-sm"
              :i18n="getLocale(locale)?.id === 'ar' ? 'ar-sa' : 'en'"
              :placeholder="$t('form.date_rage')"
              :dir="getLocale(locale)?.direction === 'rtl' ? 'ltr' : 'rtl'"
              :options="locatizedOption"
              @update:model-value="validateRange"
            />
          </div>
        </template>
      </div>
    </div>
    <div class="flex mt-3">
      <BaseButton
        :disabled="!selectedReport"
        class="flex inline-flex justify-center items-center px-2 py-2 w-4 font-medium text-white bg-green-600 rounded-md rounded-r-md transition w-fit group sm:px-4 disabled:bg-primary-300 disabled:cursor-not-allowed bg-primary-600 disabled:bg-green-300 me-1"
        @click="getReport"
      >
        <MagnifyingGlassPlusIcon class="w-6 h-6" />
        {{ $t("viewReport") }}
      </BaseButton>
      <BaseButton
        type="button"
        :disabled="!reportHtml"
        class="flex inline-flex justify-center items-center px-2 py-2 w-4 font-medium text-white bg-green-600 rounded-md rounded-r-md transition w-fit group sm:px-4 disabled:bg-primary-300 disabled:cursor-not-allowed bg-primary-600 disabled:bg-green-300 me-1"
        @click="download_table_as_csv()"
      >
        {{ $t("table.export_excel") }}
        <ArrowDownIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
      </BaseButton>
    </div>
    <div v-if="!reportHtml || proccessing" class="p-5 mt-5 text-center border">
      {{ $t("emptyState.report") }}
    </div>
    <div
      v-else
      class="overflow-scroll relative p-5 mt-5 w-full border hide-scroll"
    >
      <overlay-loader v-if="processing" :full-screen="false" />
      <div v-html="reportHtml" />
    </div>
  </div>
</template>

<style></style>
