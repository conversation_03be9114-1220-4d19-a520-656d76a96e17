<script setup lang="ts">
import { ArrowDownIcon } from '@heroicons/vue/24/outline'
import { storeToRefs } from 'pinia'
import useBooking from '@/composables/useBooking'
import excelExport from '@/composables/useExcelExport'
import type { Booking, BookingFilters } from '@/types'
import i18n from '@/i18n'
import { useTagStore } from '@/stores/tags'
import { useAppsStore } from '@/stores/apps'
import useNotifications from '@/composables/useNotifications'
const { showNotification } = useNotifications()

const tagStore = useTagStore()
const { tags } = storeToRefs(tagStore)

const { tableData, fetchEventpage } = useBooking()

const { processingExport } = excelExport('Booking')

const bookingFilters = reactive({
  BookingNum: '',
  orderNum: '',
  customerName: '',
  from: '',
  to: '',
  service: '',
  tags: '',
  status: '',
  staff: '',
  team: '',
})

const filterBookings = (filters: BookingFilters) => {
  const {
    BookingNum,
    name,
    from,
    to,
    service,
    tagsLists,
    staff,
    status,
    team,
    orderNum,

  } = filters
  const ids = [...tagsLists].map(i => i.id)

  const currentPage = tableData.paginationMeta.current_page

  const params = {
    customerName: name,
    BookingNum,
    from,
    to,
    service,
    tags: ids.join(','),
    status,
    staff,
    team,
    orderNum,
  }

  bookingFilters.customerName = params.customerName || ''
  bookingFilters.from = params.from || ''
  bookingFilters.to = params.to || ''
  bookingFilters.BookingNum = params.BookingNum || ''
  bookingFilters.service = params.service || ''
  bookingFilters.tags = params.tags
  bookingFilters.status = params.status || ''
  bookingFilters.staff = params.staff
  bookingFilters.team = params.team
  bookingFilters.orderNum = params.orderNum || ''
  fetchEventpage(false, currentPage, params)
}
onMounted(async () => {
  await Promise.all([fetchEventpage(), tagStore.fetchTags('bookings')])
})
const changeData = (page: number) => {
  fetchEventpage(false, page, bookingFilters)
}

const getFilteredExcel = () => {
  const { getExcel } = excelExport('Booking', bookingFilters)

  getExcel(true, {
    'Content-Type': 'application/json',
  }).then(({ message }) => {
    showNotification({
      title: i18n.global.t('Success'),
      type: 'success',
      message,
    })
  })
}

const isOpenOrderDetailsModal = ref(false)
const orderId = ref('')
const selectedSlot = ref({})
const openEvent = (order_id: string, booking_id: string) => {
  orderId.value = order_id
  isOpenOrderDetailsModal.value = true
}

const closeBookingModal = () => {
  isOpenOrderDetailsModal.value = false
  orderId.value = ''
}

const fetchData = async () => {
  tableData.processing = true
  fetchEventpage()
  tableData.processing = false
}
</script>

<template>
  <div>
    <BookingUpsertModal
      :booking="tableData.selectedBooking"
      :show-modal="showModal"
      :tags="tags.data"
      @closed="toggleModal"
      @created="bookingCreated"
    />
    <OrderDetailsModal
      v-if="isOpenOrderDetailsModal"
      :is-open="isOpenOrderDetailsModal"
      :order-id="orderId"
      @close="fetchData();isOpenOrderDetailsModal = false"
    />

    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-semibold text-gray-900">
        {{ $t("Bookings") }}
      </h1>

      <div class="mx-4 mt-4 sm:mt-0 sm:flex-none">
        <BaseButton
          type="button"
          :disabled="processingExport"
          class="inline-flex w-4 bg-green-600 disabled:bg-green-300 me-2"
          @click="getFilteredExcel()"
        >
          {{ $t("table.export_excel") }}
          <ArrowDownIcon class="w-4 h-4 ms-2 -me-0.5" aria-hidden="true" />
        </BaseButton>
      </div>
    </div>

    <BookingFilterForm :tags="tags.data" @changed="filterBookings" />

    <div class="flex flex-col mt-8">
      <bookings-grid
        :booking-list="tableData.bookingList"
        :is-loading="tableData.processing"
        @open-booking="openEvent"
      >
        <!-- <template #actions="prop">
                <BaseButton
                  class="inline-flex" custome-bg="bg-gray-700" show-icon
                  @click="() => editRecord(prop.booking)"
                >
                  {{ $t('form.showBooking') }}
                </BaseButton>
              </template> -->
      </bookings-grid>
      <Pagination
        v-if="tableData.bookingList.length"
        :pagination-meta="tableData.paginationMeta"
        :pagination-links="tableData.paginationLinks"
        @change="changeData"
      />
    </div>
  </div>
</template>
