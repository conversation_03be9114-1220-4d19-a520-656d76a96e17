<script lang="ts" setup>
import { find, sumBy, uniqueId } from 'lodash'
import { storeToRefs } from 'pinia'
import { usePosStore } from '@/stores/pos'
import useNotifications from '@/composables/useNotifications'

const route = useRoute()
const router = useRouter()

const authStore = useAuthStore()
const userInfo = computed(() => authStore.userInfo)

const { checkoutItems, items, checkoutData, processing, formData } = storeToRefs(usePosStore())
const { createPos, checkout, clearPos, services, checkoutCalculate } = usePosStore()
const { showNotification } = useNotifications()

const errHandle = ref([])

const hasApiError = computed(() => {
  return errHandle.value.length
})

const next = async () => {
  errHandle.value = []
  processing.value = true
  await createPos(formData.value).then((res) => {
  }).catch((e) => {
    if (e.errors)
      for (const prop in e.errors) errHandle.value.push(e.errors[prop])
  }).finally(() => {
    processing.value = false
  })
}

const bookingData = computed(() => {
  if (route.name !== 'checkout')
    return {}
  const { staff_id, booking_id, branch_id, customer, services } = route.query
  return { staff_id, booking_id, branch_id, customer: customer ? JSON.parse(customer) : null, services: services ? JSON.parse(services) : null }
})

onMounted(() => {
  if (bookingData.value.services && bookingData.value.services.length) {
    checkoutItems.value = bookingData.value.services.map((service) => {
      return {
        name: service.name,
        price: service.price,
        selectedStaff: bookingData.value.staff_id,
        id: service.uuid,
        uuid: uniqueId(),
        subTotal: service.price,
        type: 'services',
        discount: 0,
        qty: 1,
      }
    })
  }
  if (bookingData.value.customer)
    checkoutData.value.customer = bookingData.value.customer

  if (bookingData.value.booking_id)
    checkoutData.value.booking_id = bookingData.value.booking_id
})

onUnmounted(() => {
  clearPos()
})

watch(items, async (value) => {
  if (!value.length)
    return
  processing.value = true
  const data = {
    branch_id: bookingData.value.branch_id || checkoutData.value.team_id,
    customer_id: bookingData.value.customer?.id,
    booking_id: bookingData.value.booking_id,
    items: items.value.map(o => ({
      item_id: o.id,
      price: o.price,
      discount: o.discount,
      staff_id: o.selectedStaff,
      quantity: o.qty,
    })),
  }
  await checkoutCalculate(data).then((res) => {
    checkoutData.value.subTotalBeforeTax = res.subTotalBeforeTax
    checkoutData.value.discount = res.discount
    checkoutData.value.totalTaxable = res.totalTaxable
    checkoutData.value.taxes = res.taxes
    checkoutData.value.total = res.total
  }).catch((e) => {
    if (e.errors)
      for (const prop in e.errors) errHandle.value.push(e.errors[prop])
  }).finally(() => {
    processing.value = false
  })
})
</script>

<template>
  <div class="checkout block md:flex w-full overflow-visible md:overflow-hidden relative">
    <OverlayLoader v-if="processing" :full-screen="false" />
    <div
      id="checkout-items"
      class="w-auto h-auto sm:w-full sm:h-full overflow-y-scroll hide-scroll px-6 py-3 p-l-0 bg-gray-50"
    >
      <ul
        v-if="hasApiError"
        class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded relative mb-2 text-sm" role="alert"
      >
        <li v-for="(err, index) in errHandle" :key="index" class="font-bold [&>*:not(:first-child)]:mt-2">
          {{ err.join() }}
        </li>
      </ul>
      <div v-if="!checkoutItems.length" class="w-full p-6 text-center">
        {{ $t('pos.no_item') }}
      </div>

      <template v-else>
        <checkout-item
          v-for="(item, i) in items" :key="`checkout-item-${i}`" v-model="checkoutItems[i]"
          @change="checkoutItems[i].subTotal = (checkoutItems[i].price * checkoutItems[i].qty) - checkoutItems[i].discount"
        />
      </template>

      <div v-if="checkoutItems.length" class="flex justify-end items-start mt-4">
        <div class="w-full md:w-[300px]">
          <div class="flex items-center justify-between border-b-2 border-gray-200 p-3">
            <span>{{ $t('pos.subtotal') }}</span>
            <span><price-format
              :form-data="{
                price: checkoutData.subTotalBeforeTax || 0,
                currency: userInfo?.tenant?.currency || '',
              }"
            />
            </span>
          </div>

          <div class="flex items-center justify-between border-b-2 border-gray-200 p-3">
            <span>{{ $t('pos.discount') }}</span>
            <span><price-format
              :form-data="{
                price: checkoutData.discount || 0,
                currency: userInfo?.tenant?.currency || '',
              }"
            />
            </span>
          </div>

          <div class="flex items-center justify-between border-b-2 border-gray-200 p-3">
            <span>{{ $t('pos.totalTaxable') }}</span>
            <span><price-format
              :form-data="{
                price: checkoutData.totalTaxable || 0,
                currency: userInfo?.tenant?.currency || '',
              }"
            />
            </span>
          </div>

          <div class="flex items-center justify-between border-b-2 border-gray-200 p-3">
            <span>{{ $t('pos.tax') }}</span>
            <span><price-format
              :form-data="{
                price: checkoutData.taxes || 0,
                currency: userInfo?.tenant?.currency || '',
              }"
            />
            </span>
          </div>
          <div class="flex items-center justify-between border-b-2 border-gray-200 p-3">
            <span>{{ $t('pos.total') }}</span>
            <span><price-format
              :form-data="{
                price: checkoutData.total || 0,
                currency: userInfo?.tenant?.currency || '',
              }"
            />
            </span>
          </div>
        </div>
      </div>
    </div>
    <div
      id="checkout-board"
      class="relative w-auto h-auto min-h-[400px] sm:w-half md:w-[500px] bg-white shadow-lg p-5 pb-16 md:pb-16"
    >
      <checkout-product-selection :team-id="bookingData.branch_id" />
      <div class="absolute bg-white shadow-md w-full bottom-0 left-0 mt-5 p-3" @click="next">
        <button class="p-4 rounded-md text-white font-semibold w-full bg-blue-500 hover:bg-blue-700 duration-150">
          {{ $t('checkout') }}
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.checkout {
  height: calc(100vh - 3rem);
}
</style>
