import { createI18n } from 'vue-i18n'
import messages from '@intlify/unplugin-vue-i18n/messages'
import ar from '../locales/ar.json'
import en from '../locales/en.json'

// Merge all messages with priority to explicitly imported files
const loadedMessages = {
  // Messages loaded by unplugin-vue-i18n
  ...messages,
  // Explicitly loaded messages (these take precedence)
  ar,
  en,
}

const i18n = createI18n({
  legacy: false,
  locale: 'ar', // Set Arabic as default
  fallbackLocale: 'en',
  messages: {
    en,
    ar,
  },
  globalInjection: true,
  availableLocales: ['ar', 'en'],
  silentTranslationWarn: false,
  missingWarn: true,
  fallbackWarn: true,

})

export default i18n
