import { storeToRefs } from 'pinia'

interface RoutePermission {
  [key: string]: Array<string>
}
const routePermissions: RoutePermission = {
  calendar: ['calendar.all'],
  customers: ['customers'],
  bookings: ['bookings'],
  transaction: ['sales.payments'],
  sales: ['sales.invoices'],
  package: ['sales.packages'],
  services: ['services'],
  serviceMetrics: ['services', 'providers'],
  products: ['products'],
  providers: ['providers'],
  reports: ['reports'],
  bookingPage: ['marketing.booking_page'],
  reviews: ['marketing.reviews'],
  generalSettings: ['settings.general_setting'],
  branches: ['settings.branches'],
  users: ['settings.users'],
  roles: ['settings.roles'],
  categories: ['settings.categories'],
  taxes: ['settings.taxes'],
  subscriptions: ['settings.subscriptions'],
  tags: ['settings.tags'],
  customFields: ['settings.custom_fields'],
  reasons: ['settings.reasons'],
  apps: ['settings.apps'],
  notifications: ['settings.notifications'],
  payments: ['settings.payments'],
  coupons: ['marketing.coupons'],
  orders: ['orders'],
  offers: ['marketing.offers'],
  customPages: ['marketing.pages'],
  questionsAndComments: ['comments'],
  boundaries: ['boundaries'],
  packages: ['packages'],
  purchasedPackages: ['sales.purchased_packages'],
  deletingData: ['settings.deleting_data'],
  plans: ['settings.plans'],
  campaigns: ['marketing.campaigns'],
  plugins: ['plugins'],
  expenses: ['expenses'],
  expensesTypes: ['type-expenses'],
  storeSettings: ['store.settings'],
  storeAppearance: ['store.appearance'],
  storeGallery: ['store.gallery'],
  storeContacts: ['store.contacts'],
  storeWebsiteManagement: ['store.website_management'],
  // modules permissions
  get settingsModule(): string[] {
    return this.generalSettings.concat(
      this.branches,
      this.users,
      this.roles,
      this.taxes,
      this.subscriptions,
      this.tags,
      this.customFields,
      this.reasons,
      this.categories,
      this.plans,
      this.apps,
      this.notifications,
      this.payments,
      this.deletingData,
      this.sales,
      this.expenses,
      this.expensesTypes,
    )
  },
  get storeSettingModule(): string[] {
    return this.sales.concat(
      this.storeSettings,
      this.storeAppearance,
      this.storeGallery,
      this.storeContacts,
      this.storeWebsiteManagement,
    )
  },
  get marketingModule(): string[] {
    return this.coupons.concat(
      this.offers,
      this.campaigns,
    )
  },
  get managementsModule(): string[] {
    return this.services.concat(
      this.providers,
      this.serviceMetrics,
      this.packages,
      this.products,
      this.questionsAndComments,
      this.boundaries,
      this.customers,
      this.orders,

    )
  },
  get storeModule(): string[] {
    return this.sales.concat(
      this.bookingPage,
      this.customPages,
      this.reviews,
    )
  },
  get salesModule(): string[] {
    return this.sales.concat(
      this.transaction,
      this.purchasedPackages,
      this.refunds,
    )
  },
}

const routeOrders: { [key: string]: number } = {
  dashboard: 1,
  calendar: 2,
  booking: 3,
  orders: 4,
  notifications: 5,
  whatsapp: 6,
  reports: 7,
  management: 8,
  sales: 9,
  store: 10,
  marketing: 11,
  storeAnalytics: 12,
  subscriptions: 13,
  settings: 14,
}
const recursiveCheckingPermissionRoutes = (
  routes: Array<any>,
  permissions: Array<string>,
): Array<any> => {
  const { getAvailablePlugins } = storeToRefs(usePluginsStore())
  const { getSubscriptionStatus } = storeToRefs(useAuthStore())

  const accessableRoutes: Array<any> = []
  routes.forEach((route) => {
    const routeHasPlugins = route.meta?.plugins?.length ? route.meta?.plugins.some((plugin: string) => getAvailablePlugins.value.includes(plugin)) : true
    if (route.meta?.permission) {
      if (route?.children?.length) {
        if (route.meta?.permission.some((permission: string) => permissions.includes(permission)) && routeHasPlugins) {
          const children = recursiveCheckingPermissionRoutes(
            route.children,
            permissions,
          )
          children.length ? accessableRoutes.push({ ...route, children }) : null
        }
      }
      else {
        if (
          route.meta?.permission.some((permission: string) =>
            permissions.includes(permission),
          ) && routeHasPlugins
        ) {
          // speiecal case
          if (getSubscriptionStatus.value && route.name == 'pricing') {
            accessableRoutes.push({
              ...route,
              meta: {
                ...route.meta,
                showInSidebar: false,
              },
            })
            return
          }
          accessableRoutes.push(route)
        }
      }
    }
    else {
      if (routeHasPlugins)
        accessableRoutes.push(route)
    }
  })
  return accessableRoutes
}

const canAccessRoute = (routePermissions: string[], permissions: Array<string>): boolean => {
  if (routePermissions?.length)
    return routePermissions.some((permission: string) => permissions.includes(permission))

  return true
}

export { recursiveCheckingPermissionRoutes, canAccessRoute, routeOrders }
export default routePermissions
