import type { HoursMinutes } from '@/types'

export const generateNumbersArrayInRange = (start: number, end: number): Array<number> => {
  const arr = []
  for (let i = start; i <= end; i++)
    arr.push(i)

  return arr
}

export const convertToHoursAndMinutes = (minutes: number, formatted = false): HoursMinutes | string => {
  const hours = Math.floor(minutes / 60)
  const mints = minutes % 60

  if (formatted)
    return `${hours.toString().padStart(2, '0')}:${mints.toString().padStart(2, '0')}`

  return { hours, minutes: mints }
}
