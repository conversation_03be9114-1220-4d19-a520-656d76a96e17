const convertToFormdata = (payload: Object): FormData => {
  const formData = new FormData()

  Object.keys(payload).forEach((key) => {
    const value = payload[key]

    if (typeof value === 'string' || value instanceof File)
      formData.append(key, value)
    else formData.append(key, JSON.stringify(value))
  })

  return formData
}

export const convertPyloadToFormdata = (payload: Object): FormData => {
  const formData = new FormData()

  Object.keys(payload).forEach((key) => {
    const value = payload[key];
    if (typeof value === "string" || value instanceof File || typeof value === "number")
      formData.append(key, value);
    else if (Array.isArray(value)) {
      value.forEach((item, index) => {
        if (typeof item === 'object' && item !== null) {
          // Handle array of objects by flattening: array[index][key] = value
          Object.keys(item).forEach((objectKey) => {
            const objectValue = item[objectKey]
            if (typeof objectValue === 'string' || objectValue instanceof File)
              formData.append(`${key}[${index}][${objectKey}]`, objectValue)
            else
              formData.append(`${key}[${index}][${objectKey}]`, JSON.stringify(objectValue))
          })
        }
        else {
          // Handle array of primitives
          formData.append(`${key}[${index}]`, item)
        }
      })
    }
    else if (typeof value === 'object' && value !== null) {
      formData.append(key, JSON.stringify(value))
    }
  })

  return formData
}

export const accessDeepObject = (obj: any, key: string) => {
  const keys = key.split('.')
  let value = obj
  for (const currentKey of keys) {
    if (typeof value == 'undefined' || !(currentKey in value))
      return
    value = value[currentKey]
  }
  return value
}

export default convertToFormdata
