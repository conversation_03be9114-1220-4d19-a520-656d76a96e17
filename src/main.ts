import { createApp } from 'vue'
import { createPinia, storeToRefs } from 'pinia'
import { createHead } from '@vueuse/head'
import Datepicker from '@vuepic/vue-datepicker'
import '@vuepic/vue-datepicker/dist/main.css'
import vue3starRatings from 'vue3-star-ratings'
import Multiselect from 'vue-multiselect'
import 'vue-multiselect/dist/vue-multiselect.css'
import VCalendar from 'v-calendar'
import Echo from 'laravel-echo'
import Pusher from 'pusher-js'
import axios from 'axios'
import VueHtmlToPaper from './plugins/VueHtmlToPaper'
import App from './App.vue'
import router from './router'
import 'v-calendar/dist/style.css'
import { AUTH_TOKEN } from '@/constants'
import './assets/main.css'
import './styles/styles.scss'
import i18n from './i18n'
import { canAccessRoute } from '@/utils/routePermissions'
import '@/plugins/dayjs'
import FloatingVue from 'floating-vue'
import 'floating-vue/dist/style.css'

const options = {
  name: '_blank',
  specs: ['fullscreen=yes', 'titlebar=yes', 'scrollbars=yes'],
  styles: ['https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css'],
}
axios.defaults.withCredentials = true
// intercome
const user_id = JSON.parse(window.localStorage.getItem('user-id'))
const phone = JSON.parse(window.localStorage.getItem('user-phone'))
const name = JSON.parse(window.localStorage.getItem('user-name'))
const email = JSON.parse(window.localStorage.getItem('user-email'))
const created_at = JSON.parse(window.localStorage.getItem('user-email'))
const user_hash = JSON.parse(window.localStorage.getItem('user-hash'))
window.intercomSettings = {
  api_base: 'https://api-iam.intercom.io',
  app_id: import.meta.env.VITE_INTERCOME_APP_ID,
  name,
  email,
  phone,
  user_id,
  user_hash,
  created_at, // Signup date as a Unix timestamp
}

const app = createApp(App)

const pusher = new Pusher(import.meta.env.VITE_PUSHER_KEY, {
  cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER,
})

const echo = new Echo({
  broadcaster: 'pusher',
  key: import.meta.env.VITE_PUSHER_KEY,
  wsHost: import.meta.env.VITE_PUSHER_HOST,
  cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER,
  wsPort: 443,
  disableStats: true,
  forceTLS: false,
  wssPort: 443,
  encrypted: true,
  authEndpoint: `${import.meta.env.VITE_API_URL}/broadcasting/auth`,
  auth: {
    headers: {
      Authorization: `Bearer ${localStorage.getItem(AUTH_TOKEN)}`,
    },
  },
})

app.provide('$echo', echo)
const pinia = createPinia()
app.use(pinia)
pinia.use(({ store }) => {
  store.router = markRaw(router)
})
const head = createHead()

app.use(head)

router.beforeEach(async (to, from) => {
  const authStore = useAuthStore()
  const routeStore = useRoutesStore()
  const { getAvailablePlugins } = storeToRefs(usePluginsStore())

  const userAuth = localStorage.getItem('userAuth')
  const userAuthenticated = userAuth ? JSON.parse(userAuth) === true : false
  const hasUserData = authStore.hasUserData
  const { fetchAppNotifications } = useNotificationTemplate()
  try {
    // user is authenticated , and user try refresh page

    if (userAuthenticated && !hasUserData) {
      await authStore.getProfile()
      if (authStore.getUserInfo?.profile_completed) {
        await fetchAppNotifications()
        authStore.fetchUnreadModulesCount()
      }
    }
  }
  catch {
    return { name: 'auth', query: { section: 'signin' } }
  }

  const userProfileCompleted = authStore.getUserInfo?.profile_completed
  const userPhoneVerified = authStore.getUserInfo?.phone_verified
  const accessableRoutes = routeStore.getAccassableRoutes
  const hasPlugins = to.meta?.plugins?.length ? to.meta?.plugins.some((plugin: string) => getAvailablePlugins.value.includes(plugin)) : true
  const isSubscriptionAndTrialEnded = authStore.isSubscriptionAndTrialEnded
  const permissionList = authStore.getUserInfo?.permissions
  const hasPos = Boolean(authStore.getUserInfo?.tenant?.new_pos)
  if (userAuthenticated) {
    if ((!userPhoneVerified) && to.name !== 'VerificationOtp') {
      return { name: 'VerificationOtp' }
    }
    else if (userPhoneVerified && userProfileCompleted && to.meta?.auth) {
      return { name: 'dashboard' }
    }
    else if (!userProfileCompleted && userPhoneVerified && to.name !== 'onboarding' && !to.path.startsWith('/onboarding') && to.name !== 'launch-profile') {
      return { name: 'onboarding' }
    }
    else if (to.name == undefined || to.name == 'auth') {
      return { name: 'dashboard' }
    }
    else {
      // check installed plugins
      if (!hasPlugins)
        return { name: 'not-found-plugin' }

      // edge cases for pos
      if (to.name == 'point-of-sale' && hasPos) {
        window.location.href = 'https://pos.mahjoz.io'
        return { name: 'dashboard' }
      }
      // check if user has permission to access route
      if (canAccessRoute(to.meta?.permission, permissionList))
        return true

      // if user has no permission to access route
      else return { name: 'unauthorized' }
    }
    // user accessing route require authentication and user is authenticated
  }
  else {
    if (to.meta.layout !== 'authLayout')
      return { name: 'login' }
  }
})

router.onError((error, to) => {
  if (error.message.includes('Failed to fetch dynamically imported module'))
    window.location = to.fullPath
})
app.use(FloatingVue)

app.use(VueHtmlToPaper, options)
app.use(router)
app.use(i18n)
app.use(Multiselect)
app.use(VCalendar)
app.use(head)

app.component('Datepicker', Datepicker)
app.component('Vue3StarRatings', vue3starRatings)
app.component('Multiselect', Multiselect)

router.isReady().then(() => {
  app.mount('#app')
})
