<!DOCTYPE html>
<html lang="en" class="h-full bg-tertiary scroll-smooth">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>Mahjoz | محجوز</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;700&display=swap" rel="preload" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link href="https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <script>
        (function() {
            const w = window
            const ic = w.Intercom
            if (typeof ic === 'function') {
                ic('reattach_activator');
                ic('update', w.intercomSettings)
            } else {
                const d = document
                const i = function() {
                    i.c(arguments)
                }
                i.q = []
                i.c = function(args) {
                    i.q.push(args)
                }
                w.Intercom = i
                const l = function() {
                    const s = d.createElement('script')
                    s.type = 'text/javascript'
                    s.async = true
                    s.src = 'https://widget.intercom.io/widget/zze48id8'
                    const x = d.getElementsByTagName('script')[0]
                    x.parentNode.insertBefore(s, x)
                }
                if (document.readyState === 'complete')
                    l()
                else if (w.attachEvent)
                    w.attachEvent('onload', l)
                else w.addEventListener('load', l, false)
            }
        })()
    </script>
</head>

<body class="h-full bg-tertiary">
    <div id="app" class="h-full">
        <div class="flex flex-col items-center justify-center h-full ">
            <img src="./src/assets/logo.svg" alt="Mahjoz" class=" w-48 h-48  mx-auto  pulse" />
            <p class="pulse text-lg">
                Loading ...
            </p>
        </div>

    </div>
    <script type="module" src="/src/main.ts"></script>
</body>



<script type="text/javascript" id="pap_x2s6df8d" src="https://www.linkaraby.com/scripts/2xjh8l8dq0"></script>
<script type="text/javascript">
PostAffTracker.setAccountId('ab65b0d7');
try {
PostAffTracker.track();
} catch (err) { }
</script>


</html>
<style>
    .pulse {
        animation: pulse 1s infinite;
    }
    
    @keyframes pulse {
        0% {
            transform: scale(0.95);
        }
        70% {
            transform: scale(1);
        }
        100% {
            transform: scale(0.95);
        }
    }
</style>