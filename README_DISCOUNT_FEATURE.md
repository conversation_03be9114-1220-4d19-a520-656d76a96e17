# Global Discount Feature for Create Order Form

## Overview
A comprehensive discount system has been added to the Create Order form that allows applying discounts either globally to all items or individually to specific items.

## Features Implemented

### 🧾 Global Discount Input
- **Discount Amount Field**: Input field to enter the discount value
- **Discount Type Selector**: Choose between "Fixed Amount" or "Percentage"
- **Apply to All Checkbox**: Option to apply the discount to all items at once

### ✅ Item-Level Discount Application
- **Individual Checkboxes**: Each order item (Service, Product, Package) now has an "Apply Discount" checkbox
- **Automatic Conflict Resolution**: When global discount is applied, individual item discounts are automatically cleared
- **Visual Indicators**: Clear visual feedback showing which items have discounts applied

### 📤 Enhanced Payload Structure
The order payload now includes:
```json
{
  "discountAmount": 50,
  "discountType": "fixed", // or "percentage"
  "apply_all": true,
  "services": [
    {
      "id": "service-1",
      "applyDiscount": true,
      "price": 100,
      "quantity": 1
      // ... other service properties
    }
  ]
}
```

## Technical Implementation

### Modified Components
1. **ModalOrder.vue** - Main order creation form
   - Added global discount section
   - Enhanced payload creation
   - Added Apply to All functionality

2. **OrderServices.vue** - Service item component
   - Added "Apply Discount" checkbox
   - Clear individual discounts when global discount is applied

3. **OrderProducts.vue** - Product item component
   - Added "Apply Discount" checkbox with product-specific ID

4. **OrderPackage.vue** - Package item component
   - Added "Apply Discount" checkbox with package-specific ID

### Type Definitions
Updated `BookingService` interface in `src/types/orders.ts`:
```typescript
export interface BookingService {
  // ... existing properties
  applyDiscount?: boolean;
}
```

### Translation Keys
Added comprehensive translation support for both English and Arabic:
- `global_discount` - "Global Discount"
- `discount_type` - "Discount Type" 
- `fixed_amount` - "Fixed Amount"
- `percentage` - "Percentage"
- `apply_discount` - "Apply Discount"
- `apply_discount_to_all_items` - "Apply Discount to All Items"
- `discount_applied_to_all` - "Discount applied to {count} items"

## Usage Instructions

### For Users
1. **Adding Global Discount**:
   - Enter discount amount in the "Global Discount" section
   - Select discount type (Fixed Amount or Percentage)
   - Check "Apply to All Items" to apply to all items at once

2. **Individual Item Discounts**:
   - Check "Apply Discount" on specific items for selective application
   - Individual item discounts will be cleared when global discount is applied

### For Developers
1. **Backend Integration**: The payload structure is ready for backend processing with clear discount indicators
2. **Validation**: Frontend validation ensures discount amounts are positive and percentages don't exceed 100%
3. **Conflict Resolution**: Automatic handling of discount conflicts between global and individual item discounts

## Benefits
- **Flexible Discount Options**: Support for both global and item-specific discounts
- **User-Friendly Interface**: Intuitive checkboxes and clear visual feedback
- **Conflict Prevention**: Automatic resolution of discount conflicts
- **Comprehensive Payload**: Complete discount information in API calls
- **Internationalization**: Full support for multiple languages

## Future Enhancements
- Discount preview calculations
- Maximum discount limits
- Discount code integration
- Audit trail for discount applications 