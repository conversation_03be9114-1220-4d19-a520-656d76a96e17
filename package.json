{"name": "mahjoz-dashboard", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 4173", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@editorjs/attaches": "^1.3.0", "@editorjs/code": "^2.8.0", "@editorjs/delimiter": "^1.3.0", "@editorjs/editorjs": "^2.28.0", "@editorjs/embed": "^2.5.3", "@editorjs/header": "^2.7.0", "@editorjs/inline-code": "^1.4.0", "@editorjs/link": "^2.5.0", "@editorjs/list": "^1.8.0", "@editorjs/marker": "^1.3.0", "@editorjs/paragraph": "^2.10.0", "@editorjs/quote": "^2.5.0", "@editorjs/raw": "^2.4.0", "@editorjs/simple-image": "^1.5.1", "@editorjs/table": "^2.2.2", "@editorjs/warning": "^1.3.0", "@fawmi/vue-google-maps": "^0.9.79", "@flareapp/js": "^1.1.0", "@flareapp/vite": "^1.0.0", "@flareapp/vue": "^1.0.1", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.7", "@fullcalendar/interaction": "^6.1.7", "@fullcalendar/list": "^6.1.7", "@fullcalendar/resource": "^6.1.7", "@fullcalendar/resource-timegrid": "^6.1.11", "@fullcalendar/scrollgrid": "^6.1.11", "@fullcalendar/timegrid": "^6.1.7", "@fullcalendar/vue3": "^6.1.7", "@gtm-support/vue-gtm": "^2.0.0", "@headlessui/vue": "1.7.22", "@heroicons/vue": "^2.0.16", "@intlify/unplugin-vue-i18n": "^0.11.0", "@intlify/vite-plugin-vue-i18n": "^6.0.3", "@sentry/vite-plugin": "^2.9.0", "@sentry/vue": "^7.77.0", "@tailwindcss/forms": "^0.5.3", "@vuelidate/core": "2.0.0-alpha.44", "@vuelidate/validators": "2.0.0-alpha.31", "@vuepic/vue-datepicker": "^5.0.0", "@vueup/vue-quill": "^1.1.0", "@vueuse/core": "^9.13.0", "@vueuse/head": "^0.7.13", "@vueuse/integrations": "^9.13.0", "axios": "^0.27.2", "chart.js": "^4.4.0", "chartjs-plugin-datalabels": "^2.2.0", "dayjs": "^1.11.7", "editorjs-text-alignment-blocktune": "^1.0.3", "editorjs-text-color-plugin": "^2.0.4", "floating-vue": "^5.2.2", "gmap-vue": "^3.5.4", "laravel-vue-pagination": "^4.1.0", "lodash": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.0.32", "pnpm": "^7.33.7", "swiper": "^11.1.14", "universal-cookie": "^4.0.4", "v-calendar": "3.0.0-alpha.8", "vite-plugin-pwa": "^0.17.4", "vue": "^3.2.47", "vue-demi": "^0.13.11", "vue-draggable-next": "^2.2.1", "vue-html-to-paper": "^1.4.4", "vue-i18n": "^9.2.2", "vue-meta": "3.0.0-alpha.7", "vue-multiselect": "^3.0.0-alpha.2", "vue-router": "^4.1.6", "vue-sweetalert2": "^5.0.5", "vue-tailwind-datepicker": "^1.3.2", "vue3-google-login": "^2.0.14", "vue3-google-map": "^0.15.0", "vue3-google-oauth2": "^1.0.7", "vue3-otp-input": "^0.5.30", "vue3-star-ratings": "^1.1.0", "vue3-tags-input": "^1.0.7", "vue3-tel-input": "^1.0.4"}, "devDependencies": {"@antfu/eslint-config": "^0.26.3", "@rushstack/eslint-patch": "^1.2.0", "@types/node": "^18.19.80", "@types/nprogress": "^0.2.0", "@vitejs/plugin-vue": "^4.6.2", "@vue/eslint-config-typescript": "^11.0.2", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.13", "eslint": "^8.35.0", "pusher-js": "^8.0.2", "eslint-plugin-vue": "^9.9.0", "laravel-echo": "^1.15.0", "postcss": "^8.4.21", "sass": "^1.62.1", "tailwindcss": "^3.2.7", "tailwindcss-rtl": "^0.9.0", "typescript": "~4.8.4", "unplugin-auto-import": "^0.11.5", "unplugin-vue-components": "^0.22.12", "vite": "^4.5.9", "vue-tsc": "^1.6.3"}}