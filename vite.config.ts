import { dirname, resolve } from 'node:path'
import { URL, fileURLToPath } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import AutoImport from 'unplugin-auto-import/vite'
// import {VitePWA} from 'vite-plugin-pwa'
import Components from 'unplugin-vue-components/vite'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    VueI18nPlugin({
      runtimeOnly: false,
      // if you want to use Vue I18n Legacy API, you need to set `compositionOnly: false`
      // compositionOnly: false,
      compositionOnly: true,
      // you need to set i18n resource including paths !
      include: resolve(dirname(fileURLToPath(import.meta.url)), './locales/**'),
    }),

    // VitePWA({
    //   // https://vite-pwa-org.netlify.app/
    //   devOptions: {
    //     enabled: false
    //     /* other options */
    //   },
    //     registerType: 'autoUpdate',
    //     injectRegister: 'auto',
    //     includeAssets: ['favicon.svg', 'mahjoz.png'], // Corrected duplicate icon entry
    //     workbox: {
    //       globPatterns: ['**/*.{js,css,html,ico,png,svg,json,vue,txt,woff2}'],
    //       cleanupOutdatedCaches: true
    //     },
    //     manifest: {
    //       display: 'standalone',
    //       name: 'Mahjoz | محجوز',
    //       short_name: 'Mahjoz',
    //       description: 'Mahjoz is a platform that helps you manage your appointments and reservations',
    //       theme_color: '#ffffff',
    //       icons: [
    //         {
    //           src: "/mahjoz.png",
    //           sizes: "192x192",
    //           type: "image/png",
    //           purpose: "any maskable",
    //         },
    //         {
    //           src: "/mahjoz.png",
    //           sizes: "512x512",
    //           type: "image/png",
    //           purpose: "any maskable",
    //         },
    //       ],
    //     },
    // }),
    Components({
      extensions: ['vue'],
      include: [/\.vue$/, /\.vue\?vue/],
      dts: 'src/components.d.ts',
    }),

    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'vue-i18n',
        'vue/macros',
        '@vueuse/head',
        '@vueuse/core',
      ],
      dts: 'src/auto-imports.d.ts',
      dirs: [
        'src/stores',
        'src/composables',
      ],
      vueTemplate: true,
    }),

  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        silenceDeprecations: ['legacy-js-api'],
        api: 'modern',
        // Use modern API instead of legacy API
      },
    },
  },
  publicDir: 'public',
  build: {
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.endsWith('.png'))
            return 'assets/icons/industries/[name][extname]'

          return 'assets/[name]-[hash][extname]'
        },
      },
    },
  },
})
